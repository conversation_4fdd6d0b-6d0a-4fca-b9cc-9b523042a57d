import { IConfig } from 'umi-types';
import { resolve } from 'path';

const BUILD = process.env.BUILD;
let publicPath =
  BUILD === 'dev' || BUILD === 'prod'
    ? `//${(BUILD === 'dev' && 'test') || ''}fund.10jqka.com.cn/public/whw/boplatform/dist/`
    : './';
console.log('build: ' + BUILD + ' -> ' + publicPath);

const config: IConfig = {
  ignoreMomentLocale: true,
  treeShaking: true,
  history: 'hash',
  publicPath,
  targets: { ie: 9 },
  routes: !BUILD
    ? [
        // Alert: 仅在本地开发使用
        { path: '/', redirect: '/home' },
        { path: '/login', exact: true, component: './login' },
        { path: '/home', exact: true, component: './app' },
        {
          path: '/form/buyPrivate/buyPrivateList',
          exact: true,
          component: './form/buyPrivate/buyPrivateList',
        },
        {
          path: '/form/buyPrivate/setPrivateFile',
          exact: true,
          component: './form/buyPrivate/setPrivateFile',
        },
        {
          path: '/report/walletCurrencyWeightSetting',
          exact: true,
          component: './report/walletCurrencyWeightSetting',
        },
        // 通用基金池配置
        {
          path: './form/commonFundPool',
          exact: true,
          component: './form/commonFundPool',
        },
        // 合作基金
        { path: '/form/cooperateFund', exact: true, component: './form/cooperateFund' },
        { path: '/form/hotOpportunity', exact: true, component: './form/hotOpportunity' },
      ]
    : [
        { path: '/login', exact: true, component: './login' },
        {
          path: '/',
          component: '../layouts/index',
          routes: [
            { path: '/', redirect: '/home' },
            { path: '/home', exact: true, component: './app' },
            { path: '/test', component: './test' },
            { path: '/form/formrender', component: './test/formrender' },

            // 开户流程
            { path: '/form/openAccount/stay', exact: true, component: './form/openAccount/stay' },
            {
              path: '/form/openAccount/result',
              exact: true,
              component: './form/openAccount/result',
            },

            { path: '/system/user', exact: true, component: './system/user' },
            { path: '/system/userManagement', exact: true, component: './system/userManagement' },
            { path: '/system/role', exact: true, component: './system/role' },
            { path: '/system/menu', exact: true, component: './system/menu' },
            { path: '/system/dept', exact: true, component: './system/dept' },

            { path: '/monitor/online', exact: true, component: './monitor/online' },

            { path: '/mg/fechoose', exact: true, component: './mg/fechoose' },
            { path: '/mg/docx2html', exact: true, component: './mg/docx2html' },
            { path: '/mg/protocoltest', exact: true, component: './mg/protocoltest' },

            // 基顺通
            {
              path: '/report/tradeDetail',
              exact: true,
              component: './report/JiShunTong/tradeDetail',
            },
            {
              path: '/report/stockSummary',
              exact: true,
              component: './report/JiShunTong/summaryInfo',
            },

            { path: '/report/bankqy', exact: true, component: './report/bankqy' },
            { path: '/report/tradeorders', exact: true, component: './report/tradeorders' },
            { path: '/report/group', exact: true, component: './report/group' },
            { path: '/report/unqualified', exact: true, component: './report/unqualified' },
            { path: '/report/contrastTTFund', exact: true, component: './report/contrastTTFund' },
            { path: '/report/stockDataBase', exact: true, component: './report/stockDataBase' },
            {
              path: '/report/antiMoneyLaundering',
              exact: true,
              component: './report/antiMoneyLaundering',
            },
            { path: '/report/tyjData', exact: true, component: './report/tyjData' },
            { path: '/report/blackList', exact: true, component: './report/blackList' },
            // 红包资金审核后台
            { path: '/report/redpaperMoney', exact: true, component: './report/redpaperMoney' },
            //一键转仓
            { path: '/form/yjzc/edit', exact: true, component: './form/yjzc/edit' },
            { path: '/form/yjzc/product', exact: true, component: './form/yjzc/product' },
            // 基金客户端系统性风险解决优化
            { path: '/form/clientRisk/list', exact: true, component: './form/clientRisk/list' },
            { path: '/form/clientRisk/detail', exact: true, component: './form/clientRisk/detail' },
            { path: '/form/uploadFile', exact: true, component: './form/uploadFile' }, //文件上传通用页面

            // 个基指标战法
            { path: '/form/targetStrategy', exact: true, component: './form/targetStrategy' },

            // 实盘大赛
            {
              path: '/form/firmOffer/list',
              exact: true,
              component: './form/firmOfferCompetition/list',
            },
            {
              path: '/form/firmOffer/competitionInfo',
              exact: true,
              component: './form/firmOfferCompetition/competitionInfo',
            },
            {
              path: '/form/firmOffer/fundDisplay',
              exact: true,
              component: './form/firmOfferCompetition/fundDisplay',
            },
            {
              path: '/form/firmOffer/notice',
              exact: true,
              component: './form/firmOfferCompetition/notice',
            },
            {
              path: '/form/firmOffer/mainVenue',
              exact: true,
              component: './form/firmOfferCompetition/mainVenue',
            },
            {
              path: '/form/firmOffer/position',
              exact: true,
              component: './form/firmOfferCompetition/position',
            },
            {
              path: '/form/firmOffer/outFund',
              exact: true,
              component: './form/firmOfferCompetition/outFund',
            },

            { path: '/form/abtest/detail', exact: true, component: './form/abtest/detail' },
            { path: '/form/abtest', exact: true, component: './form/abtest' },
            { path: '/form/mail/detail', exact: true, component: './form/mail/mailDetail' },
            { path: '/form/mail/list', exact: true, component: './form/mail/mailList' },
            { path: '/form/push/detail', exact: true, component: './form/push/pushDetail' },
            { path: '/form/push/list', exact: true, component: './form/push/pushList' },
            { path: '/form/salarymanager', exact: true, component: './form/salarymanager' },
            // 赎回挽留弹窗配置页
            { path: '/form/redeemStay', exact: true, component: './form/redeemStay' },
            { path: '/form/redeemStay/detail', exact: true, component: './form/redeemStay/detail' },
            { path: '/form/redeemback', exact: true, component: './form/redeemback' },
            { path: '/form/fourmoney', exact: true, component: './form/fourmoney' },
            { path: '/form/fundProfitLoss', exact: true, component: './form/fundProfitLoss' },
            {
              path: '/form/activityPage/activity',
              exact: true,
              component: './form/activityPage/codeInterface',
            },
            {
              path: '/form/strategyBlock/industryBlock',
              exact: true,
              component: './form/strategyBlock/industryBlock',
            },
            {
              path: '/form/strategyBlock/valueBlock',
              exact: true,
              component: './form/strategyBlock/valueBlock',
            },
            {
              path: '/form/strategyBlock/valueRecommendBlcok',
              exact: true,
              component: './form/strategyBlock/valueRecommendBlcok',
            },
            {
              path: '/form/strategyBlock/searchValueRecommend',
              exact: true,
              component: './form/strategyBlock/searchValueRecommend',
            },
            {
              path: '/form/plateRadar/plateRadarRecommend',
              exact: true,
              component: './form/plateRadar/recommend',
            },
            {
              path: '/form/plateRadar/plateRadarHistoryList',
              exact: true,
              component: './form/plateRadar/historyList',
            },
            { path: '/form/newFund', exact: true, component: './form/newFund' },
            { path: '/form/dtbanner', exact: true, component: './form/dtbanner' },
            { path: '/form/clientSearch', exact: true, component: './form/clientSearch' },
            { path: '/form/questionConfig', exact: true, component: './form/questionConfig' },

            { path: '/crmform/tradeerr', exact: true, component: './crmform/tradeerr' },
            { path: '/crmform/registerr', exact: true, component: './crmform/registerr' },
            { path: '/crmform/tradeSucess', exact: true, component: './crmform/tradeSucess' },
            { path: '/crmform/financeQuery', exact: true, component: './crmform/financeQuery' },

            { path: '/form/fundByTheme', exact: true, component: './form/fundByTheme' },
            {
              path: '/form/strategyBuyFund/main',
              exact: true,
              component: './form/strategyBuyFund/main',
            },
            {
              path: '/form/strategyBuyFund/normal',
              exact: true,
              component: './form/strategyBuyFund/normal',
            },
            { path: '/form/topFund', exact: true, component: './form/topFund' },
            { path: '/form/ideaSuggestion', exact: true, component: './form/ideaSuggestion' },

            { path: '/form/safelyWin/icon', exact: true, component: './form/safelyWin/icon' },
            { path: '/form/safelyWin/content', exact: true, component: './form/safelyWin/content' },
            {
              path: '/form/safelyWin/moneyManagement',
              exact: true,
              component: './form/safelyWin/moneyManagement',
            },
            {
              path: '/form/safelyWin/insurance',
              exact: true,
              component: './form/safelyWin/insurance',
            },
            { path: '/form/themeFund', exact: true, component: './form/themeFund' },
            { path: '/form/publicProd', exact: true, component: './form/publicProd' },
            { path: '/form/scStayAlert', exact: true, component: './form/scStayAlert' },
            { path: '/form/smallsetRule', exact: true, component: './form/smallsetRule' },
            {
              path: '/form/regularOpenFund/regularOpenFundOrder',
              exact: true,
              component: './form/regularOpenFund/regularOpenFundOrder',
            },
            {
              path: '/form/regularOpenFund/orderList',
              exact: true,
              component: './form/regularOpenFund/orderList',
            },
            { path: '/form/groupCompanion', exact: true, component: './form/groupCompanion' },
            { path: '/form/walletBreakBack', exact: true, component: './form/walletBreakBack' },
            { path: '/wechat/menu', exact: true, component: './wechat/menu' },
            { path: '/wechat/callback', exact: true, component: './wechat/callback' },
            //kyc用户标签组配置
            { path: '/form/kycTagConfig', exact: true, component: './form/kycTagConfig' },

            { path: '/form/morningPaper', exact: true, component: './form/morningPaper' },
            // 产品准入
            {
              path: '/form/productReview/upload/details',
              exact: true,
              component: './form/productReview/upload/details',
            },
            {
              path: '/form/productReview/upload/main',
              exact: true,
              component: './form/productReview/upload/main',
            },
            {
              path: '/form/productReview/upload/edit',
              exact: true,
              component: './form/productReview/upload/edit',
            },
            {
              path: '/form/productReview/Check/details',
              exact: true,
              component: './form/productReview/Check/details',
            },
            {
              path: '/form/productReview/Check/main',
              exact: true,
              component: './form/productReview/Check/main',
            },
            // ui审核
            { path: '/form/uiVertify/upload', exact: true, component: './form/uiVertify/upload' },
            { path: '/form/uiVertify/edit', exact: true, component: './form/uiVertify/edit' },
            { path: '/form/uiVertify/add', exact: true, component: './form/uiVertify/add' },
            {
              path: '/form/uiVertify/uireview',
              exact: true,
              component: './form/uiVertify/uireview',
            },
            {
              path: '/form/uiVertify/toreview',
              exact: true,
              component: './form/uiVertify/toreview',
            },
            // 直播/视频审核
            {
              path: '/form/videoVertify/upload',
              exact: true,
              component: './form/videoVertify/upload',
            },
            { path: '/form/videoVertify/edit', exact: true, component: './form/videoVertify/edit' },
            { path: '/form/videoVertify/add', exact: true, component: './form/videoVertify/add' },
            {
              path: '/form/videoVertify/videoreview',
              exact: true,
              component: './form/videoVertify/videoreview',
            },
            {
              path: '/form/videoVertify/toreview',
              exact: true,
              component: './form/videoVertify/toreview',
            },
            // 大V实盘（组合产品配置和通顺号配置）
            { path: '/form/bigV/edit', exact: true, component: './form/bigV/edit' },
            { path: '/form/bigV/config', exact: true, component: './form/bigV/mulProdConfig' },
            { path: '/form/activityPage/tyj', exact: true, component: './form/activityPage/tyj' },
            {
              path: '/form/activityPage/generalExport',
              exact: true,
              component: './form/activityPage/generalExport',
            },
            {
              path: '/form/activityPage/packet',
              exact: true,
              component: './form/activityPage/packet',
            },
            {
              path: '/form/activityPage/kycMap',
              exact: true,
              component: './form/activityPage/kycMap',
            },
            {
              path: '/form/activityPage/tyjText',
              exact: true,
              component: './form/activityPage/tyjText',
            },
            {
              path: '/form/activityPage/activities',
              exact: true,
              component: './form/activityPage/activities',
            },
            {
              path: '/form/activityPage/packetTemp',
              exact: true,
              component: './form/activityPage/packetTemp',
            },
            {
              path: '/form/activityPage/ETFActivity',
              exact: true,
              component: './form/activityPage/ETFActivity',
            },
            {
              path: '/form/activityPage/tenBillion',
              exact: true,
              component: './form/activityPage/tenBillion',
            },
            { path: '/form/peValuation', exact: true, component: './form/peValuation' },
            { path: '/form/chooseFund', exact: true, component: './form/chooseFund' },
            { path: '/form/kycCard', exact: true, component: './form/kycCard' },
            { path: '/form/hangyeBao', exact: true, component: './form/hangyeBao' },
            { path: '/form/costKanPan', exact: true, component: './form/costKanPan' },
            { path: '/form/sevenDayLcb', exact: true, component: './form/sevenDayLcb' },

            { path: '/form/fundManager', exact: true, component: './form/fundManager' },
            { path: '/form/newFundNew', exact: true, component: './form/newFundNew' },
            { path: '/form/test/json', exact: true, component: './form/test/json' },
            { path: '/form/test/body', exact: true, component: './form/test/body' },
            { path: '/form/test/newJson', exact: true, component: './form/test/newJson' },
            { path: '/form/test/newBody', exact: true, component: './form/test/newBody' },
            { path: '/form/test/tool', exact: true, component: './form/test/tool' },

            { path: '/form/lazyCat/interact', exact: true, component: './form/lazyCat/interact' },
            { path: '/form/lazyCat/chat', exact: true, component: './form/lazyCat/chat' },
            { path: '/form/lazyCat/prod', exact: true, component: './form/lazyCat/prod' },
            { path: '/form/lazyCat/home', exact: true, component: './form/lazyCat/home' },
            { path: '/form/lazyCat/protocol', exact: true, component: './form/lazyCat/protocol' },
            {
              path: '/form/lazyCat/strategyType',
              exact: true,
              component: './form/lazyCat/strategyType',
            },
            {
              path: '/form/lazyCat/investAdviser',
              exact: true,
              component: './form/lazyCat/investAdviser',
            },
            { path: '/form/groupRedeem', exact: true, component: './form/groupRedeem' },
            { path: '/form/groupList', exact: true, component: './form/groupList' },
            { path: '/form/securityCheck', exact: true, component: './form/securityCheck' },
            { path: '/form/networkProtocol', exact: true, component: './form/networkProtocol' },
            { path: '/form/liveConfig', exact: true, component: './form/liveConfig' },
            // { path: '/form/yanxuanGoodFund/hyzt&dpzs', exact: true, component: './form/yanxuanGoodFund/hyzt&dpzs' },
            {
              path: '/form/yanxuanGoodFund/lcjj',
              exact: true,
              component: './form/yanxuanGoodFund/lcjj',
            },
            {
              path: '/form/yanxuanGoodFund/nrnj',
              exact: true,
              component: './form/yanxuanGoodFund/nrnj',
            },
            {
              path: '/form/yanxuanGoodFund/xqlc',
              exact: true,
              component: './form/yanxuanGoodFund/xqlc',
            },
            {
              path: '/form/yanxuanGoodFund/hyzt',
              exact: true,
              component: './form/yanxuanGoodFund/hyzt',
            },
            {
              path: '/form/yanxuanGoodFund/dpzs',
              exact: true,
              component: './form/yanxuanGoodFund/dpzs',
            },
            {
              path: '/form/yanxuanGoodFund/hwtz',
              exact: true,
              component: './form/yanxuanGoodFund/hwtz',
            },
            {
              path: '/form/yanxuanGoodFund/lrlc',
              exact: true,
              component: './form/yanxuanGoodFund/lrlc',
            },
            {
              path: '/form/yanxuanGoodFund/lhcq',
              exact: true,
              component: './form/yanxuanGoodFund/lhcq',
            },
            {
              path: '/form/yanxuanGoodFund/yxorder',
              exact: true,
              component: './form/yanxuanGoodFund/yxorder',
            },
            {
              path: '/form/reptiles/backStage',
              exact: true,
              component: './form/reptiles/backStage',
            },
            { path: '/form/reptiles/screen', exact: true, component: './form/reptiles/screen' },
            { path: '/form/fundConfig', exact: true, component: './form/fundConfig' },
            // 高端理财市场页配置
            { path: '/form/higherFinance', exact: true, component: './form/higherFinance' },
            { path: '/form/higherFinanceNew', exact: true, component: './form/higherFinanceNew' },
            {
              path: '/form/higherFinanceBoutique',
              exact: true,
              component: './form/higherFinanceBoutique',
            },
            // 2024.01.04 新增高端理财首页tab配置
            {
              path: '/form/higherFinanceHomePage',
              exact: true,
              component: './form/higherFinanceHomePage',
            },
            {
              path: '/form/higherFinanceOptional',
              exact: true,
              component: './form/higherFinanceOptional',
            },
            {
              path: '/form/sameSeriesProducts',
              exact: true,
              component: './form/sameSeriesProducts',
            },
            { path: '/form/steadyFinanceTemp', exact: true, component: './form/steadyFinanceTemp' },
            { path: '/form/optionalBanner', exact: true, component: './form/optionalBanner' },
            {
              path: '/form/groupToInvestAdvise/mappingRelationship',
              exact: true,
              component: './form/groupToInvestAdvise/mappingRelationship',
            },
            {
              path: '/form/groupToInvestAdvise/riskConfig',
              exact: true,
              component: './form/groupToInvestAdvise/riskConfig',
            },
            {
              path: '/form/activityPage/storage',
              exact: true,
              component: './form/activityPage/storage',
            },
            { path: '/profile', exact: true, component: './profile' },
            // 积分猜涨跌
            {
              path: '/form/guessRiseFall/taskPage',
              exact: true,
              component: './form/guessRiseFall/taskPage',
            },
            {
              path: '/form/guessRiseFall/goodsPage',
              exact: true,
              component: './form/guessRiseFall/goodsPage',
            },
            {
              path: '/form/guessRiseFall/othersPage',
              exact: true,
              component: './form/guessRiseFall/othersPage',
            },
            //策略圈相关
            { path: '/form/kyc/topic', exact: true, component: './form/kyc/topic' },
            { path: '/form/kyc/invest', exact: true, component: './form/kyc/invest' },
            { path: '/form/kyc/topV', exact: true, component: './form/kyc/topV' },
            { path: '/form/kyc/strategy', exact: true, component: './form/kyc/strategy' },
            { path: '/form/kyc/tabService', exact: true, component: './form/kyc/tabService' },

            {
              path: '/form/fundCompanyManager',
              exact: true,
              component: './form/fundCompanyManager',
            },
            { path: '/form/smallGoal', exact: true, component: './form/smallGoal' },
            {
              path: '/form/smallGoal/productConfig',
              exact: true,
              component: './form/smallGoal/ProductConfig',
            },
            {
              path: '/form/smallGoal/linkConfig',
              exact: true,
              component: './form/smallGoal/linkConfig',
            },
            { path: '/form/smallGoal/newList', exact: true, component: './form/smallGoal/newList' },
            {
              path: '/form/smallGoal/newRemind',
              exact: true,
              component: './form/smallGoal/newRemind',
            },
            {
              path: '/form/smallGoal/passConfig',
              exact: true,
              component: './form/smallGoal/passConfig',
            },
            {
              path: '/form/smallGoal/operateConfig',
              exact: true,
              component: './form/smallGoal/operateConfig',
            },
            { path: '/form/newFundBanner', exact: true, component: './form/newFundBanner' },
            { path: '/form/bankBanner', exact: true, component: './form/bankBanner' },
            { path: '/form/fundLive', exact: true, component: './form/fundLive' },
            { path: '/form/fundLiveWhiteList', exact: true, component: './form/fundLiveWhiteList' },

            { path: '/form/ETF/hotBlock', exact: true, component: './form/ETF/hotBlock' },
            { path: '/form/ETF/rank', exact: true, component: './form/ETF/ETFRank' },
            {
              path: '/form/ETF/rankIndustry',
              exact: true,
              component: './form/ETF/ETFRankIndustry',
            },
            { path: '/form/ETF/rankIndex', exact: true, component: './form/ETF/ETFRankIndex' },
            { path: '/form/ETF/ETFPlate', exact: true, component: './form/ETF/ETFPlate' },
            { path: '/form/ETF/ETFRace', exact: true, component: './form/ETF/ETFRace' },
            // 趋势强弱配置
            {
              path: '/form/trendStrongOrWeak',
              exact: true,
              component: './form/trendStrongOrWeak',
            },
            // 基金池构建
            {
              path: '/form/fundPool/fundRecommendation',
              exact: true,
              component: './form/fundPool/fundRecommendation',
            },
            {
              path: '/form/fundPool/fundCompany',
              exact: true,
              component: './form/fundPool/fundCompany',
            },
            {
              path: '/form/fundPool/productEvaluation',
              exact: true,
              component: './form/fundPool/productEvaluation',
            },
            // 资源预加载
            { path: '/form/preloadAssets', exact: true, component: './form/preloadAssets' },
            //大额红包
            { path: '/form/largered', exact: true, component: './form/largeRed' },
            // 圈子广场
            { path: '/form/circle', exact: true, component: './form/circle' },
            //基金内容管理后台
            {
              path: '/form/fundContentManage/fundConfig',
              exact: true,
              component: './form/fundContentManage/fundConfig',
            }, //资讯基金列表配置
            {
              path: '/form/fundContentManage/fundConfig/fund',
              exact: true,
              component: './form/fundContentManage/fundConfig/fund',
            }, //资讯基金配置
            // 火山计划
            // 火山计划新人礼包
            {
              path: '/form/volcanoPlan/rookieGift',
              exact: true,
              component: './form/volcanoPlan/rookieGift',
            },
            // 火山计划-1212活动后台
            {
              path: '/form/volcanoPlan/1212game',
              exact: true,
              component: './form/volcanoPlan/1212game',
            },
            //火山计划直播红包功能
            { path: '/form/volcanoPlan/zhibo', exact: true, component: './form/volcanoPlan/zhibo' },
            // 打榜
            {
              path: '/form/volcanoPlan/robotAccount',
              exact: true,
              component: './form/volcanoPlan/robotAccount',
            },

            // 精准化运营
            // 精准化运营-红包大放送
            {
              path: '/form/preciseOperation/packetProvider',
              exact: true,
              component: './form/preciseOperation/packetProvider',
            },
            // 精准化运营-红包大放送-策略详情
            {
              path: '/form/preciseOperation/packetProvider/detail',
              exact: true,
              component: './form/preciseOperation/packetProvider/detail',
            },
            // 精准化运营-全天候营销策略
            {
              path: '/form/preciseOperation/strategy',
              exact: true,
              component: './form/preciseOperation/strategy',
            },
            // 精准化运营-全天候营销策略详情
            {
              path: '/form/preciseOperation/strategy/detail',
              exact: true,
              component: './form/preciseOperation/strategy/detail',
            },

            // 精准化运营-多场景多形式触达
            {
              path: '/form/preciseOperation/multipe',
              exact: true,
              component: './form/preciseOperation/multipe',
            },
            //精准化运营-多场景多形式触达详情
            {
              path: '/form/preciseOperation/multipe/detail',
              exact: true,
              component: './form/preciseOperation/multipe/detail',
            },
            // 精准化运营-AI智能机器人陪伴
            {
              path: '/form/preciseOperation/intelligentRobot',
              exact: true,
              component: './form/preciseOperation/intelligentRobot',
            },
            // 精准化运营-AI智能机器人陪伴详情
            {
              path: '/form/preciseOperation/intelligentRobot/detail',
              exact: true,
              component: './form/preciseOperation/intelligentRobot/detail',
            },

            { path: '/form/packetProductCard', exact: true, component: './form/packetProductCard' },
            { path: '/form/complianceRisk', exact: true, component: './form/complianceRisk' },
            { path: '/form/downOlas', exact: true, component: './form/OlasMessage' },
            { path: '/form/removeSearch', exact: true, component: './form/removeSearch' },

            //用户目标服务体系
            {
              path: '/form/serviceSystem/codeScript',
              exact: true,
              component: './form/serviceSystem/codeScript',
            },
            {
              path: '/form/activityCentre',
              exact: true,
              component: './form/activityPage/activityCentre',
            }, //活动中心配置后台
            {
              path: '/form/serviceSystem/investmentPortrait',
              exact: true,
              component: './form/serviceSystem/investmentPortrait',
            },
            {
              path: '/form/serviceSystem/revenueTarget',
              exact: true,
              component: './form/serviceSystem/revenueTarget',
            },
            {
              path: '/form/serviceSystem/assetAllocation',
              exact: true,
              component: './form/serviceSystem/assetAllocation',
            },
            //指数估值
            { path: '/form/indexValue', exact: true, component: './form/indexValue' },
            // 持仓列表改版
            {
              path: '/form/holdShareList/estimateSwitch',
              exact: true,
              component: './form/holdShareList/estimateSwitch',
            },
            {
              path: '/form/holdShareList/secondCategory',
              exact: true,
              component: './form/holdShareList/secondCategory',
            },
            {
              path: '/form/holdShareList/companyAndMarketing',
              exact: true,
              component: './form/holdShareList/companyAndMarketing',
            },
            // 预约push
            { path: '/form/bookingPush', exact: true, component: './form/bookingPush' },
            // 基金配置检查
            { path: '/form/fundConfigCheck', exact: true, component: './form/fundConfigCheck' },
            // 基金经理
            {
              path: '/form/newFundManager/rankCard',
              exact: true,
              component: './form/newFundManager/rankCard',
            },
            {
              path: '/form/newFundManager/managerTrackAnalysis',
              exact: true,
              component: './form/newFundManager/managerTrackAnalysis',
            },
            //基金经理投资理念
            {
              path: '/form/newFundManager/managerSetting',
              exact: true,
              component: './form/newFundManager/managerSetting',
            },
            // 固收+ 稳盈宝专区
            { path: '/form/gsPlus/operating', exact: true, component: './form/gsPlus/operating' },
            { path: '/form/gsPlus/prodList', exact: true, component: './form/gsPlus/prodList' },
            //基金内容
            {
              path: '/form/fundContent/manager',
              exact: true,
              component: './form/fundContent/manager',
            },
            // 指数顺风车
            { path: '/form/freeRide', exact: true, component: './form/freeRide' },
            // 爱基金会员中心
            {
              path: '/form/ifundVipCenter/activityFund',
              exact: true,
              component: './form/ifundVipCenter/activityFund',
            },

            // 全天候营销模块
            { path: '/form/wholeDayMarketing', exact: true, component: './form/wholeDayMarketing' },
            // 柴进定投
            { path: '/form/cjInvestment', exact: true, component: './form/cjInvestment' },

            // 短信管理平台
            { path: '/form/smsPlatform', exact: true, component: './form/smsPlatform' },
            { path: '/form/smsPlatform/edit', exact: true, component: './form/smsPlatform/edit' },
            {
              path: '/form/smsPlatform/members',
              exact: true,
              component: './form/smsPlatform/members',
            },
            //私募
            {
              path: '/form/buyPrivate/buyPrivateList',
              exact: true,
              component: './form/buyPrivate/buyPrivateList',
            },
            //私募协议打点
            {
              path: '/form/buyPrivate/setPrivateFile',
              exact: true,
              component: './form/buyPrivate/setPrivateFile',
            },
            // 合作基金
            { path: '/form/cooperateFund', exact: true, component: './form/cooperateFund' },
            // ETF蓝链词典
            { path: '/form/etfChainDict', exact: true, component: './form/etfChainDict' },
            // 基金账号体系改造
            { path: '/form/fundAccountSystem', exact: true, component: './form/fundAccountSystem' },
            {
              path: '/form/fundAccountSystem/logon',
              exact: true,
              component: './form/fundAccountSystem/kaihu',
            },
            {
              path: '/form/fundAccountSystem/my',
              exact: true,
              component: './form/fundAccountSystem/wode',
            },

            // 同顺智投
            { path: '/form/tsztConfig', exact: true, component: './form/tsztConfig' },
            // 短链接
            { path: '/form/shortLinkGen', exact: true, component: './form/shortLinkGen' },
            // 基金抱团
            {
              path: '/form/organGroup/organPage',
              exact: true,
              component: './form/organGroup/organPage',
            },

            // feed基金流策略配置
            { path: '/form/feedConfig', exact: true, component: './form/feedConfig' },
            {
              path: '/form/feedConfig/feedTab',
              exact: true,
              component: './form/feedConfig/feedTab',
            },

            //市场机会
            { path: '/form/marketChance', exact: true, component: './form/marketChance' },
            // 手抄融合基金配置
            {
              path: '/form/scMixFund/tradeConfig',
              exact: true,
              component: './form/scMixFund/tradeConfig',
            },
            {
              path: '/form/scMixFund/tradeConfig/openAccount',
              exact: true,
              component: './form/scMixFund/tradeConfig/openAccount',
            },
            {
              path: '/form/scMixFund/tradeConfig/columnAdvertisement',
              exact: true,
              component: './form/scMixFund/tradeConfig/columnAdvertisement',
            },
            // 定投专区2021
            {
              path: '/form/fixedInvestment/investContent',
              exact: true,
              component: './form/fixedInvestment/investContent',
            },
            {
              path: '/form/fixedInvestment/kycContent',
              exact: true,
              component: './form/fixedInvestment/kycContent',
            },
            {
              path: '/form/scMixFund/tradeConfig/notice',
              exact: true,
              component: './form/scMixFund/tradeConfig/notice',
            },
            {
              path: '/form/scMixFund/tradeConfig/chooseFundTool',
              exact: true,
              component: './form/scMixFund/tradeConfig/chooseFundTool',
            },
            // 理财日
            {
              path: '/form/financialDay/redConfig',
              exact: true,
              component: './form/financialDay/redConfig',
            },
            {
              path: '/form/financialDay/productConfig',
              exact: true,
              component: './form/financialDay/productConfig',
            },
            // 红包审核列表
            { path: '/form/redMoney/examine', exact: true, component: './form/redMoney/examine' },
            // 红包生成
            { path: '/form/redMoney/create', exact: true, component: './form/redMoney/create' },
            // FW-3620 晨星2022年颁奖
            { path: '/form/chenxing2022', exact: true, component: './form/chenxing2022' },
            // 定期理财活动场景建设落地页
            { path: '/form/regularFinance', exact: true, component: './form/regularFinance' },

            // 手抄理财tab
            {
              path: '/form/scFinancialTab/pageConfig',
              exact: true,
              component: './form/scFinancialTab/pageConfig',
            },
            {
              path: '/form/scFinancialTab/pageConfig/searchhotword',
              exact: true,
              component: './form/scFinancialTab/pageConfig/searchhotword',
            },
            {
              path: '/form/scFinancialTab/pageConfig/mixbanner',
              exact: true,
              component: './form/scFinancialTab/pageConfig/mixbanner',
            },
            {
              path: '/form/scFinancialTab/pageConfig/notice',
              exact: true,
              component: './form/scFinancialTab/pageConfig/notice',
            },
            {
              path: '/form/scFinancialTab/pageConfig/gongge',
              exact: true,
              component: './form/scFinancialTab/pageConfig/gongge',
            },
            {
              path: '/form/scFinancialTab/pageConfig/gonggeall',
              exact: true,
              component: './form/scFinancialTab/pageConfig/gonggeall',
            },
            {
              path: '/form/scFinancialTab/pageConfig/operationposition',
              exact: true,
              component: './form/scFinancialTab/pageConfig/operationposition',
            },
            {
              path: '/form/scFinancialTab/pageConfig/usercomment',
              exact: true,
              component: './form/scFinancialTab/pageConfig/usercomment',
            },

            {
              path: '/form/scFinancialTab/pageConfig/coupon',
              exact: true,
              component: './form/scFinancialTab/pageConfig/coupon',
            },
            {
              path: '/form/scFinancialTab/pageConfig/operationblock',
              exact: true,
              component: './form/scFinancialTab/pageConfig/operationblock',
            },
            {
              path: '/form/scFinancialTab/pageConfig/biggiftbag',
              exact: true,
              component: './form/scFinancialTab/pageConfig/biggiftbag',
            },
            {
              path: '/form/scFinancialTab/pageConfig/operationCard',
              exact: true,
              component: './form/scFinancialTab/pageConfig/operationCard',
            },
            {
              path: '/form/scFinancialTab/pageConfig/threesteps',
              exact: true,
              component: './form/scFinancialTab/pageConfig/threesteps',
            },
            {
              path: '/form/scFinancialTab/pageConfig/productcard',
              exact: true,
              component: './form/scFinancialTab/pageConfig/productcard',
            },
            {
              path: '/form/scFinancialTab/pageConfig/multidimension',
              exact: true,
              component: './form/scFinancialTab/pageConfig/multidimension',
            },
            {
              path: '/form/scFinancialTab/pageConfig/featuredlist',
              exact: true,
              component: './form/scFinancialTab/pageConfig/featuredlist',
            },

            // 手抄理财tabV2
            {
              path: '/form/scFinancialTabV2/pageConfig',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/searchhotword',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/searchhotword',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/mixbanner',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/mixbanner',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/notice',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/notice',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/gongge',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/gongge',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/gonggeall',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/gonggeall',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/operationposition',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/operationposition',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/usercomment',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/usercomment',
            },

            {
              path: '/form/scFinancialTabV2/pageConfig/coupon',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/coupon',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/operationblock',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/operationblock',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/biggiftbag',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/biggiftbag',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/operationCard',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/operationCard',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/threesteps',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/threesteps',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/productcard',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/productcard',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/multidimension',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/multidimension',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/featuredlist',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/featuredlist',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/mainOperate',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/mainOperate',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/secOperate',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/secOperate',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/marketSituation',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/marketSituation',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/capitalTrend',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/capitalTrend',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/fundTrend',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/fundTrend',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/FindChance',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/FindChance',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/FindInsurance',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/FindInsurance',
            },
            {
              path: '/form/scFinancialTabV2/pageConfig/RobustFinancial',
              exact: true,
              component: './form/scFinancialTabV2/pageConfig/RobustFinancial',
            },
            {
              path: '/form/scFinancialTabV2/webPageConfig/FindChance',
              exact: true,
              component: './form/scFinancialTabV2/webPageConfig/FindChance',
            }, // 理财tab二期 - 找机会二级页
            //FW-4067 基金外力-大盘择时
            { path: '/form/selectTime', exact: true, component: './form/selectTime' },
            // 基金外力-趋势猎手
            { path: '/form/trendHunter', exact: true, component: './form/trendHunter' },
            // 机构情报
            {
              path: '/form/agencyIntelligence',
              exact: true,
              component: './form/agencyIntelligence',
            },

            //FW-3660 买板块改版
            { path: '/form/buyBK', exact: true, component: './form/buyBK' },
            { path: '/form/buySectorIndex', exact: true, component: './form/buySectorIndex' },
            //FW-4076 新人运营位
            { path: '/form/newerBouns', exact: true, component: './form/newerBouns' },
            // A股开户结果页承接页
            {
              path: '/form/accountOpeningResult',
              exact: true,
              component: './form/accountOpeningResult',
            },

            //首页改版
            { path: '/frontend/home', exact: true, component: './frontend' },
            { path: '/frontend/shouye', exact: true, component: './frontend/shouye' },
            { path: '/frontend/quanzi', exact: true, component: './frontend/quanzi' },
            { path: '/frontend/huati', exact: true, component: './frontend/huati' },
            { path: '/frontend/quanyi', exact: true, component: './frontend/quanyi' },
            { path: '/frontend/licai', exact: true, component: './frontend/licai' },
            { path: '/frontend/xuanji', exact: true, component: './frontend/xuanji' },
            { path: '/frontend/youliao', exact: true, component: './frontend/youliao' },
            { path: '/frontend/hangqing', exact: true, component: './frontend/hangqing' },
            { path: '/frontend/tonglanad', exact: true, component: './frontend/tonglanad' },
            { path: '/frontend/denglu', exact: true, component: './frontend/denglu' },
            { path: '/frontend/downloadbar', exact: true, component: './frontend/downloadbar' },
            { path: '/frontend/tanchuang', exact: true, component: './frontend/tanchuang' },
            { path: '/frontend/push', exact: true, component: './frontend/push' },
            { path: '/frontend/gonggao', exact: true, component: './frontend/gonggao' },
            { path: '/frontend/gggongneng', exact: true, component: './frontend/gggongneng' },
            { path: '/frontend/ggchanpin', exact: true, component: './frontend/ggchanpin' },
            { path: '/frontend/lifecycle', exact: true, component: './frontend/lifecycle' },
            { path: '/frontend/mixbanner', exact: true, component: './frontend/mixbanner' },
            { path: '/frontend/sousuo', exact: true, component: './frontend//sousuo' },
            { path: '/frontend/kaiping', exact: true, component: './frontend/kaiping' },
            { path: '/frontend/kyc', exact: true, component: './frontend/tzfxb' },
            { path: '/frontend/customize', exact: true, component: './frontend/customize' },
            { path: '/frontend/sidebutton', exact: true, component: './frontend/sidebutton' },
            { path: '/frontend/newcomergift', exact: true, component: './frontend/newcomergift' },
            { path: '/frontend/live', exact: true, component: './frontend/homepageLive' },
            { path: '/frontend/pinpaibeishu', exact: true, component: './frontend/pinpaibeishu' },
            {
              path: '/frontend/newaccount',
              exact: true,
              component: './frontend/financialtab/pageConfig/threesteps',
            },
            {
              path: '/frontend/packet',
              exact: true,
              component: './frontend/financialtab/pageConfig/coupon',
            },
            // FW-4005 支付路由
            { path: '/form/buyInit/payRoute', exact: true, component: './form/buyInit/payRoute' },
            //FW-4067 挑战正收益
            { path: '/form/ETF/earning', exact: true, component: './form/ETF/ETFEarning' },
            {
              path: '/form/ETF/buygiveactivity',
              exact: true,
              component: './form/ETF/ETFBuygiveactivity',
            },
            /** 黄金宝 */
            // 基础配置
            {
              path: '/form/hjb/base',
              exact: true,
              component: './form/hjb/base-config',
            },
            // 任务配置
            {
              path: '/form/hjb/tasks',
              exact: true,
              component: './form/hjb/task-config',
            },
            {
              path: '/form/blockDetail/index',
              exact: true,
              component: './form/blockDetail/index',
            },

            // 体验金
            {
              path: '/form/newExperienceGold/list',
              exact: true,
              component: './form/newExperienceGold/list',
            }, // 体验金列表页
            {
              path: '/form/newExperienceGold/detail',
              exact: true,
              component: './form/newExperienceGold/detail',
            }, // 体验金详情页

            // 新人礼包
            {
              path: '/form/newUserGift/list',
              exact: true,
              component: './form/newUserGift/list',
            }, // 列表页
            {
              path: '/form/newUserGift/detail',
              exact: true,
              component: './form/newUserGift/detail',
            }, // 详情页
            // 理财速递
            { path: '/form/wealthExpress', exact: true, component: './form/wealthExpress' },
            {
              path: '/form/findChanceV2',
              exact: true,
              component: './form/findChanceV2',
            }, //找机会v2
            {
              path: '/form/findChanceV2Pool',
              exact: true,
              component: './form/findChanceV2/hotSpotPool',
            }, //找机会热点池v2

            { path: '/profile', exact: true, component: './profile' },
            //FW-4617 条件选基
            { path: '/form/conditionFund', exact: true, component: './form/conditionFund' },
            // 资讯基金关系配置
            {
              path: './form/fundConnection/',
              exact: true,
              component: './form/fundConnection/',
            },
            // 资讯基金关系配置v2
            {
              path: './form/fundConnectionV2/',
              exact: true,
              component: './form/fundConnectionV2',
            },
            {
              path: '/form/sfzUploadConfig',
              exact: true,
              component: './form/sfzUploadConfig',
            },
            {
              path: '/form/followtrend/index',
              exact: true,
              component: './form/followtrend/index',
            },
            {
              path: '/form/reitsConfig/index',
              exact: true,
              component: './form/reitsConfig/index',
            },
            {
              path: '/form/pensionMarket/index',
              exact: true,
              component: './form/pensionMarket/index',
            },
            {
              path: '/form/pensionMarket/list',
              exact: true,
              component: './form/pensionMarket/list',
            },
            // 购买页挽留弹框配置
            {
              path: '/form/retainDialogForBuy',
              exact: true,
              component: './form/retainDialogForBuy',
            },

            {
              path: '/form/pensionAccount/index',
              exact: true,
              component: './form/pensionAccount/index',
            },
            {
              path: '/form/touguManage/home',
              exact: true,
              component: './form/touguManage/home',
            },
            {
              path: '/form/touguManage/investAdviser',
              exact: true,
              component: './form/touguManage/investAdviser',
            },
            // 未风险测评限制高端理财产品查看配置
            {
              path: '/form/limitgdlcProduToSee',
              exact: true,
              component: './form/limitgdlcProduToSee',
            },
            {
              path: '/form/onlineSell/list',
              exact: true,
              component: './form/onlineSell/list',
            },

            {
              path: '/form/onlineSell/FundList',
              exact: true,
              component: './form/onlineSell/FundList',
            },
            // 福利中心
            {
              path: '/form/welfareCenterConfig',
              exact: true,
              component: './form/welfareCenterConfig',
            },
            // 牛人持仓
            {
              path: '/form/eliteHold',
              exact: true,
              component: './form/eliteHold',
            },
            {
              path: '/form/jishuntong/clientDataMaintain',
              exact: true,
              component: './form/jishuntong/clientDataMaintain',
            },
            {
              path: '/form/jishuntong/clientData',
              exact: true,
              component: './form/jishuntong/clientData',
            },
            {
              path: '/form/jishuntong/salesMan',
              exact: true,
              component: './form/jishuntong/salesMan',
            },
            {
              path: 'form/citycardDoudi',
              exact: true,
              component: './form/citizienCardWalletError',
            },
            { path: '/form/ETF/ETFZone/grid', exact: true, component: './form/ETF/ETFZone/grid' }, // ETF专区3.0 宫格配置
            {
              path: '/form/ETF/ETFZone/grid/:card',
              exact: true,
              component: './form/ETF/ETFZone/grid',
            }, // ETF专区3.0 卡片配置和页面配置
            //FW-5727 私募接入线索
            { path: '/form/privateFund', exact: true, component: './form/privateFund' },
            // 买指数配置
            {
              path: './form/indexFundConfig',
              exact: true,
              component: './form/indexFundConfig',
            },
            //FW-5727 私募接入线索
            { path: '/form/privateFund', exact: true, component: './form/privateFund' },
            { path: '/form/ETF/ETFAnswer', exact: true, component: './form/ETF/ETFAnswer' }, // 百问百答
            // 资金超链算法配置
            {
              path: '/form/capitalHyperchainConfig',
              exact: true,
              component: './form/capitalHyperchainConfig',
            },
            // 资讯基金推荐组件
            {
              path: '/form/zxFundRecomConfig',
              exact: true,
              component: './form/zxFundRecomConfig',
            },
            // 基金排行配置
            {
              path: './form/fundRank',
              exact: true,
              component: './form/fundRank',
            },
            // 通用基金池配置
            {
              path: './form/commonFundPool',
              exact: true,
              component: './form/commonFundPool',
            },
            // 2023.08 新版福利中心
            { path: '/form/fullWelfareCenter', exact: true, component: './form/fullWelfareCenter' },
            {
              path: '/form/yieldConversionConfig',
              exact: true,
              component: './form/yieldConversionConfig',
            },
            {
              path: '/form/netWorth',
              exact: true,
              component: './form/netWorth',
            },
            // 趋势强弱配置
            {
              path: '/form/trendStrongOrWeak',
              exact: true,
              component: './form/trendStrongOrWeak',
            },
            /**
             * 选品平台src\pages\
             */
            {
              path: '/ijjProductPool/researchBasicPool',
              exact: true,
              component: './ijjProductPool/researchBasicPool',
            },
            {
              path: '/ijjProductPool/researchBasicPool/detail',
              exact: true,
              component: './ijjProductPool/researchBasicPool/detail',
            },
            {
              path: '/ijjProductPool/researchBasicPool/history',
              exact: true,
              component: './ijjProductPool/researchBasicPool/history',
            },
            {
              path: '/ijjProductPool/researchBasicPool/records',
              exact: true,
              component: './ijjProductPool/researchBasicPool/records',
            },
            {
              path: '/ijjProductPool/marketingBasicPool',
              exact: true,
              component: './ijjProductPool/marketingBasicPool',
            },
            {
              path: '/ijjProductPool/marketingBasicPool/detail',
              exact: true,
              component: './ijjProductPool/marketingBasicPool/detail',
            },
            {
              path: '/ijjProductPool/marketingBasicPool/history',
              exact: true,
              component: './ijjProductPool/marketingBasicPool/history',
            },
            {
              path: '/ijjProductPool/marketingBasicPool/records',
              exact: true,
              component: './ijjProductPool/marketingBasicPool/records',
            },
            {
              path: '/ijjProductPool/marketingPool',
              exact: true,
              component: './ijjProductPool/marketingPool',
            },
            {
              path: '/ijjProductPool/marketingPool/detail',
              exact: true,
              component: './ijjProductPool/marketingPool/detail',
            },
            {
              path: '/ijjProductPool/marketingPool/records',
              exact: true,
              component: './ijjProductPool/marketingPool/records',
            },
            {
              path: '/ijjProductPool/selectionPool',
              exact: true,
              component: './ijjProductPool/selectionPool',
            },
            {
              path: '/ijjProductPool/selectionPool/detail',
              exact: true,
              component: './ijjProductPool/selectionPool/detail',
            },
            {
              path: '/ijjProductPool/selectionPool/history',
              exact: true,
              component: './ijjProductPool/selectionPool/history',
            },
            {
              path: '/ijjProductPool/selectionPool/records',
              exact: true,
              component: './ijjProductPool/selectionPool/records',
            },
            {
              path: '/ijjProductPool/productDetail',
              exact: true,
              component: './ijjProductPool/productDetail',
            },
            {
              path: '/ijjProductPool/poolManagement',
              exact: true,
              component: './ijjProductPool/poolManagement',
            },
            {
              path: '/ijjProductPool/scoreManagement',
              exact: true,
              component: './ijjProductPool/scoreManagement',
            },
            {
              path: '/ijjProductPool/scoreManagement/records',
              exact: true,
              component: './ijjProductPool/scoreManagement/records',
            },
            {
              path: '/ijjProductPool/poolManagement/records',
              exact: true,
              component: './ijjProductPool/poolManagement/records',
            },
            {
              path: '/ijjProductPool/highProductPool',
              exact: true,
              component: './ijjProductPool/highProductPool',
            },
            { path: '/form/ETF/ETFPlateLink', exact: true, component: './form/ETF/ETFPlateLink' }, // 利益点链接生成
            /** 错误鉴权信息转译 */
            { path: '/form/authErrorMap', component: './form/authErrorMap' },
            { path: '/form/authErrorDictAmis', component: './form/authErrorDictAmis' },
            {
              path: '/report/walletConversionConfig',
              exact: true,
              component: './report/walletConversionConfig',
            },
            {
              path: '/report/walletCurrencyWeightSetting',
              exact: true,
              component: './report/walletCurrencyWeightSetting',
            },
            //用户足迹配置
            { path: '/form/userFootprint', exact: true, component: './form/userFootprint' },
            /** 通用/恐贪活动配置 */
            {
              path: '/form/newShareFission/list',
              exact: true,
              component: './form/newShareFission/list',
            }, // 体验金列表页
            {
              path: '/form/newShareFission/detail',
              exact: true,
              component: './form/newShareFission/detail',
            },
            /** 通用活动配置页 */
            {
              path: '/form/newShareFission/generalDetail',
              exact: true,
              component: './form/newShareFission/generalDetail',
            },
            {
              path: '/form/newShareFission/activityDetail',
              exact: true,
              component: './form/newShareFission/activityDetail',
            },
            //ETF实盘大赛配置
            {
              path: '/etfCompetition/etfGuessGame',
              exact: true,
              component: './form/ETF/ETFCompetition/etfGuessGame',
            },
            {
              path: '/etfCompetition/shareConfig',
              exact: true,
              component: './form/ETF/ETFCompetition/shareConfig',
            },
            {
              path: '/etfCompetition/blackList',
              exact: true,
              component: './form/ETF/ETFCompetition/blackList',
            },
            {
              path: '/etfCompetition/awardList',
              exact: true,
              component: './form/ETF/ETFCompetition/awardList',
            },
            {
              path: '/etfCompetition/brokerList',
              exact: true,
              component: './form/ETF/ETFCompetition/brokerList',
            },
            {
              path: '/etfCompetition/brokerManage',
              exact: true,
              component: './form/ETF/ETFCompetition/brokerManage',
            },
            {
              path: '/etfCompetition/bulletComment',
              exact: true,
              component: './form/ETF/ETFCompetition/bulletComment',
            },
            {
              path: '/etfCompetition/competitionBible',
              exact: true,
              component: './form/ETF/ETFCompetition/competitionBible',
            },
            {
              path: '/etfCompetition/competitionConfig',
              exact: true,
              component: './form/ETF/ETFCompetition/competitionConfig',
            },
            {
              path: '/etfCompetition/etfPool',
              exact: true,
              component: './form/ETF/ETFCompetition/etfPool',
            },
            {
              path: '/etfCompetition/fundCompanyList',
              exact: true,
              component: './form/ETF/ETFCompetition/fundCompanyList',
            },
            {
              path: '/etfCompetition/mainVenue',
              exact: true,
              component: './form/ETF/ETFCompetition/mainVenue',
            },
            {
              path: '/etfCompetition/noticeAndPopup',
              exact: true,
              component: './form/ETF/ETFCompetition/noticeAndPopup',
            },
            {
              path: '/etfCompetition/push',
              exact: true,
              component: './form/ETF/ETFCompetition/push',
            },
            {
              path: '/etfCompetition/rankingBanner',
              exact: true,
              component: './form/ETF/ETFCompetition/rankingBanner',
            },
            {
              path: '/etfCompetition/taskList',
              exact: true,
              component: './form/ETF/ETFCompetition/taskList',
            },
            {
              path: '/etfCompetition/rankVerify',
              exact: true,
              component: './form/ETF/ETFCompetition/rankVerify',
            },
            {
              path: '/etfCompetition/commonConfig',
              exact: true,
              component: './form/ETF/ETFCompetition/commonConfig',
            },
            //ETF综合列表页筛选配置
            {
              path: '/form/ETF/filterList',
              exact: true,
              component: './form/ETF/filterList/filterList',
            },
            {
              path: '/form/ETF/indicList',
              exact: true,
              component: './form/ETF/filterList/indicList',
            },
            //工具权限管理-角色管理
            {
              path: '/form/authority/roleManage',
              exact: true,
              component: './form/authority/roleManage',
            },
            //工具权限管理-权益管理
            {
              path: '/form/authority/equityManage',
              exact: true,
              component: './form/authority/equityManage',
            },
            //工具权限管理-权限管理
            {
              path: '/form/authority/powerManage',
              exact: true,
              component: './form/authority/powerManage',
            },
            //工具权限管理-白名单
            {
              path: '/form/authority/whiteList',
              exact: true,
              component: './form/authority/whiteList',
            },
            // 新权益包管理
            {
              path: '/form/authority/equityPackageNew',
              exact: true,
              component: './form/authority/equityPackageNew',
            },
            //aigc-图片审核
            {
              path: '/aigc/materialPictures',
              exact: true,
              component: './form/aigc/materialPictures',
            },
            //aigc-文本审核
            {
              path: '/aigc/materialContent',
              exact: true,
              component: './form/aigc/materialContent',
            },
            //aigc-基金信息
            {
              path: '/aigc/fundMatchRule',
              exact: true,
              component: './form/aigc/fundMatchRule',
            },
            //aigc-个基匹配信息
            {
              path: '/aigc/materialMatchRule',
              exact: true,
              component: './form/aigc/materialMatchRule',
            },
            //aigc-模型执行
            {
              path: '/aigc/modelExecuteTriggle',
              exact: true,
              component: './form/aigc/modelExecuteTriggle',
            },
            //aigc-模型入参
            {
              path: '/aigc/modelInputParameters',
              exact: true,
              component: './form/aigc/modelInputParameters',
            },
            // AI问句配置后台
            {
              path: '/form/AIQuestionSentence',
              exact: true,
              component: './form/AIQuestionSentence',
            },
            // 新的路由放在404前面，否则匹配不到
            { component: './404' },
            // 基顺通机构业务客户维护
          ],
        },
        { component: './404' },
      ],

  alias: {
    '@': resolve(__dirname, 'src'),
    models: resolve(__dirname, 'src/models'),
    components: resolve(__dirname, 'src/components'),
    functions: resolve(__dirname, 'src/functions'),
    assets: resolve(__dirname, 'src/assets'),
    routes: resolve(__dirname, 'src/routes'),
    api: resolve(__dirname, './src/services'),
    config: resolve(__dirname, 'config'),
    public: resolve(__dirname, 'public'),
    types: resolve(__dirname, './src/types'),
    utils: resolve(__dirname, './src/utils'),
  },

  plugins: [
    [
      'umi-plugin-react',
      {
        antd: true,
        dva: true,
        dynamicImport: { webpackChunkName: true },
        title: 'boplatfrom',
        dll: true,

        routes: {
          exclude: [
            /models\//,
            /services\//,
            /model\.(t|j)sx?$/,
            /service\.(t|j)sx?$/,
            /components\//,
          ],
        },
        pwa: {
          manifestOptions: {
            srcPath: resolve(__dirname, 'manifest.json'),
          },
        },
      },
    ],
  ],
  proxy: {
    '/api': {
      target: 'https://febs.5ifund.com:8443/yytjapi',
      pathRewrite: { '^/api': '' },
      changeOrigin: true,
    },
  },
};

export default config;
