#!/bin/sh
# install script
# branch test
# author: wang<PERSON><PERSON>
# time: 2020.11.30
# update time: 2021.01.18

echo "test"

echo "-----Begin install  $(date "+%Y-%m-%d %H:%M:%S")-------"
LANG=zh_CN.GB2312
echo $LANG

#start 此处中间位置： 留给开发自己使用，可以简单的判断是否安装成功（为下方的判断提供条件），也可以调用自己需要的脚本

#判断是否成功，如果成功的话，给flg赋值0
source /etc/profile

# copy to 8443
# scp -r /puppet_data/puppet/thsjj-jj-fetrade/app /usr/tomcat/apache-tomcat-6.0.33/webapps/ROOT/
# copy to 9445
# scp -r /puppet_data/puppet/thsjj-jj-fetrade/app /usr/tomcat/tomcat-fundSale-branch/webapps/ROOT/

# copy to testfund

scp -r /puppet_data/puppet/thsjj-jj-fefund-boplatform/dist /var/www/html/public/whw/boplatform/

flg=0
#end 此处中间位置： 留给开发自己使用，可以简单的判断是否安装成功（为下方的判断提供条件），也可以调用自己需要的脚本

#根据执行结果，判断本次安装成功还是失败（此处的判断由开发自行定义，但是一定要有返回值）
taskid=$1
server=`grep 'server=' /etc/puppet/puppet.conf | awk -F '=' '{print $2}'`
if [ $flg -ne 0 ];then
    result=$(curl http://"${server}":8080/interface/leaf_changeStatus.php -d " taskid=${taskid}&issuccess=3")
    echo "install failure"
	exit 0
else
    result=$(curl http://"${server}":8080/interface/leaf_changeStatus.php -d " taskid=${taskid}&issuccess=2")
    echo "install success"
fi

echo $result  #此处记录日志，用于puppet分析使用，请尽量提供
sleep 2s
echo "-----End install  $(date "+%Y-%m-%d %H:%M:%S")-------"

exit $flg