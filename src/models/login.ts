import { Effect } from 'dva'
import { Reducer } from 'redux'
import store from 'store'
import api from 'api'

const submitLogin = api.submitLogin;


export interface StateType {
    success?: boolean;
    message?: string;
    currentAuthority?: 'user' | 'guest' | 'admin';
}
  
export interface LoginModelType {
    namespace: string;
    state: StateType;
    effects: {
      login: Effect;
    };
    reducers: {
      changeLoginStatus: Reducer<StateType>;
    };
}
  
const LoginModel: LoginModelType = {
    namespace: 'login',
  
    state: {
      success: undefined,
    },
  
    effects: {
      *login({ payload }, { call, put }) {
        try {
          const response = yield call(submitLogin, payload);
          yield put({
            type: 'changeLoginStatus',
            payload: response,
          });
        } catch (e) {
          yield put({
            type: 'changeLoginStatus',
            payload: e,
          })
        }
      },
    },
  
    reducers: {
      changeLoginStatus(state, { payload }) {
        //if (payload.success) store.set('account', payload);
        return {
          ...state,
          success: payload.success,
          message: payload.message
        };
      },
    },
};
  
export default LoginModel;
  
