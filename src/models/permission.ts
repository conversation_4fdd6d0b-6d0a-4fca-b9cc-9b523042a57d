import { Effect } from 'dva'
import { Reducer } from 'redux'
import store from 'store'
import api from 'api'

const {
    fetchMenu
} = api;

  
export interface PermissionModelState {
    limits?: string[];
    routers?: any[];
}

export interface PermissionModelType {
    namespace: 'permission';
    state: PermissionModelState;
    effects: {
        fetchMenu: Effect;
    };
    reducers: {
        savePermissions: Reducer<PermissionModelState>;
    };
}

const PermissionModel: PermissionModelType =  {
    namespace: 'permission',
    state: {
        limits: store.get('permissions') || [],
        routers: [],
    },


    effects: {
        *fetchMenu({ name }, { call, put }) {
            try {
              const response = yield call(fetchMenu, null, name);
              
              yield put({
                type: 'savePermissions',
                payload: {
                    routers: response[0].children
                },
              });
            } catch (e) {}
        }
    },

    reducers: {
        savePermissions(state, { payload }) {
            let states: any = {
                ...state
            };

            if (payload.limits) states.limits = payload.limits;
            if (payload.routers) states.routers = payload.routers;

            return states;
        }
    }
}

export default PermissionModel
