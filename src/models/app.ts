import { Effect } from 'dva'
import { Reducer } from 'redux'
import api from 'api'
import { message } from 'antd'
import store from 'store'
message.config({
    top: '70%',
    duration: 4
});

const queryUser = api.queryUser

export interface CurrentUser {
    avatar?: string;
    name?: string;
    title?: string;
    group?: string;
    signature?: string;
    tags?: {
      key: string;
      label: string;
    }[];
    userid?: string;
    unreadCount?: number;
}
  
export interface UserModelState {
    userinfo?: CurrentUser;
    loading: boolean;
    pageLoading: boolean;
    theme: 'light' | 'dark';
}

export interface AppModelType {
    namespace: 'app';
    state: UserModelState;
    effects: {
        fetchUser: Effect;
    };
    reducers: {
        saveCurrentUser: Reducer<UserModelState>;
        triggerTheme: Reducer<UserModelState>;
        triggerLoading: Reducer<UserModelState>;
    };
}

const AppModel: AppModelType =  {
    namespace: 'app',
    state: {
        userinfo: {
            name: '基金吴彦祖',
            userid: '',
            avatar: store.get('avatar'),
        },
        loading: true,
        pageLoading: false,
        theme: store.get('theme') || 'light',
    },

    effects: {
        *fetchUser({ 
            userid,
            name,
        }, { call, put, take }) {
            try {
                const response = yield call(queryUser, null, userid);

                _.queryUserInfo = response.data;
                
                yield put({
                    type: 'saveCurrentUser',
                    payload: {
                        userid,
                        ...response,
                        name,
                    },
                });
                
                yield put({
                    type: 'permission/fetchMenu',
                    name
                })
            } catch (e) {
                store.get('user_token') && message.error('登录已过期，请重新登录');
                yield put({
                    type: 'saveCurrentUser'
                })
            }
        }
    },

    reducers: {
        saveCurrentUser(state: UserModelState, { payload }) {
            return {
              ...state,
              loading: false,
              userinfo: payload ? {
                userid: payload.userid,
                name: payload.name,
                avatar: payload.avatar || state.userinfo && state.userinfo.avatar,
              } : {},
            };
        },

        triggerTheme (state: UserModelState) {
            let theme: 'light' | 'dark' = state.theme === 'light' ? 'dark' : 'light';

            store.set('theme', theme);
            return {
                ...state,
                theme
            };
        },

        triggerLoading (state: UserModelState) {
            return {
                ...state,
                pageLoading: !state.pageLoading,
            }
        }
    }
}

export default AppModel
