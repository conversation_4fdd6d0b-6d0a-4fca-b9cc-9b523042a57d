import React, { useState, useEffect } from "react";
import FormRender from 'form-render/lib/antd';
import { connect } from 'dva'
//import axios from 'axios'
import api from 'api';
import { Button, message } from 'antd'

const {fetchTradeerr, postTradeerr} = api;

let FORM_CONFIG: any = {
    propsSchema: {
        type: 'object',
        properties: {
            "planList": {
                "title": "支付失败信息匹配",
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "key": {
                        title: '请输入方案标识(供支付失败信息匹配)',
                        type: 'string',
                        maxLength: 999,
                        "ui:width": "30%",
                    },
                    "value": {
                        title: '请输入方案描述',
                        type: 'string',
                        maxLength: 999,
                        "ui:width": "60%",
                    }
                  }
                }
            },
            "ruleList": {
                "title": "支付失败信息匹配",
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "key": {
                        title: '请输入错误原因',
                        type: 'string',
                        maxLength: 999,
                        "ui:width": "28%",
                    },
                    "value": {
                        title: '前端展示错误原因',
                        type: 'string',
                        maxLength: 999,
                        "ui:width": "28%",
                    },
                    "plan": {
                        title: '请输入解决方案(英文逗号","分割)',
                        type: 'string',
                        maxLength: 999,
                        "ui:width": "28%",
                    }
                  }
                }
            }
        },
        required: ['ruleList']
    }
};

// 
function TradeErr (props: any) {
    const canOpc = props.limits && props.limits.includes('crmform:errtxtopc');
    const [init, setInit] = useState(false);
    const [formConfig, setFormConfig] = useState({});
    const [formData, setData]: any = useState({});
    const [valid, setValid] = useState([]);
    
    useEffect(() => {
        fetchTradeerr().then((res: any) => {
            try {
                res = JSON.parse(res.data);
                if (res) {
                    let data = res.data;
                    let ruleList = [];
                    for(let i in data) {
                        ruleList.push({
                            key: i,
                            value: data[i].ths_message,
                            plan: data[i].listPlan && data[i].listPlan.map((item: any) => {
                                for (let i in res.list) {
                                    if (res.list[i].key === item) return res.list[i].key;
                                }
                                return false;
                            }).join(',')
                        });
                    }
                    FORM_CONFIG.formData = {
                        planList: res.list,
                        ruleList
                    };
                }
            } catch (e) {
                console.warn(e)
            }
            
            setInit(true);
            setFormConfig(FORM_CONFIG);
            setData(FORM_CONFIG.formData);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, [init]);

    const onSubmit = () => {
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
        } else {
            console.log(formData);
            let planKeyList = formData.planList.map((item: any) => item.key);
            let _postData: any = {
                list: formData.planList,
                data: {}
            };
            let errInfo = '';
            formData.ruleList.forEach((item: any, index: number) => {
                if (errInfo) return false;
                let plan = item.plan.replace(/\s/g, '').split(',');
                plan = plan.map((subitem: any) => {
                    if (!planKeyList.includes(subitem)) errInfo = `第${index + 1}项中方案${subitem}不存在`;
                    else return formData.planList[planKeyList.indexOf(subitem)].key;
                })

                _postData.data[item.key] = {
                    ths_message: item.value,
                    listPlan: plan
                };
            });

            if (errInfo) {
                return message.error(errInfo);
            }

            postTradeerr({
                value: JSON.stringify(_postData)
            }).then((res: any) => {
                try {
                    if (res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success('发布成功！')
                    }
                } catch (e) {
                    message.error(e.message);
                }
            })
        }
    };

    //if (!init) return '加载中'
    return (
        <div style={{ padding: 60 }}>
        {
            init && (<FormRender
                propsSchema={FORM_CONFIG.propsSchema}
                formData={formData}
                onChange={setData}
                onValidate={setValid}
                showDescIcon={true}
            />) 
        }
        
        {canOpc && <Button type="primary" onClick={onSubmit}>提交</Button>}
        </div>
    );
}



export default connect(({app, permission}: any) => ({
    limits: permission.limits
}))(TradeErr)
