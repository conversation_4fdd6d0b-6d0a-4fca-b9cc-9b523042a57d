import React, { useEffect, useState } from 'react';
import { Checkbox, Button, message, Popconfirm } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from 'form-render/lib/antd';
import styles from '../frontend.less';
import ConfigSelect from '../compoment/selectModel/index';
import TagModel from '../compoment/tagModel/index';
import uploadImg from './uploadImg';
import classNames from 'classnames';

interface dataProps {
  data: Object;
  formData: Object;
  configData: Object;
  relationData: Object;
  position: number;
  handleUpdate: Function;
  handleDelete: Function;
  showKyc: boolean;
}

export default function(props: dataProps) {
  const [isEdit, setEdit] = useState(false);
  const [valid, setValid] = useState([]);
  // const [data,setData] = useState([])

  const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
  const [formData, setFormState] = useState({});
  const [configData, setConfig] = useState({});
  const [relationData, setRelation] = useState({});

  useEffect(() => {
    console.log('formData=', props.data);
    setFormState(props.data.formData);
    setConfig(props.data.configData);
    setRelation(props.data.relationData);
  }, [props.data]);

  function onValidate(valid) {
    setValid(valid);
    // console.log(valid);
  }
  function handleChange() {
    let _isEdit = isEdit;

    if (_isEdit) {
      let _data = {
        formData,
        configData,
        relationData,
        other: props.data.other,
      };
      if (!formData.title) {
        message.error('请填写直播标题');
      } else if (!formData.sid || !/^\d+$/.test(formData.sid)) {
        message.error('请填写正确直播间id');
      } else if (!formData.fid || !/^\d+$/.test(formData.fid)) {
        message.error('请填写正确帐号id');
      } else if (!formData.livePhotoUrl) {
        message.error('请填写直播图标');
      } else if (!formData.company) {
        message.error('请填写基金公司');
      } else if (!formData.liveIntro) {
        message.error('请填写直播简介');
      } else if (!formData.startTime) {
        message.error('请填写直播开始时间');
      } else if (!formData.endTime) {
        message.error('请填写直播结束时间');
      } else if (new Date(formData.startTime) > new Date(formData.endTime)) {
        message.error('请填写直播时间格式有误');
      } else if (formData.needLogin === '') {
        message.error('请填写是否登录后可见');
      } else if (configData.platform.length === 0) {
        message.error('请选择适用平台');
      } else if (configData.utype.length === 0) {
        message.error('请选择用户类型');
      } else {
        props.handleUpdate(_data, props.position);
        setEdit(!_isEdit);
      }
    } else {
      setEdit(!_isEdit);
    }
  }
  function handleSelect(data) {
    console.log('data', data);
    setConfig(data);
  }
  function handleTag(data: any) {
    console.log('tag', data);
    setRelation(data);
  }

  // if (!init) return '加载中'
  return (
    <div className={classNames(styles['m-card'], 'g-pr')}>
      <div className={styles['m-header']}>
        <Button ghost className={styles['m-button']} onClick={handleChange}>
          {isEdit === true ? '保存' : '编辑'}
        </Button>
        <Popconfirm
          title="确定删除?"
          onConfirm={() => {
            props.handleDelete(props.position);
          }}
          okText="是"
          cancelText="否"
        >
          <Button ghost type="danger" className={styles['m-button']}>
            {' '}
            删除
          </Button>
        </Popconfirm>
      </div>
      {isEdit ? null : (
        <div
          className="g-pa"
          style={{
            width: 800,
            height: 500,
            top: 100,
            left: 0,
            zIndex: 99,
          }}
        ></div>
      )}
      <FormRender
        propsSchema={formConfig}
        displayType="row"
        formData={formData}
        onValidate={onValidate}
        onChange={setFormState}
        readOnly={!isEdit}
        widgets={{ uploadImg: uploadImg }}
      />
      <div style={{display: props.showKyc ? '' : 'none'}}>
        <ConfigSelect handleChange={handleSelect} isHead={false} isEdit={isEdit} data={configData} />
        <TagModel
          handleChange={handleTag}
          data={relationData}
          kycTag={props.kycTag}
          olasTag={props.olasTag}
          isEdit={isEdit}
        />
      </div>
    </div>
  );
}
