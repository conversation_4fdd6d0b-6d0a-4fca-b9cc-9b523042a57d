import React, {useEffect, useState} from 'react';
import { Checkbox, Button, message, Popconfirm } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../frontend.less'
import ConfigSelect from '../compoment/selectModel/index'
import TagModel from '../compoment/tagModel/index';
interface dataProps {
    data: Object;
    // formData: Object;
    // configData: Object;
    // relationData: Object;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
    showKyc: boolean;
}

export default function (props:dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    // const [data,setData] = useState([])

    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [formData,setFormState] = useState({});
    const [configData,setConfig] = useState({});
    const [relationData,setRelation] = useState({});

    useEffect(() => {
        console.log('formData=',props.formData)
        setFormState(props.data.formData)
        setConfig(props.data.configData)
        setRelation(props.data.relationData)
    },[props.data])
    
    
    function onValidate(valid) {
        setValid(valid);
        // console.log(valid);
    }
    function handleChange(){
        let _isEdit = isEdit

        if(_isEdit){
            let _data = {
                formData,
                configData,
                relationData,
                other: props.data.other
            }
            if (!formData.titleName) {
                message.error('请填写push标题')
            } else if (!formData.webViewUrl) {
                message.error('请填写webView访问地址')
            } else if(formData.needLogin === ''){
                message.error('请填写是否登录后可见')
            } else if (configData.platform.length === 0) {
                message.error('请选择适用平台')
            } else if (configData.utype.length === 0) {
                message.error('请选择用户类型')
            } else {
                props.handleUpdate(_data,props.position)
                setEdit(!_isEdit)
            }
        } else {
            setEdit(!_isEdit)
        }
        
    }
    function handleSelect(data){
        console.log('data',data)
        setConfig(data)
    }
    function handleTag(data:any){
        console.log('tag',data)
        setRelation(data)
    }
    
    // if (!init) return '加载中'
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {props.handleDelete(props.position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={onValidate}
                    onChange={setFormState}
                    readOnly={!isEdit}
                />
                <div style={{display: props.showKyc ? '' : 'none'}}>
                    <ConfigSelect
                        handleChange={handleSelect}
                        isHead={false}
                        isEdit={isEdit}
                        data={configData}  
                    />
                    <TagModel
                        handleChange={handleTag}
                        data={relationData}
                        kycTag={props.kycTag}
                        olasTag={props.olasTag}
                        isEdit={isEdit}
                    />
                </div>
    
            </div>

}