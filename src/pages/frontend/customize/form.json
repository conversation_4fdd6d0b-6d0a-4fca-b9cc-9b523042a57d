{"schema": {"type": "object", "properties": {"titleName": {"title": "自定义运营模块名称", "type": "string", "description": "", "ui:width": "60%", "ui:options": {}}, "webViewUrl": {"title": "webView访问地址", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "needLogin": {"title": "是否登录后可见", "type": "string", "enum": ["1", "2", "3", "4", "5", "6"], "enumNames": ["全部可见", "无登录记录可见", "有登录记录可见", "登录中可见", "未登录可见", "未登录可见（有登录记录）"], "ui:width": "60%"}, "version": {"title": "版本控制", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "60%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "60%"}}, "required": ["<PERSON><PERSON><PERSON>", "webViewUrl", "needLogin"]}, "displayType": "row", "showDescIcon": true, "labelWidth": 120}