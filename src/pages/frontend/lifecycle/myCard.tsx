import React, {useEffect, useState} from 'react';
import { Checkbox, Button, message, Popconfirm } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../frontend.less'
import ConfigSelect from '../compoment/selectModel/index'
import TagModel from '../compoment/tagModel/index';
import ImgUpload from '../compoment/uploadImg/index.jsx';
interface dataProps {
    data: Object;
    // formData: Object;
    // configData: Object;
    // relationData: Object;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
    showKyc: boolean;
}

export default function (props:dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    // const [data,setData] = useState([])

    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [imgData, setImgData] = useState({});
    const [formData,setFormState] = useState({});
    const [configData,setConfig] = useState({});
    const [relationData,setRelation] = useState({});

    useEffect(() => {
        console.log('formData=',props.formData)
        setFormState(props.data.formData)
        setImgData(props.data.imgData)
        setConfig(props.data.configData)
        setRelation(props.data.relationData)
    },[props.data])
    
    
    function onValidate(valid) {
        setValid(valid);
        // console.log(valid);
    }
    function selectReview(arr:Array<any>){
        let flag = true;
        for( let i = 0; i < arr.length ; i++){
            if(arr[i] === 'u5'){
                message.error('用户类型不可为成长用户')
                flag = false
                break
            } else if(arr[i] === 'u6'){
                message.error('用户类型不可为成熟用户')
                flag = false
                break
            } else if(arr[i] === 'u7'){
                message.error('用户类型不可为高净值用')
                flag = false
                break
            } else if(arr[i] === 'u8'){
                message.error('用户类型不可为休眠用户')
                flag = false
                break
            } else if(arr[i] === 'u9'){
                message.error('用户类型不可为流失用户')
                flag = false
                break
            } else if(arr[i] === 'F'){
                message.error('用户类型不可为F类审核用户')
                flag = false
                break
            }
        }
        return flag

    }
    function handleChange(){
        let _isEdit = isEdit

        if(_isEdit){
            let _data = {
                formData,
                imgData,
                configData,
                relationData,
                other: props.data.other
            }
            if (!formData.navigationColor) {
                message.error('请填写导航栏色值')
            } else if (formData.navigationColor.indexOf('#')<0) {
                message.error('请正确填写导航栏色值，首字符为#')
            } else if (!formData.jumpAction) {
                message.error('请填写跳转链接')
            } else if (!formData.secondBtnTitle) {
                message.error('请填写按钮标题')
            } else if (!formData.thirdBtnTitle) {
                message.error('请填写按钮标题')
            } else if (!formData.buttonJumpLink) {
                message.error('请填写按钮跳转链接')
            } else if (!formData.needLogin) {
                message.error('请选择是否登录后可见')
            } else if (!imgData.backImage) {
                message.error('请上传背景图片')
            } else if (!imgData.secondImg) {
                message.error('请上传第二步标题图片')
            } else if (!imgData.thirdImg) {
                message.error('请上传第三步标题图片')
            } else if (configData.platform.length === 0) {
                message.error('请选择适用平台')
            } else if (configData.utype.length === 0) {
                message.error('请选择用户类型')
            } else if (!selectReview(configData.utype)) {
                
            } else {
                props.handleUpdate(_data,props.position)
                setEdit(!_isEdit)
            }
        } else {
            setEdit(!_isEdit)
        }
        
    }
    function handleSelect(data){
        console.log('data',data)
        setConfig(data)
    }
    function handleTag(data:any){
        console.log('tag',data)
        setRelation(data)
    }
    function handleBackImg(data:any){
        console.log(data)
        let _imgData = imgData
        _imgData.backImage = data
        setImgData(_imgData)
    }
    function handleSecondImg(data:any){
        let _imgData = imgData
        _imgData.secondImg = data
        setImgData(_imgData)
    }
    function handleThirdImg(data:any){
        let _imgData = imgData
        _imgData.thirdImg = data
        setImgData(_imgData)
    }
    
    // if (!init) return '加载中'
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {props.handleDelete(props.position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <ImgUpload 
                    handleChange={handleBackImg}
                    imageUrl={imgData.backImage}
                    size={['750px*284px']}
                    isEdit={isEdit}
                    title='背景图片'
                />
                <ImgUpload 
                    handleChange={handleSecondImg}
                    imageUrl={imgData.secondImg}
                    size={['686px*46px']}
                    isEdit={isEdit}
                    title='第二步标题图片'
                />
                <ImgUpload 
                    handleChange={handleThirdImg}
                    imageUrl={imgData.thirdImg}
                    size={['686px*46px']}
                    isEdit={isEdit}
                    title='第三步标题图片'
                />
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={onValidate}
                    onChange={setFormState}
                    readOnly={!isEdit}
                />
                <div style={{display: props.showKyc ? '' : 'none'}}>
                    <ConfigSelect
                        handleChange={handleSelect}
                        isHead={false}
                        isEdit={isEdit}
                        data={configData}  
                    />
                    <strong style={{marginLeft:'110px',marginBottom:'20px'}}>注意：因生命周期模块仅对未交易用户展示，可选择类型仅限于默认、新手用户、次新用户、休眠新用户、流失新用户</strong>
                    <div style={{marginBottom:'20px'}}></div>
                    <TagModel
                        handleChange={handleTag}
                        data={relationData}
                        kycTag={props.kycTag}
                        olasTag={props.olasTag}
                        isEdit={isEdit}
                    />
                </div>
            </div>

}