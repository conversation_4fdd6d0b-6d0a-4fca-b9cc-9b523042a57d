import React, {useEffect, useState} from 'react';
import {Button, message, Collapse } from 'antd';
import FORM_CONFIG from './form.json';
import api from 'api';
import MyCard from './myCard'
import ConfigSelect from '../compoment/selectModel/index'
import LocationModel from '../compoment/locationModel/index'
import styles from '../frontend.less'
import group from '@/pages/report/group';
const {Panel} = Collapse
interface dataProps {
    
}
const {fetchGggongneng, fetchCBAS, fetchOLAS} = api;
const showKyc = location.href.includes('edit=1');
export default function (props:dataProps) {

    const [init, setInit] = useState(false);
    const [valid, setValid] = useState([]);
    const [originData,setData] = useState([])
    const [allData,setAllData] = useState({})
    const [isModify,setModify] = useState(false)
    const [config,setConfig] = useState({user:[], platform:[]})
    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [kycTag,setKycTag] = useState([]);
	const [olasTag,setOlasTag] = useState([]);
    const handleChange = (data:any) => {
        console.log(data)
        setConfig(data)

    }
    useEffect(() => {
        fetchCBAS().then((res:any) => {
            if (res.code === '0000' && res.data) {
                setKycTag(res.data)
            }
        })
    }, [])
    useEffect(() => {
        fetchOLAS().then((res:any) => {
            if (res.code === '0000' && res.data) {
                let _olasTag = []
                let data = res.data
                for (let prop in data) {
                    _olasTag.push({"groupid":prop, "description":data[prop].description})
                    console.log(_olasTag)
                }
                setOlasTag(_olasTag)
            }
        })
    }, [])
    useEffect(() => {
        console.log(formConfig)
    },[])

    useEffect(() => {
      fetchGggongneng({
            type:'query'
          }).then((res)=>{
            try {
                if (res) {
                    let _data = []
                    res.data.confs.map((val,index) => {
                        let obj = {
                            formData: {
                                ggTitle: val.ggTitle,
                                jumpAction: val.jumpAction,
                                needLogin: val.needLogin,
                                version: val.version,
                                subScript: val.subScript,
                                startTime: val.startTime,
                                endTime: val.endTime
                            },
                            imgData: {
                                image: val.image
                            },
                            configData:{
                                platform: val.platform || [],
                                utype: val.utype || [],
                            },
                            relationData:{
                                targetType: val.targetType,
                                kycLogic: val.kycLogic,
                                kycs:val.kycs,
                                olasId: val.olasId
                            },
                            other:{
                                sv: val.sv,
                                needLogin: val.needLogin,
                                updateTime: val.updateTime
                            }
                        }
                        _data.push(obj)
                    })
                    setData(_data)
                    setAllData(res.data)
                  }
            } catch (e) {
              message.error(e.message);
            }
            setInit(true)
          })
        // fetchOLAS().then(res => console.log('Olas',res))
      },[])
    
    
      function checkUser(arr:Array<any>){
        let numArr = {
            u0: {num:0, name:'默认'},
            u1: {num:0, name:'新手用户'},
            u2: {num:0, name:'次新用户'},
            u3: {num:0, name:'休眠新用户'},
            u4: {num:0, name:'流失新用户'},
            u5: {num:0, name:'成长用户'},
            u6: {num:0, name:'成熟用户'},
            u7: {num:0, name:'高净值用户'},
            u8: {num:0, name:'休眠用户'},
            u9: {num:0, name:'流失用户'},
            F:{num:0, name:'F类审核用户'}
        }
        arr.map((val)=>{
            val.utype.map((item)=> {
                numArr[item].num ++
            })
        })
        let flag = true
        for(let key in numArr){
            if(numArr[key].num<5){
                message.error(`${numArr[key].name}的数量为${numArr[key].num}，最低为五个`)
                flag = false
                return
            }
        }
        return flag
    }
      let onSubmit = () => {
        if (valid.length > 0) {
          message.error(`校验未通过字段：${valid.toString()}`);
        }else {
            let _value = []
            let isStop = false
            originData.map((val,index) => {
                let _data = {...val.other,...val.formData,...val.imgData,...val.configData,...val.relationData}
                if (!_data.ggTitle) {
                    message.error(`请填写第${index+1}项宫格名称`)
                    isStop = true
                    return
                } else if (!_data.jumpAction) {
                    message.error(`请填写第${index+1}项跳转链接`)
                    isStop = true
                    return
                } else if (!_data.needLogin) {
                    message.error(`请选择第${index+1}项是否登录后可见`)
                    isStop = true
                    return
                } else if (!_data.image) {
                    message.error(`请上传第${index+1}项图标`)
                    isStop = true
                    return
                } else if (_data.platform.length === 0) {
                    message.error(`请选择第${index+1}项适用平台`)
                    isStop = true
                    return
                } else if (_data.utype.length === 0) {
                    message.error(`请选择第${index+1}项用户类型`)
                    isStop = true
                    return
                }
                _value.push(_data)
            })
            if(isStop || !checkUser(_value)) return
            let _sendData ={
                type: 'update',
                value: JSON.stringify({
                    type: allData.type,
                    fv: allData.fv,
                    index: allData.index,
                    fvUpdateTime: allData.fvUpdateTime,
                    floorDto: allData.floorDto,
                    confs: _value,
                }),
                lastEditor: localStorage.name
            }
            console.log('send',_sendData)
            fetchGggongneng(
                _sendData
            ).then((res)=>{
                try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('提交成功！');
                    setTimeout(() => {
                        location.href = `#/frontend/${showKyc ? 'home' : 'shouye'}`
                    }, 1000);
                }
                } catch (e) {
                message.error(e.message);
                }
            })
        
        }
    
      }
    function addItem(){
        let obj = {
            formData: {
                ggTitle:'',
                jumpAction:'',
                needLogin:'',
                version: '',
                subScript:'',
                startTime:'',
                endTime:''
            },
            imgData: {
                image: ''
            },
            configData:{
                platform:config.platform,
                utype:config.user,
            },
            relationData:{
                targetType:'kyc',
                kycLogic:'and',
                kycs:[
				],
				olasId:[]
            },
            other:{
                sv: null,
                needLogin: null,
                updateTime: null
            }
           
        }
        let data = [].concat(originData,obj)
        setData(data)
    }

    function handleUpdate(data:any, index:number){
        if(!isModify) setModify(true)
        let _originData: any = [].concat(originData)
        _originData[index] = data
        setData(_originData)
    }
    function handleDelete(index:number){
        if(!isModify) setModify(true)
        console.log(index)
        let _originData = [].concat(originData)
        _originData.splice(index,1)
        setData(_originData)
    }
    function onValidate(valid) {
        setValid(valid);
        console.log(valid);
    }
    function handleSelect(item) {
      // console.log(item)
      // console.log(config)
        let tag = 0;
        if (config.user.length === 0 && config.platform.length === 0) {
            if(item.configData.platform.length === 0 && item.configData.utype.length === 0){
                tag = 1;
            }
        } else if (config.user.length === 0) {
            for (let data of item.configData.platform) {
            if (config.platform.indexOf(data) != -1) {
                tag = 1;
                break;
            }
            }
        } else if (config.platform.length === 0) {
            for (let data of item.configData.utype) {
            if (config.user.indexOf(data) != -1) {
                tag = 1;
                break;
            }
            }
        } else {
            for (let data of item.configData.platform) {
                if (config.platform.indexOf(data) != -1) {
                    for (let data of item.configData.utype) {
                        if (config.user.indexOf(data) != -1) {
                            tag = 1;
                            break;
                        }
                    }
                    if (tag === 1) {
                        break;
                    }
                }
            }
        }
      // console.log('tag=',tag)
        return !!tag
    }
    function goUp(e, item, index){
        e.stopPropagation();

        if(index === 0){
            message.info('已在最上方')
            return
        }
        let _originData = [].concat(originData)
        _originData.splice(index,1)
        _originData.splice(index-1,0,item)
        console.log(_originData)
        setData(_originData)
        if(!isModify) setModify(true)
    }
    function goDown(e, item, index){
        e.stopPropagation();
        if(index === originData.length-1){
            message.info('已在最下方')
            return
        }
        let _originData = [].concat(originData)
        _originData.splice(index,1)
        _originData.splice(index+1,0,item)
        console.log(_originData)
        setData(_originData)
        if(!isModify) setModify(true)
    }

    if (!init) return '加载中'
    return <div>
        <LocationModel location={'宫格-功能'} />
        <div style={{display: showKyc ? '' : 'none'}} >
            <ConfigSelect handleChange={handleChange} isHead={true} />
        </div>
        <Button type="primary" onClick={onSubmit} disabled={!isModify}>保存</Button>
        {/* {console.log('faormadas',originData)} */}
        <Collapse>
        {
            originData.map((item: any,index) => {
              
                return handleSelect(item) ? 
                <Panel header={`宫格名称:${item.formData.ggTitle}`} 
                  key={index} 
                  extra={<div className={styles['m-collpase-button']}><Button onClick={(e)=>{goUp(e,item,index)}}>上移</Button><Button onClick={(e)=>{goDown(e,item,index)}}>下移</Button></div>}
                  >
                <MyCard 
                    showKyc={showKyc}
                    data={item}
                    position={index} 
                    kycTag={kycTag}
					olasTag={olasTag}
                    handleDelete={handleDelete} 
                    handleUpdate={handleUpdate} 
                    key={index}></MyCard></Panel>
                  : null
            })
        }
        </Collapse>

        
        <Button onClick={addItem} type='primary' style={{marginTop:'20px'}}>增加</Button>
    </div>

}