import React, {useEffect, useState} from 'react';
import { Checkbox, Button, message, Popconfirm } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../frontend.less'
import ConfigSelect from '../compoment/selectModel/index'
import TagModel from '../compoment/tagModel/index';
import ImgUpload from '../compoment/uploadImg/index.jsx';
interface dataProps {
    data: Object;
    kycTag: Array<any>;
    olasTag: Array<any>;
    // formData: Object;
    // configData: Object;
    // relationData: Object;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
    showKyc: boolean;
}

export default function (props:dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    // const [data,setData] = useState([])

    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [imgData, setImgData] = useState({'720p':'','1080p':''});
    const [formData,setFormState] = useState({});
    const [configData,setConfig] = useState({});
    const [relationData,setRelation] = useState({});

    useEffect(() => {
        console.log('formData=',props.formData)
        setFormState(props.data.formData)
        setImgData(props.data.imgData)
        setConfig(props.data.configData)
        setRelation(props.data.relationData)
    },[props.data])
    
    
    function onValidate(valid) {
        setValid(valid);
    }
    function handleChange(){
        let _isEdit = isEdit

        if(_isEdit){
            let _data = {
                formData,
                imgData,
                configData,
                relationData,
                other: props.data.other
            }
            console.log(_data)
            if (!imgData['720p'] || !imgData['1080p']) {
                message.error('请上传开屏页图片')
            } else if (!formData.screenName) {
                message.error('请填写开屏名称')
            } else if (!formData.jumpAction) {
                message.error('请填写跳转链接')
            } else if (!formData.jumpTitle) {
                message.error('请填写按钮文案')
            } else if (!formData.startTime) {
                message.error('请填写开始时间')
            } else if (!formData.endTime) {
                message.error('请填写结束时间')
            } else if (configData.platform.length === 0) {
                message.error('请选择适用平台')
            } else if (configData.utype.length === 0) {
                message.error('请选择用户类型')
            } else {
                props.handleUpdate(_data,props.position)
                setEdit(!_isEdit)
            }
        } else {
            setEdit(!_isEdit)
        }   
    }
    function handleSelect(data){
        console.log('data',data)
        setConfig(data)
    }
    function handleTag(data:any){
        console.log('tag',data)
        setRelation(data)
    }
    function handleImg(data:any, key:any){
        let _imgData = imgData
        _imgData[key] = data
        console.log(_imgData)
        setImgData(_imgData)
    }
    
    // if (!init) return '加载中'
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {props.handleDelete(props.position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                {Object.keys(imgData).map((item,index) => {
                    return <ImgUpload 
                        handleChange={(val) => handleImg(val, item)}
                        imageUrl={imgData[item]}
                        isEdit={isEdit}
                        title='开屏页图片'
                        size={item === '720p' ? ['720px*1048px'] : ['1080px*1920px']}
                        key={index}
                    />
                })}
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={onValidate}
                    onChange={setFormState}
                    readOnly={!isEdit}
                />
                <div style={{display: props.showKyc ? '' : 'none'}}>
                    <ConfigSelect
                        handleChange={handleSelect}
                        isHead={false}
                        isEdit={isEdit}
                        data={configData}  
                    />
                    <TagModel
                        handleChange={handleTag}
                        data={relationData}
                        kycTag={props.kycTag}
                        olasTag={props.olasTag}
                        isEdit={isEdit}
                    />
                </div>
            </div>

}