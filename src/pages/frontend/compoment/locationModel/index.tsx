import React from 'react'
import {Breadcrumb} from 'antd'

const showKyc = window.location.href.includes('edit=1');
export default function (props) {
    const {location} = props
    return (
        <section style={{marginBottom:20}}>
            <Breadcrumb separator=">">
                <Breadcrumb.Item href={`#/frontend/${showKyc ? 'home' : 'shouye'}`}>首页配置</Breadcrumb.Item>
                <Breadcrumb.Item>{location}</Breadcrumb.Item>
            </Breadcrumb>
        </section>
    )
}