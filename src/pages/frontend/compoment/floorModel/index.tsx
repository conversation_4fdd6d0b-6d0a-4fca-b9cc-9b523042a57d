import React, {useEffect, useState} from 'react';
import { Checkbox, Button, message } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../../frontend.less';

export default function (props: Object) {
    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [formData,setFormState] = useState({});
    const [valid, setValid] = useState([]);
    const [isEdit, setEdit] = useState(false);
    useEffect(() => {
        setFormState(props.data)
    }, [props])

    function onValidate(valid) {
        setValid(valid);
    }

    function handleChange() {
        let _isEdit = isEdit
        if(_isEdit){
            if (!formData.floorTitle) {
                message.error('请填写楼层标题')
            } else {
                props.handleUpdate(formData)
                setEdit(!_isEdit)
            }
        } else {
            setEdit(!_isEdit)
        }
    }

    return (
        <section className={styles['m-card']}>
            <div className={styles['m-floor']}>
                <div style={{marginLeft:40}}>楼层信息</div>
                <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
            </div>
            <FormRender
                propsSchema={formConfig}
                displayType='row'
                formData={formData}
                onValidate={onValidate}
                onChange={setFormState}
                readOnly={!isEdit}
            />
        </section>

    )
}