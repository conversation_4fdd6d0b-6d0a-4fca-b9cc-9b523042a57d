import React, {useEffect, useState} from 'react';
import { Checkbox, Button, message, Popconfirm } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../frontend.less'
import ConfigSelect from '../compoment/selectModel/index'
import TagModel from '../compoment/tagModel/index';
import ImgUpload from '../compoment/uploadImg/index.jsx'
interface dataProps {
    data: Object;
    // formData: Object;
    // configData: Object;
    // relationData: Object;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
    showKyc: boolean;
}

export default function (props:dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    // const [data,setData] = useState([])

    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [formData,setFormState] = useState({});
    const [imgData,setImgData] = useState({})
    const [configData,setConfig] = useState({});
    const [relationData,setRelation] = useState({});

    useEffect(() => {
        console.log('formData=',props.formData)
        setFormState(props.data.formData)
        setImgData(props.data.imgData)
        setConfig(props.data.configData)
        setRelation(props.data.relationData)
    },[props.data])
    
    
    function onValidate(valid) {
        setValid(valid);
        // console.log(valid);
    }
    function selectReview(arr:Array<any>){
        let flag = true;
        console.log(arr)
        for( let i = 0; i < arr.length ; i++){
            if(arr[i] === 'and'){
                message.error('适用平台不可为App-安卓')
                flag = false
                break
            } else if(arr[i] === 'ios'){
                message.error('适用平台不可为App-iOS')
                flag = false
                break
            } else if(arr[i] === 'iossdkvip'){
                message.error('适用平台不可为ios至尊')
                flag = false
                break
            } else if(arr[i] === 'andsdkvip'){
                message.error('适用平台不可为安卓至尊')
                flag = false
                break
            } 
        }
        return flag

    }
    function handleChange(){
        let _isEdit = isEdit
        if(_isEdit){
            let _data = {
                formData,
                imgData,
                configData,
                relationData,
                other: props.data.other
            }
            if (!formData.resourceName) {
                message.error('请填写资源名称')
            } else if (!formData.title) {
                message.error('请填写标题')
            } else if (!formData.explain) {
                message.error('请填写说明')
            } else if (!imgData.image) {
                message.error('请上传图标')
            } else if (configData.platform.length == 0) {
                message.error('请选择适用平台')
            } else if (configData.utype.length == 0) {
                message.error('请选择用户类型')
            } else if (!selectReview(configData.platform)) {
                
            } else {
                props.handleUpdate(_data,props.position)
                setEdit(!_isEdit)
            }
        } else {
            setEdit(!_isEdit)
        }
        
    }
    function handleSelect(data){
        console.log('data',data)
        setConfig(data)
    }
    function handleTag(data:any){
        console.log('tag',data)
        setRelation(data)
    }
    function handleImg(data:any){
        setImgData({image: data})
    } 
    
    // if (!init) return '加载中'
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {props.handleDelete(props.position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <ImgUpload 
                    handleChange={handleImg}
                    imageUrl={imgData.image}
                    size={['40px*40px']}
                    isEdit={isEdit}
                    title='图标'
                />
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={onValidate}
                    onChange={setFormState}
                    readOnly={!isEdit}
                />
                <div style={{display: props.showKyc ? '' : 'none'}}>
                    <ConfigSelect
                        handleChange={handleSelect}
                        isHead={false}
                        isEdit={isEdit}
                        data={configData}  
                    />
                    <strong style={{marginLeft:'110px',marginBottom:'20px'}}>注意：适用平台仅可选择SDK-安卓和SDK-iOS</strong>
                    <div style={{marginBottom:'20px'}}></div>
                    <TagModel
                        handleChange={handleTag}
                        data={relationData}
                        kycTag={props.kycTag}
                        olasTag={props.olasTag}
                        isEdit={isEdit}
                    />
                </div>
    
            </div>

}