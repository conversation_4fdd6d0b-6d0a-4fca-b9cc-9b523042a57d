export const handleApi = (hash: string) => {
    let get =  '';
    let post = ''
    switch(hash) {
      
        case 'coupon':
            get = 'fetchSYPacket';
            post = 'fetchSYPacket';
            break;
  
       
        case 'threesteps':
            get = 'fetchSYNewAccount';
            post = 'fetchSYNewAccount';
            break;
        
    }
    return {
        get,
        post
    };
}
export const modelName = (hash: string) => {
    const obj = {
        
        'coupon': {
            location: '优惠券',
            name: 'couponName',
            type: 'packet'
        },
        
        
        'threesteps': {
            location: '新人三步走',
            name: 'name',
            type: 'newaccount'
        },
        
    }
    return obj[hash]
}

export const fetchData = (item: any, hash: string) => {
    let formData: any = {};
    switch(hash) {
        
        case 'coupon':
            formData = {
                name: item.name,
                page: item.page,
                content: item.content,
                packetId: item.packetId,
                receiveButton: item.receiveButton,
                watchButton: item.watchButton,
                watchUrl: item.watchUrl,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        
        case 'threesteps':
            formData = {
                name: item.name,
                page: item.page,
                title: item.title,
                steps: item.steps ?? [],
                finishButton: item.finishButton,
                url: item.url,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        
    }          
    return formData;        
}
export const addData = (hash: string) => {
    let formData: any = {};
    switch(hash) {
        
        case 'coupon':
            formData = {
                couponName: '',
                imageUrl: '',
                couponText: '',
                couponId: '',
                achieveText: '',
                seeText: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: '',
            }
            break;
        
        case 'threesteps':
            formData = {
                name: '',
                page: '',
                title: '',
                steps: [],
                finishButton: '',
                url: '',
                version: '',
                startTime: '',
                endTime: '',
            }
            break;
        
    }          
    return formData;        
}
export const checkUrl = (str: string, sort: string = '') => {
    if (str) {
        if (!/^(http:\/\/|https:\/\/|client.html).+/.test(str)) {
            return {
                isError: true,
                msg: `请填写${sort}正确的跳转链接`
            }
        }
        if (str.length !== str.trim().length) {
            return {
                isError: true,
                msg: `${sort}跳转链接前后不能有空格`
            }
        } else {
            return {
                isError: false,
                msg: ''
            }
        }
    } else {
        return {
            isError: false,
            msg: ''
        }
    }
}