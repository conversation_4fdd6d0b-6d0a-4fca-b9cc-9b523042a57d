import React from 'react';
import { Breadcrumb } from 'antd';

interface iprops {
  location: string;
}
const showKyc = window.location.href.includes('edit=1');
export default function({ location }: iprops) {
  return (
    <section style={{ marginBottom: 20 }}>
      <Breadcrumb separator=">">
        <Breadcrumb.Item href={`#/frontend/${showKyc ? 'home' : 'shouye'}`}>当前位置：新版首页配置</Breadcrumb.Item>

        {/* <Breadcrumb.Item href="#/form/scFinancialTabV2/pageConfig">页面配置</Breadcrumb.Item> */}
        <Breadcrumb.Item>{location}</Breadcrumb.Item>
      </Breadcrumb>
    </section>
  );
}
