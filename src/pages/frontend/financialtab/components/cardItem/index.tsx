import React, {useEffect, useState, useCallback, useRef} from 'react';
import { Button, message, Popconfirm, Select, Input, InputNumber } from 'antd';
import FormRender from "form-render/lib/antd";
import styles from '../../pageConfig/index.less';
import DesignatedUser from '../DesignatedUser';
import api from 'api';
import { timeFormat2 } from '@/utils/utils';
import { checkUrl } from '../../script/utils'; 

import COUPON_FORM_CONFIG from '../../pageConfig/coupon/form.json';

import STEP_FORM_CONFIG from '../../pageConfig/threesteps/form.json';

import classnames from 'classnames';

import CouponUploadImg from '../../pageConfig/coupon/uploadImg';

import StepUploadImg from '../../pageConfig/threesteps/uploadImg';

import StepList from '../../pageConfig/threesteps/stepList';

import ConfigSelect from '../../../compoment/selectModel/index';
import TagModel from '../../../compoment/tagModel/index'

const { updateUserType, checkCoupon, postActivityDetails } = api;
const { Option } = Select;
interface dataProps {
    data: any;
    kycTag: any;
    olasTag: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
    gonggeClassify?: any;
    floorType: string;
    showKyc: boolean;
}
interface iSelectProps {
    onChange: Function,
    value: string,
    readonly: boolean
}
// import ConfigSelect from '../../../compoment/selectModel/index'
export default function ({floorType, gonggeClassify, data, kycTag, olasTag, position, handleUpdate, handleDelete, showKyc}: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    const [designatedData, setDesignatedData] = useState<any>();
    const [formConfig, setFormConfig] = useState<any>(null);
    const [formData, setFormState] = useState<any>({});
    const [commentList, setCommentList] = useState([]);
    const [codeEdit, setCodeEdit] = useState(true);
    useEffect(() => {
        let json: any = '';
        switch(floorType) {
            // case 'searchhotword':
            //     json = SEARCH_FORM_CONFIG;
            //     break;
            // case 'mixbanner':
            //     json = BANNER_FORM_CONFIG;
            //     break;
            // case 'notice':
            //     json = NOTICE_FORM_CONFIG;
            //     break;
            // case 'gongge':
            //     json = ICON_FORM_CONFIG;
            //     break;
            // case 'gonggeall':
            //     json = ICON_ALL_FORM_CONFIG;
            //     break;
            // case 'operationposition':
            //     json = POSITION_FORM_CONFIG;
            //     break;
            // case 'usercomment':
            //     json = USER_FORM_CONFIG;
            //     break;
            case 'coupon':
                json = COUPON_FORM_CONFIG;
                break;
            // case 'operationblock':
            //     json = BLOCK_FORM_CONFIG;
            //     break;
            // case 'biggiftbag':
            //     json = BAG_FORM_CONFIG;
            //     break;
            // case 'operationCard':
            //     json = CARD_FORM_CONFIG;
            //     break;
            case 'threesteps':
                json = STEP_FORM_CONFIG;
                break;
            // case 'multidimension':
            //     json = MULT_FORM_CONFIG;
            //     break;
            // case 'featuredList':
            //     json = FEATURED_FORM_CONFIG;
            //     break;
            // case 'mainOperate': 
            //     json = MAINOPERATE_FORM_CONFIG;
            //     break;
            // case 'secOperate':
            //     json = SECOPERATE_FORM_CONFIG;
            //     break;
            // case 'marketSituation':
            //     json = MARKETSITUATION_FORM_CONFIG;
            //     break;
            // case 'capitalTrend':
            //     json = CAPITALTREND_FORM_CONFIG;
            //     break;
            // case 'FindChance':
            //     json = INVESTMENTOPORTUNITY_FORM_CONFIG;
            //     break;
            // case 'FindInsurance':
            //     json = FINDINSURANCE_FORM_CONFIG;
            //     break;
            // case 'RobustFinancial':
            //     json = ROBUSTFINANCIAL_FORM_CONFIG;
            //     break;
        }
        setFormConfig(json);
    }, [])
    useEffect(() => {
        setFormState(data.formData);
        // if (floorType === 'usercomment') {
        //     setCommentList(data.commentList);
        // }   
        setDesignatedData({
            configData: data.configData,
            relationData: data.relationData
        })
    }, [data]);

    const onValidate = (valid: any) => {
        setValid(valid);
    }
    const handleChange = async () => {
        if(isEdit){
            
            if (floorType === 'coupon') {
                if (!formData.name) {
                    message.error('请填写优惠券运营位名称')
                    return;
                }
                if (!formData.page) {
                    message.error('请上传优惠券图标')
                    return;
                }
                if (!formData.content) {
                    message.error('请填写优惠券展示文案')
                    return;
                }
                if (!formData.packetId) {
                    message.error('请填写优惠券ID')
                    return;
                }
                if (!formData.receiveButton) {
                    message.error('请填写领取按钮文描')
                    return;
                }
                if (!formData.watchButton) {
                    message.error('请填写查看按钮文描')
                    return;
                }
                if (!formData.watchUrl) {
                    message.error('请填写跳转链接')
                    return;
                }  
                try {
                    let result = await checkCoupon({}, formData.packetId);
                    const { status_code, status_msg } = result;
                    if (status_code === 0) {
                        if (result?.data?.length > 0) {
                            for (let i = 0, len = result.data.length; i < len; i++) {
                                const { couponStartDate, couponEndDate, couponId } = result.data[i];
                                if ( couponStartDate && couponEndDate ) {
                                    let time = timeFormat2();
                                    time = time.replace(/[^\d]/g, '');
                                    if (time < couponStartDate || time > couponEndDate) {
                                        message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                        return;
                                    }
                                    if (formData.startTime) {
                                        let startTime = formData.startTime.replace(/[^\d]/g, '');
                                        if (startTime < couponStartDate) {
                                            message.error('模块的时间范围必须在优惠券的时间范围之内');
                                            return;
                                        }
                                    }
                                    if (formData.endTime) {
                                        let endTime = formData.endTime.replace(/[^\d]/g, '');
                                        if (endTime >= couponEndDate) {
                                            message.error('模块的时间范围必须在优惠券的时间范围之内');
                                            return;
                                        }
                                    }
                                } else {
                                    message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                    return;
                                }
                            }
                        } else {
                            message.error('请检查优惠券ID是否正确');
                            return;
                        }
                    } else {
                        message.error(status_msg || '网络请求错误，请稍后再试');
                        return;
                    }
                } catch(e) {
                    message.error(e.message);
                    return;
                }
            } 
            
            
             else if (floorType === 'threesteps') {
                if (!formData.name) {
                    message.error('请填写三步走策略名称')
                    return;
                }
                if (!formData.page) {
                    message.error('请上传小图标')
                    return;
                }
                if (!formData.title) {
                    message.error('请填写模块标题')
                    return;
                }
                
                let activityIdObj: any = {
                    coupon: '',
                    gold: []
                }
                for (let i = 0; i < 3; i++) {
                    let obj = formData?.steps[i] ?? {};
                    const { type, activityId, page, status, url, button, buttonUrl, rightTime } = obj;
                    if (!type) {
                        message.error('请选择权益类型')
                        return;
                    }
                    if (['1','2', '3'].includes(type) && !activityId) {
                        message.error(`请填写${type === '1' ? '体验金活动' : type === '2' ? '优惠券' : '活动'}ID`)
                        return;
                    }
                    if (!page) {
                        message.error(`请填写步骤${i+1}图片`)
                        return;
                    }
                    if (type === '3' && !rightTime) {
                        message.error(`请填写金牛会员权益时间`)
                        return;
                    }
                    if (!status) {
                        message.error(`请填写步骤${i+1}状态文描`)
                        return;
                    }
                    if (!url) {
                        message.error('请填写权益查看跳转链接')
                        return;
                    }
                    let result = checkUrl(url, `步骤${i+1}`);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }

                    if (!button) {
                        message.error(`请填写步骤${i+1}按钮文描`)
                        return;
                    }
                    if (i === 2 ) {
                        if (!buttonUrl) {
                            message.error(`请填写跳转链接`)
                            return;
                        } else {
                            let result = checkUrl(buttonUrl, `步骤${i+1}`);
                            if (result.isError) {
                                message.error(result.msg);
                                return;
                            }
                        }
                        
                    } 
                    
                    if (type === '1') {
                        activityIdObj.gold.push(activityId);
                    }
                    if (type === '2') {
                        activityIdObj.coupon += activityId + ',';
                    }
                }
                if (!formData.finishButton) {
                    message.error('请填写完成按钮文描')
                    return;
                }
                try {
                    let promiseAll = []
                    if (activityIdObj.coupon) {
                        let promiseFace = await checkCoupon({}, activityIdObj.coupon);
                        promiseAll.push(promiseFace);
                    }
                    if (activityIdObj.gold.length > 0) {
                        for (let i = 0, len = activityIdObj.gold.length; i < len; i++ ) {
                            let promiseFace = await postActivityDetails({
                                type: 'query',
                                activityIndex: activityIdObj.gold[i]?.trim()
                            });
                            promiseAll.push(promiseFace);
                        }
                    }
                    let resArr = await Promise.all(promiseAll);
                    if (activityIdObj.coupon) {
                        let res0 = resArr[0];
                        const { status_code, status_msg } = res0;
                        if (status_code === 0) {
                            if (res0?.data?.length > 0) {
                                for (let i = 0, len = res0.data.length; i < len; i++) {
                                    const { couponStartDate, couponEndDate, couponId } = res0.data[i];
                                    if ( couponStartDate && couponEndDate ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        if (time < couponStartDate || time > couponEndDate) {
                                            message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let startTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (startTime < couponStartDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                            let endTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (endTime >= couponEndDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                        return;
                                    }
                                }
                            } else {
                                message.error('请检查优惠券ID是否正确');
                                return;
                            }
                        } else {
                            message.error(status_msg || '网络请求错误，请稍后再试');
                            return;
                        }
                    }
                    if (activityIdObj.gold.length > 0) {
                        let start = activityIdObj.coupon ? 1 : 0;
                        for (let i = start, len = resArr.length; i < len; i++ ) {
                            const { code } = resArr[i];
                            if (code === '0000') {
                                if (resArr[i]?.data) {
                                    let { startTime, endTime, indexStr } = resArr[i]?.data;
                                    if ( startTime && endTime ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        startTime = startTime.replace(/[^\d]/g, '');
                                        endTime = endTime.replace(/[^\d]/g, '');
                                        if (time < startTime || time > endTime) {
                                            message.error(`当前体验金活动ID-${indexStr}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let formStartTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (formStartTime < startTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                             let formEndTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (formEndTime >= endTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回体验金活动ID-${indexStr}生效时间`);
                                        return;
                                    }
                                } else {
                                    message.error('请检查体验金活动ID是否正确');
                                    return;
                                }
                            } else {
                                message.error(resArr[i]?.message || '网络请求错误，请稍后再试');
                                return;
                            }
                        }
                    }
                }catch(err) {
                    message.error(err.message);
                    return;
                }
            } 
            
            if (formData.startTime && formData.endTime) {
                let startTime = formData.startTime.replace(/[^\d]/g, '');
                let endTime = formData.endTime.replace(/[^\d]/g, '');
                if (startTime >= endTime) {
                    message.error('开始时间应早于结束时间');
                    return;
                }
            }
            if (['coupon'].includes(floorType)) {
                let result = checkUrl(formData.jumpAction);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (['threesteps'].includes(floorType)) {
                let result = checkUrl(formData.url);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            }
            

            let realData: any = {};
            const {configData, relationData} = designatedData;
          
            if (relationData?.targetType === 'kyc') {
                realData.olasId = '';
                realData.olasType = '';
            } else if (relationData?.targetType === 'olas') {
                realData.kycLogic = '';
                realData.kycs = [];
            }

            let _data: any = {
                formData,
                configData,
                relationData,
            }
          
            if (configData.platform?.length === 0) {
                message.error('请选择使用平台')
                return;
            }
            if (configData.utype?.length === 0) {
                message.error('请选择用户类型')
                return;
            }
            console.log('saved', _data);
            handleUpdate(_data, position)
            setEdit(!isEdit)   
           
            
        } else {
            setEdit(!isEdit)
        }
    }
   
    const returnWidgets = () => {
        let widgets = {};
        switch(floorType) {
           
            case 'coupon': 
                widgets = {
                    uploadImg: CouponUploadImg
                }
                break;
           
            case 'threesteps': 
                widgets = {
                    uploadImg: StepUploadImg,
                    stepList: StepList
                }
                break;
           
        }
        return widgets
    }
    const onFormChange = (val: any) => {
      
        setFormState(val)
    }
   
    function handleSelect(data){
        console.log('data',data)
        //setConfig(data)
        setDesignatedData({
            ...designatedData,
            configData: data,
            
        })
    }
    function handleTag(data:any){
        console.log('tag',data)
        //setRelation(data)
        setDesignatedData({
            ...designatedData,
            // configData: data,
            relationData:data
        })
    }
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} disabled={!codeEdit} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {handleDelete(position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <h1 className="g-fs20 f-bold">配置详情</h1>
               
                 <FormRender
                        propsSchema={formConfig?.schema}
                        displayType='row'
                        formData={formData}
                        onValidate={onValidate}
                        onChange={onFormChange}
                        readOnly={!isEdit}
                        labelWidth={135}
                        widgets={returnWidgets()}
                    />
                <div style={{display: showKyc ? '' : 'none'}}>
                    <ConfigSelect
                        handleChange={handleSelect}
                        isHead={false}
                        isEdit={isEdit}
                        data={designatedData?.configData}  
                    />
                    <TagModel
                        handleChange={handleTag}
                        data={designatedData?.relationData}
                        kycTag={kycTag}
                        olasTag={olasTag}
                        isEdit={isEdit}
                    />
                </div>
            </div>

}