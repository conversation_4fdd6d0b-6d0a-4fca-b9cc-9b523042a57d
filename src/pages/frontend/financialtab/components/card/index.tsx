import React, { useEffect, useState } from 'react';
import { Button, message, Collapse, Input, Popconfirm } from 'antd';
import api from 'api';
import MyCard from '../cardItem';
import styles from '../../pageConfig/index.less';
import ConfigSelect from '../../../compoment/selectModel/index';
import LocationModel from '../locationModel';
import { timeFormat2 } from '@/utils/utils';
import { fetchData, addData, handleApi, modelName } from '../../script/utils';
import { checkUrl } from '../../script/utils'; 
import FloorModel from '@/pages/frontend/compoment/floorModel';

const { fetchCBAS, fetchOLAS, checkCoupon, postActivityDetails } = api;
const showKyc = location.href.includes('edit=1');
const { Panel } = Collapse;

interface iProps {
    floorType: string
}

export default function ({floorType}: iProps) {
    const [init, setInit] = useState(true);
    const [originData, setOriginData] = useState<any>([]);
    const [isModify, setModify] = useState(false);
    const [config, setConfig] = useState<{ utype: string[], platform: string[] }>({ utype: [], platform: [] });
    const [allData, setAllData] = useState({})
    const [kycTag, setKycTag] = useState([]);
    const [olasTag, setOlasTag] = useState([]);
    const [activeKey, setActiveKey] = useState(0);
    const [gonggeClassify, setGonggeClassify] = useState<any>([]); // 宫格分类


    const handleChange = (data: any) => {
        console.log('configData', data);
        setConfig(data);
    }
    useEffect(() => {
        fetchCBAS().then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                if (data) {
                    setKycTag(data)
                }
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])
    
    useEffect(() => {
        fetchOLAS().then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                if (data) {
                    let _olasTag: any = [];
                    for (let prop in data) {
                        _olasTag.push({ "groupid": prop, "description": data[prop].description });
                    }
                    setOlasTag(_olasTag);
                }
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, []) 

    useEffect(() => {
        api[handleApi(floorType).get]({
            type:"query"
        }).then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                setInit(true);
                let initData: any = data , _data: any = [];
                console.log(initData);
                initData?.confs?.forEach((item: any) => {
                    let obj: any = {
                        formData: fetchData(item, floorType),
                        configData: {
                            platform: item.platform || [],
                            utype: item.utype || [],
                        },
                        
                        relationData: {
                            
                            targetType: item.targetType,
                            kycLogic: item.kycLogic,
                            kycs: item.kycs,
                            olasId: item.olasId,
                           
                        },
                        other: {
                            sv: item.sv,
                            needLogin: item.needLogin,
                            updateTime: item.updateTime
                        }
                    }
                    if (floorType === 'usercomment') {
                        obj.commentList = item.commentList;
                    }
                    _data.push(obj);
                })
               
                setOriginData(_data); 
                setAllData(res.data||{});
               
                console.log('initdata:', _data);
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])

    const onSubmit = async () => {
        let _value: any = [];
        let _sendData: any = {};
        
        for (let i = 0, len = originData?.length; i < len; i++) {
            const { formData, configData, relationData, commentList } = originData[i];
            
             if (floorType === 'coupon') {
                if (!formData.name) {
                    message.error(`请填写第${i+1}项优惠券运营位名称`)
                    return;
                }
                if (!formData.page) {
                    message.error(`请上传第${i+1}项优惠券图标`)
                    return;
                }
                if (!formData.content) {
                    message.error(`请填写第${i+1}项优惠券展示文案`)
                    return;
                }
                if (!formData.packetId) {
                    message.error(`请填写第${i+1}项优惠券ID`)
                    return;
                }
                if (!formData.receiveButton) {
                    message.error(`请填写第${i+1}项领取按钮文描`)
                    return;
                }
                if (!formData.watchButton) {
                    message.error(`请填写第${i+1}项查看按钮文描`)
                    return;
                }
                if (!formData.watchUrl) {
                    message.error(`请填写第${i+1}项跳转链接`)
                    return;
                }
                try {
                    let result = await checkCoupon({}, formData.packetId);
                    const { status_code, status_msg } = result;
                    if (status_code === 0) {
                        if (result?.data?.length > 0) {
                            for (let i = 0, len = result.data.length; i < len; i++) {
                                const { couponStartDate, couponEndDate, couponId } = result.data[i];
                                if ( couponStartDate && couponEndDate ) {
                                    let time = timeFormat2();
                                    time = time.replace(/[^\d]/g, '');
                                    if (time < couponStartDate || time > couponEndDate) {
                                        message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                        return;
                                    }
                                    if (formData.startTime) {
                                        let startTime = formData.startTime.replace(/[^\d]/g, '');
                                        if (startTime < couponStartDate) {
                                            message.error('模块的时间范围必须在优惠券的时间范围之内');
                                            return;
                                        }
                                    }
                                    if (formData.endTime) {
                                        let endTime = formData.endTime.replace(/[^\d]/g, '');
                                        if (endTime >= couponEndDate) {
                                            message.error('模块的时间范围必须在优惠券的时间范围之内');
                                            return;
                                        }
                                    }
                                } else {
                                    message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                    return;
                                }
                            }
                        } else {
                            message.error('请检查优惠券ID是否正确');
                            return;
                        }
                    } else {
                        message.error(status_msg || '网络请求错误，请稍后再试');
                        return;
                    }
                } catch(e) {
                    message.error(e.message);
                    return;
                }
            }
          
             else if (floorType === 'threesteps') {
                if (!formData.name) {
                    message.error(`请填写第${i+1}项三步走策略名称`)
                    return;
                }
                if (!formData.page) {
                    message.error(`请上传第${i+1}项小图标`)
                    return;
                }
                if (!formData.title) {
                    message.error(`请填写第${i+1}项模块标题`)
                    return;
                }

                let activityIdObj: any = {
                    coupon: '',
                    gold: []
                }
                for (let j = 0; j < 3; j++) {
                    let obj = formData?.steps[j] ?? {};
                    const { type, activityId, page, status, url, button, buttonUrl,rightTime } = obj;
                    if (!type) {
                        message.error(`请选择第${i+1}项步骤${j+1}权益类型`)
                        return;
                    }
                    if (['1','2', '3'].includes(type) && !activityId) {
                        message.error(`请填写第${i+1}项步骤${j+1}${type === '1' ? '体验金活动' : type === '2' ? '优惠券' : '活动'}ID`)
                        return;
                    }
                    if (!page) {
                        message.error(`请填写第${i+1}项步骤${j+1}图片`)
                        return;
                    }
                    if (type === '3' && !rightTime) {
                        message.error(`请填写第${i+1}项步骤${j+1}金牛会员权益时间`)
                        return;
                    }
                    if (!status) {
                        message.error(`请填写第${i+1}项步骤${j+1}状态文描`)
                        return;
                    }
                    if (!url) {
                        message.error(`请填写第${i+1}项步骤${j+1}权益查看跳转链接`)
                        return;
                    }
                    let result = checkUrl(url, `第${i+1}项步骤${j+1}`);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }
                    if (!button) {
                        message.error(`请填写第${i+1}项步骤${j+1}按钮文描`)
                        return;
                    }
                    if (j === 2 ) {
                        if (!buttonUrl) {
                            message.error(`请填写第${i+1}项步骤${j+1}跳转链接`)
                            return;
                        } else {
                            let result = checkUrl(buttonUrl, `第${i+1}项步骤${j+1}`);
                            if (result.isError) {
                                message.error(result.msg);
                                return;
                            }
                        }
                        
                    }
                    if (type === '1') {
                        activityIdObj.gold.push(activityId);
                    }
                    if (type === '2') {
                        activityIdObj.coupon += activityId + ',';
                    }
                }
                if (!formData.finishButton) {
                    message.error(`请填写第${i+1}项完成按钮文描`)
                    return;
                }
                try {
                    let promiseAll = []
                    if (activityIdObj.coupon) {
                        let promiseFace = await checkCoupon({}, activityIdObj.coupon);
                        promiseAll.push(promiseFace);
                    }
                    if (activityIdObj.gold.length > 0) {
                        for (let i = 0, len = activityIdObj.gold.length; i < len; i++ ) {
                            let promiseFace = await postActivityDetails({
                                type: 'query',
                                activityIndex: activityIdObj.gold[i]?.trim()
                            });
                            promiseAll.push(promiseFace);
                        }
                    }
                    let resArr = await Promise.all(promiseAll);
                    if (activityIdObj.coupon) {
                        let res0 = resArr[0];
                        const { status_code, status_msg } = res0;
                        if (status_code === 0) {
                            if (res0?.data?.length > 0) {
                                for (let i = 0, len = res0.data.length; i < len; i++) {
                                    const { couponStartDate, couponEndDate, couponId } = res0.data[i];
                                    if ( couponStartDate && couponEndDate ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        if (time < couponStartDate || time > couponEndDate) {
                                            message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let startTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (startTime < couponStartDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                            let endTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (endTime >= couponEndDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                        return;
                                    }
                                }
                            } else {
                                message.error('请检查优惠券ID是否正确');
                                return;
                            }
                        } else {
                            message.error(status_msg || '网络请求错误，请稍后再试');
                            return;
                        }
                    }
                    if (activityIdObj.gold.length > 0) {
                        let start = activityIdObj.coupon ? 1 : 0;
                        for (let i = start, len = resArr.length; i < len; i++ ) {
                            const { code } = resArr[i];
                            if (code === '0000') {
                                if (resArr[i]?.data) {
                                    let { startTime, endTime, indexStr } = resArr[i]?.data;
                                    if ( startTime && endTime ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        startTime = startTime.replace(/[^\d]/g, '');
                                        endTime = endTime.replace(/[^\d]/g, '');
                                        if (time < startTime || time > endTime) {
                                            message.error(`当前体验金活动ID-${indexStr}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let formStartTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (formStartTime < startTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                            let formEndTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (formEndTime >= endTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回体验金活动ID-${indexStr}生效时间`);
                                        return;
                                    }
                                } else {
                                    message.error('请检查体验金活动ID是否正确');
                                    return;
                                }
                            } else {
                                message.error(resArr[i]?.message || '网络请求错误，请稍后再试');
                                return;
                            }
                        }
                    }
                }catch(err) {
                    message.error(err.message);
                    return;
                }
            }
          
            if (formData.startTime && formData.endTime) {
                let startTime = formData.startTime.replace(/[^\d]/g, '');
                let endTime = formData.endTime.replace(/[^\d]/g, '');
                if (startTime >= endTime) {
                    message.error(`第${i+1}项开始时间应早于结束时间`);
                    return;
                }
            } 
           
            if ([ 'coupon'].includes(floorType)) {
                let result = checkUrl(formData.jumpAction, `第${i+1}项`);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (['threesteps'].includes(floorType)) {
                let result = checkUrl(formData.url, `第${i+1}项`);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } 
           
            if (configData.platform.length === 0) {
                message.error(`请选择第${i+1}项适用平台`)
                return
            } else if (configData.utype.length === 0) {
                message.error(`请选择第${i+1}项用户类型`)
                return
            }
            if (!relationData) {
                message.error(`请先保存第${i+1}项指定用户数据配置`)
                return
            }
        }
        for (let i = 0, len = originData?.length; i < len; i++) {
            let val = originData[i];
            let realData: any = {};
            let _data: any = {};
            if (val.relationData?.userType === '1') {
                const {blackCustId, whiteCustId, ...other} = val.relationData;
                realData = {...other}
            } else {
                const {blackUserId, whiteUserId, blackCustId, whiteCustId, ...other} = val.relationData;;
                realData = {
                    ...other,
                    blackUserId: blackCustId,
                    whiteUserId: whiteCustId
                }
            }
            if (val.relationData?.targetType === 'kyc') {
                realData.olasId = '';
                realData.olasType = '';
            } else if (val.relationData?.targetType === 'olas') {
                realData.kycLogic = '';
                realData.kycs = [];
            }
           
                _data = { ...val.formData, ...val.configData, ...realData};
            
            _value.push(_data);
        }
       
            _sendData = {
                type:"update",
                value: JSON.stringify({
                    type: allData.type,
                    fv: allData.fv,
                    index: allData.index,
                    fvUpdateTime: allData.fvUpdateTime,
                    floorDto: allData.floorData,
                    confs: _value,
                    // type: modelName(floorType).type
                }),
                lastEditor:localStorage.name
            
        }
        
        console.log('send', _sendData)
        api[handleApi(floorType).post](
            _sendData
        ).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.message);
            } else {
                message.success('保存成功！');
                let timer = setTimeout(() => {
                    clearTimeout(timer);
                    history.back();
                    // location.href = `#/form/scFinancialTabV2/pageConfig`
                }, 1000);
            }
        }).catch((e: Error) => {
            message.error(e?.message);
        })
    }
    function addItem() {
        let obj:any = {
            formData: addData(floorType),
            configData: {
                platform: config.platform,
                utype: config.user,
            },
           
            relationData: {
              
                targetType: '',
                kycLogic: '',
                kycs: [],
                olasId: '',
                
            },
            other: {
                sv: null,
                needLogin: null,
                updateTime: null
            }
        }
        
        let data = [...originData, obj];
        setOriginData(data);
        setActiveKey(data.length - 1);
    }

    function handleUpdate(data: any, index: number) {
        if (!isModify) setModify(true);
        let _originData: any = [...originData];
        _originData[index] = data;
        setOriginData(_originData);  
    }
    function handleDelete(index: number) {
        if (!isModify) setModify(true);
        let _originData = [...originData];
        _originData.splice(index, 1);
        setOriginData(_originData);
    }
    function handleSelect(item: any) {
        console.log("handleSelect",config,item)
        let tag = 0;
        if (item?.configData.platform?.length === 0 && item?.configData.utype?.length === 0) {
            tag = 1;
        } else if (config.user?.length === 0) {
            for (let data of item?.configData?.platform) {
                if (config.platform?.indexOf(data) != -1) {
                    tag = 1;
                    break;
                }
            }
        } else if (config.platform?.length === 0) {
            for (let data of item?.configData.utype) {
                if (config.user?.indexOf(data) != -1) {
                    console.log("cautch",data)
                    tag = 1;
                    break;
                }
            }
        } else {
            for (let data of item?.configData?.platform) {
                if (config.platform?.indexOf(data) != -1) {
                    for (let data of item?.configData?.utype) {
                        if (config.user?.indexOf(data) != -1) {
                            tag = 1;
                            break;
                        }
                    }
                    if (tag === 1) {
                        break;
                    }
                }
            }
        }
        return !!tag
    }
    function handleActiveKey (key: any) {
        setActiveKey(key);
    }
    function goUp(e: any, item: any, index: number) {
        e.stopPropagation();

        if (index === 0) {
            message.info('已在最上方');
            return
        }
        let _originData = [...originData];
        _originData.splice(index, 1);
        _originData.splice(index - 1, 0, item);
        setOriginData(_originData);
        if (!isModify) setModify(true);
    }
    function goDown(e: any, item: any, index: number) {
        e.stopPropagation();
        if (index === originData.length - 1) {
            message.info('已在最下方');
            return
        }
        let _originData = [...originData];
        _originData.splice(index, 1);
        _originData.splice(index + 1, 0, item);
        setOriginData(_originData);
        if (!isModify) setModify(true);
    }
   
    function handleFloor(data:any){
        if(!isModify) setModify(true)
        console.log(data)
        setFloorData(data)
    }
    if (!init) {
        return (
            <div>加载中</div>
        );
    }
    return <div>
        <LocationModel location={modelName(floorType).location} />
        <div style={{display: showKyc ? '' : 'none'}} >
            <ConfigSelect handleChange={handleChange} isHead={true} />
        </div>
        <Button type="primary" style={{marginBottom: 20}} onClick={onSubmit} disabled={!isModify}>保存</Button>
       
        <Collapse activeKey={activeKey} onChange={handleActiveKey}>
            {
                originData?.map((item: any, index: number) => {
                    return handleSelect(item) ?
                <Panel 
                    header={(<span style={{height: 22, display: 'inline-block', verticalAlign: 'middle'}}>#{index+1} {item.formData?.[modelName(floorType).name]}</span>)} 
                    extra={<div className={styles['m-collpase-button']}><Button onClick={(e) => { goUp(e, item, index) }}>上移</Button><Button onClick={(e) => { goDown(e, item, index) }}>下移</Button></div>}
                    key={index}>
                    <MyCard
                        showKyc={showKyc}
                        data={item}
                        gonggeClassify={gonggeClassify}
                        position={index}
                        floorType={floorType}
                        kycTag={kycTag}
                        olasTag={olasTag}
                        handleDelete={handleDelete}
                        handleUpdate={handleUpdate}></MyCard>
                </Panel>
                : null

                })
            }
        </Collapse>
        <Button onClick={addItem} type='primary' style={{ marginTop: '20px' }}>添加</Button>
    </div>

}