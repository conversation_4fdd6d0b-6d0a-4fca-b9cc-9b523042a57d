import React from 'react';
import { FormComponentProps } from 'antd/es/form';
import styles from '../index.less';
import {Button, Popconfirm, Form, Input, Select, message, Switch, DatePicker, Checkbox, Radio, Modal } from 'antd';
import UploadImg from '../../components/wrapperUploadImg'; 
import classNames from 'classnames';

const { Option } = Select;

const formItemLayout = {
  labelCol: {
    span: 3,
  },
  wrapperCol: {
    span: 21
  }
};
interface StepFormProps extends FormComponentProps {
    stepData: any,
    handleUpdate: (data: any, index: number) => void,
    position: number,
    isEdit: boolean
}

interface iState {
  page: string
}

class Step extends React.Component<StepFormProps, iState> {
    constructor(props: StepFormProps) {
      super(props);
      this.state = {
        page: props.stepData?.page ?? ''
      }
    }
    handleImage = (val: string) => {
      const { position, stepData, handleUpdate } = this.props;
      this.setState({
        page: val
      })
      handleUpdate({
        ...stepData,
        version: stepData?.version ?? '',
        buttonVersion: stepData?.buttonVersion ?? '',
        page: val
      }, position);
    }
    static getDerivedStateFromProps(nextProps: any, prevState: any) {
      if (nextProps.stepData?.page && nextProps.stepData.page !== prevState.stepData?.page) {
        return {
          page: nextProps.stepData.page,
        }; 
      }
      return null;
  }
    render() {
      const { page } = this.state;
      const { getFieldDecorator } = this.props.form;
      const { position, stepData, isEdit } = this.props;
      const { type, activityId, status, url, version, button, buttonUrl, buttonVersion, rightTime } = stepData;
      return (
        <div className={classNames(styles['m-card'], styles['m-sc-border'])}>
          <h1 className="g-fs16 f-bold">步骤{position+1}：{position === 0 ? '登录同花顺账号' : position === 1 ? '实名制绑定银行卡' : '首购'}</h1>
          <Form {...formItemLayout}>
            <Form.Item label="权益类型" wrapperCol={{span: 8}}>
                {getFieldDecorator('type', {
                    initialValue: type,
                    rules: [{ required: true, message: '请选择权益类型' }],
                })(
                  <Select style={{width: 200}} disabled={!isEdit}>
                    <Option value="1">体验金</Option>
                    <Option value="2">优惠券</Option>
                    <Option value="3">金牛会员</Option>
                    <Option value="4">自定义模块</Option>
                  </Select>
                )}
            </Form.Item>
            { 
              ['1', '2', '3'].includes(type) && (
                <Form.Item label={ type === '1' ? '体验金活动ID' : type === '2' ? '优惠券ID' : '活动ID' }>
                    {getFieldDecorator('activityId', {
                        initialValue: activityId,
                        rules: [{ required: true, message: `请填写${type === '1' ? '体验金活动ID' : type === '2' ? '优惠券ID' : '活动ID'}` }],
                    })(
                        <Input disabled={!isEdit} style={{width: '150px', marginRight: '10px'}}/>
                    )}
                    { ['1', '3'].includes(type) && <span className={styles['m-required']}>只可配置一个活动ID</span> }
                    { type === '2' && <span className={styles['m-required']}>如有多个优惠券ID，请用英文逗号隔开</span> }
                </Form.Item>
              )
            }
            { type === '3' && (
              <Form.Item label="金牛会员权益时间" wrapperCol={{span: 12}}>
                {getFieldDecorator('rightTime', {
                    initialValue: rightTime,
                    rules: [{ required: true, message: '请填写金牛会员权益时间' }]
                })(
                    <Input disabled={!isEdit}/>
                )}
              </Form.Item>
            ) }
            <Form.Item label={`步骤${position+1}图片`} required={true}>
              <UploadImg onChange={this.handleImage} imgUrl={page} disabled={!isEdit} size={['270*285']}></UploadImg>
            </Form.Item>
            
            <Form.Item label={`步骤${position+1}状态文描`} wrapperCol={{span: 12}}>
                {getFieldDecorator('status', {
                    initialValue: status,
                    rules: [{ required: true, message: `请填写步骤${position+1}状态文描` }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label="权益查看跳转链接" wrapperCol={{span: 18}}>
                {getFieldDecorator('url', {
                    initialValue: url,
                    rules: [{ required: true, message: '请填写权益查看跳转链接' }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label="跳转链接版本控制" wrapperCol={{span: 12}}>
                {getFieldDecorator('version', {
                    initialValue: version ?? '',
                })(
                    <Input disabled={!isEdit}/>
                )}
                <span className={styles['m-card-required']} s-cr="#f5222d">格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist</span>
            </Form.Item>
            <Form.Item label={`步骤${position+1}按钮文描`} wrapperCol={{span: 12}}>
                {getFieldDecorator('button', {
                    initialValue: button,
                    rules: [{ required: true, message: `请填写步骤${position+1}按钮文描` }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            { position === 2 && (
              <>
                <Form.Item label="跳转链接" wrapperCol={{span: 18}}>
                    {getFieldDecorator('buttonUrl', {
                        initialValue: buttonUrl,
                        rules: [{ required: true, message: '请填写跳转链接' }],
                    })(
                        <Input disabled={!isEdit}/>
                    )}
                </Form.Item>
                <Form.Item label="跳转链接版本控制" wrapperCol={{span: 12}}>
                    {getFieldDecorator('buttonVersion', {
                        initialValue: buttonVersion ?? '',
                    })(
                        <Input disabled={!isEdit}/>
                    )}
                    <span className={styles['m-card-required']} s-cr="#f5222d">格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist</span>
                </Form.Item>
              </>
            ) }
            
          </Form>
        </div>
      )
    }
}

const WrappedGift = Form.create<StepFormProps>({ 
  name: 'biggift',
  onValuesChange: (props, changedValues, allValues) => {
    const { handleUpdate, position, stepData } = props;
    handleUpdate({
      ...allValues,
      page: stepData.page
    }, position);
  }
 })(Step);
export default WrappedGift;