import React, {useEffect, useState} from 'react';
import WrappedStep from './step';

interface dataProps {
    onChange: Function,
    value: any,
    readonly: boolean
}
export default function ({onChange, value, readonly }:dataProps ) {
    
    const handleUpdate = (data: any, index: number) => {
        value[index] = data;
        onChange('steps', value);
    }
    return (
        <div>
            {
                [0, 1, 2].map((item) => {
                    let stepData = value[item] ?? {}
                    return (
                        <div key={item}>
                            <WrappedStep position={item} handleUpdate={handleUpdate} isEdit={!readonly} stepData={stepData}></WrappedStep>
                        </div>
                        
                    )
                })
            }
            
        </div>
    )

}