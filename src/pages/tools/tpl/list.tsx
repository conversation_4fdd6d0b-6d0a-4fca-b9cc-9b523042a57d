import React, { useEffect, useState } from 'react';
// 接口
import api from 'api';
// 组件
import { Table, Button, message,Popconfirm } from 'antd';
// 路由
import { history } from 'umi';


const key = "{{pageKey}}"

const { fetchHashAll, postHashDel } = api;
function renderListString(text:string){
  return text ? text : '--'
}
function renderListEnum(arr:any,text:number){

    
      return arr[text];
    
  
}
export default function() {

  const columns = {{columns}}
  // 表格数据
  const [dataSource, setDataSource] = useState<any[]>();
  // 表格加载动画
  const [loadingFlag, setLoadingFlag] = useState<boolean>();

  useEffect(() => {
    setLoadingFlag(true)
    searchAllList();
  }, []);
  // 查询活动列表
  const searchAllList = () => {
    
    fetchHashAll({
      key,
    })
      .then((res: any) => {
        if (res.code === '0000') {
          const list = []
          const data = res.data;
          const keys = Object.keys(data);
          for(const key of keys){
            const item = JSON.parse(data[key]);
            item.indexStr = key
            list.push(item)
          }
          setDataSource(list);
          setLoadingFlag(false);
        } else {
          message.error(res.message);
        }
      })
      .catch((err: unknown) => {
        message.error('查询列表错误');
        console.log('查询列表错误',err);
      });
  };
  
  // 查看
  const handleCheck = (record: any) => {
    history.push({
      pathname: 'detail',
      query: {
        type: 'check',
        id: record.indexStr,
      },
    });
  };
    // 删除
    const handleDelete = (record: any) => {
      const indexStr = record.indexStr;
      postHashDel({
        key,
        propName:indexStr
      }).then((res: { code: string; message: string })=>{
        if(res.code ==="0000"){
          message.success("删除成功")
        }
        else{
          message.success(res.message)
        }
      })
    };
  // 编辑
  const handleEdit = (record: any) => {
    history.push({
      pathname: 'detail',
      query: {
        id: record.indexStr,
      },
    });
  };
 
  return (
    <article>
      <section style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="primary"
          onClick={() => {
            history.push('detail');
          }}
          style={{ marginBottom: '20px' }}
        >
          新增
        </Button>
        <Button
         
          onClick={() => {
            searchAllList()
          }}
          style={{ marginBottom: '20px' ,marginLeft:"20px"}}
        >
         刷新
        </Button>
      </section>
      <Table
        columns={columns}
        dataSource={dataSource}
        scroll={{ x: 'max-content' }}
        loading={loadingFlag}
      ></Table>
    </article>
  );
}
