import React from 'react';
// @ts-ignore
import ImgUpload from './uploadImg/index.jsx';
interface dataProps {
  onChange: Function;
  value: string;
  size: string[];
  title: string;
  disabled: boolean;
  isRequired?: boolean;
}

export default function({ onChange, value, size, title, disabled, isRequired = true }: dataProps) {
  return (
    <div style={{ width: 600 }}>
      <ImgUpload
        handleChange={(value: any) => onChange(value)}
        imageUrl={value}
        isEdit={!disabled}
        title={title}
        size={size}
        isRequired={isRequired}
      />
    </div>
  );
}
