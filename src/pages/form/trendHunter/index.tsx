import React, { useState, useEffect } from 'react';
import { But<PERSON>, Popconfirm, message, Divider } from 'antd';
import IndustryPlateTable from './IndustryPlateTable';
import ShortLineTable from './ShortLineTable';
import PlayIntroductionTable from './PlayIntroductionTable';
import QuestionTable from './QuestionTable';
import api from 'api';

const Title = ({ title }: { title: string }) => {
  return <h2 style={{ marginTop: 24 }}>{title}</h2>;
};

const TrendHunter = () => {
  const [industryPlateTableData, setIndustryPlateTableData] = useState<any[]>([]);
  const [shortLineTableData, setShortLineTableData] = useState<any[]>([]);
  const [playIntroductionTableData, setPlayIntroductionTableData] = useState<any[]>([]);
  const [questionTableData, setQuestionTableData] = useState<any[]>([]);

  useEffect(() => {
    getConfig();
  }, []);

  /**
   * @description: 获取通用配置
   */

  const getConfig = () => {
    api.getTrendHunterConfig().then(res => {
      try {
        const _data = res?.data ? JSON.parse(res?.data) : {};
        console.log(_data);
        setIndustryPlateTableData(_data?.industryPlateTableData || []);
        setShortLineTableData(_data?.shortLineTableData || []);
        setPlayIntroductionTableData(_data?.playIntroductionTableData || []);
        setQuestionTableData(_data?.questionTableData || []);
      } catch (error) {
        console.log(error);
      }
    });
  };
  const handleIndustryPlateTableData = data => {
    setIndustryPlateTableData(data);
  };
  const handleShortLineTableData = data => {
    setShortLineTableData(data);
  };
  const handlePlayIntroductionTableData = data => {
    setPlayIntroductionTableData(data);
  };
  const handleQuestionTableData = data => {
    setQuestionTableData(data);
  };

  /**
   * @description: 发布配置保存
   */

  const publishHandle = () => {
    console.log(industryPlateTableData, 'industryPlateTableData');
    console.log(shortLineTableData, 'shortLineTableData');
    console.log(playIntroductionTableData, 'playIntroductionTableData');
    console.log(questionTableData, 'questionTableData');

    const formData = {
      industryPlateTableData,
      shortLineTableData,
      playIntroductionTableData,
      questionTableData,
      needUpdate: 'yes'
    };
    console.log('保存的表单',formData)

    api
      .postTrendHunterConfig({
        value: JSON.stringify(formData),
      })
      .then(res => {
        if (res?.code !== '0000') {
          message.error('发布失败');
          console.log(res);
        } else {
          message.success('发布成功！');
        }
      });
  };
  return (
    <>
      <Button type="primary" onClick={publishHandle}>
        发布
      </Button>
      <section>
        <Title title="行业与板块配置" />
        <IndustryPlateTable tableData={industryPlateTableData} handleData={handleIndustryPlateTableData} />
      </section>
      <section>
        <Title title="短线研习群" />
        <ShortLineTable tableData={shortLineTableData} handleData={handleShortLineTableData} />
      </section>
      <section>
        <Title title="玩法攻略" />
        <PlayIntroductionTable tableData={playIntroductionTableData} handleData={handlePlayIntroductionTableData} />
      </section>
      <section>
        <Title title="常见问答" />
        <QuestionTable tableData={questionTableData} handleData={handleQuestionTableData} />
      </section>
    </>
  );
};

export default TrendHunter;
