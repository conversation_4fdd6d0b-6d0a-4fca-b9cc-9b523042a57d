import React, { useState, useEffect } from 'react';
import { Table, Button, Popconfirm, message } from 'antd';
import ModalContent from './modal';
import record from '../../redMoney/record';
let _index = 0;

const CustomTable = ({ tableData, handleData }) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [edit, setEdit] = useState<number>(0); //0 新增 1编辑
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [currentData, setCurrentData] = useState<any>({}); // 单条数据

  useEffect(() => {
    setDataSource(tableData || []);
  }, [tableData]);

  const handleDelete = (record, index: number) => {
    setDataSource(pre => {
      const _data = JSON.parse(JSON.stringify(pre));
      _data.splice(index, 1);
      handleData([..._data]);
      return [..._data];
    });
  };
  const handleEdit = (record, index: number) => {
    _index = index;
    setCurrentData(record);
    setEdit(1);
    setShowDialog(true);
  };
  /** 操作 */
  const renderOperate = (text: string, record: any, index: number) => {
    return (
      <div className="u-l-middle">
        <Button
          type="link"
          onClick={() => {
            handleEdit(record, index);
          }}
        >
          编辑
        </Button>
        <Popconfirm
          title="是否确认删除？"
          okText="确认"
          cancelText="取消"
          onConfirm={() => {
            handleDelete(record, index);
          }}
        >
          <Button type="link" className="g-ml10">
            <span style={{ color: '#ff0000' }}>删除</span>
          </Button>
        </Popconfirm>
      </div>
    );
  };
  const renderPic = (text, record) => {
    return <img src={record?.imgUrl} alt="" style={{ width: '60px', height: '60px' }} />;
  };
  const renderType = (text, record) => {
    const TYPEMAP = {
      'community': '社群',
      'live': '直播',
      'product': '产品',
    }
    return TYPEMAP[record?.type] || ''; 
  }
  const columns = [
    { title: '名称', dataIndex: 'name', key: 'name',width: '6%' },
    { title: 'ifind代码', dataIndex: 'ifindCode', key: 'ifindCode',width: '7%' },
    { title: '同花顺代码', dataIndex: 'thsCode', key: 'thsCode',width: '7%' },
    { title: '类型', dataIndex: 'type', key: 'type', render: renderType,width: '4%' },
    { title: '头像', dataIndex: 'imgUrl', key: 'imgUrl', render: renderPic,width: '5%' },
    { title: '牛人观点', dataIndex: 'mogulViewpoint', key: 'mogulViewpoint',width: '15%' },
    { title: '标签1', dataIndex: 'firstLabel', key: 'firstLabel',width: '9%' },
    { title: '标签2', dataIndex: 'secondLabel', key: 'secondLabel',width: '9%' },
    { title: '跳转链接', dataIndex: 'jumpUrl', key: 'jumpUrl',width: '20%'},
    { title: '开始时间', dataIndex: 'startTime', key: 'startTime',width: '8%' },
    { title: '结束时间', dataIndex: 'endTime', key: 'endTime',width: '8%' },
    { title: '操作', key: '_operate', render: renderOperate,width: '10%'},
  ];

  const addColumn = () => {
    console.log(tableData.length)
    const tableDataMaxLength = 50; // 最多配置条数
    if (tableData.length >= tableDataMaxLength) {
      message.error(`行业与板块最多配置${tableDataMaxLength}条`);
      return;
    }
    setCurrentData([]);
    setEdit(0);
    setShowDialog(true);
  };

  const onEditClose = () => {
    setShowDialog(false);
  };

  const handleSingleData = (singleData: any) => {
    console.log(singleData);
    if (edit === 0) {
      setDataSource(pre => {
        const _data = pre;
        _data.push(singleData);
        handleData([..._data]);
        return [..._data];
      });
    } else if (edit === 1) {
      setDataSource(pre => {
        const _data = pre;
        _data[_index] = singleData;
        handleData([..._data]);
        return [..._data];
      });
    }
  };

  return (
    <>
      <Table
        rowKey="index"
        bordered
        style={{ width: '100%' }}
        // rowSelection={rowSelection}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
      />
      <Button type="dashed" onClick={addColumn} style={{ width: '100%' }}>
        <span style={{ color: '#1890ff' }}>新增</span>
      </Button>
      {showDialog && (
        <ModalContent
          currentData={currentData}
          showDialog={showDialog}
          onEditClose={onEditClose}
          handleSingleData={handleSingleData}
          edit={edit}
        ></ModalContent>
      )}
    </>
  );
};

export default CustomTable;
