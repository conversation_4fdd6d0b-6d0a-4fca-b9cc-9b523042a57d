import React, { useEffect, useState } from 'react';
import { Form, Input, Radio, Row, Col, message, Button, Spin, Modal, DatePicker, ConfigProvider } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import UploadImg from '../components/UploadImg';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import moment from 'moment';

interface ModalContentProps extends FormComponentProps {
  showDialog: boolean;
  onEditClose: () => void;
  currentData: any;
  edit: number;
  handleSingleData: (val: any) => void;
  children?: React.ReactNode;
}

const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};
const marginStyle = { marginBottom: '10px' };

const ModalContent: React.FC<ModalContentProps> = ({
  showDialog,
  onEditClose,
  edit,
  currentData,
  form,
  handleSingleData,
}) => {
  const { getFieldDecorator } = form;
  const { RangePicker } = DatePicker;
  const [loading, setLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState<any>({});
  const [picUrl, setPicUrl] = useState('');
  const [time, setTime] = useState<any>([undefined,undefined]);

  useEffect(() => {
    if (edit === 0) {
      form.resetFields();
    }
  }, []);

  useEffect(() => {
    setFormData({ ...currentData });
    if (currentData?.imgUrl) {
      setPicUrl(currentData.imgUrl);
    }
    if (currentData?.startTime && currentData?.endTime) {
      setTime([moment(currentData?.startTime, 'YYYY-MM-DD HH:mm:ss'),moment(currentData?.endTime, 'YYYY-MM-DD HH:mm:ss')]);
    }
  }, [currentData]);

  const getCharLength = (str: string) => {
    let charLength = 0;
    for ( let i = 0; i < str.length; i++) {
        if (str.charCodeAt(i) > 255) {
          charLength += 2;
        } else {
          charLength += 1;
        }
    }
    return charLength;
  };

  const onSubmit = () => {
    form.validateFields((err: any, values: any) => {
      if (getCharLength(values.name) > 16) {
        message.error('行业名称最多8个汉字或16个字符!');
        return;
      }
      if (!picUrl) {
        message.error('请上传图片!');
        return;
      }
      if (values.type !== 'product' && (!values.firstLabel || !values.secondLabel)) {
        message.error('非产品类型标签1和标签2必填!');
        return;
      }
      if (values.type !== 'product' && (!time[0] || !time[1])) {
        message.error('非产品类型请选择开始/结束时间!');
        return;
      }
      if (!err) {
        handleSave(values);
      } else {
        message.error('请检查必填项');
      }
    });
  };

  /**
   * @description: 新增或编辑保存调用ajax方法
   */

  const handleSave = (values: any) => {
    handleSingleData({ 
      ...values, 
        imgUrl: picUrl, 
        startTime: !time[0] ? null : time[0].format('YYYY-MM-DD HH:mm:ss'),
        endTime: !time[1] ? null : time[1].format('YYYY-MM-DD HH:mm:ss'),
      });
    onEditClose();
  };

  return (
    <Modal
      visible={showDialog}
      maskClosable={false}
      title={edit === 0 ? '添加' : '编辑'}
      closable={false}
      width={700}
      onCancel={onEditClose}
      footer={
        <Spin spinning={loading}>
          <Button key="back" onClick={onEditClose}>
            取消
          </Button>
          <Button htmlType="submit" key="submit" type="primary" onClick={onSubmit}>
            保存
          </Button>
        </Spin>
      }
    >
      <Spin spinning={loading}>
        <Form {...formItemLayout} onSubmit={onSubmit} labelAlign="left">
          <Form.Item label="名称" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('name', {
              initialValue: formData.name,
              rules: [{ required: true, message: '请输入名称' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="ifind代码" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('ifindCode', {
              initialValue: formData.ifindCode,
              rules: [{ required: true, message: '请输入ifind代码' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="同花顺代码" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('thsCode', {
              initialValue: formData.thsCode,
              rules: [{ required: true, message: '请输入同花顺代码' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="类型">
            {getFieldDecorator('type', {
              initialValue: formData.type,
              rules: [{ required: true, message: '请选择类型' }],
            })(
              <Radio.Group>
                <Radio style={marginStyle} value={'community'}>
                  社群
                </Radio>
                <Radio style={marginStyle} value={'live'}>
                  直播
                </Radio>
                <Radio style={marginStyle} value={'product'}>
                  产品
                </Radio>
              </Radio.Group>,
            )}
          </Form.Item>
          <UploadImg
            value={picUrl}
            // size={['176*176']}
            title="头像"
            onChange={setPicUrl}
            disabled={false}
          />
          <Form.Item label="牛人观点" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('mogulViewpoint', {
              initialValue: formData.mogulViewpoint,
              rules: [{ required: true, message: '请输入牛人观点' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="标签1" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('firstLabel', {
              initialValue: formData.firstLabel,
              rules: [{ required: false, message: '请输入标签1' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="标签2" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('secondLabel', {
              initialValue: formData.secondLabel,
              rules: [{ required: false, message: '请输入标签2' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="跳转链接" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('jumpUrl', {
              initialValue: formData.jumpUrl,
              rules: [{ required: true, message: '请输入跳转链接' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="开始/结束时间" wrapperCol={{ span: 12 }}>
          <ConfigProvider locale={zh_CN}>
              <RangePicker
                showToday={false}
                style={{ width: '100%' }}
                showTime
                value={[time[0], time[1]]}
                onChange={e => {
                  setTime(e);
                }}
              />
            </ConfigProvider>
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<ModalContentProps>()(ModalContent);
