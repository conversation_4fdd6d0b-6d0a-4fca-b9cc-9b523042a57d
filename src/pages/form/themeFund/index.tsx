import * as React from 'react';
import classNames from 'classnames';
import { Button, message, Popconfirm } from 'antd';
import api from 'api';
import FormRender from 'form-render/lib/antd';
import FROM_JSON from './form.json';

const { fetchThemeFund, postThemeFund } = api;

const { useState, useEffect } = React;

interface iProps {
  className?: string
}

function fetchThemeFundConfig() {
  return new Promise((resolve, reject) => {
    fetchThemeFund().then((data: any) => {
      if (data.code === '0000') {
        resolve(data.data && JSON.parse(data.data));
      } else {
        message.info(data.message);
        reject();
      }
    }).catch((e: any) => {
      message.info(e.message);
    });
  });
}

function postThemeFundConfig(value: any) {
  const dataToSend = { value: JSON.stringify(value) };
  return new Promise((resolve, reject) => {
    postThemeFund(dataToSend).then((data: any) => {
      if (data.code === '0000') {
        resolve(data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch((e: any) => {
      message.info(e.message);
    });
  });
}

export default function({ className }: iProps) {
  const [formData, setFormData] = useState({});
  const [valid, setValid] = useState([]);
  useEffect(() => {
    fetchThemeFundConfig().then((data: any) => {
      if (data) {
        setFormData(data);
      }
    });
  }, []);
  const onSubmit = () => {
    // valid 是校验判断的数组，valid 长度为 0 代表校验全部通过
    if (valid.length > 0) {
      message.info(`校验未通过字段：${valid.toString()}`);
    } else {
      postThemeFundConfig(formData).then(() => {
        message.info('编辑成功');
      });
    }
  };
  return (
    <section className={classNames(className)}>
      <FormRender
        propsSchema={FROM_JSON}
        formData={formData}
        onChange={setFormData}
        onValidate={setValid}
      />
      <Popconfirm
        title="确认呢要提交么？如果是修改会覆盖线上配置！"
        onConfirm={onSubmit}
        okText="确定"
        cancelText="取消"
      >
        < Button type={'primary'}>提交</Button>
      </Popconfirm>
    </section>
  );
}
