import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Modal, Switch, Table } from 'antd';
import api from 'api';
import { getPreferenceTableColumns } from './utils';
import { SUCCESS_CODE } from './constants';
import { IPreferenceTableItem, IResponse } from './types';
import { FormComponentProps } from 'antd/lib/form';

import styles from './index.less';
import { nanoid } from 'nanoid';

const { fetchPrefenenceList, postPrefenenceList } = api;

const UserPreference = (props: FormComponentProps) => {
  const { getFieldDecorator, validateFields, setFieldsValue, resetFields } = props.form;
  const [dataSource, setDataSource] = useState<IPreferenceTableItem[]>([]);
  const [showModal, setShowModal] = useState(false);
  // 正在编辑的key
  const [editingKey, setEditingKey] = useState('');

  useEffect(() => {
    fetchPrefenenceList()
      .then((res: IResponse) => {
        if (res.code === SUCCESS_CODE) {
          setDataSource(JSON.parse(res.data || '[]'));
        } else {
          message.error(res.message);
        }
      })
      .catch(() => {
        message.error('网络错误，请稍后再试');
      });
  }, []);
  const handleAdd = () => {
    setEditingKey(nanoid(12));
    setShowModal(true);
    resetFields();
  };
  const handleEdit = (item: IPreferenceTableItem, index: number) => {
    setEditingKey(item.key);
    setShowModal(true);
    setFieldsValue(item);
  };
  const handleDelete = (key: string) => {
    const reqData = dataSource.filter(item => item.key !== key);
    postPrefenenceList({ value: JSON.stringify(reqData) })
      .then((res: IResponse) => {
        if (res.code === SUCCESS_CODE) {
          setDataSource(reqData);
          message.success('删除成功');
        } else {
          message.error(res.message);
        }
      })
      .catch(() => {
        message.error('网络错误，请稍后再试');
      });
  };
  const handleSubmit = () => {
    validateFields((err, values: IPreferenceTableItem) => {
      if (!err) {
        let reqData: IPreferenceTableItem[];
        const editingIndex = dataSource.findIndex(item => item.key === editingKey);
        if (editingIndex === -1) {
          if (dataSource.find(data => data.customKey === values.customKey)) {
            message.error('key值不能重复');
            return;
          }
          reqData = [...dataSource, { ...values, key: editingKey }];
        } else {
          reqData = dataSource.map((item, index) => {
            if (index === editingIndex) {
              return { ...item, ...values };
            }
            return item;
          });
        }
        postPrefenenceList({ value: JSON.stringify(reqData) })
          .then((res: IResponse) => {
            if (res.code === SUCCESS_CODE) {
              setShowModal(false);
              setDataSource(reqData);
              message.success('修改成功');
            } else {
              message.error(res.message);
            }
          })
          .catch(() => {
            message.error('网络错误，请稍后再试');
          });
      } else {
        message.error('请输入完整数据');
      }
    });
  };
  return (
    <div className={styles['preference-table']}>
      <div className={styles['title']}>用户偏好设置配置</div>
      <Button type="primary" className={styles['add-btn']} onClick={handleAdd}>
        新增用户偏好
      </Button>
      <Table
        columns={getPreferenceTableColumns(handleEdit, handleDelete)}
        dataSource={dataSource}
      />
      <Modal visible={showModal} onCancel={() => setShowModal(false)} footer={null}>
        <Form onSubmit={handleSubmit}>
          <Form.Item label="自定义key值">
            {getFieldDecorator('customKey', {
              rules: [{ required: true, message: '请输入自定义key值' }],
            })(<Input disabled={!!dataSource.find(item => item.key === editingKey)} />)}
          </Form.Item>
          <Form.Item>
            <span>是否启用</span>
            &nbsp;&nbsp;&nbsp;
            {getFieldDecorator('isEnable', {
              initialValue: true,
              valuePropName: 'checked',
            })(<Switch />)}
          </Form.Item>
          <Form.Item label="偏好项（不超过6个字符）">
            {getFieldDecorator('preference', {
              rules: [{ required: true, message: '请输入偏好项' }],
            })(<Input maxLength={6} />)}
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Form.create({ name: 'UserPreference' })(UserPreference);
