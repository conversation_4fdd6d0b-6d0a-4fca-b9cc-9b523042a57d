export interface IResponse {
  code: string;
  data: string | null;
  message: string;
}

export interface IQuestionsTableItem {
  key: string;
  /** 是否启用 */
  isEnable: boolean;
  /** 问句 */
  questions: string;
  /** 筛选条件 */
  filterList: {
    filterField: string;
    filterSymbol: 'GREATER' | 'LESS' | 'BETWEEN' | 'IN';
    filterValue: string;
  }[];
  /** 优先级是否最高 */
  isLevelHighest: '0' | '1';
  /** 额外指定基金 */
  additionalFundStr: string
}

export interface IQuestionsFormData {
  isEnable: boolean;
  questions: string;
  [key: `filterField${number}`]: string;
  [key: `filterSymbol${number}`]: string;
  [key: `filterValue${number}`]: string;
  fundTypeValue: string[];
  yearsSymbol: 'GREATER' | 'BETWEEN';
  yearsStartValue: string;
  yearsEndValue: string;
  isLevelHighest: '0' | '1';
  additionalFundStr: string
}

export interface IPreferenceTableItem {
  key: string;
  customKey: string;
  isEnable: boolean;
  preference: string;
}
