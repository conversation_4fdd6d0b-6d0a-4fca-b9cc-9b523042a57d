import React from 'react';
import { IPreferenceTableItem, IQuestionsFormData, IQuestionsTableItem } from './types';
import { Icon, Popconfirm } from 'antd';
import { FILTER_MAP, FUNDTYPE_MAP } from './constants';

// 将表格数据转化为表单数据，打开表单的时候用
export const transformTableDataToFormData = (tableItem: IQuestionsTableItem, index: number) => {
  if (index !== -1) {
    const temp = {} as any;
    for (let i = 0; i < tableItem.filterList.length; i++) {
      temp[`filterField${i}`] = tableItem.filterList[i].filterField;
      temp[`filterSymbol${i}`] = tableItem.filterList[i].filterSymbol;
      temp[`filterValue${i}`] = tableItem.filterList[i].filterValue;
    }
    // 波段值需要改变数据格式
    for (let key in temp) {
      if (temp[key] === 'rsi') {
        temp[key.replace('Field', 'Value')] = (
          Number(temp[key.replace('Field', 'Value')]) * 100
        ).toString();
      }
      if (temp[key] === 'fundScale') {
        temp[key.replace('Field', 'Value')] = (
          Number(temp[key.replace('Field', 'Value')]) / 1e8
        ).toString();
      }
    }

    const fundTypeFilterArr = tableItem.filterList.filter(item => item.filterField === 'l2code');
    const yearsFilterArr = tableItem.filterList.filter(item => item.filterField === 'nowDayAmount');
    if (fundTypeFilterArr.length === 0 && yearsFilterArr.length === 0) {
      return {
        isEnable: tableItem.isEnable,
        questions: tableItem.questions,
        ...temp,
        isLevelHighest: tableItem.isLevelHighest,
        additionalFundStr: tableItem.additionalFundStr,
      } as IQuestionsFormData;
    }
    if (fundTypeFilterArr.length) {
      temp['fundTypeValue'] = fundTypeFilterArr[0].filterValue.split(',');
    }
    if (yearsFilterArr.length) {
      temp['yearsSymbol'] = yearsFilterArr[0].filterSymbol;
      if (yearsFilterArr[0].filterSymbol === 'BETWEEN') {
        temp['yearsStartValue'] = Number(yearsFilterArr[0].filterValue.split(',')[0]) / 365;
        temp['yearsEndValue'] = Number(yearsFilterArr[0].filterValue.split(',')[1]) / 365;
      } else if (yearsFilterArr[0].filterSymbol === 'GREATER') {
        temp['yearsStartValue'] = Number(yearsFilterArr[0].filterValue) / 365;
      }
    }
    return {
      isEnable: tableItem.isEnable,
      questions: tableItem.questions,
      ...temp,
      isLevelHighest: tableItem.isLevelHighest,
      additionalFundStr: tableItem.additionalFundStr,
    } as IQuestionsFormData;
  } else {
    return {};
  }
};

// 将表单数据转化为请求数据，提交表单的时候用
const transformFormDataToReqItem = (formData: IQuestionsFormData, key: string) => {
  // 以filter开头的字段为普通筛选条件相关字段，如filterField1、filterField3
  const filterKeys = Object.keys(formData).filter(item => item.match('filter'));
  const maxFieldIndex = Math.max(...filterKeys.map(item => Number(item.match(/\d+/g)[0])));
  const filterList = [];
  for (let i = 0; i < maxFieldIndex + 1; i++) {
    if (formData[`filterField${i}`]) {
      filterList.push({
        filterField: formData[`filterField${i}`],
        filterSymbol: formData[`filterSymbol${i}`],
        filterValue: formData[`filterValue${i}`],
      });
    }
  }
  filterList.forEach(item => {
    // 波段值的数据为小数
    if (item.filterField === 'rsi') {
      item.filterValue = (Number(item.filterValue) / 100).toString();
    }
    // 规模的单位为1
    if (item.filterField === 'fundScale') {
      item.filterValue = (Number(item.filterValue) * 1e8).toString();
    }
  });
  // 新增基金类型筛选条件
  if (formData.fundTypeValue) {
    filterList.push({
      filterField: 'l2code',
      filterSymbol: 'IN',
      filterValue: formData.fundTypeValue.toString(),
    });
  }
  // 新增成立年限筛选条件
  if (formData.yearsSymbol === 'BETWEEN') {
    filterList.push({
      filterField: 'nowDayAmount',
      filterSymbol: 'BETWEEN',
      filterValue: `${Number(formData.yearsStartValue) * 365},${Number(formData.yearsEndValue) *
        365}`,
    });
  } else if (formData.yearsSymbol === 'GREATER') {
    filterList.push({
      filterField: 'nowDayAmount',
      filterSymbol: 'GREATER',
      filterValue: (Number(formData.yearsStartValue) * 365).toString(),
    });
  }

  const reqItem = {
    key,
    questions: formData.questions,
    isEnable: formData.isEnable,
    filterList,
    isLevelHighest: formData.isLevelHighest,
    additionalFundStr: formData.additionalFundStr,
  } as IQuestionsTableItem;
  return reqItem;
};

export const transformFormDataToReqData = (
  formData: IQuestionsFormData,
  tableData: IQuestionsTableItem[],
  key: string,
) => {
  const targetIndex = tableData.findIndex(item => item.key === key);
  let questionsList;
  // 是编辑还是新增
  if (targetIndex !== -1) {
    questionsList = tableData.map((tableItem, index) => {
      if (index === targetIndex) {
        return transformFormDataToReqItem(formData, key);
      }
      return tableItem;
    });
  } else {
    questionsList = [...tableData, transformFormDataToReqItem(formData, key)];
  }
  return questionsList;
};

export const getQuestionsTableColumns = (
  handleEdit: (dataItem: IQuestionsTableItem, index: number) => void,
  handleDelete: (key: string) => void,
) => [
  {
    title: '是否启用',
    dataIndex: 'isEnable',
    key: 'isEnable',
    render: (isEnable: boolean) => {
      return (
        <Icon
          type={isEnable ? 'check-circle' : 'close-circle'}
          style={{ fontSize: '20px', color: isEnable ? '#52c41a' : 'red' }}
        />
      );
    },
  },
  {
    title: '问句',
    dataIndex: 'questions',
    key: 'questions',
  },
  {
    title: '推送筛选条件',
    key: 'filterList',
    render: (dataItem: IQuestionsTableItem) => {
      if (dataItem.filterList.length) {
        return dataItem.filterList.map((item, index) => {
          // 基金类型、成立年限、普通筛选类型
          if (item.filterField === 'l2code') {
            return (
              <div key={index}>
                基金类型为：
                {item.filterValue
                  .split(',')
                  .map(valueItem => FUNDTYPE_MAP.get(valueItem))
                  .join('、')}
              </div>
            );
          }
          if (item.filterField === 'nowDayAmount') {
            if (item.filterSymbol === 'GREATER') {
              return <div key={index}>成立年限为：大于{Number(item.filterValue) / 365}年</div>;
            }
            if (item.filterSymbol === 'BETWEEN') {
              return (
                <div key={index}>
                  成立年限区间为：{Number(item.filterValue.split(',')[0]) / 365} —{' '}
                  {Number(item.filterValue.split(',')[1]) / 365} 年
                </div>
              );
            }
          }
          if (item.filterField === 'rsi') {
            return (
              <div key={index}>
                波段值{item.filterSymbol === 'GREATER' ? '大于' : '小于'}
                {Number(item.filterValue) * 100}
              </div>
            );
          }
          if (item.filterField === 'fundScale') {
            return (
              <div key={index}>
                规模{item.filterSymbol === 'GREATER' ? '大于' : '小于'}
                {Number(item.filterValue) / 1e8}亿
              </div>
            );
          }
          return (
            <div key={index}>
              {FILTER_MAP.get(item.filterField) +
                (item.filterSymbol === 'GREATER' ? '大于' : '小于') +
                item.filterValue +
                '%'}
            </div>
          );
        });
      }
      return '无额外条件';
    },
  },
  {
    title: '额外指定基金',
    dataIndex: 'additionalFundStr',
    key: 'additionalFundStr',
  },
  {
    title: '优先级是否最高',
    key: 'isLevelHighest',
    dataIndex: 'isLevelHighest',
    render: (text: '0' | '1') => (text === '0' ? '否' : text === '1' ? '是' : '- -'),
  },
  {
    title: '操作',
    key: 'action',
    render: (dataItem: IQuestionsTableItem, _: IQuestionsTableItem, index: number) => {
      return (
        <>
          <a onClick={() => handleEdit(dataItem, index)}>编辑</a>
          &nbsp;&nbsp;
          <Popconfirm
            title="确定要删除这条问句吗？"
            okText="是"
            cancelText="否"
            onConfirm={() => handleDelete(dataItem.key)}
          >
            <a>删除</a>
          </Popconfirm>
        </>
      );
    },
  },
];

export const getPreferenceTableColumns = (
  handleEdit: (item: IPreferenceTableItem, index: number) => void,
  handleDelete: (key: string) => void,
) => [
  {
    key: 'customKey',
    dataIndex: 'customKey',
    title: '自定义key值（不可修改）',
  },
  {
    key: 'isEnable',
    dataIndex: 'isEnable',
    title: '是否启用',
    render: (isEnable: boolean) => {
      return (
        <Icon
          type={isEnable ? 'check-circle' : 'close-circle'}
          style={{ fontSize: '20px', color: isEnable ? '#52c41a' : 'red' }}
        />
      );
    },
  },
  {
    key: 'preference',
    dataIndex: 'preference',
    title: '偏好项（不超过6个字符）',
  },
  {
    title: '操作',
    key: 'action',
    render: (dataItem: IPreferenceTableItem, _: IPreferenceTableItem, index: number) => {
      return (
        <>
          <a onClick={() => handleEdit(dataItem, index)}>编辑</a>
          &nbsp;&nbsp;
          <Popconfirm
            title="确定要删除这项偏好吗？"
            okText="是"
            cancelText="否"
            onConfirm={() => handleDelete(dataItem.key)}
          >
            <a>删除</a>
          </Popconfirm>
        </>
      );
    },
  },
];
