import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Modal, Radio, Select, Switch, Table } from 'antd';
import { nanoid } from 'nanoid';
import api from 'api';
import { FILTER_MAP, FUNDTYPE_MAP, SUCCESS_CODE } from './constants';
import {
  transformFormDataToReqData,
  transformTableDataToFormData,
  getQuestionsTableColumns,
} from './utils';
import { IQuestionsFormData, IQuestionsTableItem, IResponse } from './types';
import { FormComponentProps } from 'antd/lib/form';

import styles from './index.less';

const { Option } = Select;
const { fetchQuestionsList, postQuestionsList } = api;

let filterListId = 0;

const QuestionSentence = (props: FormComponentProps) => {
  const {
    getFieldDecorator,
    setFieldsValue,
    resetFields,
    getFieldValue,
    validateFields,
  } = props.form;
  const [dataSource, setDataSource] = useState<IQuestionsTableItem[]>([]);
  const [showModal, setShowModal] = useState(false);
  // 新增基金类型和成立年限
  const [hasFundType, setHasFundType] = useState(false);
  const [hasYears, setHasYears] = useState(false);
  const [showSecondInput, setShowSecondInput] = useState(true);
  // 当前正在编辑的key
  const [editingKey, setEditingKey] = useState('');
  const [keys, setKeys] = useState<number[]>([]);

  useEffect(() => {
    fetchQuestionsList()
      .then((res: IResponse) => {
        if (res.code === SUCCESS_CODE) {
          setDataSource(JSON.parse(res.data || '[]'));
        } else {
          message.error(res.message);
        }
      })
      .catch(() => {
        message.error('网络错误，请重试');
      });
  }, []);

  const checkInputSize = (_, value: string, callback) => {
    if (
      value &&
      Number(getFieldValue('yearsStartValue')) >= Number(getFieldValue('yearsEndValue'))
    ) {
      callback('开始年限只能小于结束年限');
    } else {
      callback();
    }
  };

  // 过滤条件可能有多个，需要灵活控制长度
  const filterList = keys.map((item, index) => (
    <Form.Item label={`普通筛选条件${index + 1}`} key={index} required>
      <Form.Item style={{ margin: '0' }}>
        {getFieldDecorator('filterField' + item, {
          rules: [{ required: true, message: '请输入普通筛选条件' }],
        })(
          <Select placeholder="数据项">
            {Array.from(FILTER_MAP.entries()).map(([key, value]) => {
              return (
                <Option value={key} key={key}>
                  {value}
                </Option>
              );
            })}
          </Select>,
        )}
      </Form.Item>
      <Form.Item style={{ margin: '0' }}>
        {getFieldDecorator('filterSymbol' + item, {
          rules: [{ required: true, message: '请输入普通筛选条件' }],
        })(
          <Select placeholder="比较项">
            <Option value="GREATER">大于</Option>
            <Option value="LESS">小于</Option>
          </Select>,
        )}
      </Form.Item>
      <Form.Item style={{ margin: '0' }}>
        <span>
          {getFieldDecorator('filterValue' + item, {
            rules: [{ required: true, message: '请输入普通筛选条件' }],
          })(
            <Input
              type="number"
              className={styles['short-input']}
              onWheel={event => (event.target as HTMLInputElement).blur()}
            />,
          )}{' '}
          %（除波段值的单位为数值、规模的单位为亿外，其余均为百分比）
        </span>
      </Form.Item>
      <Button type="danger" onClick={() => handleRemoveNormalCondition(item)}>
        删除
      </Button>
    </Form.Item>
  ));
  // 是否有基金类型和成立年限选项
  if (hasFundType) {
    filterList.push(
      <Form.Item label="基金类型筛选条件" key={'fundType'} required>
        <Form.Item style={{ margin: '0' }}>
          {getFieldDecorator('fundTypeValue', {
            rules: [{ required: true, message: '请选择基金类型' }],
          })(
            <Select
              mode="multiple"
              style={{ width: '100%' }}
              placeholder="请选择基金类型（可多选）"
            >
              {Array.from(FUNDTYPE_MAP.entries()).map(([key, value]) => {
                return (
                  <Option value={key} key={key}>
                    {value}
                  </Option>
                );
              })}
            </Select>,
          )}
        </Form.Item>
        <Button type="danger" onClick={() => setHasFundType(false)}>
          删除
        </Button>
      </Form.Item>,
    );
  }
  if (hasYears) {
    filterList.push(
      <Form.Item label="成立年限筛选条件" key="years" required>
        <Form.Item style={{ margin: '0' }}>
          {getFieldDecorator('yearsSymbol', {
            rules: [{ required: true, message: '请输入年限' }],
          })(
            <Select
              placeholder="比较项"
              onChange={value => setShowSecondInput(value === 'BETWEEN')}
            >
              <Option value="GREATER">大于</Option>
              <Option value="BETWEEN">区间</Option>
            </Select>,
          )}
        </Form.Item>
        <Form.Item style={{ margin: '0', display: 'inline-block' }}>
          <span>
            {getFieldDecorator('yearsStartValue', {
              rules: [
                { required: true, message: '请输入年限' },
                { pattern: /^[0-9]+$/, message: '年限必须为非负整数' },
              ],
            })(
              <Input
                type="number"
                className={styles['short-input']}
                onWheel={event => (event.target as HTMLInputElement).blur()}
              />,
            )}
            年&nbsp;
          </span>
        </Form.Item>
        <Form.Item style={{ margin: '0', display: 'inline-block' }}>
          {showSecondInput ? (
            <span>
              —&nbsp;
              {getFieldDecorator('yearsEndValue', {
                rules: [
                  { required: true, message: '请输入年限' },
                  { pattern: /^[0-9]+$/, message: '年限必须为非负整数' },
                  { validator: checkInputSize },
                ],
              })(
                <Input
                  type="number"
                  className={styles['short-input']}
                  onWheel={event => (event.target as HTMLInputElement).blur()}
                />,
              )}
              年
            </span>
          ) : null}
        </Form.Item>
        <div>
          <Button type="danger" onClick={() => setHasYears(false)}>
            删除
          </Button>
        </div>
      </Form.Item>,
    );
  }
  const handleAdd = () => {
    filterListId = 0;
    setKeys([]);
    setEditingKey('');
    setEditingKey(nanoid(12));
    setHasFundType(false);
    setHasYears(false);
    resetFields();
    setShowModal(true);
  };
  const handleEdit = (dataItem: IQuestionsTableItem, index: number) => {
    filterListId = 0;
    const commonFilterArr = dataItem.filterList.filter(
      item => item.filterField !== 'l2code' && item.filterField !== 'nowDayAmount',
    );
    const fundTypeFilterArr = dataItem.filterList.filter(item => item.filterField === 'l2code');
    const yearsFilterArr = dataItem.filterList.filter(item => item.filterField === 'nowDayAmount');
    setHasFundType(fundTypeFilterArr.length !== 0);
    setHasYears(yearsFilterArr.length !== 0);
    if (yearsFilterArr[0]) {
      if (yearsFilterArr[0].filterSymbol === 'GREATER') {
        setShowSecondInput(false);
      } else if (yearsFilterArr[0].filterSymbol === 'BETWEEN') {
        setShowSecondInput(true);
      }
    }
    setEditingKey(dataItem.key);
    setShowModal(true);
    setKeys(
      commonFilterArr.map((_item, index) => {
        filterListId++;
        return index;
      }),
    );
    // 为了让表单渲染完之后再设置数据
    setTimeout(() => setFieldsValue(transformTableDataToFormData(dataItem, index)), 0);
  };
  const handleDelete = (key: string) => {
    const newDataSource = dataSource.filter(item => item.key !== key);
    postQuestionsList({ value: JSON.stringify(newDataSource) })
      .then((res: IResponse) => {
        if (res.code === SUCCESS_CODE) {
          message.success('删除成功');
          setDataSource(newDataSource);
        } else {
          message.error(res.message);
        }
      })
      .catch(() => {
        message.error('网络错误，请稍后再试');
      });
  };
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    validateFields((err, value: IQuestionsFormData) => {
      if (!err) {
        const reqData = transformFormDataToReqData(value, dataSource, editingKey);
        postQuestionsList({ value: JSON.stringify(reqData) }).then((res: IResponse) => {
          if (res.code === SUCCESS_CODE) {
            setShowModal(false);
            setDataSource(reqData);
            message.success('修改成功');
          } else {
            message.error('修改失败');
          }
        });
      } else {
        message.error('请输入完整数据');
      }
    });
  };
  const handleAddNormalCondition = () => {
    setKeys([...keys, filterListId++]);
  };
  const handleRemoveNormalCondition = (item: number) => {
    setKeys(keys.filter(key => key !== item));
  };
  return (
    <div className={styles['sentence-table']}>
      <div className={styles['title']}>基金详情页 主动式问句配置</div>
      <Button className={styles['add-btn']} type="primary" onClick={handleAdd}>
        新增问句
      </Button>
      <Table
        columns={getQuestionsTableColumns(handleEdit, handleDelete)}
        dataSource={dataSource}
        bordered
        pagination={false}
      ></Table>
      <Modal visible={showModal} onCancel={() => setShowModal(false)} footer={null}>
        <Form onSubmit={handleSubmit}>
          <Form.Item>
            <span>是否启用</span>
            &nbsp;&nbsp;&nbsp;
            {getFieldDecorator('isEnable', {
              initialValue: true,
              valuePropName: 'checked',
            })(<Switch />)}
          </Form.Item>
          <Form.Item label="问句">
            {getFieldDecorator('questions', {
              rules: [{ required: true, message: '请输入问句' }],
            })(<Input />)}
          </Form.Item>
          {filterList}
          {keys.length === 0 && !hasFundType && !hasYears ? (
            <div style={{ marginBottom: '20px', fontWeight: 'bold' }}>暂无筛选条件</div>
          ) : null}
          <div>
            <Button
              type="primary"
              onClick={handleAddNormalCondition}
              style={{ marginRight: '10px' }}
            >
              新增普通条件
            </Button>
            <Button
              type="primary"
              disabled={hasFundType}
              onClick={() => setHasFundType(true)}
              style={{ marginRight: '10px' }}
            >
              新增基金类型条件
            </Button>
            <Button type="primary" disabled={hasYears} onClick={() => setHasYears(true)}>
              新增成立年限条件
            </Button>
          </div>
          <Form.Item label="额外指定基金（多只用英文逗号分隔，不会校验基金代码，请确保正确性）">
            {getFieldDecorator('additionalFundStr', {
              rules: [
                {
                  pattern: /^[0-9a-zA-Z]{6}(,[0-9a-zA-Z]{6})*$/,
                  message: '输入的基金代码不符合规则',
                },
              ],
            })(<Input placeholder="请输入基金代码" />)}
          </Form.Item>
          <Form.Item>
            <span>优先级是否最高</span>
            &nbsp;&nbsp;&nbsp;
            {getFieldDecorator('isLevelHighest', {
              rules: [{ required: true, message: '请选择优先级' }],
            })(
              <Radio.Group>
                <Radio value={'1'}>是</Radio>
                <Radio value={'0'}>否</Radio>
              </Radio.Group>,
            )}
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Form.create({ name: 'QuestionSentence' })(QuestionSentence);
