import React, { useState, useEffect } from 'react';
import api from 'api';
import { router } from 'umi';
import { Button, Popconfirm, message, Table } from 'antd';

const { fetchAccountConfig } = api;

interface iQueryAllDetails {
    type: string
    index: number
    lastEditor: string
    lastEditorTime: string
    remark: string
    [key: string]: any
}
function FundAccountSystem() {
    const [init, setInit] = useState(false);
    const [dataSource, setDataSource] = useState<iQueryAllDetails[]>([
        { type: "logon", index: 0, name: '开户成功页', lastEditor: '', lastEditorTime: '', remark: '' },
        { type: "my", index: 1, name: '我的', lastEditor: '', lastEditorTime: '', remark: '' },
    ]);
    const columns = [
      {
        title: '楼层样式',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '楼层内容',
        dataIndex: 'remark',
        key: 'remark',
      },
      {
        title: '最后编辑人',
        dataIndex: 'lastEditor',
        key: 'lastEditor',
      },
      {
        title: '最后编辑时间',
        dataIndex: 'lastEditorTime',
        key: 'lastEditorTime',
      },
      {
        title: '操作',
        dataIndex: 'operation',
        render: (text: any, record: iQueryAllDetails) => {
          const { type } = record;
          return (
            <>
              <Button type="primary" onClick={() => {handleEdit(type)}}>编辑</Button>
            </>
          )
        }
      },
    ];

    useEffect(() => {
      fetchAccountConfig({
        type:'query'
      }).then((res: any)=>{
          console.log(res);
          const { code, data } = res;
          if (code === '0000') {
            let _dataSource = [...dataSource];
            data?.confs?.forEach((val: any)=> {
              
              const { type, index, lastEditor, lastEditorTime, remark } = val;
              let _target = _dataSource.find((item) => item.type === type)
              if (_target) {
                _target.lastEditor = lastEditor;
                _target.lastEditorTime = lastEditorTime;
                _target.remark = remark;
                _target.index = index;
              }
            })
            _dataSource.sort((a,b)=> a.index - b.index);
            setDataSource(_dataSource);
            setInit(true);
          } else {
            message.error(res?.message || '系统繁忙');
          }
          
      }).catch((e: Error) => {
        message.error(e?.message);
      })
    },[])

    const handleSubmit = (num: number) => { 
      if (num === 1) {
        let _data = [...dataSource]
        _data.sort((a, b) =>  (a.lastEditorTime || b.lastEditorTime) ? (new Date(b.lastEditorTime)?.getTime() - new Date(a.lastEditorTime)?.getTime()) : 1)
        console.log(_data)
        if(_data[0].lastEditor === localStorage.name){
            message.error('最后一次保存用户与发布用户不能为同一人')
            return
        }
      }
      
      fetchAccountConfig({
          type:'save',
          value:JSON.stringify({
              confs: dataSource 
          })
        }).then((res: any) => {
          if (res.code !== '0000') {
            message.error(res.message);
          } else {
            message.success('保存成功！');
          }
        }).then(()=>{
          fetchAccountConfig({
              type:'submit'
            }).then((res: any)=>{
                if (res.code === '0000') {
                  message.success('发布成功！');
                  setTimeout(()=>location.reload(),1000)
                } else {
                    message.error(res.message);
                }
            })
        }).catch((e: Error) => {
          message.error(e?.message);
        })
      
    } 
    const handleEdit = (type: string) => {
      history.push(`/form/fundAccountSystem/${type}`)
    }

    if (!init) {
        return '加载中'
    }
    return (
        <div>
            <Popconfirm
                title="确定保存并发布？"
                onConfirm={() => handleSubmit(1)}
                okText="确定"
                cancelText="取消"
            >
                <Button type="primary" style={{ marginBottom: 20, marginRight: 20 }} onClick={() => {}}>保存并发布</Button>
            </Popconfirm>
            {/* <Popconfirm
                title="确定保存并发布？"
                onConfirm={() => handleSubmit(2)}
                okText="确定"
                cancelText="取消"
            >
                <Button type="primary">保存并发布(测试环境使用，无人员校验)</Button>
            </Popconfirm> */}
            <Table
                columns={columns}
                dataSource={dataSource}
                rowKey={record => `${record.index}`}
            />
        </div>
    )
}

export default FundAccountSystem;