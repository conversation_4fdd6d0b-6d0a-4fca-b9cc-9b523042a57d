import FORM_JSON from './form.json';
import <PERSON>act<PERSON><PERSON> from 'react-dom';
import React, { useState, useEffect } from 'react';
import {<PERSON><PERSON>, Card, Row, message, Popconfirm, Collapse} from 'antd';
import { autobind } from 'core-decorators';
import api from 'api';
import Form<PERSON><PERSON> from 'form-render/lib/antd';

export default function () {
    const { fetchTemporaryStrategyBuyFundMain, postTemporaryStrategyBuyFundMain, fetchStrategyBuyFundMain, postStrategyBuyFundMain } = api;

    const [init, setInit] = useState(false);
    const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);
    
    useEffect( () => {
        fetchTemporaryStrategyBuyFundMain().then((res: any) => {
            let _data = FORM_JSON.formData
            try {
                res = JSON.parse(res.data);
                if (res) {
                    _data = res.formData
                }
            } catch (e) {
                console.warn(e)
            }
            setInit(true);
            setData(_data);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, [init]);

    const save = () => {
        console.log(formData)
        postTemporaryStrategyBuyFundMain ({
            value: JSON.stringify({formData})
        }).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！')
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    const onSubmit = () => {
        let _formData: any = formData;
        for (let i: number = 0; i < _formData.strategyBuyFund.length; i++) {
            for (let j: number = i; j < _formData.strategyBuyFund.length; j++) {
                if (_formData.strategyBuyFund[i].type === _formData.strategyBuyFund[j].type && i !== j) {
                    message.error(`索引重复 ${i+1}  ${j+1}，请修改新索引`);
                    return;
                }
            }
        }
        if (valid.length > 0) {
            console.log(formData)
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }
        for (let i: number = 0; i < _formData.strategyBuyFund.length; i++) {
            if (!_formData.strategyBuyFund[i].jumpUrl) {
                _formData.strategyBuyFund[i].jumpUrl = `https://${window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'test' : ''}fund.10jqka.com.cn/ifundapp_app/public/xyd/lowPosition/dist/index$1158F7.html?type=${_formData.strategyBuyFund[i].type}#/`
            }
        }
        postTemporaryStrategyBuyFundMain ({
            value: JSON.stringify({formData: _formData})
        }).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！');
                    postStrategyBuyFundMain ({
                        value: JSON.stringify({formData: _formData})
                    }).then( (res: any) => {
                        try {
                            if (res.code !== '0000') {
                                message.error(res.message);
                            } else {
                                message.success('发布成功！')
                            }
                        } catch (e) {
                            message.error(e.message);
                        }
                    })
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    return (
        <article> 
            {
                init ?
                <article>
                    <FormRender
                        propsSchema={FORM_JSON.propsSchema}
                        uiSchema={FORM_JSON.uiSchema}
                        onValidate={setValid}
                        formData={formData}
                        onChange={setData}
                        displayType="row"
                        showDescIcon={true}
                        column={2}
                    />
                    <Button
                        type="primary" 
                        onClick={save}
                        style={{marginRight: '300px'}}
                    >
                        保存
                    </Button>


                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要提交么'}
                        onConfirm={onSubmit}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button
                            type="danger" 
                        >
                            提交修改
                        </Button>
                    </Popconfirm>
                </article>
                :
                ''
            }
        </article>
    )
}