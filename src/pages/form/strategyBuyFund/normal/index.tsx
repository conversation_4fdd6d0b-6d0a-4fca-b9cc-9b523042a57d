import FORM_JSON from './form.json';
import React, { useState, useEffect } from 'react';
import {Select, Button, Radio, Input, Card, Row, message, Popconfirm, Collapse} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import FormRender from 'form-render/lib/antd';

export default function () {
    const { fetchTemporaryStrategyBuyFundMain, postTemporaryStrategyBuyFundMain, fetchTemporaryStrategyBuyFundNormal,
    postTemporaryStrategyBuyFundNormal, fetchStrategyBuyFundNormal,
    postStrategyBuyFundNormal} = api;

    const [init, setInit] = useState(false);
    const [mains, setMains] = useState([]);
    const [get, setGet] = useState(false);
    const [option, setOption] = useState({});
    const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);
    const [radio, setRadio] = useState('modify');
    const [name, setName] = useState('');//策略名字
    const [index, setIndex] = useState('');//索引

    useEffect( () => {
        fetchTemporaryStrategyBuyFundMain().then((res: any) => {
            try {
                res = JSON.parse(res.data);
                if (res) {
                    console.log(res)
                    setMains(res.formData.strategyBuyFund);
                    setInit(true)   
                }
            } catch (e) {
                console.warn(e)
            }
        })
    }, [init])

    const changeRadio = (e: any) => {
        setGet(false)
        setRadio(e.target.value);
    }

    const getNormal = () => {
        let _url = location.href.split('form')[0] + 'form';
        if (option === 'thjj') {
            location.href = _url + '/topFund';
        }
        fetchTemporaryStrategyBuyFundNormal({
            propName: `strategyBuyFundNormal_${option}`
        }).then( (res: any) => {
            let _data = FORM_JSON.formData
            try {
                res = JSON.parse(res.data);
                if (res) {
                    _data = res.formData;
                }
            } catch (e) {
                console.warn(e);
            }
            setGet(true);
            setData(_data);
        })
    }
    
    const save = () => {
        postTemporaryStrategyBuyFundNormal ({
            value: JSON.stringify({formData}),
            propName: `strategyBuyFundNormal_${option}`
        }).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！')
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    const uploadStrategy = () => {
        let _mains: any = mains;
        for (let i: number = 0; i < _mains.length; i ++) {
            if (name === '' || index === '') {
                message.error('输入不能为空');
                return ;
            }
            if (name === _mains[i].strategyName) {
                message.error('策略名字重复');
                return ;
            } else if (index === _mains[i].index) {
                message.error('索引重复');
                return ;
            }
        }
        _mains.push({
            "strategyName": name,
            "mainTitle": "",
            "subTitle": "",
            "jumpUrl": "",
            "icon": "",
            "type": index
        });
        postTemporaryStrategyBuyFundMain ({
            value: JSON.stringify({formData: {strategyBuyFund: _mains}})
        }).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！')
                    fetchTemporaryStrategyBuyFundNormal({
                        propName: index
                    }).then( (res: any) => {
                        let _data = FORM_JSON.formData
                        try {
                            res = JSON.parse(res.data);
                            if (res) {
                                _data = res.formData;
                            }
                        } catch (e) {
                            console.warn(e);
                        }
                        setOption(index);
                        setGet(true);
                        setData(_data);
                    })
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    const onSubmit = () => {
        let _formData:any = formData;
        if (!_formData.mainTitle || !_formData.subTitle || !_formData.introduction || !_formData.shareTitle || !_formData.shareContent || _formData.mainTitle.length > 6 || _formData.subTitle.length > 25 || _formData.introduction.length > 60 || _formData.shareTitle.length > 25 || _formData.shareContent.length > 30){
            message.error("请检查输入");
            return;
        }
        let _items = _formData.items;
        if (_items.length === 0) {
            message.error("请检查个数");
            return;
        }
        for (let i: number = 0; i < _items.length; i++) {
            _items[i].fundCode = _items[i].fundCode.trim();
            if (!_items[i].fundCode || _items[i].des.length > 2) {
                message.error("请检查输入");
                return;
            }
        }

        postTemporaryStrategyBuyFundNormal ({
            value: JSON.stringify({formData}),
            propName: `strategyBuyFundNormal_${option}`,
        }).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！');
                    postStrategyBuyFundNormal ({
                        value: JSON.stringify({formData}),
                        propName: option
                    }).then( (res: any) => {
                        try {
                            if (res.code !== '0000') {
                                message.error(res.message);
                            } else {
                                message.success('发布成功！')
                            }
                        } catch (e) {
                            message.error(e.message);
                        }
                    })
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    return (
        <article> 
            <section>
                <Radio.Group onChange={changeRadio} style={{marginBottom: 20}} value={radio}>
                    <Radio value={'add'}>我想添加策略</Radio>
                    <Radio value={'modify'}>我想修改策略</Radio>
                </Radio.Group>
            </section>
            {
                radio === 'add' ? 
                <section>
                    <span style={{ width: 200, display: "inline-block" }}> 策略名： </span>
                    <Input style={{ width: 200 }} onChange={(e) =>{
                        setName(e.target.value)
                    }}></Input> 
                    <br></br>
                    <br></br>
                    <span style={{ width: 200, display: "inline-block" }}>索引：</span>
                    <Input style={{ width: 200 }} onChange={(e) => {
                        setIndex(e.target.value)
                    }}></Input>
                    <br></br>
                    <br></br>
                    <Popconfirm
                        style={{marginLeft: '100px', marginBottom: '100px'}}
                        placement="rightBottom"
                        title={'你确定要提交么，索引无法修改'}
                        onConfirm={uploadStrategy}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button
                            type="primary" 
                        >
                            添加配置策略
                        </Button>
                    </Popconfirm>
                    <br></br>
                    <br></br>
                </section>
                :
                <section>
                    <span style={{ width: 200 }}> 请选择策略： </span>
                    <Select style={{ width: 200 }} onChange={(value:string) => {
                        setOption(value); 
                        setGet(false);}}>
                        {
                            mains.map( (item: any, index: number) => {
                                return (
                                <Select.Option 
                                value={item.type}
                                key={index}
                                >{item.strategyName}</Select.Option>
                                )
                            })
                        }
                    </Select>
                    <Button
                        type="primary" 
                        style={{ marginLeft: '200px', marginBottom: '100px' }}
                        onClick={getNormal}
                    >查询该策略配置</Button>
                </section>
            }
            
            {
                get
                ?
                <section>
                    <FormRender
                        propsSchema={FORM_JSON.propsSchema}
                        formData={formData}
                        onChange={setData}
                        onValidate={setValid}
                        showDescIcon={true}
                        column={2}
                    />

                        <Button
                            type="primary" 
                            onClick={save}
                            style={{marginRight: '300px'}}
                        >
                            保存
                        </Button>
                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要提交么'}
                        onConfirm={onSubmit}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button
                            type="danger" 
                        >
                            提交该配置策略
                        </Button>
                    </Popconfirm>
                </section>
                :
                ''
            }
        </article>
    )
}