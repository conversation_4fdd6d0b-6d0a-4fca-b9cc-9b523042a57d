import React, { useEffect, useState ,useRef} from 'react';
// 接口
import api from 'api';
// 组件
import { Table, Button, message,Modal,Popconfirm } from 'antd';
// 路由
import { history } from 'umi';
import Detail from './detail'

const key = "buy_sale_config"

const { fetchHashAll, postHashDel } = api;
function renderListString(text:string){
  return text ? text : '--'
}
function renderListEnum(arr:any,text:number){

    
      return arr[text];
    
  
}
export default function() {

  const columns = [
        
        {
            title:"基金代码",
            dataIndex: "fundCode",
            render: renderListString,
        },
        {
            title:"基金名称",
            dataIndex: "fundName",
            render: renderListString,
        },
        {
            title:"上穿阈值",
            dataIndex: "buyRate",
            render: renderListString,
        },
        {
            title:"下穿阈值",
            dataIndex: "saleRate",
            render: renderListString,
        },
        {
            title:"起始时间",
            dataIndex: "startTime",
            render: renderListString,
        },
        {
            title:"滚动回撤年份",
            dataIndex: "year",
            render: renderListString,
        }, {
        title: '操作',
        dataIndex: 'options',
        render: (text: unknown, record: any) => {
            function tempRender(
                inner: string,
                fn: (record: any) => void,
                marginRight: number = 0,
                type:
                  | 'default'
                  | 'link'
                  | 'ghost'
                  | 'primary'
                  | 'dashed'
                  | 'danger'
                  | undefined = 'primary',
            ) {
                if (type === 'danger') {
                    return (
                        <Popconfirm
                            title="你确定删除么？"
                            okText="确定"
                            cancelText="取消"
                            onConfirm={() => fn(record)}
                        >
                            <Button type={type} style={{ marginRight: marginRight+"px" }}>
                                {inner}
                            </Button>
                        </Popconfirm>
                    );
                }
                return (
                    <Button
                        type={type}
                        style={{ marginRight: marginRight+"px" }}
                        onClick={() => fn(record)}
                    >
                        {inner}
                    </Button>
                );
            }
            return (
                <section>
                  
                    {tempRender('编辑', handleEdit, 20, 'primary')}
                    {tempRender('删除', handleDelete, 0, 'danger')}
                </section>
            );
        },
    }
]
  // 表格数据
  const [dataSource, setDataSource] = useState<any[]>();
  // 表格加载动画
  const [loadingFlag, setLoadingFlag] = useState<boolean>();
 const [showModal,setShowModal] = useState(false);
  const [modalProps,setModalProps] = useState();
  const ref = useRef()
  useEffect(() => {
    setLoadingFlag(true)
    searchAllList();
  }, []);
  // 查询活动列表
  const searchAllList = () => {
  
    fetchHashAll({
      key,
    })
      .then((res: any) => {
        if (res.code === '0000') {
          if(!res.data){
            return
          }
          const list = []
          const data = res.data;
          const keys = Object.keys(data);
          for(const key of keys){
            const item = JSON.parse(data[key]);
            item.indexStr = key
            list.push(item)
          }
          setDataSource(list);
          setLoadingFlag(false);
        } else {
          message.error(res.message);
        }
      })
      .catch((err: unknown) => {
        message.error('查询列表错误');
        console.log('查询列表错误',err);
      });
  };
  
  // 查看
  const handleCheck = (record: any) => {
    
    setModalProps({
      type:"check",
      id: record.indexStr,
    })
    setShowModal(true)
  };
    // 删除
    const handleDelete = (record: any) => {
      const indexStr = record.indexStr;
      postHashDel({
        key,
        propName:indexStr
      }).then((res: { code: string; message: string })=>{
        if(res.code ==="0000"){
          message.success("删除成功");
          searchAllList()
        }
        else{
          message.success(res.message)
        }
      })
    };
  // 编辑
  const handleEdit = (record: any) => {
    setModalProps({

      id: record.indexStr,
    })
    setShowModal(true)
   
  };
  const handleCancel = () => {
    setShowModal(false)
    searchAllList()
  };
  const onOk = ()=>{
     ref.current &&  ref.current.onSubmitClick()
  } 
  return (
    <article>
      <section style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="primary"
          onClick={() => {
            setModalProps({

              id: null,
            })
            setShowModal(true)
          }}
          style={{ marginBottom: '20px' }}
        >
          新增
        </Button>

      </section>
      <Table
        columns={columns}
        dataSource={dataSource}
        scroll={{ x: 'max-content' }}
        loading={loadingFlag}
      ></Table>
      {showModal && <Modal visible  onCancel={handleCancel} onOk={onOk} width="60VW">
          <Detail ref={ref}  {...modalProps} handleCancel={handleCancel}/>
      </Modal>}
    </article>
  );
}
