import React, { useState, useEffect } from "react";
import <PERSON><PERSON><PERSON> from 'form-render/lib/antd';
import api from 'api';
import { Button, message } from 'antd'
import FORM_CONFIG from './form.json';

const { fetchSmallsetRule, postSmallsetRule, postSmallsetRuleCode } = api;

export default function () {
  const [init, setInit] = useState(false);
  const [formConfig, setFormConfig] = useState({});
  const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);
  const [configCode, setConfigCode] = useState([]);

  useEffect(() => {
    fetchSmallsetRule().then((res: any) => {
      try {
        res = JSON.parse(res.data);
        // console.log(res);
        if (res) {
          FORM_CONFIG.formData = {
            ...res
          };
          let _code = [];
          let _smallsetList = res.smallsetList
          for (let i = 0; i < _smallsetList.length; i++) {
            _code.push(_smallsetList[i].code);
          }
          setConfigCode(_code);

        }
      } catch (e) {
        console.warn(e)
      }
      setInit(true);
      setFormConfig(FORM_CONFIG);
      setData(FORM_CONFIG.formData);
    }).catch((e: Error) => {
      message.error(e.message);
    })
  }, [init]);

  const onSubmit = () => {
    if (valid.length > 0) {
      message.error(`校验未通过字段：${valid.toString()}`);
    } else {
      let _postDataCode = [
        ...formData.smallsetList
      ]
      console.log(_postDataCode);
      let _postCode = [];
      for (let i = 0; i< _postDataCode.length; i++) {
        _postCode.push(_postDataCode[i].code);
        postSmallsetRuleCode({
          value: JSON.stringify(_postDataCode[i])
        }, `${_postDataCode[i].code}`).then((res: any) => {
          try {
            if (res.code !== '0000') {
              message.error(res.message);
            } else {
              message.success(`${_postDataCode[i].code}发布成功！`);
            }
          } catch (e) {
            message.error(e.message);
          }
        })
      }
      let _configCode = [...configCode];
      setConfigCode(_postCode);
      for (let i = 0; i < _configCode.length; i++) {
        if (_postCode.indexOf(_configCode[i]) < 0) {
          postSmallsetRuleCode({
            value: ''
          }, `${_configCode[i]}`).then((res: any) => {
            try {
              if (res.code !== '0000') {
                message.error(res.message);
              }
            } catch (e) {
              message.error(e.message);
            }
          })
        }
      }
      let _postData = {
        ...formData
      }
      postSmallsetRule({
        value: JSON.stringify(_postData)
      }).then((res: any) => {
        try {
          if (res.code !== '0000') {
            message.error(res.message);
          } else {
            message.success(`列表更新成功！`);
          }
        } catch (e) {
          message.error(e.message);
        }
      })
    }
  };

  if (!init) return '加载中'
  return (
    <div style={{ padding: 60 }}>
      <FormRender
        propsSchema={FORM_CONFIG.propsSchema}
        formData={formData}
        onChange={setData}
        onValidate={setValid}
        showDescIcon={true}
        displayType="row"
        column={2}
      />
      <Button type="primary" onClick={onSubmit}>提交</Button>
    </div>
  );
}
