/*
*接口配置
* <AUTHOR>
* @time 2019.12
*/

import React from 'react';
import {Button, message, Popconfirm} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import FormRender from 'form-render/lib/antd';

const { fetchSecurityCheck, postSecurityCheck} = api;
const schema = {
    "propsSchema": {
        "type": "object",
        "properties": {
        "custIdList": {
            "title": "客户号列表",
            "type": "array",
            "ui:widget": "select",
            "ui:options": {
            "mode": "tags"
            }
        }
        }
    },
    "formData": {
        "custIdList": []
    }
};


@autobind
class securityCheck extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            formData: schema.formData || {}
        };
    }


    onChange = formData => {
        this.setState({ 
            formData 
        }, () => {console.log(this.state)});
    }
    
    dataInit () {
        fetchSecurityCheck({
            type: 'query'
        }).then(data => {
            if (data.data) {
                this.setState({
                    formData: {
                        custIdList: data.data.custIdList
                    }
                });
            }
        })
    }
    
    updateForm () {
        const { custIdList } = this.state.formData
        postSecurityCheck ({
            type: 'update',
            value: JSON.stringify({custIdList})
        }).then(data => {
            if (data.code === '0000') {
                message.success('修改成功,10分钟后生效');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
    }

    componentDidMount () {
        this.dataInit();
        // this.newBoyDataInit();
    }

    render () {
        return (
            <section className="codeInterface" style={{ width: '800px' }}>

                <FormRender
                    propsSchema={schema.propsSchema}
                    formData={this.state.formData}
                    onChange={this.onChange}
                />

                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交么'}
                    onConfirm={this.updateForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="danger" 
                        // style={{ marginLeft: '300px' }}
                    >
                        提交修改
                    </Button>
                </Popconfirm>

            </section>
        )
    }
}

export default securityCheck;