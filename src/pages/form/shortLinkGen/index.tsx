import React, { useEffect, useState } from 'react';
import { Form, Input, Row, Col, message, Button, Spin, Modal } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import api from 'api';
import { getQueryVariable } from '@/utils/utils';

interface ShortLinkGenProps extends FormComponentProps {
  children?: React.ReactNode;
}

const formItemLayout = {
  labelCol: {
    span: 3,
  },
  wrapperCol: {
    span: 10,
  },
};

const ShortLinkGen: React.FC<ShortLinkGenProps> = ({
  form,
}) => {
  const { getFieldDecorator } = form;
  const [loading, setLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState<any>({});
  const [genUrl, setGenUrl] = useState<string>('');
  const [preHost, setPreHost] = useState<string>('');
  const [shortLink, setShortLink] = useState<string>('');

  useEffect(() => {
    setPreHost(window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'test' : '')
    form.resetFields();
  }, []);

  const handleParams = (paramObj: any): string => {
    const params: any = [];
    Object.keys(paramObj).map(key => {
      let value = paramObj[key]
      if (typeof value === 'undefined') {
        value = ''
      }
      if (value) {
        params.push([key, encodeURIComponent(value)].join('='))
      }
    })
    return params.join('&');
  }

  const onBlur = () => {
    // setGenUrl(_url)
  }

  /**
   * @description: 生成短链接
   */

  const genShortLink = () => {
    const originUrl = form.getFieldValue('originUrl');
    const url = form.getFieldValue('url');
    const action = form.getFieldValue('action');
    const shortLinkBackWash = form.getFieldValue('shortLinkBackWash');
    if (!originUrl) {
      Modal.error({ content: '请输入原链接！', okText: '确定' });
      return;
    }
    if (url && action) {
      Modal.error({ content: 'url和action参数二选一！', okText: '确定' });
      return;
    }
    const _paramObj = {
      ...getQueryVariable(originUrl),
      ...{
        url: url || '',
        action: action || '',
        shortLinkBackWash: shortLinkBackWash || '',
      }
    }
    // console.log(_paramObj)
    // console.log(handleParams(_paramObj))
    let _url = `${originUrl.split('?')[0]}${handleParams(_paramObj) ? ('?' + handleParams(_paramObj)) : ''}`
    setGenUrl(_url)
    setLoading(true);
    api.genShortLink({ originalUrl: _url }).then((res: any) => {
      console.log(res)
      if (res?.code !== '0000') {
        Modal.error({ content: res?.message || '生成失败，请稍后重试' });
        return;
      } else {
        const _prefixLink = window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'r-test.thsi.cn/' : 'r.thsi.cn/'
        setShortLink(_prefixLink + res?.data?.result)
      }
    }).finally(() => {
      setLoading(false);
    })
  };

  /**
   * @description: 复制链接
   */
  const handleCopy = () => {
    try {
      let input = document.createElement('input');
      input.value = shortLink;
      input.setAttribute('readonly', 'readonly');
      document.body.appendChild(input);
      input.focus();
      input.setSelectionRange(0, input.value.length);
      document.execCommand('copy');
      document.body.removeChild(input);
      message.success('复制成功')
    } catch (e) {
      message.error('复制失败')
    }
  }

  return (
    <Spin spinning={loading}>
      <Form {...formItemLayout} labelAlign="left">
        <Form.Item label="原始链接输入：" wrapperCol={{ span: 16 }}>
          <Row>
            <Col span={17}>
              {getFieldDecorator('originUrl', {
                initialValue: formData.originUrl,
                rules: [{ required: true, message: '请输入原始链接' }],
              })(<Input onBlur={onBlur} placeholder="请输入原网址" />)}
            </Col>
            <Col offset={1} span={6}>
            </Col>
          </Row>

        </Form.Item>
        <Form.Item label="action参数：">
          {getFieldDecorator('action', {
            initialValue: formData.action,
          })(<Input onBlur={onBlur} placeholder="请输入action参数（协议跳转）" />)}
        </Form.Item>
        <Form.Item label="url参数：">
          {getFieldDecorator('url', {
            initialValue: formData.url,
          })(<Input onBlur={onBlur} placeholder="请输入url参数（链接跳转）" />)}
        </Form.Item>
        <Form.Item label="shortLinkBackWash参数：">
          {getFieldDecorator('shortLinkBackWash', {
            initialValue: formData.shortLinkBackWash,
          })(<Input onBlur={onBlur} placeholder="请输入shortLinkBackWash参数（埋点追踪）" />)}
        </Form.Item>
        <Form.Item>
          <Button style={{padding: '0 22px'}} onClick={genShortLink} type="primary">生成短链接</Button>
        </Form.Item>
        <Form.Item label="拼接后的链接为：">
          <span style={{ marginRight: 20 }}>{genUrl || '---'}</span>
        </Form.Item>
        <Form.Item label="生成的短链接为：">
          <span style={{ marginRight: 20 }}>{shortLink || '---'}</span>
          <Button type="primary" disabled={!Boolean(shortLink)} onClick={handleCopy}>复制</Button>
        </Form.Item>
      </Form>
      <h3 style={{ color: 'red' }}>注：url和action参数二选一！</h3>
      <div>外网虚拟机本地要配置hosts，************* r.thsi.cn</div>

      <br />
      <h2>链接参数说明：</h2>
      <br />
      <ul>
        <li>1、通过协议跳转：参数为action，参数值参考<a target="_blank" href="http://**************:8003/pages/viewpage.action?pageId=9512201">http://**************:8003/pages/viewpage.action?pageId=9512201</a>。若协议跳转不需要带参，则在action处直接填写，例如sy；若需要带参，则以英文逗号分隔参数，例如fund,code=000001</li>
        <li>2、通过url跳转：参数为url，参数值为对应页面网页url；在url处填写对应url，如https://www.baidu.com</li>
        <li>3、埋点追踪参数：参数为shortLinkBackWash</li>
      </ul>

      <br />
      <h2>参数追踪：</h2>
      <br />
      {/* <p>确保链接具有参数追踪功能，与开发约定参数形式即可。</p> */}
      <p>例如生成不同的分支：</p>
      <p>{`https://${preHost}fund.10jqka.com.cn/ifundapp_app/public/m/transferLink/dist/index.html?shortLinkBackWash=1`}</p>
      <p>{`https://${preHost}fund.10jqka.com.cn/ifundapp_app/public/m/transferLink/dist/index.html?shortLinkBackWash=2`}</p>
      <p>{`https://${preHost}fund.10jqka.com.cn/ifundapp_app/public/m/transferLink/dist/index.html?shortLinkBackWash=3`}</p>
      <p>这里参数“1、2、3”就是追踪参数。</p>
      <p>然后当用户点击相应页面时，追踪相应埋点参数。</p>

      <br />
      <h2>常用链接（拼接后的链接，请将其中的action/url/shortLinkBackWash参数对应填写到上面的输入框。如跳转到APP，则原始链接处均统一填写 https://fund.10jqka.com.cn/ifundapp_app/public/m/transferLink/dist/index.html）：</h2>
      <br />
      <ol>
        <li>{`跳转APP首页： https://${preHost}fund.10jqka.com.cn/ifundapp_app/public/m/transferLink/dist/index.html?action=sy&shortLinkBackWash=`}</li>
        <li>{`跳转"我的"： https://${preHost}fund.10jqka.com.cn/ifundapp_app/public/m/transferLink/dist/index.html?action=tradepage&shortLinkBackWash=`}</li>
        <li>{`跳转具体产品页： https://${preHost}fund.10jqka.com.cn/ifundapp_app/public/m/transferLink/dist/index.html?url=xxxxxxx&shortLinkBackWash=`}</li>
      </ol>

      {/* <div style={{ float: 'right' }}>
        <Button key="back" onClick={onEditClose}>
          取消
        </Button>
        <Button htmlType="submit" key="submit" type="primary" onClick={onSubmit}>
          保存
        </Button>
      </div> */}
    </Spin>
  );
};

export default Form.create<ShortLinkGenProps>()(ShortLinkGen);
