import React, { useState, useEffect } from 'react';
import styles from './index.less';
import api from 'api';
import { Button, Input, message, Radio, Select, DatePicker, TimePicker } from 'antd';
import classNames from 'classnames';
import { RadioChangeEvent } from 'antd/lib/radio';
import { IMessageTemplate, MessageData } from '../type';
import TagModel from '../components/tag';
const { TextArea } = Input;
const { Option } = Select;
const { msgOption, fetchOlasTags, uploadMsgTxt, downloadMsgTxt, fetchMessageTemplates } = api;
import moment from 'moment';
import { handleTimeStr } from '../fn';

const olasTagMap: any = {
  '1': [],
  '3': [],
}
const relationMap: any = {
  '0': {
    id: 'olas',
    ugroup: 'olasId'
  },
  '1': {
    id: 'file',
    ugroup: 'fileId'
  },
  'olas': {
    id: '0',
    ugroup: 'olasId'
  },
  'file': {
    id: '1',
    ugroup: 'fileId'
  },
}
const dateFormat = 'YYYY-MM-DD';
const timeFormat = 'HH:mm';
let checkUserRange = false;

export default function edit() {
  const data: any = JSON.parse(localStorage.getItem('sms_msg_data') as any);
  const [init, setInit] = useState(false);
  const [msg, setMsg] = useState({
    mtype: '0',
    mname: '',
    mcontent: '',
    mbackground: '',
    mpurpose: '',
    mstrategy: '',
    templateId: '',
  } as MessageData);
  const [userType, setUserType] = useState<any>('');
  const [relationData, setRelationData] = useState({
    targetType: 'olas',
    olasId: '',
    fileId: '',
    ucount: '0',
    udescription: '',
    txtUrl: '',
    changeType: '',
  });
  const [olasTag, setOlasTag] = useState<any>([]);
  const [canConfirm, setCanConfirm] = useState(false);
  const [confirm, setConfirm] = useState(false);
  const [sendEdit, setSendEdit] = useState(false);
  const [sendConfig, setSendConfig] = useState({
    ucount: '0',
    mcount: '',
    concurrency: '5',
    periodTime: '',
    stype: '0',
    stime: '',
  });
  // 短信模板列表
  const [messageTemplate, setMessageTemplate] = useState<IMessageTemplate[]>([]);

  useEffect(() => {
    if (!init) {
      Init();
    }
  }, []);
  useEffect(() => {
    if (init && userType) {
      if (olasTagMap[userType]?.length > 0) {
        setOlasTag(olasTagMap[userType]);
      } else {
        fetchTags();
      }
    }
  }, [userType])
  useEffect(() => {
    if (relationData?.targetType && relationData.changeType) {
      setSendEdit(true);
      getSendConfig(sendConfig.concurrency);
    }
  }, [relationData])
  useEffect(() => {
    setCanConfirm(checkoutEdit());
  }, [relationData, msg])

  const Init = () => {
    fetchMessageTemplates().then(res => {
      if (res.success) {
        setMessageTemplate(res.data);
      }
    });
    fetchEditData();
    setInit(true);
  };
  // 从localStorage中获取短信数据
  const fetchEditData = () => {
    if (data) {
      const {
        mtype,
        mname,
        mcontent,
        templateId,
        mbackground,
        mpurpose,
        mstrategy, // 短信内容
        utype,
        tagType,
        ugroup,
        ucount,
        udescription, // 指定用户
        periodTime,
        stime,
        stype,
        concurrency,
        mcount,
        txtUrl, // 发送设置
      } = data;
      const relationKey = relationMap[tagType];
      const _relation = {
        targetType: relationKey.id,
        olasId: tagType === '0' ? ugroup : '',
        fileId: tagType === '1' ? ugroup : '',
        changeType: '',
        ucount, udescription, txtUrl
      }

      setMsg({ mtype, mname, mcontent, mbackground, mpurpose, mstrategy, templateId });
      setUserType(utype);
      setRelationData(_relation);
      console.log('fetchEditData', _relation)
      setSendConfig({ ucount, periodTime, stime, stype, concurrency, mcount })
      if (mtype === '2' && mname && mbackground && mpurpose && mcontent && mstrategy ) {
        setSendEdit(true);
      }
    } else {
      setUserType('3')
    }
  }
  // 请求olasTag
  const fetchTags = () => {
    fetchOlasTags({
      userType: userType
    }).then((res: any) => {
      if (res?.code === '0000') {
        const olasTags: any[] = res?.data || [];
        olasTagMap[userType] = olasTags;
        setOlasTag(olasTags);
        return true;
      } else {
        console.log(res?.message);
        return false;
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error(e.message);
      return false;
    });;
  };
  const canEdit = () => {
    return _.fn.getUrlParam('edit') === 'true';
  };
  // 计算目标人数及发送短信数
  const getSendConfig = (concurrency: string) => {
    let _sendConfig = sendConfig;
    const msgStore = (msg.mcontent || '')?.length;
    let ucount = relationData.ucount;
    let mcount = msgStore ? 
      (Math.ceil(msgStore / 70) * Number(relationData.ucount)).toFixed(0) :
      '0';
    let periodTime = (Number(mcount) / Number(concurrency) / 3600).toFixed(0);
    _sendConfig = { ..._sendConfig, ucount, mcount, periodTime, concurrency };
    setSendConfig(_sendConfig);
  }
  // 短信内容 校验
  const checkoutEdit = () => {
    // 营销类校验
    if (msg.mtype === '0' && (!msg.mname || !msg.mcontent || !msg.mbackground || !msg.mpurpose || !userType || (!relationData.olasId && !relationData.fileId))) {
      return false;
    }
    // 通知类校验
    if (msg.mtype === '1' && (!msg.mname || !msg.mcontent || !userType || (!relationData.olasId && !relationData.fileId))) {
      return false;
    }
    // 策略类校验
    if (msg.mtype === '2' && (!msg.mname || !msg.mbackground || !msg.mpurpose|| !msg.mcontent || !msg.mstrategy )) {
      return false;
    }
    return true;
  }
  const getTimeFormat = (type: 'date'|'time') => {
    if (!sendConfig?.stime) return null;
    switch(type) {
      case 'date': {
        const format = moment(sendConfig.stime.slice(0, 10), dateFormat);
        return format
      }
      case 'time': {
        const format = moment(sendConfig.stime.slice(-5), timeFormat);
        return format
      }
    }
  }

  // 新增、更新 短信数据
  const submitEdit = () => {
    let value: MessageData;
    const relationKey: any = relationMap[relationData.targetType];
    value = {
      ...msg,
      ...sendConfig,
      id: data.id,
      utype: userType,
      tagType: relationKey.id,
      ugroup: relationData[relationKey.ugroup as 'olasId'|'fileId'],
      udescription: relationData.udescription,
      txtUrl: relationData.txtUrl,
      editor: JSON.parse(localStorage.getItem('name') || ''),
      etime: handleTimeStr(new Date()),
      lcomment: '',
      contentReviewComment: '',
      leComment: '',
      ocomment: '',
    }
    if (data) {
      // 更新
      value = {
        ...data,
        ...value,
      }
    }
    console.log(value, JSON.stringify(value));

    msgOption({
      type: 'update',
      value: JSON.stringify(value)
    }).then((res: any) => {
      console.log(res);
      if (res?.code === '0000') {
        message.success('提交成功');
        handleCancelEdit();
      } else {
        message.error('提交失败');
      }
    }).catch((e: Error) => {
      message.error('提交失败');
    })
  }
  // 控制 取消
  const handleCancelEdit = () => {
    let url = location.href.replace(/(?<=\/|(\.html))\?.*(?=#)/, '');
    url = url.replace('/edit', '');
    location.href = url;
  }
  // 点击 确认用户范围
  const submitUserConfig = () => {
    checkUserRange = true;
    setConfirm(true);
  };
  // 控制 短信内容变化
  const handleMsgChange = (
    value: string,
    type: 'name' | 'type' | 'content' | 'background' | 'purpose' | 'strategy' | 'template',
  ) => {
    const message: MessageData = JSON.parse(JSON.stringify(msg));
    if (type === 'template') {
      message.templateId = value;
      message.mcontent = messageTemplate.find(item => item.templateId === value)?.content;
    } else {
      const param = ('m' + type) as
        | 'mname'
        | 'mtype'
        | 'mcontent'
        | 'mbackground'
        | 'mpurpose'
        | 'mstrategy';
      message[param] = value as any;
    }
    setMsg(message);
    if (message.mtype === '2' && message.mname && message.mbackground && message.mpurpose && message.mcontent && message.mstrategy ) {
      setSendEdit(true);
    } else {
      setSendEdit(false);
    }
  };
  const handleIdType = (e: RadioChangeEvent) => {
    setUserType(e.target.value);
    setSendEdit(false);
  };
  // 控制用户类型变化
  const handleLabelSelect = (data: any) => {
    console.log('指定用户提交', data);
    setRelationData(data);
    if (checkUserRange) {
      checkUserRange = false;
    } else {
      setSendEdit(false);
    }
  };
  // 下载模板
  const handleDownloadModel = () => {
    console.log('下载模板')
    window.open('https://testfund.10jqka.com.cn/s3/fund-resource/message_template.txt')
  }
  // 控制 并发数
  const handleConcurrencyChange = (value: string) => {
    console.log('更改并发数', value)
    getSendConfig(value);
  }
  // 控制 发送类型
  const handleSendType = (e: RadioChangeEvent) => {
    const _sendConfig = {...sendConfig};
    _sendConfig.stype = e.target.value;
    setSendConfig(_sendConfig);
  }
  // 控制 发送时间
  const handleSendTime = (value: string, type: 'date'|'time') => {
    if (type === 'time') {
      value = value.slice(0, -3);
    }
    const _sendConfig = {...sendConfig};
    const _date = _sendConfig.stime.split(' ');
    _date[type === 'date' ? 0 : 1] = value;
    if (_date[0] && !_date[1]) {
      _date[1] = '00:00:00'
    } else if (!_date[0] && _date[1]) {
      _date[0] = new Date().toLocaleDateString();
    }
    _sendConfig.stime = _date.join(' ');
    setSendConfig(_sendConfig);
  }

  return (
    <section className={classNames(styles['m-edit'])}>
      <p className={styles['m-title']}>短信编辑</p>
      <div className={styles['m-msg-content']}>
        <div className={classNames(styles['m-data-item'], !msg.mname ? 'm-required' : '')}>
          <span>短信推送名称：</span>
          <Input
            key="msg-name"
            id="msg-name"
            placeholder="请输入短信名称"
            disabled={!canEdit()}
            value={msg.mname || ''}
            onChange={(e: any) => handleMsgChange(e.target.value, 'name')}
          />
        </div>
        <div className={styles['m-data-item']}>
          <span>短信类型：</span>
          <Radio.Group
            value={msg.mtype}
            disabled={!canEdit()}
            onChange={(e: any) => handleMsgChange(e.target.value, 'type')}
          >
            <Radio value="0">营销类短信（需要运维审核）</Radio>
            <Radio value="1">通知类短信</Radio>
            <Radio value="2">策略类短信</Radio>
          </Radio.Group>
        </div>
        <div className={classNames(styles['m-data-item'],styles['item-background'])} style={{display: msg.mtype === '0' || msg.mtype === '2' ? '' : 'none'}}>
          <span>发送背景：</span>
          <div className={!msg.mbackground ? 'm-required' : ''}>
            <TextArea
              key="msg-background"
              id="msg-background"
              placeholder="请输入发送背景"
              disabled={!canEdit()}
              value={msg.mbackground}
              onChange={(e: any) => handleMsgChange(e.target.value, 'background')}
            />
            <span>字数 {msg?.mbackground?.length || 0}</span>
          </div>
        </div>
        <div className={classNames(styles['m-data-item'],styles['item-purpose'])} style={{display: msg.mtype === '0' || msg.mtype === '2' ? '' : 'none'}}>
          <span>发送目的：</span>
          <div className={!msg.mpurpose ? 'm-required' : ''}>
            <TextArea
              key="msg-purpose"
              id="msg-purpose"
              placeholder="请输入发送目的"
              disabled={!canEdit()}
              value={msg.mpurpose}
              onChange={(e: any) => handleMsgChange(e.target.value, 'purpose')}
            />
            <span>字数 {msg?.mpurpose?.length || 0}</span>
          </div>
        </div>
        <div className={classNames(styles['m-data-item'])}>
          <span>选择模板：</span>
          <Select
            style={{ width: 350 }}
            placeholder="请选择短信模板"
            value={msg.templateId || void 0}
            showSearch={true}
            onSelect={(value: string) => handleMsgChange(value, 'template')}
            optionFilterProp="children"
            disabled={!canEdit()}
          >
            {messageTemplate.map(item => (
              <Option value={item.templateId} key={item.templateId}>
                {item.name}
              </Option>
            ))}
          </Select>
        </div>
        <div className={classNames(styles['m-data-item'], styles['item-context'])}>
          <span>短信内容：</span>
          <div className={!msg.mcontent ? 'm-required' : ''}>
            <TextArea
              key="msg-context"
              id="msg-context"
              placeholder="选择模板后自动填充"
              disabled={true}
              value={msg.mcontent}
              onChange={(e: any) => handleMsgChange(e.target.value, 'content')}
            />
            <span>字数 {msg?.mcontent?.length || 0}</span>
          </div>
        </div>
        <div className={classNames(styles['m-data-item'],styles['item-strategy'])} style={{display: msg.mtype === '2' ? '' : 'none'}}>
          <span>预期策略：</span>
          <div className={!msg.mstrategy ? 'm-required' : ''}>
            <TextArea
              key="msg-strategy"
              id="msg-strategy"
              placeholder="请输入预期策略"
              disabled={!canEdit()}
              value={msg.mstrategy}
              onChange={(e: any) => handleMsgChange(e.target.value, 'strategy')}
            />
            <span>字数 {msg?.mstrategy?.length || 0}</span>
          </div>
        </div>
        <div className={styles['m-custom-choose']} style={{display: msg.mtype !== '2' ? '' : 'none'}}>
          <p>指定用户：</p>
          <div className={styles['m-items']}>
            <div className={styles['m-data-item']}>
              <span>用户池选择：</span>
              <div>
                <Radio.Group value={userType} disabled={!canEdit()} onChange={handleIdType}>
                  <Radio value="3">通过Cust-id筛选</Radio>
                  <Radio value="1">通过User-id筛选</Radio>
                </Radio.Group>
              </div>
            </div>
            <TagModel
              editInit={init}
              handleChange={handleLabelSelect}
              downloadModel={handleDownloadModel}
              olasTag={olasTag}
              isEdit={canEdit()}
              data={relationData}
              userType={userType}
              uploadTxt={uploadMsgTxt}
              confirm={confirm}
              downloadTxt={downloadMsgTxt}
              resetConfirm={() => setConfirm(false)}
            />
          </div>
        </div>
        <Button
          className={styles['m-check-user']}
          type="primary"
          onClick={submitUserConfig}
          disabled={!canEdit() || !canConfirm}
          style={{display: msg.mtype !== '2' ? '' : 'none'}}
        >
          确定用户范围，开始计算目标人数及发送短信数
        </Button>
      </div>
      <div className={styles['m-send-config']} style={{display: msg.mtype !== '2' ? '' : 'none'}}>
        <div className={styles['m-data-item']}>
          <span>目标人数预计：</span>
          <span>{sendConfig.ucount}人（已去重）</span>
        </div>
        <div className={styles['m-data-item']}>
          <span>发送短信数预计：</span>
          <span>{sendConfig.mcount}条（已去重），每70字计一条短信</span>
        </div>
        <div className={classNames(styles['m-concurrency'], styles['m-data-item'])}>
          <span>并发数：</span>
          <Select
            value={sendConfig.concurrency}
            disabled={!sendEdit}
            onChange={handleConcurrencyChange}
          >
            <Option value="5">5</Option>
            <Option value="10">10</Option>
            <Option value="15">15</Option>
            <Option value="20">20</Option>
            <Option value="25">25</Option>
            <Option value="30">30</Option>
            <Option value="35">35</Option>
            <Option value="40">40</Option>
          </Select>
          <span>条/秒</span>
          <span>预计需要{sendConfig.periodTime}小时（从开始发送到全部发送完毕）</span>
        </div>
        <p>注意事项：常规情况下，发送时间涉及工作日09:15~10:30时，并发数请选择10以内；其它时间段，可以选择20以内。如果有超出以上范围的并发数需求，请确保提前与运维沟通后再进行选择。最大允许并发数为40。</p>
        <div className={classNames(styles['m-send-time'], styles['m-data-item'])}>
          <span>发送时间：</span>
          <div>
            <Radio.Group
              className={styles['m-send-type']}
              value={sendConfig.stype}
              onChange={handleSendType}
              disabled={!sendEdit}
            >
              <Radio value="0">通过审核后立即发送</Radio>
              <Radio value="1">定时发送（若在发送时间前仍未通过审核，则状态自动变更为“发送失败”）</Radio>
            </Radio.Group>
            <div className={classNames(styles['m-time-picker'], sendConfig.stype !== '1' ? 'z-hide' : '')}>
                <DatePicker
                  disabled={!sendEdit}
                  placeholder="请选择日期"
                  value={sendConfig?.stime ? getTimeFormat('date') : null}
                  format={dateFormat}
                  onChange={(date: any, dateString: string) => {handleSendTime(dateString, 'date')}}
                />
                <TimePicker
                  disabled={!sendEdit}
                  placeholder="请选择时间"
                  value={sendConfig?.stime ? getTimeFormat('time') : null}
                  // format={timeFormat}
                  onChange={(time: any, timeString: string) => {handleSendTime(timeString, 'time')}}
                />
            </div>
          </div>
        </div>
        <p>PS:通过法务审核后，会自动发送到测试人员手机上。</p>
      </div>
      <div className={styles['m-submit']}>
        <Button type="danger" onClick={submitEdit} disabled={!sendEdit}>提交审核</Button>
        <Button type="primary" onClick={handleCancelEdit}>取消</Button>
      </div>
    </section>
  );
}
