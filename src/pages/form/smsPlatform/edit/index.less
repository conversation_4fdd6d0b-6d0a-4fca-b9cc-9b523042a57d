.m-edit {
  width: 700px;
  .m-title {
    margin: 0;
    color: #333;
    text-align: start;
    font-size: 30px;
    font-weight: bold;
  }
  .m-msg-content, .m-send-config {
    padding-left: 20px;
  }
  .m-msg-content {
    .m-check-user {
      min-width: 340px;
      height: 40px;
      left: 50%;
      position: relative;
      transform: translate(-50%);
    }
    .item-context, .item-background, .item-purpose, .item-strategy {
      > div {
        display: flex;
        flex-flow: column;
        > span {
          margin-left: auto;
          color: #9c9c9c;
          font-size: 12px;
        }
      }
    }
    .m-data-item {
      margin-bottom: 10px;
    }
  }
  .m-send-config {
    .m-concurrency {
      > div:nth-child(2) > div {
        min-width: 60px;
      }
      > span:nth-child(n + 3) {
        margin: 0 6px;
      }
      & + p {
        margin-left: 120px;
        color: #c1c1c1;
        font-size: 12px;
      }
    }
    .m-send-time {
      align-items: flex-start;
      > div:nth-child(2) {
        padding-top: 15px;
        .m-send-type {
          margin-bottom: 15px;
          label {
            margin-bottom: 5px;
            display: block;
          }
        }
        .m-time-picker {
          > * {
            margin-right: 6px;
          }
        }
      }
      & + p {
        color: #c1c1c1;
        font-size: 14px;
      }
    }
  }
  .m-submit {
    margin-top: 30px;
    > button {
      margin-right: 40px;
    }
  }
  .m-data-item {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    > span:nth-child(1) {
      padding: 15px 0;
      min-width: 120px;
      font-weight: bold;
    }
  }
  .m-custom-choose {
    > p {
      padding: 15px 0;
      font-weight: bold;
    }
    .m-items {
      > * {
        transform: translateX(40px);
      }
    }
  }
}

[id*="msg-"] {
  width: 480px;
  padding: 2px 5px;
  border: 1px solid rgba(0,0,0,.3);
  border-radius: 4px;
}
input[id="msg-name"] {
  height: 30px;
}
textarea[id="msg-background"] {
  height: 100px;
}
textarea[id="msg-purpose"] {
  height: 100px;
}
textarea[id="msg-context"] {
  height: 160px;
}
textarea[id="msg-strategy"] {
  height: 160px;
}
:global {
  .m-required {
    > textarea, > textarea:hover, > textarea:focus,
    > input, > input:hover, > input:focus {
      border: 1px solid rgba(255,0,0,.8);
      box-shadow: 0 0 3px 2px rgba(255,0,0,.3);      
    }
  }
}