import React, { useState, useEffect } from 'react';
import styles from './index.less';
import api from 'api';
import classNames from 'classnames';
import { Button, Input, message } from 'antd';
const { TextArea } = Input;
const { fetchMsgTestMembers, postMsgTestMembers } = api;

export default function Members () {
  const [text, setText] = useState('');
  useEffect(() => {
    getMembers()
  }, [])

  const getMembers = () => {
    fetchMsgTestMembers().then((res: any) => {
      try {
        if (res?.code === '0000') {
          console.log(res.data);
          let arr = res.data.split(',');
          setText(arr.join('\n'));
        } else {
          message.error(res.message);
        }
      } catch (e: any) {
        message.error(e.message);  
      }
    }).catch((e: Error) => {
      message.error(e.message);
    })
  }

  const handleDownloadModel = () => {
    window.open('https://testfund.10jqka.com.cn/s3/fund-resource/message_template.txt')
  }
  const handleTextChange = (e: any) => {
    setText(e.target.value);
  }
  const handleSave = () => {
    const reg = /^[1-9][0-9]*$/;
    let arr = text.split(/\r|\n/);
    arr = arr.filter(item => !!item);
    for (let i = 0; i < arr.length; i++) {
      arr[i] = arr[i].replace(/ *$/, '');
      if (!reg.test(arr[i])) {
        message.warning('输入格式有误，请修改后保存。')
        return;
      }
    }
    setText(arr.join('\n'));
    const str = arr.join(',');
    postMsgTestMembers({
      value: str
    }).then((res: any) => {
      try {
        if (res?.code === '0000') {
          message.success('提交成功！');
        } else {
          message.error(res.message);
        }
      } catch (e: any) {
        message.error(e.message);
      }
    }).catch((e: Error) => {
      message.error(e.message);
    })
  }
  const handleCancel = () => {
    const url = location.href.replace(/(?<=smsPlatform)\/members.*$/g, '');
    location.href = url;
  }

  return (
    <section className={styles['m-members']}>
      <p>
        <span>短信测试人员配置（输入Cust-id，一行一个）</span>
        <Button type="link" onClick={handleDownloadModel}>查看范例</Button>
      </p>
      <p>
        PS:通过法务审核后，会自动发送到测试人员手机上。
      </p>
      <TextArea
        value={text}
        placeholder="输入的号码会在这里显示，分行显示。发送时后端自动进行号码与相应手机号的匹配。"
        onChange={handleTextChange}
      />
      <div className={styles['m-options']}>
        <Button type="primary" onClick={handleSave}>保存</Button>
        <Button type="danger" onClick={handleCancel}>取消</Button>
      </div>
    </section>
  )
}
