import React, { useState, useEffect } from 'react';
import styles from './index.less';
import api from 'api';
import { Table, Button, Pagination, ConfigProvider, message, Popconfirm } from 'antd';
import { ColumnProps } from 'antd/lib/table';
import zhCN from 'antd/es/locale/zh_CN';
import { roleKey, roleVerifyKey, processMap, safeValueShow, handleTimeStr } from './fn';
import { MessageData, Step, StepValueKey } from './type';
import VerifyView from './components/verifyView';
import ProcessView from './components/processView';

const { fetchMsgList, postMsgVerify, msgOption } = api;

// 接口状态对应step状态
const statusList = ['waiting', 'success', 'fail'];
const addRoles = ['operate', 'admin'];
const editor = JSON.parse(localStorage.getItem('name') || '');

export default function smsPlatform() {
  /**
   * 渲染操作项
   */
  const renderOptions = (options: any, record: any, index: number) => {
    const { edit, verify, del } = options;
    return (
      <div key={`record-options-${index + 1}`} className={styles['m-record-options']}>
        <Button type="primary" onClick={() => handleProcessView(index)}>查看流程</Button>
        <Button type="primary" onClick={() => handleEdit(index, edit)}>{edit ? '编辑' : '查看内容'}</Button>
        <Button disabled={!verify} type="primary" onClick={() => handleVerify(index)}>审核</Button>
        <Popconfirm title="确认要删除吗？" disabled={!del} onConfirm={() => handleDelete(index)} >
          <Button disabled={!del} type="danger">删除</Button>
        </Popconfirm>
      </div>
    )
  };
  /**
   * 渲染短信类型
   * @param type 类型
   * @returns 
   */
  const renderMsgType = (type: '0'|'1') => {
    return processMap[type].type;
  }
  const columns: ColumnProps<any>[] = [
    { dataIndex: 'id', key: 'id', title: '短信id' },
    { dataIndex: 'mname', key: 'mname', title: '短信推送名称' },
    { dataIndex: 'mtype', key: 'mtype', title: '短信类型', render: renderMsgType },
    { dataIndex: 'mcontent', key: 'mcontent', title: '短信内容' },
    { dataIndex: 'udescription', key: 'udescription', title: '用户类型', render: value => safeValueShow(value) },
    { dataIndex: 'mcount', key: 'mcount', title: '发送短信数', render: value => safeValueShow(value) },
    { dataIndex: 'status', key: 'status', title: '流程状态' },
    { dataIndex: '_stime', key: '_stime', title: '发送时间' },
    { dataIndex: 'options', key: 'options', title: '操作按钮', render: renderOptions },
  ];
  const [role, setRole] = useState('');
  const [tableData, setTableData] = useState<any>([]);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [tableTotal, setTableTotal] = useState(0);
  const [verifyIndex, setVerifyIndex] = useState(-1);
  const [processIndex, setProcessIndex] = useState(-1);

  useEffect(() => {
    init();
  }, []);
  useEffect(() => {
    if (role && pageNum && pageSize) {
      fetchMessages()
    }
  }, [role, pageNum, pageSize])

  const init = () => {
    // 设置身份，从localStorage中取，若使用role参数设置，取该角色
    let _role = JSON.parse(localStorage.getItem('roles') || '[""]')[0];
    let set_role = (/(?<=(\?|&)role=).*(?=(&|))/.exec(location.hash) || [])[0];
    if (set_role) {
      _role = decodeURIComponent(set_role);
    }
    setRole(roleKey[_role]);
  }
  const fetchMessages = () => {
    fetchMsgList(
      {},
      pageNum,
      // ((pageNum - 1) * pageSize).toString(),
      pageSize
    ).then((res: any) => {
      if (res?.code === '0000') {
        res = res?.data || {};
        let list = (res?.dataList || []).map((msg: MessageData, index: number) => {
          let { mtype, stype, stime } = msg;
          mtype = mtype || '0';
          stype = stype || '0';
          let steps: any[] = [];
          steps = getSteps(msg);
          let status, options;
          const length = steps.length || 1;
          const lastStep = steps[length - 1];
          const process: any = processMap[mtype].process;
          let _stime = stype === '0' ? '审核通过后立即发送' : stime;
          _stime = (lastStep.status === 'waiting' ? '（等待中）' : '') + _stime;
          if (mtype === '2') _stime = '--';
          options = getOptions(mtype, lastStep.status, steps);
          switch (lastStep.status) {
            case 'success': {
              // 审核成功
              status = process[length - 1].statusSuccess;
              // 如果是最后一步
              if (length === process.length) {
                _stime = lastStep.time;
              }
              break;
            }
            case 'waiting': {
              // 审核等待
              status = process[length - 1].statusWaiting;
              break;
            }
            case 'fail': {
              // 审核失败
              status = process[length - 1].statusFail;
              // options = { edit: false, verify: false, del: delRoles.includes(role) };
              _stime = '--';
              break;
            }
            case 'timeout': {
              // 审核超时
              status = process[process.length - 1].statusFail;
              // options = { edit: false, verify: false, del: delRoles.includes(role) };
              _stime = '--';
              break;
            }
          }
          return {
            key: msg?.id || index + 1,
            ...msg,
            mtype, steps, status, options, _stime
          }
        })
        setTableData(list);
        setTableTotal(res?.size || 0);
      } else {
        console.log(res?.message || '网络错误，请稍后再试')
      }
    }).catch((e: Error) => {
      console.log(e.message);
    })
  }
  const getSteps = (data: MessageData) => {
    let mtype = data?.mtype || '0';
    let stype = data?.stype || '0';
    let stime = data?.stime || '';
    // 根据mtype确认流程类型
    const process = processMap[mtype as string].process;
    let _steps: Step[] = [];
    for (let i = 0; i < process.length; i++) {
      const item: StepValueKey = process[i];
      const { editorKey, statusKey, timeKey, commentKey } = item;
      let step: Step;
      if (item.status === 'start') { 
        step = {
          status: 'success',
          editor: data[editorKey] || '',
          time: data[timeKey] || '',
        }
      } else if (item.status === 'end') {
        break;
      } else {
        const lastStatus = _steps[_steps.length - 1].status;
        if (!data[statusKey] || lastStatus === 'waiting' || lastStatus === 'fail') break;
        const status: any = statusList[Number(data[statusKey])]
        step = {
          status,
          editor: data[editorKey] || '',
          time: data[timeKey] || '',
          failReason: data[commentKey] || '',
        }
      }
      _steps.push(step);
    }
    const length = _steps.length;
    _steps.forEach((step, index) => {
      switch(step.status) {
        case 'success': step.statusText = process[index].statusSuccess; break;
        case 'waiting': step.statusText = process[index].statusWaiting; break;
        case 'fail': step.statusText = process[index].statusFail; break;
      }
    })
    // 发送步骤的填充
    if (length > 0) {
      const lastStep = _steps[length - 1];
      let ifSendStep = '0';
      let desc: string[] = [];
      let sendStep: Step = { editor: '', time: stime, status: 'success' };
      switch (true) {
        // 等待定时发送
        case (lastStep.status === 'success' && length === process.length - 1 && stype === '1' && new Date(stime) >= new Date()): {
          ifSendStep = 'waiting';
          sendStep.statusText = process[process.length - 1].statusWaiting;
          break;
        }
        // 发送成功
        case (lastStep.status === 'success' && length === process.length - 1): {
          ifSendStep = 'success';
          desc = ['全部流程已完成'];
          // 如果是立即发送，发送时间就是上一步审核通过时间。
          if (stype === '0') {
            sendStep.time = lastStep.time;
          }
          sendStep.statusText = process[process.length - 1].statusSuccess;
          break;
        }
        // 发送失败（超时）
        case (lastStep.status === 'waiting' && stype === '1' && new Date(stime) < new Date()): {
          ifSendStep = 'timeout';
          sendStep.statusText = process[process.length - 1].statusFail;
          break;
        }
        // 审核不通过
        case (lastStep.status === 'fail'): {
          desc = [
            `${process[length - 1].statusFail}，请返回重新编辑。`,
            `编辑完成后，可重新进行审核流程。`
          ]
          break;
        }
      }
      if (ifSendStep !== '0') {
        sendStep = {
          ...sendStep,
          editor: '',
          status: ifSendStep as 'success'|'waiting'|'timeout',
        }
        _steps.push(sendStep)
      }
      if (desc.length > 0) {
        _steps[_steps.length - 1].finishDesc = desc;
      }
      return _steps;
    } else {
      return [];
    }
  }
  const getOptions = (mtype: any, status: string, steps: Step[]) => {
    const roleMap: any = {legal: 'legal', operate: 'operate', admin: 'operate'};
    let _role = roleMap[role];
    const length = steps.length;
    let canVerify;
    if (mtype === '1' && _role === 'operate') {
      canVerify = status === 'waiting' && (length === roleVerifyKey[_role].verifyIndex)
    } else {
      canVerify = status === 'waiting' && (length === roleVerifyKey[_role].verifyIndex + 1)
    }
    switch (_role) {
      case 'admin': return { edit: true, verify: true, del: true };
      // case 'admin':
      case 'legal': {
        return {
          edit: false,
          verify: canVerify,
          del: false,
        }
      }
      // case 'admin':
      case 'operate': {
        const ifSame = JSON.parse(localStorage.getItem('name') || '') === steps[0]?.editor;
        const canEdit = !(length === processMap[mtype].process.length && steps[length - 1].status === 'success');
        return {
          edit: canEdit,
          verify: !ifSame && canVerify,
          del: canEdit
        }
      }
      default: return { edit: false, verify: false, del: false, }
    }
  }

  /**
   * 页码控制器
   * @param current 当前页码
   */
  const handlePageNum = (current: number) => {
    setPageNum(current);
  };
  /**
   * 页容量控制器
   * @param pageSize 当前页容量
   */
  const handlePageSize = (pageNum: number, pageSize: number) => {
    setPageSize(pageSize);
  };
  /**
   * 查看流程
   * @param index 记录索引
   */
  const handleProcessView = (index: number) => {
    setProcessIndex(index);
  };
  // 查看流程返回
  const handleProcessReturn = () => {
    setProcessIndex(-1);
  }
  // 控制新增
  const handleAdd = () => {
    handleEdit(-1, true);
  }
  /**
   * 编辑/查看内容
   * @param index 记录索引
   * @param edit 是否可编辑
   */
  const handleEdit = (index: number, edit: boolean) => {
    // localStorage存储记录信息
    localStorage.setItem('sms_msg_data', index > -1 ? JSON.stringify(tableData[index]) : 'false');
    location.href = location.href
      .replace(/(?<=\/|(\.html))(?=#\/)/, `?edit=${edit}`)
      .replace(/(?<=smsPlatform)/, '/edit')
  };
  /**
   * 审核
   * @param index 记录索引
   */
  const handleVerify = (index: number) => {
    setVerifyIndex(index);
  }
  /**
   * 提交审核结果
   * @param verify 审核结果
   */
  const handleVerifySubmit = (verify: boolean, failReason?: string) => {
    let msg: any = JSON.parse(JSON.stringify(tableData[verifyIndex]));
    const process = processMap[msg.mtype as string].process;
    const stepIndex = (msg?.steps?.length || 2) - 1;
    const { editorKey, statusKey, timeKey, commentKey } = process[stepIndex];
    msg[editorKey] = editor;
    msg[statusKey] = verify ? '1' : '2';
    msg[timeKey] = handleTimeStr(new Date());
    msg[commentKey] = !verify ? failReason : '';
    postMsgVerify({
      value: JSON.stringify(msg)
    }).then((res: any) => {
      console.log(res)
      if (res?.code === '0000') {
        message.success('提交审核成功');
        setVerifyIndex(-1);
        fetchMessages(); // 审核成功后刷新
      } else {
        console.log(res.message);
        message.error('提交审核失败');
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error('提交审核失败');
    })

  }
  /**
   * 删除
   * @param index 记录索引
   */
  const handleDelete = (index: number) => {
    msgOption({
      type: 'delete',
      value: JSON.stringify({id: tableData[index].id})
    }).then((res: any) => {
      console.log(res);
      if (res?.code === '0000') {
        message.success('删除成功');
        fetchMessages(); // 删除成功更新列表
      } else {
        console.log(res.message);
        message.error('删除失败');
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error('删除失败');
    }) 
  }
  /**
   * 关闭弹窗
   * @param type 弹窗类型
   */
  const handleCancel = (type: 'verify'|'process') => {
    switch (type) {
      case 'verify': setVerifyIndex(-1); break;
      case 'process': setProcessIndex(-1); break;
    }
  }

  return (
    <section className={styles['m-sms-platform']}>
      <p className={styles['m-title']}>短信列表</p>
      <div className={styles['m-options']}>
        <Button type="primary" disabled={!addRoles.includes(role)} onClick={handleAdd}>新增</Button>
      </div>
      <div className={styles['m-table']}>
        <ConfigProvider locale={zhCN}>
          <Table columns={columns} dataSource={tableData} pagination={false} />
        </ConfigProvider>
        <div className={styles['m-pagination']}>
          <ConfigProvider locale={zhCN}>
            <Pagination
              current={pageNum}
              pageSize={pageSize}
              total={tableTotal}
              showSizeChanger
              pageSizeOptions={['10', '20', '30', '50']}
              onChange={handlePageNum}
              onShowSizeChange={handlePageSize}
              hideOnSinglePage={tableTotal < 10}
            />
          </ConfigProvider>
        </div>
      </div>
      {
        verifyIndex > -1 ?
        <VerifyView
          data={tableData[verifyIndex]}
          role={role}
          onSubmit={handleVerifySubmit}
          onCancel={() => handleCancel('verify')}
        />
        : null
      }
      {
        processIndex > -1 ?
        <ProcessView
          data={tableData[processIndex].steps}
          mtype={tableData[processIndex].mtype}
          onReturn={handleProcessReturn}
        />
        : null
      }
    </section>
  );
}
