import React from 'react';
import { Button } from 'antd';
import styles from './index.less';
import { StepValueKey } from './type';

export const roleKey: any = {
  '法务': 'legal',
  '法务审核': 'legal',
  '合规': 'legal',
  '运营': 'operate',
  '管理员': 'admin',
  'legal': 'legal',
  'operate': 'operate',
  'admin': 'admin',
};
export const roleVerifyKey: any = {
  legal: {
    role: '合规',
    verifyIndex: 1,
  },
  operate: {
    role: '运营',
    verifyIndex: 3,
  },
  admin: {
    role: '管理员'
  }
};
const stepsTextMap = [
  { processText: '创建该推送', statusSuccess: '创建该推送', statusWaiting: '创建推送中', statusFail: '创建失败' },
  { processText: '法务审核', statusSuccess: '法务审核通过', statusWaiting: '法务审核中', statusFail: '法务审核未通过' },
  { processText: '内容审核', statusSuccess: '内容审核通过', statusWaiting: '内容审核中', statusFail: '内容审核未通过' },
  { processText: '负责人审核', statusSuccess: '负责人审核通过', statusWaiting: '负责人审核中', statusFail: '负责人审核未通过' },
  { processText: '运维审核', statusSuccess: '运维审核通过', statusWaiting: '运维审核中', statusFail: '运维审核未通过' },
  { processText: '发送完成', statusSuccess: '发送完成', statusWaiting: '审核通过，定时发送等待中', statusFail: '发送失败（ 审核已超时，请重新发送）' }
]
const stepsValueMap = [
  { editorKey: 'editor', timeKey: 'etime', status: 'start' },
  { editorKey: 'legal', statusKey: 'lstatus', timeKey: 'ltime', commentKey: 'lcomment' },
  { editorKey: 'contentReview', statusKey: 'contentReviewStatus', timeKey: 'contentReviewTime', commentKey: 'contentReviewComment' },
  { editorKey: 'leader', statusKey: 'leStatus', timeKey: 'leTime', commentKey: 'leComment' },
  { editorKey: 'operation', statusKey: 'ostatus', timeKey: 'otime', commentKey: 'ocomment' },
  { status: 'end' }
]
export const processMap: any = {
  '0': {
    type: '营销类短信',
    process: [0, 1, 2, 3, 4, 5]
  },
  '1': {
    type: '通知类短信',
    process: [0, 1, 3, 5]
  },
  '2': {
    type: '策略类短信',
    process: [0, 1, 2, 3, 4, 5]
  },
};
const setProcess = function () {
  for (let type in processMap) {
    let process = processMap[type].process;
    processMap[type].process = process.map((item: any) => {
      item = {
        ...stepsValueMap[item],
        ...stepsTextMap[item]
      }
      return item;
    })
  }
}
setProcess();

/**
* 返回节流函数
* @param {*} intervalTime 间隔时间
* @param {*} f1           用户连续执行执行函数
* @param {*} f2           连续执行结束执行函数
*/
export const funcBlock = function(intervalTime: number, f1: Function, f2: Function) {
  let lastFunc = 0;
  let timer: number;
  // 连续执行结束，重置lastFunc，等待新的首次执行
  const funcEnd =  function (args?: any) {
      f2 && f2(...arguments);
      lastFunc = 0;
  }
  // 首次执行和连续执行调用。
  return function() {
      if(lastFunc !== 0) {
          // 连续执行需清理定时器
          clearTimeout(timer);		
      }
      lastFunc = new Date().getTime();
      f1 && f1(...arguments);
      // 每次执行，启用定时器，定时器若执行，判断为连续执行结束。
      timer = window.setTimeout(() => {
          clearTimeout(timer);
          funcEnd(...arguments);
      }, intervalTime, ...arguments)
  }
}

/**
 * 判断是否相等
 */
export function equal(value: string, compare = 0) {
  return parseInt(value) === compare
}

/**
 * 安全显示
 * @value 检查是否需要安全显示的值
 */
const SAFE_SYMBOEL: string = '--'; // 安全显示符号

export function safeValueShow(value: any) {
  return !value && !equal(value, 0) ? SAFE_SYMBOEL : value;
}

/**
* @description 处理时间戳为yyyy-mm-dd hh:mm
* @param timeStr
* @return {string}
*/
export function handleTimeStr(timeStr: Date | number) {
  let _date;
  if (timeStr instanceof Date) _date = timeStr;
  else _date = new Date(timeStr);
  let _year: (number | string) = _date.getFullYear(),
      _month: (number | string) = _date.getMonth() + 1,
      _day: (number | string) = _date.getDate(),
      _hour: (number | string) = _date.getHours(),
      _minute: (number | string) = _date.getMinutes();
  if (_month < 10) _month = '0' + _month;
  if (_day < 10) _day = '0' + _day;
  if (_hour < 10) _hour = '0' + _hour;
  if (_minute < 10) _minute = '0' + _minute;
  return `${_year}-${_month}-${_day} ${_hour}:${_minute}`;
}