.m-sms-platform {
  .m-title {
    margin: 0;
    color: #333;
    text-align: center;
    font-size: 30px;
    font-weight: bold;
  }
  .m-options {
    margin: 10px 0;
    display: flex;
    > button {
      width: 90px;
      margin-left: auto;
    }
  }
  .m-table {
    .m-record-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    [class*="ant-table-row-level"] {
      > td {
        padding: 12px 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      > td:nth-child(3) {
        max-width: 220px;
      }
      > td:nth-child(4) {
        max-width: 160px;
      }
    }
  }
  .m-pagination {
    margin-top: 10px;
    display: flex;
    > ul {
      margin-left: auto;
    }
  }
}
:global {
  .m-required {
    > textarea, > input {
      border: 1px solid rgba(255,0,0,.8);
      box-shadow: 0 0 3px 2px rgba(255,0,0,.3);      
    }
  }
}