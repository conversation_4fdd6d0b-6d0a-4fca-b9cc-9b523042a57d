import React, { useState, useEffect, FormEvent } from 'react';
import { Step, VerifyData } from '../type';
import { processMap, roleKey, roleVerifyKey } from '../fn';
import { Button, Timeline, Icon } from 'antd';
import styles from './index.less';
import classNames from 'classnames';

interface ProcessProps {
  data?: Step[];
  mtype: '0'|'1';
  onReturn: () => void;
}

export default function ProcessView ({
  data = [],
  mtype,
  onReturn = () => {}
}: ProcessProps) {
  const finishDesc: string[] = data[data.length - 1].finishDesc || [];
  const timelineDot = (status: 'success'|'waiting'|'fail'|'timeout') => {
    switch (status) {
      case 'success': {
        return <Icon type="check-circle" theme="twoTone" twoToneColor="#52c41a" />;
      }
      case 'waiting': {
        return <Icon type="clock-circle" theme="twoTone" twoToneColor="#c3c3c3" />;
      }
      case 'fail':
      case 'timeout': {
        return <Icon type="close-circle" theme="twoTone" twoToneColor="#ff0000" />;
      }
    }
  }

  return (
    <section className={styles['m-pop-view']}>
      <div className={styles['m-mask']}></div>
      <section className={styles['m-processView']}>
        <p>查看流程</p>
        <div className={styles['m-time-line']}>
          <Timeline>
            {
              data?.length && data.map((item: Step, index: number) => {
                return (
                  <Timeline.Item
                    key={`timeline-item=${index + 1}`}
                    dot={timelineDot(item.status || 'success')}
                  >
                    {
                      item.status === 'waiting' || item.status === 'timeout' ?
                      item.statusText
                      :
                      (item.editor ? item.editor + '  ' : '')
                      + '于  '
                      + item.time + '  '
                      + item.statusText
                    }
                    {
                      item.failReason ?
                      <p>{item.failReason}</p>
                      : null
                    }
                  </Timeline.Item>
                )
              })
            }
          </Timeline>
        </div>
        <div className={styles['m-desc']}>
          {
            finishDesc?.length > 0 ? finishDesc.map((desc: string, index: number) => {
              return <p key={`process-desc-${index + 1}`}>{desc}</p>
            })
            : null
          }
        </div>
        <Button type="primary" onClick={onReturn}>返回</Button>
      </section>
    </section>
  )
}