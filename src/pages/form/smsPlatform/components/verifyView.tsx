import React, { useState, useEffect, FormEvent } from 'react';
import { VerifyData } from '../type';
import { processMap, roleKey, roleVerifyKey } from '../fn';
import styles from './index.less';
import { Button, message, Radio } from 'antd';
import { RadioChangeEvent } from 'antd/lib/radio';
import classNames from 'classnames';

interface VerifyProps {
  data: VerifyData;
  role?: string;
  onSubmit?: (verify: boolean, failReason?: string) => void; // 提交回调
  onCancel?: () => void; // 取消回调
}

export default function verifyView ({
  data,
  role = 'legal',
  onSubmit = () => {},
  onCancel = () => {},
}: VerifyProps) {
  const [verify, setVerify] = useState(true);
  const [failReason, setFailReason] = useState('');

  const { id, mname, mtype, mcontent, mcount, stime, concurrency, steps, stype, mbackground, mpurpose, mstrategy } = data;

  const handleSelect = (e: RadioChangeEvent) => {
    setVerify(e.target.value);
  }
  const handleTextArea = (e: FormEvent) => {
    const text = e.target as any;
    setFailReason(text.value);
  }
  const handleSubmit = () => {
    onSubmit(verify, failReason)
  }

  return (
    <section className={styles['m-pop-view']}>
      <div className={styles['m-mask']}></div>
      <section className={classNames(styles['m-verifyView'])}>
        <div className={styles['m-verify-item']}>
          <span>推送id：</span>
          <span>{id}</span>
        </div>
        <div className={styles['m-verify-item']}>
          <span>短信推送名称：</span>
          <span>{mname}</span>
        </div>
        <div className={styles['m-verify-item']}>
          <span>短信类型：</span>
          <span>{processMap[mtype || '0'].type}</span>
        </div>
        <div className={styles['m-verify-item']} style={{display: mtype === '0' || mtype === '2' ? '' : 'none'}}>
          <span>发送背景：</span>
          <span>{mbackground}</span>
        </div>
        <div className={styles['m-verify-item']} style={{display: mtype === '0' || mtype === '2' ? '' : 'none'}}>
          <span>发送目的：</span>
          <span>{mpurpose}</span>
        </div>
        <div className={styles['m-verify-item']}>
          <span>短信内容：</span>
          <span>{mcontent}</span>
        </div>
        <div className={styles['m-verify-item']} style={{display: mtype === '2' ? '' : 'none'}}>
          <span>预期策略：</span>
          <span>{mstrategy}</span>
        </div>
        <div className={styles['m-verify-item']} style={{display: mtype !== '2' ? '' : 'none'}}>
          <span>发送短信预计：</span>
          <span>{mcount}条</span>
        </div>
        <div className={styles['m-verify-item']} style={{display: mtype !== '2' ? '' : 'none'}}>
          <span>预计发送时间：</span>
          <span>{stype === '0' ? '通过审核后立即发送' : stime}</span>
        </div>
        <div className={styles['m-verify-item']} style={{display: mtype !== '2' ? '' : 'none'}}>
          <span>并发数：</span>
          <span>{concurrency}条，预计{(0.5 + (Number(mcount) / Number(concurrency)) / 3600 | 0)}小时发送完毕</span>
        </div>
        <div className={styles['m-verify-item']}>
          <span>您的身份权限是：</span>
          <span>{roleVerifyKey[role]?.role || ''}</span>
        </div>
        <div className={styles['m-verify-item']}>
          <span>当前正在进行：</span>
          <span>
            {
              (processMap[mtype || '0']
              .process[(steps?.length || 1) - 1] as any)
              .processText
            }
          </span>
        </div>
        <div className={styles['m-result']}>
          <span>审核决定：</span>
          <Radio.Group onChange={handleSelect} value={verify}>
            <Radio value={true}>审核通过</Radio>
            <Radio value={false}>审核未通过</Radio>
          </Radio.Group>
          <div
            style={{display: verify ? 'none' : '', }}
            className={failReason ? '' : 'm-required'}
          >
            <textarea onChange={handleTextArea} value={failReason} placeholder="请输入审核不通过原因"></textarea>
          </div>
        </div>
        <div className={styles['m-btns']}>
          <Button type="danger" onClick={handleSubmit} disabled={!verify && !failReason}>提交</Button>
          <Button type="primary" ghost onClick={onCancel}>返回</Button>
        </div>
      </section>
    </section>  
  )
}