import React, { useState, useEffect } from 'react';
import { Radio, Select, Input, Button, Popconfirm, message, Upload, Row, Col } from 'antd';
import api from 'api';
const { Option } = Select;
import styles from './index.less';
import classNames from 'classnames';
import { RcFile, UploadChangeParam } from 'antd/lib/upload';
const { TextArea } = Input;

interface iProps {
  editInit: boolean; // 编辑页是否初始化
  isEdit: boolean;
  olasTag: Array<any>;
  data: any;
  userType: '1'|'3';
  confirm: boolean; // 是否开始下载文件并计算
  handleChange: (data: any) => void;
  // fileType?: string,
  // saveFile: (file: any, type?: string) => void,
  downloadModel: () => void, //下载模板文件
  uploadTxt: any, // 上传文件
  downloadTxt: any, // 下载文件
  resetConfirm: () => void, // 设置触发器
  // isExist?: boolean  //文件是否存在
}

let initData = true;

export default function TagModel(props: iProps) {
  const {
    editInit,
    isEdit,
    olasTag,
    data,
    userType,
    confirm,
    uploadTxt,
    downloadTxt,
    handleChange,
    downloadModel,
    resetConfirm
    // , saveFile, fileType, , isExist
  } = props;
  const [radio, setRadio] = useState(data.targetType);
  const [olasId, setOlasId] = useState('');
  const [fileId, setFileId] = useState('');
  const [files, setFiles] = useState<any>([]);
  // 查询编辑页，获取初始文本
  const [fileData, setFileData] = useState<any>();

  useEffect(() => {
    if (confirm && data?.targetType) {
      confirmRange();
      resetConfirm();
    }
  }, [confirm])

  useEffect(() => {
    if (data?.targetType !== radio) {
      setRadio(data.targetType);
    }
    if (data?.olasId !== olasId) {
      setOlasId(data.olasId);
    }
    if (data?.fileId !== fileId) {
      setFileId(data.fileId);
    }
  }, [data])
  useEffect(() => {
    if (editInit && data?.targetType === 'file' && data?.fileId && userType && data?.txtUrl) {
      initData = false;
      confirmRange();
    }
  }, [editInit])

  // 点击确认用户范围回调
  const confirmRange = () => {
    switch (data?.targetType) {
      case 'olas': {
        let tag: any = {};
        for (let i = 0; i < olasTag.length; i++) {
          if (olasTag[i].taskId === olasId) {
            tag = olasTag[i];
            break;
          }
        }
        let ucount = olasId ? tag?.nums.toString() : '0';
        let udescription = olasId ? tag?.description : '';
        onSubmit(radio, olasId, fileId, ucount, udescription, '', 'olas');
        break;
      }
      case 'file':{ 
        downloadFile(data?.targetType, data?.fileId, userType, data?.txtUrl);
        break;
      }
    }
  }
  // 上传文件到s3
  const uploadFile = (file: RcFile) => {
    let formData = new FormData();
    formData.append('fileName', file);
    formData.append('userType', userType);
    return uploadTxt(formData);
  }
  // 从s3获取文件
  const downloadFile = (radio: string, hashKey: string, userType: string, txtUrl: string) => {
    return downloadTxt({
      hashKey,
      userType,
      objectId: txtUrl.replace('fund-resource/', '')
    }).then((res_download: any) => {
      if (res_download?.code === '0000') {
        res_download = res_download.data;
        let arr = checkoutFileData(res_download.custs as string);
        arr = [...new Set(arr)];
        const _fileData = arr.join('\n');
        setFileData(_fileData);
        let ucount = arr?.length > 0 ? arr.length.toString() : '0';
        let udescription = arr?.length > 0 ? arr.slice(0, 3).join('/') : '';
        onSubmit(radio, '', hashKey, ucount, udescription, txtUrl, 'file');
      } else {
        console.log(res_download?.message);
        message.error('获取上传文件失败');
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error('获取上传文件失败');
    });
  }
  // 校验导入文本内容
  const checkoutFileData = (value: string) => {
    const reg = /^[1-9][0-9]*$/;
    let list: string[] = value.split('\r\n');
    list = list.filter(item => !!item);
    list = list.map(item => {
      item = item.replace(/ /g, '');
      if (!reg.test(item)) {
        message.warning('文件中存在不合规id，请修改后另行上传');
      }
      return item
    });
    return list || [];
  }

  //提交时返回给父组件的回调
  const onSubmit = (
    radio: any,
    olasId: any,
    fileId: any,
    ucount?: string,
    udescription?: string,
    txtUrl?: string,
    changeType?: string
  ) => {
    let _data = {
      targetType: radio,
      olasId,
      fileId,
      ucount, // 发送信息人数
      udescription, // 用户标签
      txtUrl: txtUrl || '', // s3Url
      changeType: changeType || '', // 是否下载文件
    };
    handleChange(_data);
  };
  const onRadioChange = (value: any) => {
    let ucount: string;
    let udescription: string;
    if (value === 'olas') {
      let tag: any = {};
      for (let i = 0; i < olasTag.length; i++) {
        if (olasTag[i].taskId === olasId) {
          tag = olasTag[i];
          break;
        }
      }
      ucount = olasId ? tag?.nums.toString() : '0';
      udescription = olasId ? tag?.description : '';
    } else if (value === 'file') {
      ucount = fileData ? fileData.split('\n')?.length.toString() : '0';
      udescription = fileData ? fileData : '';
    } else {
      ucount = '0';
      udescription = '';
    }
    setRadio(value);
    onSubmit(value, olasId, fileId, ucount, udescription);
  };
  const onOlasChange = (val: any, a: any) => {
    let ucount = '0';
    let udescription = '';
    // let ucount = olasTag[val]?.nums.toString() || '0';
    // let udescription = olasTag[val]?.description || '';
    setOlasId(val);
    onSubmit(radio, val, fileId, ucount, udescription);
  };
  // 上传回调，控制上传文件数量
  const onFileChange = (info: UploadChangeParam) => {
    let _files: any = info.fileList;
    _files = _files.slice(-1);
    setFiles(_files);
  }
  // 上传文件
  const handleBeforeUpload = (file: RcFile) => {
    uploadFile(file).then((res_upload: any) => {
      if (res_upload?.code === '0000') {
        res_upload = res_upload.data;
        const { uploadHashKey, s3Url } = res_upload;
        message.success('上传文件成功！')
        onSubmit(radio, '', uploadHashKey, '', '', s3Url);
      } else {
        console.log(res_upload?.message);
        message.error('上传文件失败');
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error('上传文件失败');
    })
    return false;
  }
  // 清空文件
  const handleRemoveFile = () => {
    onSubmit(radio, olasId, '', '0');
    setFiles([]);
    setFileData('');
  }
  return (
    <section className={classNames(styles['m-tag-model'], styles['m-data-item'])}>
      <span>标签选择:</span>
      <Radio.Group onChange={e => onRadioChange(e.target.value)} value={radio} disabled={!isEdit}>
        <Radio value={'olas'} key="olas">
          通过OLAS平台推送
        </Radio>
        <Radio value={'file'} key="file">
          手动上传用户名单
        </Radio>
      </Radio.Group>
      <Button
        onClick={() => {
          onRadioChange('');
        }}
        disabled={!isEdit}
      >
        清除指定用户
      </Button>
      <br />
      <div
        key="olas"
        className={classNames(styles['m-tagModel-row'], styles['m-data-item'], radio !== 'olas' ? 'z-hide' : '')}
      >
        <span>用户群体:</span>
        <Select
          showSearch
          style={{ width: 260 }}
          optionFilterProp="children"
          onChange={onOlasChange}
          value={olasId}
          filterOption={(input, option: any) => option.props.children.indexOf(input) >= 0}
          disabled={!isEdit}
        >
          {olasTag.map((item: any, index: number) => {
            return (
              <Option value={item.taskId} key={index}>
                {item.description}
              </Option>
            );
          })}
        </Select>
      </div>
      <div
        key="file"
        className={classNames(styles['m-tagModel-row'], radio !== 'file' ? 'z-hide' : '', 'g-mb20')}
      >
        <div className={styles['m-data-item']}>
          <span>导入号码：</span>
          <div className={styles['m-file-area']}>
            <div>
              <Button onClick={handleRemoveFile} disabled={!isEdit}>清空</Button>
              <Upload
                accept=".txt"
                beforeUpload={handleBeforeUpload}
                customRequest={(e) => {console.log(e)}}
                onRemove={handleRemoveFile}
                onChange={onFileChange}
                fileList={files}
                disabled={!isEdit}
              >
                <Button disabled={!isEdit}>导入TXT</Button>
              </Upload>
              <Button type="link" onClick={downloadModel} disabled={!isEdit}>查看范例</Button>
            </div>
            <div>
              <TextArea
                value={fileData}
                onChange={(value) => {console.log(value)}}
                disabled={true}
                placeholder="导入的号码会在这里显示，分行显示，因页面大小所以只展示前1000个。发送时后端自动进行号码与相应手机号的匹配。"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
