.m-pop-view {
  left: 0;
  top: 0;
  width: 100%;
  min-height: 100%;
  position: absolute;
  .m-mask {
    width: 100%;
    height: 100%;
    position: absolute;
    background-color: rgba(0,0,0,.3);
    z-index: 1;
  }
  .m-verifyView, .m-processView {
    left: 50%;
    top: 120px;
    position: fixed;
    display: inline-block;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 0 3px 1px #fff;
    transform: translate(-50%);
    z-index: 2;
  }
  .m-verifyView {
    width: 420px;
    max-height: 700px;
    overflow-y: auto;
    padding: 20px;
    .m-verify-item {
      display: flex;
      padding: 5px 0;
      > span:nth-child(1) {
        min-width: 120px;
        display: inline-block;
        font-weight: bold;
      }
      > span {
        word-break: break-all;
      }
      > div {
        position: relative;
        left: 50%;
        transform: translate(-50%);
      }
    }
    .m-result {
      padding: 5px 0;
      > span {
        display: inline-block;
        min-width: 120px;
        font-weight: bold;
      }
      textarea {
        width: 200px;
        height: 100px;
        margin-top: 10px;
        margin-left: 120px;
        padding: 2px 5px;
        border: 1px solid rgba(0,0,0,.3);
      }
    }
    .m-btns {
      width:100%;
      display: flex;
      justify-content: space-around;
      padding-top: 20px;
    }
  }
  .m-processView {
    min-width: 300px;
    padding: 20px 30px;
    p {
      text-align: center;
    }
    .m-time-line {
      svg {
        width: 20px;
        height: 20px;
      }
      p {
        text-align: start;
        color: #ccc;
        font-size: 14px;
      }
      div[class*="ant-timeline-item-content"] {
        margin-left: 25px;
      }
    }
    > button {
      left: 50%;
      position: relative;
      transform: translate(-50%);
    }
  }
}
.m-tag-model {  
  .m-tagModel-row {
    .m-data-item {
      display: flex;
      flex-flow: row nowrap;
    }
    .m-file-area {
      > div:nth-child(1) {
        padding: 8px 0;
        margin-bottom: 30px;
        display: flex;
        align-items: flex-start;
        > button:nth-child(1) {
          margin-right: 25%;
        }
        > *:nth-child(2) {
          height: 32px;
        }
        > button:nth-last-child(1) {
          margin-left: auto;
        }
      }
      > div:nth-child(2) {
        textarea {
          width: 400px;
          height: 160px;
        }
      }
    }
  }
}
.m-data-item {
  > span:nth-child(1) {
    display: inline-block;
    padding: 15px 0;
    min-width: 120px;
    font-weight: bold;
  }
}