export interface MessageData {
  id?: string; // 短信id id
  mname: string; // 短信名称 mname
  mtype: string; // 短信类型 mtype 营销类 0；通知类 1；策略类：2；
  mcontent?: string; // 短信内容 mcontent
  templateId?: string; // 短信模板id
  mbackground?: string; // 发送背景
  mpurpose?: string; // 发送目的
  mstrategy?: string; // 预期策略
  tagType?: string; // 用户类型 tagType
  mcount?: string; // 发送条数 mcount
  stime?: string; // 发送时间 stime
  _stime?: string; // 发送时间 文本
  stype?: string; // 发送类型（审核通过立即发送 0; 定时发送 1） stype
  concurrency?: string; // 并发数 concurrency
  status?: string; // 流程状态 
  steps?: Step[]; // 流程列表
  utype?: string; // 用户类型 1:userId; 3:custId
  udescription?: string; // 用户类型描述
  ugroup?: string; // 用户群体id
  txtUrl?: string; // s3链接

  editor?: string; // 创建人
  etime?: string; // 创建时间

  legal?: string; // 法务
  lstatus?: '0'|'1'|'2'; // 法务审核状态 未审核：0; 通过：1; 不通过：2
  ltime?: string; // 法务审核时间
  lcomment?: string; // 法务审核评论

  contentReview?: string; // 内容审核人
  contentReviewStatus?: '0'|'1'|'2'; // 内容审核状态 未审核：0; 通过：1; 不通过：2
  contentReviewTime?: string; // 内容审核时间
  contentReviewComment?: string; // 内容审核评论

  leader?: string; // 负责人
  leStatus?: '0'|'1'|'2'; // 负责人审核状态 未审核：0; 通过：1; 不通过：2
  leTime?: string; // 负责人审核时间
  leComment?: string; // 负责人审核评论
  
  operation?: string; // 运维
  ostatus?: '0'|'1'|'2'; // 运维审核状态 未审核：0; 通过：1; 不通过：2
  otime?: string; // 运维审核时间
  ocomment?: string; // 运维审核评论

}
export interface VerifyData extends MessageData {
  verify?: boolean; // 审核
}
export interface Step {
  status: 'success'|'waiting'|'fail'|'timeout'; // 步骤是否成功
  editor: string; // 操作人
  time: string; // 步骤时间
  statusText?: string; // 步骤文字
  // statusSuccess?: string; // 步骤成功
  // statusWaiting?: string; // 步骤等待
  // statusFail?: string; // 步骤失败
  // statusTimeout?: string; // 步骤超时
  failReason?: string; // 审核失败原因
  finishDesc?: string[]; // 流程完成情况描述
}

export interface StepValueKey {
  editorKey: 'editor'|'legal'|'contentReview'|'leader'|'operation',
  statusKey: 'lstatus'|'contentReviewStatus'|'leStatus'|'ostatus',
  timeKey: 'etime'|'ltime'|'contentReviewTime'|'leTime'|'otime',
  commentKey: 'lcomment'|'contentReviewComment'|'leComment'|'ocomment',
  status?: string;
}

// 短信模板
export interface IMessageTemplate {
  // 模板名称
  name: string;
  // 模板id
  templateId: string;
  // 模板内容
  content: string;
}
