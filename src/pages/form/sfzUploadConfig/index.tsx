import React, { useState, useEffect } from 'react';
import api from 'api';
import {
  Button,
  message,
  Popover,
  Table,
  Row,
  Col,
  Input,
  Select,
  Modal,
  Switch,
  PageHeader,
  List,
  Divider,
  InputNumber,
} from 'antd';
import classNames from 'classnames';
import data from '../indexValue/data';
import Search from 'antd/lib/input/Search';
import content from '../safelyWin/content';
const { Option } = Select;
const { TextArea } = Input;
const { getSfzWithFund, postSfzWithFund, getFundName, fetchHash, postHash } = api;
interface fundProp {
  fundId: string;
  fundName?: string;
}

const pageSize = 10;
//
interface iFundRelation {
  insideRate: number;
  outsideRate: number; // 场外基金占比，小数
  fundRate: number; // 投资占比阈值，小数
  scale: number; // 基金规模阈值，单位：千万
  subjectFundMap: any;
}
interface subjectFundMapProps {
  subjectId: string;
  fundCode: string;
}
export default function() {
  const columns = [
    {
      title: '基金名称',
      dataIndex: 'fundName',
      width: '30%',
    },
    {
      title: '基金ID',
      dataIndex: 'fundId',
    },
    {
      dataIndex: 'options',
      title: '操作',
      render: (text: unknown, fund: fundProp) => (
        <Button type="primary" onClick={() => handleDelete(fund)}>删除</Button>
      ),
    },
  ];
  const [kvSfzFund, setKvSfzFund] = useState<string[]>([]);
  const [fundCode, setFundCode] = useState('');
  const [fundBinded, setFundBinded] = useState<fundProp[]>([]);
  const [isEdited,setEdited] = useState(false);
  const KEY = 'SFZ_WITH_FUND';
  const PROP_NAME = 'SFZ_WITH_FUND';
  useEffect(() => {
    getSfzWithFundBind();
  }, []);
  // 设置列表
  const getFundBindList=(fundIdArr)=>{
    const tradeCodeList = fundIdArr;
    const typeList = 'simpleName';
    getFundName({ tradeCodeList: tradeCodeList, typeList: typeList }).then((indicObj: any) => {
      if (indicObj?.status_code === 0) {
        const sfzWithCodeTemp: fundProp[] = [];
        tradeCodeList?.forEach(e => {
          indicObj.data[e] &&
            sfzWithCodeTemp.push({
              fundName: indicObj.data[e].simpleName,
              fundId: e.toString(),
            });
        });
        setFundBinded(sfzWithCodeTemp);
      }
    })
  }
  // 获取身份证和基金的绑定关系
  const getSfzWithFundBind = async () => {
    try {
      const res = await fetchHash({ key: KEY,propName:PROP_NAME});

      if (res?.code !== '0000') {
        throw new Error(`获取信息失败：${res?.message}`);
      }
      if (res?.data) {
        const data: string[] = JSON.parse(res.data) || [];
        setKvSfzFund(data);
        getFundBindList(data);
      }
    } catch (e) {
      message.error(e?.message);
    }
  };
  const handlePublish = async () => {
    try {
      const res2 = isEdited && await postHash({ key: KEY,propName:PROP_NAME, value: JSON.stringify(kvSfzFund) });
      if (res2.code !== '0000') {
        throw new Error(`保存数据失败：${res2.message}`);
      }
      getFundBindList(kvSfzFund);
      message.success('保存成功');
    } catch (e) {
      message.error(e.message || '未知错误');
    }
  };
  const addSfzFund = () => {
    setEdited(true)
    const sfzFundArr = [...kvSfzFund];
    sfzFundArr.push(fundCode);
    setKvSfzFund([...sfzFundArr]);
    getFundBindList([...sfzFundArr]);
  };
  const handleDelete = (fund: fundProp) => {
    setEdited(true)
    const sfzFundArr = [...kvSfzFund];
    const index = sfzFundArr.findIndex(e => {
      return e === fund.fundId;
    });
    sfzFundArr.splice(index, 1);
    setKvSfzFund([...sfzFundArr]);
  };
  return (
    <div>
      <section>
        <Row>
          <Button style={{ marginBottom: '20px' }} onClick={handlePublish} type={isEdited? 'primary': ''}>
            保存并发布
          </Button>
        </Row>
        <Row className={'g-mb10'} style={{ marginLeft: '24px' }}>
          <Col span={20}>
            <span>申购节点基金代码</span>
            <Input
              defaultValue={''}
              value={fundCode}
              onChange={e => setFundCode(e.target.value)}
              style={{ width: '200px' }}
              placeholder={'输入基金代码'}
            />
            <Button onClick={addSfzFund} type="primary" className={'g-ml20'}>
              {' '}
              添加
            </Button>
          </Col>
        </Row>
        <Table columns={columns} dataSource={fundBinded} pagination={false}></Table>
      </section>
    </div>
  );
}
