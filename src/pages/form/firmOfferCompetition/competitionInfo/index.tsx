import React, { useState, useEffect, FormEvent } from 'react';
// 路由
import { history } from 'umi';
// 接口
import api from 'api';
// 组件
import { Button, Form, Input, Upload, Icon, message } from 'antd';
import { UploadImg } from '../component';
// TypeScript
import { FormComponentProps } from 'antd/lib/form/Form';
import { UploadFile } from 'antd/lib/upload/interface';
import { allCompetitionInfo, Competition, FirmOfferCompetition, CusRouter } from '../tsFile';
// webStorage库
import store from 'store';
// 时间工具库
import moment from 'moment';
// excel
import * as XLSX from 'xlsx';

const {
  postFirmOffer,
  postWithdrawalList,
  getWithdrawalList,
  getCompetitionBlacklist,
  postCompetitionBlacklist,
} = api;
// 表单整体布局
const formLayout = {
  labelCol: {
    span: 2,
    push: 2,
  },
  wrapperCol: {
    span: 5,
    push: 3,
  },
};
// 表单项布局
const formItemLayout = {
  labelCol: {
    span: 2,
    push: 2,
  },
  wrapperCol: {
    span: 13,
    push: 3,
  },
};
const CompetitionInfo: React.FC<FormComponentProps<any>> = ({
  form,
  location,
}: {
  form: any;
  location: CusRouter;
}) => {
  const { getFieldDecorator, validateFields } = form;
  // 大赛相关信息
  const [competitionInfo, setCompetitionInfo] = useState<Competition<string>>({
    headImage: '',
    etfRewardImage: '',
    fundRewardImage: '',
    rule: '',
    desSignUpBtn: '',
    baseNumber: '',
    endList: '',
    file: '',
  });
  // 所有页面信息
  const [allCompetitionInfo, setAllCompetitionInfo] = useState<allCompetitionInfo>();
  // 退赛名单是否上传
  const [status, setStatus] = useState<string>('0');
  // 黑名单是否上传
  const [blackStatus, setBlackStatus] = useState<string>('0');
  useEffect(() => {
    if (location.state) {
      // 初始化大赛相关信息
      if (location.state['infoConfig']) {
        let infoConfig = location.state['infoConfig'];
        setCompetitionInfo(infoConfig);
        setStatus(infoConfig.endList);
        setBlackStatus(infoConfig.file);
      }
      setAllCompetitionInfo(location.state);
    }
  }, []);
  // 保存
  const onSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    validateFields((errors: unknown, values: Competition<string>) => {
      if (!errors) {
        const {
          headImage,
          rule,
          baseNumber,
          desSignUpBtn,
          etfRewardImage,
          fundRewardImage,
        } = values;
        let { endList, file } = competitionInfo;
        // 判断是否有上传名单
        if (values['endList']) {
          if (values['endList'] !== 'onlyStatus') {
            let formData = new FormData();
            formData.append('endList', values['endList']);
            endList = '1';
            postWithdrawalList(formData)
              .then((res: { code: string; message: string }) => {
                if (res.code !== '0000') {
                  message.error(res.message);
                }
              })
              .catch((err: unknown) => {
                console.log(err);
                message.error('网络请求错误，请稍后再试');
              });
          }
        }
        if (values['file']) {
          if (values['file'] !== 'onlyStatus') {
            let formData = new FormData();
            formData.append('file', values['file']);
            file = '1';
            postCompetitionBlacklist(formData)
              .then((res: { status_code: number; status_msg: string }) => {
                if (res.status_code !== 0) {
                  message.error(res.status_msg);
                }
              })
              .catch((err: unknown) => {
                console.log(err);
                message.error('网络请求错误，请稍后再试');
              });
          }
        }
        postFirmOffer({
          value: JSON.stringify({
            ...allCompetitionInfo,
            infoConfig: {
              editer: store.get('name'),
              time: moment().format('YYYY-MM-DD HH:mm:ss'),
              headImage,
              rule,
              desSignUpBtn,
              baseNumber,
              etfRewardImage,
              fundRewardImage,
              endList: endList,
              file: file,
            },
          }),
        })
          .then((res: FirmOfferCompetition) => {
            if (res.code === '0000') {
              message.success('保存成功');
              history.push('list');
            } else {
              message.error(res.message);
            }
          })
          .catch((err: unknown) => {
            console.log(err);
            message.error('网络请求错误，请稍后再试');
          });
      }
    });
  };
  // 宣传头图
  const uploadImage = (label: string, formId: string, value: string) => {
    return (
      <Form.Item label={label}>
        {getFieldDecorator(formId, {
          rules: [
            {
              required: true,
              message: '不能为空',
            },
          ],
          initialValue: value,
        })(<UploadImg ifFormItem={true} width={1125} height={358} />)}
      </Form.Item>
    );
  };
  return (
    <article>
      <Form onSubmit={onSubmit} layout="vertical" {...formLayout}>
        {uploadImage('未报名宣传头图', 'headImage', competitionInfo.headImage)}
        {uploadImage('场内宣传头图', 'etfRewardImage', competitionInfo.etfRewardImage)}
        {uploadImage('场外宣传头图', 'fundRewardImage', competitionInfo.fundRewardImage)}
        <Form.Item label="比赛规则">
          {getFieldDecorator('rule', {
            rules: [
              {
                required: true,
                message: '不能为空',
              },
            ],
            initialValue: competitionInfo.rule,
          })(<Input />)}
        </Form.Item>
        <Form.Item label="报名按钮文描">
          {getFieldDecorator('desSignUpBtn', {
            rules: [
              {
                required: true,
                message: '不能为空',
              },
            ],
            initialValue: competitionInfo.desSignUpBtn,
          })(<Input />)}
        </Form.Item>
        <Form.Item label="领奖黑名单" {...formItemLayout}>
          {getFieldDecorator('file')(
            <UploadExcel
              tip={'请上传xlsx文档，每行填入一个基金客户号（cust_id），第一行不要填数据'}
              status={blackStatus}
              title="实盘大赛领奖黑名单.xls"
            />,
          )}
        </Form.Item>
        <Form.Item label="退赛名单" {...formItemLayout}>
          {getFieldDecorator('endList')(
            <UploadExcel
              tip={'请上传xlsx文档，每行填入一个同花顺账号（user_id），第一行不要填数据'}
              status={status}
              title="实盘大赛退赛名单.xls"
            />,
          )}
        </Form.Item>
        <Form.Item label="报名人数累加基数">
          {getFieldDecorator('baseNumber', {
            initialValue: competitionInfo.baseNumber,
          })(<Input />)}
        </Form.Item>
        <Form.Item>
          <Button
            onClick={() => history.push('list')}
            style={{ marginLeft: '135px', marginRight: '20px' }}
          >
            取消
          </Button>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
        </Form.Item>
      </Form>
    </article>
  );
};
// 上传excel组件
const UploadExcel = (props: any) => {
  const [fileList, setFileList] = useState<UploadFile<any>[]>();
  const uploadProps = {
    beforeUpload: (file: File) => {
      const fileReader = new FileReader();
      fileReader.onload = (event: any) => {
        try {
          const { result } = event.target;
          // 以二进制流方式读取得到整份excel表格对象
          const workbook = XLSX.read(result, { type: 'binary' });
          let data: any = []; // 存储获取到的数据
          // 遍历每张工作表进行读取（这里默认只读取第一张表）
          for (const sheet in workbook.Sheets) {
            // 利用 sheet_to_json 方法将 excel 转成 json 数据
            data = XLSX.utils.sheet_to_json(workbook.Sheets[sheet], {
              header: ['user_id'], // 如果header被指定，第一行就会被当做数据行；如果header未指定，第一行是header并且不作为数据。
            });
            break;
          }
          console.log(data);
          // 是否为数字
          const ifNumber = (value: any) => {
            const ifString = typeof value === 'string';
            let ifAllNumber = true;
            if (ifString) {
              ifAllNumber = /^\d+$/.test(value);
            }
            return ifAllNumber;
          };
          if (
            file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.type === 'application/vnd.ms-excel'
          ) {
            // 第一行为一些数据时数据会失效，所以第一行不能为数据
            if (data[0].__rowNum__ === 0 && ifNumber(data[0].user_id)) {
              return message.error('请上传正确格式的excel');
            } else {
              setFileList([
                {
                  uid: '1',
                  status: 'done',
                  name: file.name,
                  size: file.size,
                  type: file.type,
                },
              ]);
              props.onChange(file);
            }
          } else {
            return message.error('请上传正确格式的excel');
          }
        } catch (e) {
          return message.error('请上传正确文件类型');
        }
      };
      fileReader.readAsBinaryString(file);
      return false;
    },
    onRemove: () => false,
  };
  // 下载名单
  const handleDownLoad = () => {
    if (props.title === '实盘大赛退赛名单.xls') {
      getWithdrawalList({}, '', '', { responseType: 'blob' })
        .then((res: any) => {
          if (!res.success) message.error(res.message);
        })
        .catch((err: unknown) => {
          message.warn('网络请求错误，请稍后重试');
          console.warn(err);
        });
    }
    if (props.title === '实盘大赛领奖黑名单.xls') {
      getCompetitionBlacklist({}, '', '', { responseType: 'blob' })
        .then((res: any) => {
          if (!res.success) message.error(res.message);
        })
        .catch((err: unknown) => {
          message.warn('网络请求错误，请稍后重试');
          console.warn(err);
        });
    }
  };
  useEffect(() => {
    if (props.status === '1') {
      setFileList([
        {
          uid: '1',
          status: 'done',
          name: props.title,
          size: 0,
          type: '',
        },
      ]);
      props.onChange('onlyStatus');
    }
  }, [props.status]);
  return (
    <section style={{ display: 'flex' }}>
      <Upload {...uploadProps} fileList={fileList}>
        <Button style={{ marginRight: '100px' }}>
          <Icon type="upload" /> 选择文件
        </Button>
      </Upload>
      <Button onClick={handleDownLoad}>
        <Icon type="download" /> 下载名单
      </Button>
      <span style={{ color: 'red', marginLeft: '20px' }}>{props.tip}</span>
    </section>
  );
};

export default Form.create<FormComponentProps>()(CompetitionInfo);
