import React, { useState, useEffect } from 'react';
// 配置文件
import FORM_JSON from './form.json';
// 接口
import api from 'api';
// 样式
import styles from './index.less';
// 组件
import FormRender from 'form-render/lib/antd';
import { Button, message } from 'antd';
// 路由
import { history } from 'umi';
// TypeScript
import { allCompetitionInfo, recommendFund, FirmOfferCompetition, CusRouter } from '../tsFile';
// webStorage库
import store from 'store';
// 时间工具库
import moment from 'moment';

const { postFirmOffer } = api;

export default function({ location }: { location: CusRouter }) {
  const [formData, setFormData] = useState<recommendFund<string>>();
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);
  // 所有页面信息
  const [allCompetitionInfo, setAllCompetitionInfo] = useState<allCompetitionInfo>();
  useEffect(() => {
    if (location.state) {
      // 初始化大赛相关信息
      if (location.state['recommendConfig']) {
        let fundDisplay: any = location.state['recommendConfig'].value;
        setFormData(fundDisplay);
      }
      setAllCompetitionInfo(location.state);
    }
  }, []);
  // 提交表单信息
  const handleForm = () => {
    setShowValidate(true);
    if (formValid.length === 0 && formData) {
      setShowValidate(false);
      postFirmOffer({
        value: JSON.stringify({
          ...allCompetitionInfo,
          recommendConfig: {
            value: {
              ...formData,
            },
            editer: store.get('name'),
            time: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
        }),
      })
        .then((res: FirmOfferCompetition) => {
          if (res.code === '0000') {
            message.success('保存成功');
            history.push('list');
          } else {
            message.error(res.message);
          }
        })
        .catch((err: unknown) => {
          console.log(err);
          message.error('网络请求错误，请稍后再试');
        });
    }
  };
  return (
    <article className={styles['m-fund-display']}>
      <FormRender
        propsSchema={FORM_JSON}
        formData={formData ? formData : {}}
        onChange={setFormData}
        onValidate={setFormValid}
        showValidate={showValidate}
        displayType="row"
      />
      <Button
        onClick={() => history.push('list')}
        style={{ marginLeft: '300px', marginRight: '40px' }}
      >
        取消
      </Button>
      <Button type="danger" onClick={handleForm}>
        保存
      </Button>
    </article>
  );
}
