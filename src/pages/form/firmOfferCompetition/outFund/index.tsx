import React, { useState, useEffect } from 'react';
// 配置文件
import FORM_JSON from './form.json';
// 接口
import api from 'api';
// 样式
import styles from './index.less';
// 组件
import { UploadImg } from '../component/index';
import FormRender from 'form-render/lib/antd';
import { Button, message } from 'antd';
// 路由
import { history } from 'umi';
// TypeScript
import { allCompetitionInfo, OutSideFund, FirmOfferCompetition, CusRouter } from '../tsFile';
// 时间工具库
import moment from 'moment';
// 国际化
import 'moment/locale/zh-cn';
// webStorage库
import store from 'store';

const { postFirmOffer } = api;
type FormRenderProps = {
  name: string;
  value: string;
  onChange: (name: string, value: string) => void;
};

export default function({ location }: { location: CusRouter }) {
  const [formData, setFormData] = useState<OutSideFund<string>>();
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);
  // 所有页面信息
  const [allCompetitionInfo, setAllCompetitionInfo] = useState<allCompetitionInfo>();
  useEffect(() => {
    if (location.state) {
      // 初始化大赛相关信息
      if (location.state['fundOperationConfig']) {
        let fundOperationConfig = location.state['fundOperationConfig'];
        setFormData(fundOperationConfig);
      }
      setAllCompetitionInfo(location.state);
    }
  }, []);
  // 处理表单信息
  const handleForm = () => {
    setShowValidate(true);
    if (formValid.length === 0 && formData) {
      setShowValidate(false);
      postFirmOffer({
        value: JSON.stringify({
          ...allCompetitionInfo,
          fundOperationConfig: {
            ...formData,
            editer: store.get('name'),
            time: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
        }),
      })
        .then((res: FirmOfferCompetition) => {
          if (res.code === '0000') {
            message.success('保存成功');
            history.push('list');
          } else {
            message.error(res.message);
          }
        })
        .catch((err: unknown) => {
          console.log(err);
          message.error('网络请求错误，请稍后再试');
        });
    }
  };
  const UploadMainImg = ({ onChange, name, value }: FormRenderProps) => {
    return (
      <UploadImg
        ifFormItem={false}
        width={180}
        height={180}
        name={name}
        onChange={onChange}
        value={value}
      />
    );
  };
  const UploadPositionImg = ({ onChange, name, value }: FormRenderProps) => {
    return (
      <UploadImg
        ifFormItem={false}
        width={686}
        height={160}
        name={name}
        onChange={onChange}
        value={value}
      />
    );
  };
  const UploadRankImg = ({ onChange, name, value }: FormRenderProps) => {
    return (
      <UploadImg
        ifFormItem={false}
        width={686}
        height={196}
        name={name}
        onChange={onChange}
        value={value}
      />
    );
  };
  return (
    <article className={styles['m-outside-fund']}>
      <FormRender
        propsSchema={FORM_JSON}
        formData={formData ? formData : {}}
        onChange={setFormData}
        onValidate={setFormValid}
        showValidate={showValidate}
        displayType="row"
        widgets={{
          UploadMainImg,
          UploadPositionImg,
          UploadRankImg,
        }}
      />
      <Button
        onClick={() => history.push('list')}
        style={{ marginLeft: '200px', marginRight: '40px' }}
      >
        取消
      </Button>
      <Button type="danger" onClick={handleForm}>
        保存
      </Button>
    </article>
  );
}
