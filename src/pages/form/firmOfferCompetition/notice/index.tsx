import React, { useState, useEffect } from 'react';
// 路由
import { history } from 'umi';
// 接口
import api from 'api';
// 组件
import { Button, message } from 'antd';
import { UploadImg } from '../component';
import FormRender from 'form-render/lib/antd';
// TypeScript
import { allCompetitionInfo, NoticeFormData, FirmOfferCompetition, CusRouter } from '../tsFile';
// webStorage库
import store from 'store';
// 时间工具库
import moment from 'moment';
// 国际化
import 'moment/locale/zh-cn';
// 样式
import styles from './index.less';
// 配置文件
import FORM_JSON from './form.json';

const { postFirmOffer } = api;
type FormRenderProps = {
  name: string;
  value: string;
  onChange: (name: string, value: string) => void;
};

export default function({ location }: { location: CusRouter }) {
  const [formData, setFormData] = useState<NoticeFormData<string>>();
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);
  // 所有页面信息
  const [allCompetitionInfo, setAllCompetitionInfo] = useState<allCompetitionInfo>();
  useEffect(() => {
    if (location.state) {
      // 初始化大赛相关信息
      if (location.state['noticeConfig']) {
        let noticeConfigInfo = location.state['noticeConfig'];
        setFormData(noticeConfigInfo);
      }
      setAllCompetitionInfo(location.state);
    }
  }, []);
  // 提交表单信息
  const handleForm = () => {
    setShowValidate(true);
    if (formValid.length === 0 && formData) {
      setShowValidate(false);
      const {
        noticeContent,
        noticeUrl,
        startTime,
        endTime,
        totalListRewardImage,
        monthListRewardImage,
        weekListRewardImage,
      } = formData;
      postFirmOffer({
        value: JSON.stringify({
          ...allCompetitionInfo,
          noticeConfig: {
            noticeContent,
            noticeUrl,
            startTime,
            endTime,
            totalListRewardImage,
            monthListRewardImage,
            weekListRewardImage,
            editer: store.get('name'),
            time: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
        }),
      })
        .then((res: FirmOfferCompetition) => {
          if (res.code === '0000') {
            message.success('保存成功');
            history.push('list');
          } else {
            message.error(res.message);
          }
        })
        .catch((err: unknown) => {
          console.log(err);
          message.error('网络请求错误，请稍后再试');
        });
    }
  };
  const UploadImage = ({ onChange, name, value }: FormRenderProps) => {
    return (
      <UploadImg
        ifFormItem={false}
        width={686}
        height={196}
        name={name}
        onChange={onChange}
        value={value}
      />
    );
  };
  return (
    <article className={styles['m-etf-notice']}>
      <FormRender
        propsSchema={FORM_JSON}
        formData={formData ? formData : {}}
        onChange={setFormData}
        onValidate={setFormValid}
        showValidate={showValidate}
        displayType="row"
        widgets={{
          UploadImg: UploadImage,
        }}
      />
      <Button
        onClick={() => history.push('list')}
        style={{ marginLeft: '200px', marginRight: '40px' }}
      >
        取消
      </Button>
      <Button type="danger" onClick={handleForm}>
        保存
      </Button>
    </article>
  );
}
