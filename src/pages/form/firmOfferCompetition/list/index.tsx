import React, { useEffect, useState } from 'react';
import { Table, Button, message } from 'antd';
import api from 'api';
import { allCompetitionInfo, FirmOfferCompetition } from '../tsFile';
import store from 'store';
import { history } from 'umi';

const { getFirmOffer, postRealFirmOffer } = api;

enum pageName {
  competition = '大赛相关信息',
  fundDisplay = '推荐基金展示',
  notice = '公告及榜单 - 场内ETF',
  mainVenue = '主会场运营位 - 场内ETF',
  position = '持仓运营位 - 场内ETF',
  fund = '相关运营位 - 场外基金',
}
type Record = {
  module: string;
  lastEditor: string;
  lastTime: string;
};

export default function() {
  const columns = [
    {
      dataIndex: 'module',
      title: '模块',
    },
    {
      dataIndex: 'lastEditor',
      title: '最后编辑人',
    },
    {
      dataIndex: 'lastTime',
      title: '最后编辑时间',
    },
    {
      dataIndex: 'options',
      title: '操作',
      render: (text: unknown, record: Record) => (
        <Button onClick={() => handleEdit(record)}>编辑</Button>
      ),
    },
  ];
  const [dataSource, setDataSource] = useState<Record[]>();
  const [allState, setAllState] = useState<allCompetitionInfo>();
  useEffect(() => {
    // 获取通用接口保存的信息
    getFirmOffer()
      .then((res: FirmOfferCompetition) => {
        if (res.code === '0000') {
          function tempFn(objName: string) {
            if (res.data && JSON.parse(res.data)[objName]) {
              return JSON.parse(res.data)[objName];
            } else {
              return '';
            }
          }
          if (res.data) {
            setAllState(JSON.parse(res.data));
          }
          setDataSource([
            {
              module: '大赛相关信息',
              lastEditor: tempFn('infoConfig').editer,
              lastTime: tempFn('infoConfig').time,
            },
            {
              module: '推荐基金展示',
              lastEditor: tempFn('recommendConfig').editer,
              lastTime: tempFn('recommendConfig').time,
            },
            {
              module: '公告及榜单 - 场内ETF',
              lastEditor: tempFn('noticeConfig').editer,
              lastTime: tempFn('noticeConfig').time,
            },
            {
              module: '主会场运营位 - 场内ETF',
              lastEditor: tempFn('mainOperationConfig').editer,
              lastTime: tempFn('mainOperationConfig').time,
            },
            {
              module: '持仓运营位 - 场内ETF',
              lastEditor: tempFn('holdOperationConfig').editer,
              lastTime: tempFn('holdOperationConfig').time,
            },
            {
              module: '相关运营位 - 场外基金',
              lastEditor: tempFn('fundOperationConfig').editer,
              lastTime: tempFn('fundOperationConfig').time,
            },
          ]);
        } else {
          message.error(res.message);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
        message.error('网络请求错误，请稍后再试');
      });
  }, []);
  const handleEdit = (record: Record) => {
    const { competition, notice, mainVenue, position, fund, fundDisplay } = pageName;
    function toPage(name: string) {
      history.push({
        pathname: name,
        state: allState,
      });
    }
    switch (record.module) {
      case competition:
        toPage('competitionInfo');
        break;
      case fundDisplay:
        toPage('fundDisplay');
        break;
      case notice:
        toPage('notice');
        break;
      case mainVenue:
        toPage('mainVenue');
        break;
      case position:
        toPage('position');
        break;
      case fund:
        toPage('outFund');
        break;
      default:
        break;
    }
  };
  // 保存并发布
  const handlePublish = () => {
    // 获取通用接口保存的信息
    getFirmOffer()
      .then((res: FirmOfferCompetition) => {
        if (res.code === '0000') {
          if (res.data) {
            // 发布者和所有模块中最后编辑的最后编辑人不为同一人。
            let obj = JSON.parse(res.data);
            let arr = Object.keys(obj);
            let editer = store.get('name');
            let ifPass = true;
            let maxTime = '1970-01-01 00:00:00';
            let lastEditer = '';
            arr.map(item => {
              if (obj[item].time > maxTime) {
                maxTime = obj[item].time;
                lastEditer = obj[item].editer;
              }
            });
            lastEditer === editer ? (ifPass = false) : void 0;
            if (!ifPass) {
              message.error('最后一次保存用户与发布用户不能为同一人');
            } else {
              _postRealFirmOffer(obj);
            }
          }
        } else {
          message.error(res.message);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
        message.error('网络请求错误，请稍后再试');
      });
    // 发布信息
    function _postRealFirmOffer(allState: allCompetitionInfo) {
      postRealFirmOffer({
        value: JSON.stringify(allState),
      })
        .then((res: FirmOfferCompetition) => {
          if (res.code === '0000') {
            message.success('发布成功');
          } else {
            message.error(res.message);
          }
        })
        .catch((err: unknown) => {
          console.log('postRealFirmOffer', err);
          message.error('网络请求错误，请稍后再试');
        });
    }
  };
  return (
    <article>
      <Button type="primary" style={{ marginBottom: '20px' }} onClick={handlePublish}>
        保存并发布
      </Button>
      <Table columns={columns} dataSource={dataSource} pagination={false}></Table>
    </article>
  );
}
