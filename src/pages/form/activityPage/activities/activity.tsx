import React, {useEffect, useState} from 'react'
import FORM_JSON from './form.json'
import FormRender from 'form-render/lib/antd';
import {But<PERSON>, Popconfirm, message} from 'antd';
import api from 'api';

const {
    handleActivity,
    fetchFundAwardRegister
} = api

export default function({
    isAdd = false,
    activity,
    setIsShowAdd = () => {}
}: {
    isAdd?: boolean
    activity?: any
    setIsShowAdd: Function
}) {

    const [formData, setData] = useState(FORM_JSON.formData);
    const [valid, setValid] = useState([]);
    const [schema, setSchema] = useState(FORM_JSON.schema)

    useEffect(() => {
        if (!activity) return
        setData(activity)
    } ,[activity])

    useEffect(() => {
        if (!isAdd) return
        let _schema = JSON.parse(JSON.stringify(schema))
        delete _schema.properties.activityId
        setSchema(_schema)
    }, [isAdd])

    function add() {
        if (valid && valid.length > 0) return message.error('请检查输入')
        handleActivity({
            type: "insert",
            value: JSON.stringify(formData)
        }).then((res: any) => {
            if (res.code !== '0000') return message.error(res.message)
            setIsShowAdd(false)
            message.success('提交成功！')
            window.location.reload(true)
        })
    }

    function update() {
        if (valid && valid.length > 0) return message.error('请检查输入')
        handleActivity({
            type: "update",
            value: JSON.stringify(formData)
        }).then((res: any) => {
            if (res.code !== '0000') return message.error(res.message)
            setIsShowAdd(false)
            message.success('提交成功！')
            window.location.reload(true)
        })
    }

    function downLoadFundActivity () {
        if (!formData.activityId) {
            message.error('请填写活动ID');
            return;
        }
        fetchFundAwardRegister({},
            formData.activityId,
            ``,
            {
            responseType: 'blob'
        }).then((data: any) => {
            if (data.data && data.data.size === 0) message.error('下载失败，请查看参数是否正确或稍后再试')
        })
    }


    return (
        <div>
            <FormRender
                propsSchema={schema}
                uiSchema={{}}
                onValidate={setValid}
                formData={formData}
                onChange={setData}
                displayType="row"
                showDescIcon={true}
                column={2}
            />
            {
                isAdd ?
                <div className="u-r-middle" style={{margin: 20}}>
                    <Button type="primary" onClick={() => {setIsShowAdd(false)}} style={{marginRight: 20}}>取消</Button>
                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要提交么'}
                        onConfirm={add}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button
                            type="danger" 
                        >
                            提交
                        </Button>
                    </Popconfirm>
                </div>
                :
                <div className="u-r-middle" style={{margin: 20}}>
                     <Button type="primary" style={{marginRight: 20}} onClick={downLoadFundActivity}>
                        基金奖励导出
                    </Button>
                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要修改么'}
                        onConfirm={update}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button
                            type="danger" 
                        >
                            提交修改
                        </Button>
                    </Popconfirm>
                </div>
            }
        </div>
    )
}