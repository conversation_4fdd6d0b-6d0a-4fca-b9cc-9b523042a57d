import React, {useEffect, useState} from 'react'
import api from 'api';
import Activity from './activity'
import {Button,message, Card, Collapse, Modal, Drawer, Table} from 'antd';

const {
    fetchETFActivity,
    fetchETFActivityAward
} = api

interface ETFActivityItem {
    "activityId": string
    "type": string
    "cardType": string
    "activityName": string
    "startDate": string
    "endDate": string
    "status": string
    "title": string
    "subTitle": string
    "tipTitle": string
    "tipContext": string
    "recommendTitle": string
    "recommendCode": string
    "recommendName": string
    "profitDescribe": string
    "productTitle": string
    "purchaseContext": string
    "windowText": string
    "activityRules": string | any
    "codeRate": string
    "cashVoucherList": any
}

export default function () {
    const [activities, setActivities] = useState([] as ETFActivityItem[]) //列表
    const [isShowAdd, setIsShowAdd] = useState(false) //是否显示modal
    const [selectedActivity, setSelectedActivity] = useState({}) //选中的活动
    const [isAdd, setIsAdd] = useState(true) //是否为添加
    
const columns = [
    {
      title: '项目id',
      dataIndex: 'activityId',
      key: 'activityId'
    },
    {
      title: '项目名称',
      dataIndex: 'activityName',
      key: 'activityName',
    },
    {
      title: '起始时间',
      dataIndex: 'startDate',
      key: 'startDate',
    },
    {
        title: '中止时间',
        dataIndex: 'endDate',
        key: 'endDate',
    },
    {
      title: '按钮',
      key: 'button',
      render: (item: any) => {
          return (
            <>
                <Button type="primary" style={{marginRight: 20}} onClick={() => {
                    queryActivity(item.activityId)
                    }}>查看全部</Button>
            </>
            )
        }
    }
  ];

    useEffect(() => {
        handleFetchETFActivity();
    }, [])

    /**
     * 查询ETF活动列表
     */

    function handleFetchETFActivity() {
        _.fundLoading()
        fetchETFActivity({
            type: 'queryAll'
        }).then((data: any) => {
            _.hideFundLoading()
            if (data.code === '0000') {
                setActivities(data.data)
            } else {
                message.error(data.message)
            }
        }).catch(() => {
            _.hideFundLoading()
        })
    }

    /**
     * 查询单个
     * @param activityId 
     */
    function queryActivity(activityId: string) {
        _.fundLoading()
        fetchETFActivity({
            type: 'query',
            activityId
        }).then((data: any) => {
            _.hideFundLoading()
            if (data.code === '0000') {
                data.data.activityRules = JSON.parse(data.data.activityRules)
                if (data.data.codeRate) data.data.codeRate = Number(data.data.codeRate)
                if (!data.data.exchangeCodeConfig) {
                    data.data.exchangeCodeConfig = {};
                }
                setSelectedActivity(data.data)
                setIsAdd(false)
                setIsShowAdd(true)
            } else {
                message.error(data.message)
            }
        }).catch(() => {
            _.hideFundLoading()
        })
    }

    function showAdd() {
        setSelectedActivity({})
        setIsAdd(true)
        setIsShowAdd(true)
    }

    return (
        <div>
            <Table columns={columns} dataSource={activities}></Table>
            <Button type="primary" style={{marginTop: '20px'}} onClick={showAdd}>增加活动</Button>

            <Drawer 
            title="增加活动"
            width={1200}
            onClose={() => {setIsShowAdd(false)}}
            visible={isShowAdd}
            bodyStyle={{paddingBottom: 80}}
            >
                <Activity 
                    isAdd = {isAdd}
                    isShowAdd = {isShowAdd}
                    activities = {activities}
                    activity = {selectedActivity}
                    setActivity = {setSelectedActivity}
                    setIsShowAdd = {setIsShowAdd}
                    handleFetchETFActivity={handleFetchETFActivity}
                />
            </Drawer>
        </div>
    )
}