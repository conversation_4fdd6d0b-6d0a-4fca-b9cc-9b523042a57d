import React, { useEffect, useState } from 'react';
import { Button, message, Collapse } from 'antd';
import api from 'api';
import MyCard from '../../component/myCard';
import ConfigSelect from '../../component/selectModel';
import LocationModel from '../../component/locationModel';

const { fetchscMixFund, fetchCBAS, fetchOLAS } = api;
const { Panel } = Collapse;
export default function () {
    const [init, setInit] = useState(true);
    const [originData, setOriginData] = useState<any>([]);
    const [isModify, setModify] = useState(false);
    const [config, setConfig] = useState<{ platform: string[] }>({ platform: [] })
    const [kycTag, setKycTag] = useState([]);
    const [olasTag, setOlasTag] = useState([]);
    const [activeKey, setActiveKey] = useState(0);

    const handleChange = (data: any) => {
        setConfig(data)
    }
    useEffect(() => {
        fetchCBAS().then((res: any) => {
            if (res.code === '0000' && res.data) {
                setKycTag(res.data)
            }
        })
    }, [])
    useEffect(() => {
        fetchOLAS().then((res: any) => {
            if (res.code === '0000' && res.data) {
                let _olasTag: any = []
                let data = res.data
                for (let prop in data) {
                    _olasTag.push({ "groupid": prop, "description": data[prop].description })
                    console.log(_olasTag)
                }
                setOlasTag(_olasTag)
            }
        })
    }, [])
    useEffect(() => {
        fetchscMixFund({
            operateType: 'query',
            type: 'ad',
        }).then((res: any) => {
            let { code, data } = res;
            if (code === '0000') {
                let _data: any = [];
                setInit(true);
                data?.confs?.forEach((val: any, index: number) => {
                    let obj = {
                        formData: {
                            adTitle: val?.adTitle,
                            adImg: val?.adImg,
                            adUrl: val?.adUrl,
                            startTime: val?.startTime,
                            endTime: val?.endTime,
                        },
                        relationData: {
                            targetType: val.targetType,
                            kycLogic: val.kycLogic,
                            kycs: val.kycs,
                            olasId: val.olasId
                        },
                        configData: {
                            platform: val?.platform ?? []
                        }
                    }
                    _data.push(obj)
                })
                setOriginData(_data)
            } else {
                message.error(res.message);
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])

    function onSubmit() {
        let arr: any = [];
        for (let i = 0,len = originData?.length; i < len; i++) {
            let val = originData[i];
            let _data = { ...val.configData, ...val.formData, ...val.relationData };
            let { startTime, endTime } = _data;
            if ( startTime && endTime ) {
                startTime = startTime?.replace(/[^\d]/g, '');
                endTime = endTime?.replace(/[^\d]/g, '');
                if (startTime >= endTime) {
                    message.error(`第${i+1}项结束时间应晚于开始时间`)
                    return;
                }
            }
            if (!_data.adTitle) {
                message.error(`请选择第${i+1}项广告名称`)
                return
            }
            if (!_data.adImg) {
                message.error(`请填写第${i+1}项广告图片`)
                return
            }
            if (!_data.adUrl) {
                message.error(`请选择第${i+1}项跳转链接`)
                return
            }
            if (_data.platform?.length === 0) {
                message.error(`请选择第${i+1}项适用平台`)
                return
            }
            arr.push(_data)
        }
        fetchscMixFund({
            operateType: 'update',
            type: 'ad',
            value: JSON.stringify({
                confs: arr
            }),
            lastEditor: localStorage.name
          }).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.message);
            } else {
                message.success('提交成功！');
                setModify(false);
                setTimeout(() => {
                    location.href = `#/form/scMixFund/tradeConfig`
                }, 1000);
            }
        }).catch((e: Error) => {
            message.error(e?.message);
        }) 
    }
    function addItem() {
        let obj = {
            formData: {
                adTitle: '',
                adImg: '',
                adUrl: '',
                startTime: '',
                endTime: '',
            },
            configData: {
                platform: config.platform,
            },
            relationData: {
                targetType: 'kyc',
                kycLogic: 'and',
                kycs: [],
                olasId: ''
            },
            isNew: true
        }
        let data = [...originData, obj];
        setOriginData(data)
        setActiveKey(data.length - 1);
    }

    function handleUpdate(data: any, index: number, isNew: boolean = false) {
        if (!isModify && !isNew) setModify(true)
        let _originData: any = [...originData];
        _originData[index] = data;
        setOriginData(_originData);
    }
    function handleDelete(index: number) {
        if (!isModify) setModify(true)
        console.log(index)
        let _originData: any = [...originData];
        _originData.splice(index, 1)
        setOriginData(_originData)
    }
    function handleSelect(item: any) {
        let tag = 0;
        if (item.configData.platform?.length === 0) {
            tag = 1;
        } else {
            for (let data of item.configData.platform) {
                if (config.platform.indexOf(data) != -1) {
                    tag = 1;
                    break;
                }
            }
        }
        return !!tag
    }
    function handleActiveKey (key: any) {
        setActiveKey(key);
    }
    if (!init) return '加载中';
    return <div>
        <LocationModel location='通栏广告' />
        <ConfigSelect handleChange={handleChange} isHead={true}/>
        <Button type="primary" onClick={onSubmit} disabled={!isModify}>保存</Button>
        <Collapse activeKey={activeKey} onChange={handleActiveKey}>
            {
                originData.map((item: any, index: number) => {
                    return handleSelect(item) ?
                <Panel header={(<span style={{height: 22, display: 'inline-block', verticalAlign: 'middle'}}>{`${index+1}    ${item.formData.adTitle}`}</span>)} key={index}>
                    <MyCard
                        data={item}
                        position={index}
                        handleDelete={handleDelete}
                        handleUpdate={handleUpdate}
                        key={index}
                        kycTag={kycTag}
                        olasTag={olasTag}
                        type={'columnAdvertisement'}
                    ></MyCard>
                </Panel>
                : null

                })
            }
        </Collapse>
        <Button onClick={addItem} type='primary' style={{ marginTop: '20px' }}>添加</Button>
    </div>
}
