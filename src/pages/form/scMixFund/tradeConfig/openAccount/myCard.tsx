import React, {useEffect, useState} from 'react';
import { Button, message, Popconfirm } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../index.less';
import ConfigSelect from '../../component/selectModel';
import TagModel from '../../component/tagModel';
import UploadFile from '../../component/uploadFile';
import UploadImg from './uploadImg'; 
import UploadImg2 from './uploadImg2'; 
import UploadImage3 from './brandFoldUrlUploadImage'; 
import UploadImage4 from './saleFoldUrlUploadImage'; 

interface dataProps {
    data: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
    kycTag: Array<string>;
    olasTag: Array<string>;
}

export default function (props: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);

    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [formData, setFormState] = useState<any>({});
    const [configData, setConfig] = useState<any>({});
    const [relationData, setRelation] = useState({});
    const [fileExist, setFileExist] = useState({
        blackListFile: false,
        whiteListFile: false
    });
    const [fileList, setFileList] = useState<any>({}); // 文件列表

    useEffect(() => {
        console.log('formData=',props)
        setFormState(props.data?.formData)
        setConfig(props.data?.configData)
        setRelation(props.data?.relationData)
        setFileExist(props.data?.fileExist ?? {
            blackListFile: false,
            whiteListFile: false
        })
    }, [props.data])
    useEffect(()=>{
        console.log('form,Confg',formConfig)
    },[formConfig])
    async function handleChange(){
        if(isEdit){
            if (valid.length > 0) {
                return;
            } else {
                if (configData?.platform?.length > 0) {
                    const fileFormData = new FormData();
                    const { whiteListFile, blackListFile } = fileList;
                    if ( whiteListFile || blackListFile ) {
                        fileFormData.append('type', 'openAccount');
                        fileFormData.append('configIndex', `${props.position}`);
                        if (whiteListFile) {
                            fileFormData.append('whiteListFile', whiteListFile);
                        }
                        if (blackListFile) {
                            fileFormData.append('blackListFile', blackListFile);
                        }
                    }
                    let _relationData: any = {...relationData};
                    if (_relationData?.targetType === 'kyc') {
                        _relationData.olasId = [];
                    } else if (_relationData?.targetType === 'olas') {
                        _relationData.kycLogic = '';
                        _relationData.kycs = [];
                    } 
                    let _data = {
                        formData,
                        configData,
                        relationData: _relationData,
                        fileExist: props.data.fileExist
                    }
                    props.handleUpdate(_data, props.position, {[props.position]: fileFormData})
                } else {
                    message.error('适用平台不能为空')
                    return;
                }
            }
        }
        setEdit(!isEdit)
    }
    function handleSelect(data: any){
        console.log('platform',data)
        setConfig(data)
    }
    /**
     * kyc指定用户修改
     * @param data 修改的kyc标签
     */
    const handleRelationChange = (data: any) => {
        if(data.targetType === 'kyc' && !data.kycLogic) {
            data.kycLogic = 'and'
        }
        data = {
            ...relationData,
            ...data
        }
        setRelation(data)
    }
    /**
     * 下载黑白名单文件
     */
    const downloadFile = (type: string) => {
        // fetchAccountDownLoad({}, `?type=logon&filename=${type}&configIndex=${props.position}`, '', { responseType: 'blob' }).then((res: any) => {
        //     if (!res.success) message.error(res.message)
        // })
    }
    /**
     * 下载黑白名单模板
     */
    const downloadModel = (type: string) => {
        // fetchAccountTemplate({}, `?type=logon&filename=${type}`, '', { responseType: 'blob' }).then((res: any) => {
        //     if (!res.success) message.error(res.message)
        // })
    }
    /**
     * 保存指定黑白名单文件
     */
    const saveFile = (file: any, type: string) => {
        // let _fileList = {...fileList};
        // _fileList[type] = file;
        // setFileList(_fileList);
    }
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {props.handleDelete(props.position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={setValid}
                    onChange={setFormState}
                    readOnly={!isEdit}
                    widgets={{
                        uploadImg: UploadImg,
                        uploadImg2: UploadImg2,
                        brandFoldUrlUpload: UploadImage3,
                        uploadImg4: UploadImage4
                    }}
                />
                <ConfigSelect
                    handleChange={handleSelect}
                    isHead={false}
                    isEdit={isEdit}
                    data={configData}
                />
                <TagModel
                    handleChange={handleRelationChange}
                    kycTag={props.kycTag}
                    olasTag={props.olasTag}
                    isEdit={isEdit}
                    data={relationData}
                    fileType={'whiteListFile'}
                    isExist={fileExist['whiteListFile']}
                    downloadFile={() => downloadFile('whiteListFile')}
                    downloadModel={() => downloadModel('whiteListFile')}
                    saveFile={saveFile}
                />
                <UploadFile
                    name={'黑名单'}
                    position={props.position}
                    pageType={'openAccount'}
                    fileType={'blackListFile'}
                    saveFile={saveFile}
                    isEdit={isEdit}
                    describe={'请上传xlsx文档，每行填入一个基金客户号（cust_id），第一行不要填数据'}
                    isExist={fileExist['blackListFile']}
                />
                <UploadFile
                    name={'手动上传用户名单'}
                    position={props.position}
                    pageType={'openAccount'}
                    fileType={'whiteListFile'}
                    saveFile={saveFile}
                    isEdit={isEdit}
                    describe={'请上传xlsx文档，每行填入一个基金客户号（cust_id），第一行不要填数据'}
                    isExist={fileExist['whiteListFile']}
                />
            </div>

}