import React, { useEffect, useState } from 'react';
import { Button, message, Collapse } from 'antd';
import api from 'api';
import MyCard from '../../component/myCard';
import ConfigSelect from '../../component/selectModel';
import LocationModel from '../../component/locationModel';

const { fetchscMixFund, fetchCBAS, fetchOLAS } = api;
const { Panel } = Collapse;
export default function () {
    const [init, setInit] = useState(true);
    const [originData, setOriginData] = useState<any>([]);
    const [isModify, setModify] = useState(false);
    const [config, setConfig] = useState<{ platform: string[] }>({ platform: [] })
    const [kycTag, setKycTag] = useState([]);
    const [olasTag, setOlasTag] = useState([]);
    const [activeKey, setActiveKey] = useState(0);

    const handleChange = (data: any) => {
        setConfig(data)
    }
    useEffect(() => {
        fetchCBAS().then((res: any) => {
            if (res.code === '0000' && res.data) {
                setKycTag(res.data)
            }
        })
    }, [])
    useEffect(() => {
        fetchOLAS().then((res: any) => {
            if (res.code === '0000' && res.data) {
                let _olasTag: any = []
                let data = res.data
                for (let prop in data) {
                    _olasTag.push({ "groupid": prop, "description": data[prop].description })
                    console.log(_olasTag)
                }
                setOlasTag(_olasTag)
            }
        })
    }, [])
    useEffect(() => {
        fetchscMixFund({
            operateType: 'query',
            type: 'open',
        }).then((res: any) => {
            let { code, data } = res;
            if (code === '0000') {
                let _data: any = [];
                setInit(true);;
                data?.confs?.forEach((val: any, index: number) => {
                    let obj = {
                        formData: {
                            strategyName: val?.strategyName,
                            brandUrl: val?.brandUrl,
                            saleUrl: val?.saleUrl,
                            brandFoldUrl:val?.brandFoldUrl,
                            saleFoldUrl:val?.saleFoldUrl,
                            popPrompt: val?.popPrompt,
                            button1: val?.button1,
                            button2: val?.button2,
                            breakPoint1: val?.breakPoint1,
                            breakPoint2: val?.breakPoint2,
                        },
                        relationData: {
                            targetType: val.targetType,
                            kycLogic: val.kycLogic,
                            kycs: val.kycs,
                            olasId: val.olasId
                        },
                        configData: {
                            platform: val?.platform ?? []
                        },
                    }
                    _data.push(obj)
                })
                setOriginData(_data)
            } else {
                message.error(res.message);
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])

    function onSubmit() {
        let arr: any = [];
        for (let i = 0,len = originData?.length; i < len; i++) {
            let val = originData[i];
            let _data = { ...val.configData, ...val.formData, ...val.relationData };
            if (!_data.strategyName) {
                message.error(`请选择第${i+1}项转化策略名称`)
                return
            } 
            if (!_data.brandUrl) {
                message.error(`请选择第${i+1}项头部品牌宣传图`)
                return
            }
            if (!_data.saleUrl) {
                message.error(`请填写第${i+1}项营销转化图`)
                return
            }
            if (!_data.brandFoldUrl) {
                message.error(`请选择第${i+1}项头部品牌宣传图(折叠屏)`)
                return
            }
            if (!_data.saleFoldUrl) {
                message.error(`请填写第${i+1}项营销转换图(折叠屏)`)
                return
            }
            if (!_data.popPrompt) {
                message.error(`请选择第${i+1}项弹窗提示文描`)
                return
            }
            if (!_data.button1) {
                message.error(`请填写第${i+1}项按钮1文描`)
                return
            }
            if (!_data.button2) {
                message.error(`请选择第${i+1}项按钮2文描`)
                return
            }
            if (!_data.breakPoint1) {
                message.error(`请选择第${i+1}项开户断点阶段2文描`)
                return
            } 
            if (!_data.breakPoint2) {
                message.error(`请选择第${i+1}项开户断点阶段3文描`)
                return
            }
            if (_data.platform?.length === 0) {
                message.error(`请选择第${i+1}项适用平台`)
                return
            }
            arr.push(_data)
        }
        fetchscMixFund({
            operateType: 'update',
            type: 'open',
            value: JSON.stringify({
                confs: arr
            }),
            lastEditor: localStorage.name
          }).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.message);
            } else {
                message.success('提交成功！');
                setModify(false);
                setTimeout(() => {
                    location.href = `#/form/scMixFund/tradeConfig`
                }, 1000);
            }
        }).catch((e: Error) => {
            message.error(e?.message);
        })
    }
    function addItem() {
        let obj = {
            formData: {
                strategyName: '',
                brandUrl: '',
                saleUrl: '',
                popPrompt: '',
                button1: '',
                saleFoldUrl:'',
                brandFoldUrl:'',
                button2: '',
                breakPoint1: '',
                breakPoint2: '',
            },
            configData: {
                platform: config.platform,
            },
            relationData: {
                targetType: 'kyc',
                kycLogic: 'and',
                kycs: [],
                olasId: ''
            },
            isNew: true
        }
        let data = [...originData, obj];
        setOriginData(data)
        setActiveKey(data.length - 1);
    }

    function handleUpdate(data: any, index: number, isNew: boolean = false) {
        if (!isModify && !isNew) setModify(true)
        let _originData: any = [...originData];
        _originData[index] = data;
        setOriginData(_originData);
    }
    function handleDelete(index: number) {
        if (!isModify) setModify(true)
        console.log(index)
        let _originData: any = [...originData];
        _originData.splice(index, 1)
        setOriginData(_originData)
    }
    function handleSelect(item: any) {
        let tag = 0;
        if (item.configData.platform?.length === 0) {
            tag = 1;
        } else {
            for (let data of item.configData.platform) {
                if (config.platform.indexOf(data) != -1) {
                    tag = 1;
                    break;
                }
            }
        }
        return !!tag
    }
    function handleActiveKey (key: any) {
        setActiveKey(key);
    }
    if (!init) return '加载中';
    return <div>
        <LocationModel location='开户引导' />
        <ConfigSelect handleChange={handleChange} isHead={true}/>
        <Button type="primary" onClick={onSubmit} disabled={!isModify}>保存</Button>
        <Collapse activeKey={activeKey} onChange={handleActiveKey}>
            {
                originData.map((item: any, index: number) => {
                    return handleSelect(item) ?
                <Panel header={(<span style={{height: 22, display: 'inline-block', verticalAlign: 'middle'}}>{`${index+1}    ${item?.formData?.strategyName}`}</span>)} key={index}>
                    <MyCard
                        data={item}
                        position={index}
                        handleDelete={handleDelete}
                        handleUpdate={handleUpdate}
                        key={index}
                        kycTag={kycTag}
                        olasTag={olasTag}
                        type={'openAccount'}
                    ></MyCard>
                </Panel>
                : null

                })
            }
        </Collapse>
        <Button onClick={addItem} type='primary' style={{ marginTop: '20px' }}>添加</Button>
    </div>
}
