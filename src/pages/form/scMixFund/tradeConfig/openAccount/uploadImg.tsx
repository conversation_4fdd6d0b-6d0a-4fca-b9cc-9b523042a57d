import React from 'react';
import ImgUpload from '../../component/uploadImg/index.jsx';
interface dataProps {
    onChange: Function,
    value: string,
    readonly: boolean
}

export default function (props:dataProps) {
    console.log('image', props);
    return <div style={{width: 514}}>
                <ImgUpload 
                        handleChange={(value: any) => props.onChange('brandUrl', value)}
                        imageUrl={props.value}
                        isEdit={props.readonly}
                        title=''
                    />
            </div>

}