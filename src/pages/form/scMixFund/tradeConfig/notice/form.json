{"schema": {"type": "object", "properties": {"announceContent": {"title": "公告内容", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "announceUrl": {"title": "公告跳转链接", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "60%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "60%"}}, "required": ["announce<PERSON>ontent"]}, "displayType": "row", "showDescIcon": true, "labelWidth": 120}