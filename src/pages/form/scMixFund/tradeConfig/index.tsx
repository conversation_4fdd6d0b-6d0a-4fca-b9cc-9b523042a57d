import React, { useState, useEffect } from 'react';
import api from 'api';
import { router } from 'umi';
import styles from '../index.less';
import { Button, Popconfirm, message, Row, Col } from 'antd';

const { queryListscMixFund, submitscMixFund } = api;

interface iTradeSource {
    type: string
    index: number
    lastEditor: string
    editorTime: string
    [key: string]: any
}
function TradeConfig() {
    const [init, setInit] = useState(false);
    const [dataSource, setDataSource] = useState<iTradeSource[]>([
        { type: "openAccount", index: 0, name: '开户引导', lastEditor: '', editorTime: '', title: ''},
        { type: "notice", index: 1, name: '公告', lastEditor: '', editorTime: '', title: ''},
        { type: "columnAdvertisement", index: 2, name: '通栏广告', lastEditor: '', editorTime: '', title: ''},
        { type: "chooseFundTool", index: 3, name: '选基工具', lastEditor: '', editorTime: '', title: ''},
    ]);
    useEffect(() => {
      queryListscMixFund().then((res: any)=>{
          const { code, data } = res;
          if (code === '0000') {
            let _dataSource = [...dataSource];
            let arr: any = [];
            if (data) {
              Object.keys(data)?.forEach((key) => {
                let obj = data[key];
                switch(key) {
                  case 'open':
                    obj.type = 'openAccount';
                    break;
                  case 'announce':
                    obj.type = 'notice';
                    break;
                  case 'ad':
                    obj.type = 'columnAdvertisement';
                    break;
                  case 'select':
                    obj.type = 'chooseFundTool';
                    break;
                }
                arr.push(obj);
              })
            }
            arr?.forEach((val: any)=> {
              const { type, index, lastEditor, editorTime, title } = val;
              let _target = _dataSource.find((item) => item.type === type)
              if (_target) {
                _target.lastEditor = lastEditor;
                _target.editorTime = editorTime;
                _target.index = index;
                _target.title = title;
              }
            })
            _dataSource.sort((a,b)=> a.index - b.index);
            setDataSource(_dataSource);
            setInit(true);
          } else {
            message.error(res?.message || '系统繁忙');
          }
          
      }).catch((e: Error) => {
        message.error(e?.message);
      })
    },[])

    const handleSubmit = (num: number) => { 
      if (num === 1) {
        let _data = [...dataSource]
        _data.sort((a, b) =>  (a.editorTime || b.editorTime) ? (new Date(b.editorTime)?.getTime() - new Date(a.editorTime)?.getTime()) : 1)
        console.log(_data)
        if(_data[0].lastEditor === localStorage.name){
            message.error('最后一次保存用户与发布用户不能为同一人')
            return
        }
      }
      submitscMixFund().then((res: any)=>{
          if (res.code === '0000') {
            message.success('发布成功！');
            setTimeout(()=>location.reload(),1000)
          } else {
              message.error(res.message);
          }
      }).catch((e: Error) => {
        message.error(e?.message);
      })
      
    } 
    const handleEdit = (type: string) => {
      history.push(`/form/scMixFund/tradeConfig/${type}`)
    }

    if (!init) {
        return '加载中'
    }
    return (
      <div className={styles['m-drag']}>
        <div className={'g-mb20'}>
            <span className={'g-ml20'}>
                <Popconfirm
                    title="确定保存并发布？"
                    onConfirm={() => handleSubmit(1)}
                    okText="确定"
                    cancelText="取消"
                >
                    <Button type="primary">保存并发布</Button>
                </Popconfirm>
                
            </span>
            <span className={'g-ml20'}>
                <Popconfirm
                    title="确定保存并发布？"
                    onConfirm={() => handleSubmit(2)}
                    okText="确定"
                    cancelText="取消"
                >
                    <Button type="primary">保存并发布(测试环境使用，无人员校验)</Button>
                </Popconfirm>
                
            </span>
            
            
        </div>
        <div  className={styles['m-head']}>
            <Row className={styles['m-row']}>
                <Col span={3}>配置模块</Col>
                <Col span={12}>内容</Col>
                <Col span={3}>最后编辑人</Col>
                <Col span={3}>最后编辑时间</Col>
                <Col span={3}>操作</Col>
            </Row> 
            
        </div>   
        {dataSource.map((tag, index) => {
          return <div className={styles['tag']} key={index}>
            <Row className={styles['m-row-top']}>
              <Col span={3}>{tag.name}</Col>
              <Col span={12} className={styles['tag-content']}>{tag.title}</Col>
              <Col span={3}>{tag.lastEditor}</Col>
              <Col span={3}>{tag.editorTime}</Col>
              <Col span={3}>
                  <Button onClick={()=> handleEdit(tag.type)}>编辑</Button>
              </Col>
            </Row>
          </div>
        })}            
      </div>
    )
}

export default TradeConfig;