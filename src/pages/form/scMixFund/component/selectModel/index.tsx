import React, { useEffect, useState } from 'react';
import { Checkbox, Button } from 'antd';
import styles from './index.less'
interface selectProps {
    isHead?: boolean; //顶部筛选与内部配置项公用一个组件
    isEdit?: boolean; //内部配置项是否可用
    data?: {
        platform:Array<string>
    };
    isOpenAccount?: boolean;
    handleChange: (data: any) => void;
}

export default function (props: selectProps) {
    const { handleChange } = props;
    const [platform, setPlatform] = useState<Array<string>>([]);
    const [checkAllPlatform, setCheckAllPlatform] = useState(false);
    const [indeterminatePlat, setiPlat] = useState(true)
    const platformOptions = [ 
        { label: 'SDK-安卓', value: 'andsdk' },
        { label: 'SDK-iOS', value: 'iossdk' },
    ]
    useEffect(() => {
        
        let _getAll1: string[] = platformOptions.map((item) => item.value);
        setPlatform(_getAll1);
        setCheckAllPlatform(true);
        setiPlat(false);
        let data = {
            platform: _getAll1,
        }
        handleChange(data)
    },[])
    useEffect(() => {
        if (props.data) {
            if (JSON.stringify(props.data) === "{}") {
                return;
            }
            setPlatform(props.data.platform)
            setiPlat(!!props.data.platform.length && props.data.platform.length < platformOptions.length)
            setCheckAllPlatform(props.data.platform.length === platformOptions.length)
        }
    }, [props.data])
    const onChangePlatform = (platform: string[]) => {
        console.log(platform)
        setiPlat(!!platform.length && platform.length < platformOptions.length)
        setPlatform(platform)
        setCheckAllPlatform(platform.length === platformOptions.length)
        if (!props.isHead) {
            let data = {
                platform: platform,
            }
            handleChange(data)
        }
    }

    const onCheckAllChangePlatform = (e: any) => {
        setiPlat(false)
        setCheckAllPlatform(e.target.checked);
        let _getAll: string[] = platformOptions.map((item, index) => item.value);
        setPlatform(e.target.checked ? _getAll : [])
        if (!props.isHead) {
            let data = {
                platform: e.target.checked ? _getAll : [],
            }
            handleChange(data)
        }
    }
    const handleSelect = () => {
        let data = {
            platform: platform,
        }
        handleChange(data)
    }
    return (
        <section className={styles['m-config']}>
            <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>适用平台:</p>
            <Checkbox
                indeterminate={indeterminatePlat}
                onChange={onCheckAllChangePlatform}
                checked={checkAllPlatform}
                disabled={!props.isHead && !props.isEdit}
            >
                全选
            </Checkbox>
            <br />
            <Checkbox.Group
                options={platformOptions}
                onChange={onChangePlatform}
                value={platform}
                style={{ marginLeft: '110px', marginBottom: '20px' }}
                disabled={!props.isHead && !props.isEdit} />
            <br />
            {props.isHead ? <Button onClick={handleSelect} type='primary' style={{ marginTop: '20px' }}>查询</Button> : null}
        </section>
    )
}

