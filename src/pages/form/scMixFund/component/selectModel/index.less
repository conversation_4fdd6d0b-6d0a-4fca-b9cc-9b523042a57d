.m-title {
    // width: 80%;
    font-size: 24px;
    border-bottom: 1px solid rgba(0,0,0,.2);
    margin: 30px 0;
    padding: 10px;
}


  .tag {
    margin: 3px;
    font-size: 13px;
    border: 1px dashed #cccccc;
    border-radius: 4px;
    // padding: 0 8px;
    line-height: 30px;
    color: #666666;
    background: rgba(255, 255, 255, 0.7);
    
  }
  .m-config {
    margin-bottom:20px;
    .m-title {
      display: inline-block;
      padding-left: 40px;
    }
}
.m-required {
    margin: 1px 4px 0 0;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
}
.m-card-label{
  width:110px;
  color: #333;
  text-align: right;
  padding-right: 12px;
  display: inline-block;

}
.m-card-label2{
  width:120px;
  color: #333;
  color: #333;
  text-align: right;
  padding-right: 12px;
  display: inline-block;

}
.m-lh30 {
  line-height: 30px;
}


.m-tagModel-button{
  margin-left: 20px;
}
.m-tagModel-span{
  margin-right: 10px;
}
.m-tagModel-row{
  margin: 10px 0;
}
.m-collpase-button {
  margin-top: -5px;
}

.m-required {
    margin: 1px 4px 0 0;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
}

.m-trigger-row {
    margin: 10px 0;
    
}
.trigger-border {
  border: 1px solid #bdbbbb;
  margin-bottom: 10px;
  padding: 10px;
}