import React, {useEffect, useState} from 'react';
import { Button, message, Popconfirm } from 'antd';
import FormRender from "form-render/lib/antd";
import styles from '../../index.less';
import ConfigSelect from '../selectModel';
import TagModel from '../tagModel';

import FORM_CONFIG1 from '../../tradeConfig/chooseFundTool/form.json';
import FORM_CONFIG2 from '../../tradeConfig/columnAdvertisement/form.json';
import FORM_CONFIG3 from '../../tradeConfig/notice/form.json';
import FORM_CONFIG4 from '../../tradeConfig/openAccount/form.json';
import UploadImg1 from '../../tradeConfig/chooseFundTool/uploadImg'; 
import UploadImg5 from '../../tradeConfig/chooseFundTool/uploadImg2'; 
import UploadImg2 from '../../tradeConfig/columnAdvertisement/uploadImg'; 
import UploadImg3 from '../../tradeConfig/openAccount/uploadImg'; 
import UploadImg4 from '../../tradeConfig/openAccount/uploadImg2'; 
import brandFoldUrlUploadImage from '../../tradeConfig/openAccount/brandFoldUrlUploadImage';
import saleFoldUrlUploadImage from '../../tradeConfig/openAccount/saleFoldUrlUploadImage';

interface dataProps {
    data: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
    kycTag?: any;
    olasTag?: any;
    type?: string
}

export default function (props: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);

    const [formData, setFormState] = useState<any>({});
    const [configData, setConfig] = useState<any>({});
    const [relationData, setRelation] = useState<any>({});

    useEffect(() => {
        console.log('formData=',props)
        setFormState(props.data?.formData)
        setConfig(props.data?.configData)
        setRelation(props.data?.relationData)
        if (props.data?.isNew) {
            setEdit(true);
            const { isNew, ...other } = props.data;
            props.handleUpdate(other, props.position, true);
        }
    }, [props.data])
    
    async function handleChange(){
        if(isEdit){
            if (valid.length > 0) {
                return;
            } else {
                console.log(11,formData);
                let { startTime, endTime } = formData;
                if ( startTime && endTime ) {
                    startTime = startTime?.replace(/[^\d]/g, '');
                    endTime = endTime?.replace(/[^\d]/g, '');
                    if (startTime >= endTime) {
                        message.error('结束时间应晚于开始时间')
                        return;
                    }
                }
                if (configData?.platform?.length > 0) {
                    let _relationData: any = {...relationData};
                    if (_relationData?.targetType === 'kyc') {
                        _relationData.olasId = '';
                    } else if (_relationData?.targetType === 'olas') {
                        _relationData.kycLogic = '';
                        _relationData.kycs = [];
                    } else {
                        _relationData.olasId = '';
                        _relationData.kycLogic = '';
                        _relationData.kycs = [];
                    }
                    let _data = {
                        formData,
                        configData,
                        relationData: _relationData,
                    }
                    props.handleUpdate(_data, props.position)
                } else {
                    message.error('适用平台不能为空')
                    return;
                }
            }
        }
        setEdit(!isEdit)
    }
    function handleSelect(data: any){
        console.log('platform',data)
        setConfig(data)
    }
    /**
     * kyc指定用户修改
     * @param data 修改的kyc标签
     */
    const handleRelationChange = (data: any) => {
        if(data.targetType === 'kyc' && !data.kycLogic) {
            data.kycLogic = 'and'
        }
        data = {
            ...relationData,
            ...data
        }
        setRelation(data)
    }
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {props.handleDelete(props.position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                { props.type === 'openAccount' && <FormRender
                        propsSchema={FORM_CONFIG4.schema}
                        displayType='row'
                        formData={formData}
                        onValidate={setValid}
                        onChange={setFormState}
                        readOnly={!isEdit}
                        widgets={{
                            uploadImg: UploadImg3,
                            uploadImg2: UploadImg4,
                            brandFoldUrlUpload: brandFoldUrlUploadImage,
                            saleFoldUrlUpload:saleFoldUrlUploadImage,
                        }}
                /> }
                { props.type === 'notice' && <FormRender
                        propsSchema={FORM_CONFIG3.schema}
                        displayType='row'
                        formData={formData}
                        onValidate={setValid}
                        onChange={setFormState}
                        readOnly={!isEdit}
                /> }
                { props.type === 'columnAdvertisement' && <FormRender
                        propsSchema={FORM_CONFIG2.schema}
                        displayType='row'
                        formData={formData}
                        onValidate={setValid}
                        onChange={setFormState}
                        readOnly={!isEdit}
                        widgets={{
                            uploadImg: UploadImg2,
                        }}
                /> }
                { props.type === 'chooseFundTool' && <FormRender
                        propsSchema={FORM_CONFIG1.schema}
                        displayType='row'
                        formData={formData}
                        onValidate={setValid}
                        onChange={setFormState}
                        readOnly={!isEdit}
                        widgets={{
                            uploadImg: UploadImg1,
                            uploadImg2: UploadImg5,

                        }}
                /> }

                <ConfigSelect
                    handleChange={handleSelect}
                    isHead={false}
                    isEdit={isEdit}
                    data={configData}
                />
                <TagModel
                    handleChange={handleRelationChange}
                    kycTag={props.kycTag}
                    olasTag={props.olasTag}
                    isEdit={isEdit}
                    data={relationData}
                />
            </div>

}