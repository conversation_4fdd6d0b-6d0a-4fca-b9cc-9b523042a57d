import React, { useState, useEffect } from 'react';
import { Collapse, Switch, Radio, Input, Tag, message, Button, Card, Spin } from 'antd';
import api from 'api';
import ImgUpload from '../../frontend/compoment/uploadImg';

const { fetchTSZTConfig, postTSZTConfig } = api;
const { Panel } = Collapse;

interface ICommentProps {
	title: string;
	subTitle: string;
	describe: string;
	avatar: string;
}

interface IConfigProps {
	calculatorJumpUrl: string;
	calculatorStatus: boolean;
	calculatorThsJumpUrl: string;
	operationDesc: string;
	operationImageUrl: string;
	operationJumpUrl: string;
	operationStatus: boolean;
	operationThsJumpUrl: string;
	status: boolean;
}

export default function() {
  const [pensionConfig, setPensionConfig] = useState<IConfigProps>({
    status: false, // 开关
    operationStatus: false, // 运营位开关
    calculatorStatus: false, // 计算器开关
    operationDesc: '', // 运营位说明
    operationImageUrl: '', // 运营位图片
    operationJumpUrl: '', // 运营位链接
    operationThsJumpUrl: '', // 运营位链接 手炒
    calculatorJumpUrl: '', // 计算器链接
    calculatorThsJumpUrl: '', // 计算器链接 手炒
  }); // 养老规划
  const [educateConfig, setEducateConfig] = useState<IConfigProps>({
    status: false, // 开关
    operationStatus: false, // 运营位开关
    calculatorStatus: false, // 计算器开关
    operationDesc: '', // 运营位说明
    operationImageUrl: '', // 运营位图片
    operationJumpUrl: '', // 运营位链接
    operationThsJumpUrl: '', // 运营位链接 手炒
    calculatorJumpUrl: '', // 计算器链接
    calculatorThsJumpUrl: '', // 计算器链接 手炒
  }); // 教育储备
	const [commentList, setCommentList] = useState<ICommentProps[]>([]);
	const [init, setInit] = useState<boolean>(false);

	/**
	 * 监控表格的填写
	 */
  const wacthChange = (value: any, key: string, type: string, index: number = 0) => {
    switch (type) {
      case 'pensionConfig':
        let _pensionConfig: any = { ...pensionConfig };
        _pensionConfig[key] = value;
        setPensionConfig(_pensionConfig);
        break;
      case 'educateConfig':
				let _educateConfig: any = { ...educateConfig };
				_educateConfig[key] = value;
				setEducateConfig(_educateConfig);
        break;
			case 'commentList':
				let _commentList: ICommentProps[] = [...commentList];
				_commentList[index][key] = value;
				setCommentList(_commentList);
				break;
      default:
        break;
    }
  };

	/**
	 * 新增评论
	 */
	const addComment = () => {
		if (commentList.length >= 5) return;
		let _commentList: ICommentProps[] = [...commentList];
		_commentList.push({
			title: '',
			subTitle: '',
			describe: '',
			avatar: ''
		});
		setCommentList(_commentList);
	}

	/**
	 * 删除评论
	 */
	const deleteComment = (index: number) => {
		let _commentList: ICommentProps[] = [...commentList];
		_commentList.splice(index, 1);
		setCommentList(_commentList);
	}

	/**
	 * 保存
	 */
	const submit = () => {
		// 检查
		const checkUrl = (url: string): boolean => {
			if (url.includes(' ')) {
				message.error('检查链接中是否包含了空格');
				return false;
			}
			if (((/^https:\/\/.*/).test(url) || (/^client/).test(url))) {
				return true;
			}
			return false;
		}
		const checkConfig = (config: IConfigProps, type: 'pensionConfig' | 'educateConfig'): boolean => {
			const { status, operationStatus, calculatorStatus } = config;
			const TYPE = type === 'pensionConfig' ? '养老规划' : '教育储备'
			if (status) {
				if (operationStatus) {
					const { operationDesc, operationImageUrl, operationJumpUrl, operationThsJumpUrl } = config;
					if (!operationDesc) {
						message.error(TYPE + ' 运营位说明未配置');
						return false;
					}
					if (!operationImageUrl) {
						message.error(TYPE + ' 运营位图片未上传');
						return false;
					}
					if (!operationJumpUrl) {
						message.error(TYPE + ' 运营位基金链接未配置');
						return false;
					} else if (!checkUrl(operationJumpUrl)) {
						message.error(TYPE + ' 运营位基金链接格式错误');
						return false;
					}
					if (!operationThsJumpUrl) {
						message.error(TYPE + ' 运营位手炒链接未配置');
						return false;
					} else if (!checkUrl(operationThsJumpUrl)) {
						message.error(TYPE + ' 运营位手炒链接格式错误');
						return false;
					}
				} else if (calculatorStatus) {
					message.error(TYPE + ' 配置有误，计算器和运营位必须同时开启');
					return false;
				}
				if (calculatorStatus) {
					const { calculatorJumpUrl, calculatorThsJumpUrl } = config;
					if (!calculatorJumpUrl) {
						message.error(TYPE + ' 计算器基金链接未配置');
						return false;
					} else if (!checkUrl(calculatorJumpUrl)) {
						message.error(TYPE + ' 计算器基金链接格式错误');
						return false;
					}
					if (!calculatorThsJumpUrl) {
						message.error(TYPE + ' 计算器手炒链接未配置');
						return false;
					} else if (!checkUrl(calculatorThsJumpUrl)) {
						message.error(TYPE + ' 计算器手炒链接格式错误');
						return false;
					}
				}
			}
			return true;
		}
		const checkList = (list: ICommentProps[]): boolean => {
			if (list.length > 5) {
				message.error('用户精评最多配置5个');
				return false;
			}
			for (let i = 0; i < list.length; i++) {
				if (!list[i].title) {
					message.error(`第${i + 1}个用户精评缺少名称`);
					return false;
				}
				if (!list[i].describe) {
					message.error(`第${i + 1}个用户精评缺少描述`);
					return false;
				}
				if (!list[i].avatar) {
					message.error(`第${i + 1}个用户精评缺少头像`);
					return false;
				}
			}
			return true;
		}
		if (checkConfig(pensionConfig, 'pensionConfig') && checkConfig(educateConfig, 'educateConfig') && checkList(commentList)) {
			let _postObj = {
				pensionConfig,
				educateConfig,
				commentList
			}
			postTSZTConfig({
				value: JSON.stringify(_postObj)
			}).then((res: any) => {
				const { code } = res;
				if (code === '0000') {
					message.success('保存成功');
				} else {
					message.error(res.msg || '保存配置数据失败');
				}
			}).catch((e: Error) => {
				message.error(e?.message || '保存配置数据失败');
			})
		}
	}

	useEffect(() => {
		fetchTSZTConfig().then((res: any) => {
			const { code, data } = res;
			if (code === "0000") {
				if (data) {
					let _data = JSON.parse(data);
					setPensionConfig(_data.pensionConfig);
					setEducateConfig(_data.educateConfig);
					setCommentList(_data.commentList);
					
				}
				setInit(true);
			} else {
				message.error('获取配置数据失败，请刷新重试')
			}
		}).catch((e: Error) => {
				message.error(e?.message || '获取配置数据失败');
		});
	}, [])

  return (
    <Spin spinning={!init}>
      <Collapse defaultActiveKey={'3'}>
        <Panel
          header={
            <div>
              <span style={{ marginRight: 10 }}>养老规划</span>
              {pensionConfig.status ? (
                <Tag color="#ff330a">开启中</Tag>
              ) : (
                <Tag color="#999">关闭中</Tag>
              )}
            </div>
          }
          key="1"
        >
          <div>
            <div style={{ marginBottom: 10 }}>
              <Switch
                checked={pensionConfig.status}
                onChange={v => {
                  wacthChange(v, 'status', 'pensionConfig');
                }}
              />
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>是否显示运营位：</span>
              <Radio.Group
                value={pensionConfig.operationStatus}
                onChange={e => {
                  wacthChange(e.target.value, 'operationStatus', 'pensionConfig');
                }}
              >
                <Radio value={true}>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>是否显示计算器：</span>
              <Radio.Group
                value={pensionConfig.calculatorStatus}
                onChange={e => {
                  wacthChange(e.target.value, 'calculatorStatus', 'pensionConfig');
                }}
              >
                <Radio value={true}>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>运营位说明：
							{ (pensionConfig.status && pensionConfig.operationStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <Input
                style={{ width: 800 }}
								maxLength={36}
								placeholder="最多36个字"
                value={pensionConfig.operationDesc}
                onChange={e => {
                  wacthChange(e.target.value, 'operationDesc', 'pensionConfig');
                }}
              />
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>运营位图片：
							{ (pensionConfig.status && pensionConfig.operationStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <ImgUpload
                handleChange={(url: string) => {
                  wacthChange(url, 'operationImageUrl', 'pensionConfig');
                }}
                imageUrl={pensionConfig.operationImageUrl}
                limit={0.5}
                isEdit={true}
                title=""
								size={['58px*40px']}
              />
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>运营位基金链接：
							{ (pensionConfig.status && pensionConfig.operationStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <Input
                style={{ width: 800 }}
                value={pensionConfig.operationJumpUrl}
                onChange={e => {
                  wacthChange(e.target.value, 'operationJumpUrl', 'pensionConfig');
                }}
              />
            </div>
						<div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>运营位手炒链接：
							{ (pensionConfig.status && pensionConfig.operationStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <Input
                style={{ width: 800 }}
                value={pensionConfig.operationThsJumpUrl}
                onChange={e => {
                  wacthChange(e.target.value, 'operationThsJumpUrl', 'pensionConfig');
                }}
              />
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>计算器基金链接：
							{ (pensionConfig.status && pensionConfig.calculatorStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <Input
                style={{ width: 800 }}
                value={pensionConfig.calculatorJumpUrl}
                onChange={e => {
                  wacthChange(e.target.value, 'calculatorJumpUrl', 'pensionConfig');
                }}
              />
            </div>
						<div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>计算器手炒链接：
							{ (pensionConfig.status && pensionConfig.calculatorStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <Input
                style={{ width: 800 }}
                value={pensionConfig.calculatorThsJumpUrl}
                onChange={e => {
                  wacthChange(e.target.value, 'calculatorThsJumpUrl', 'pensionConfig');
                }}
              />
            </div>
          </div>
        </Panel>
        <Panel
          header={
            <div>
              <span style={{ marginRight: 10 }}>教育储备</span>
              {educateConfig.status ? (
                <Tag color="#ff330a">开启中</Tag>
              ) : (
                <Tag color="#999">关闭中</Tag>
              )}
            </div>
          }
          key="12"
        >
          <div>
            <div style={{ marginBottom: 10 }}>
              <Switch
                checked={educateConfig.status}
                onChange={v => {
                  wacthChange(v, 'status', 'educateConfig');
                }}
              />
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>是否显示运营位：</span>
              <Radio.Group
                value={educateConfig.operationStatus}
                onChange={e => {
                  wacthChange(e.target.value, 'operationStatus', 'educateConfig');
                }}
              >
                <Radio value={true}>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>是否显示计算器：</span>
              <Radio.Group
                value={educateConfig.calculatorStatus}
                onChange={e => {
                  wacthChange(e.target.value, 'calculatorStatus', 'educateConfig');
                }}
              >
                <Radio value={true}>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>运营位说明：
							{ (educateConfig.status && educateConfig.operationStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <Input
                style={{ width: 800 }}
								maxLength={36}
								placeholder="最多36个字"
                value={educateConfig.operationDesc}
                onChange={e => {
                  wacthChange(e.target.value, 'operationDesc', 'educateConfig');
                }}
              />
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>运营位图片：
							{ (educateConfig.status && educateConfig.operationStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <ImgUpload
                handleChange={(url: string) => {
                  wacthChange(url, 'operationImageUrl', 'educateConfig');
                }}
                imageUrl={educateConfig.operationImageUrl}
                limit={0.5}
                isEdit={true}
                title=""
								size={['58px*40px']}
              />
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>运营位基金链接：
							{ (educateConfig.status && educateConfig.operationStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <Input
                style={{ width: 800 }}
                value={educateConfig.operationJumpUrl}
                onChange={e => {
                  wacthChange(e.target.value, 'operationJumpUrl', 'educateConfig');
                }}
              />
            </div>
						<div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>运营位手炒链接：
							{ (educateConfig.status && educateConfig.operationStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <Input
                style={{ width: 800 }}
                value={educateConfig.operationThsJumpUrl}
                onChange={e => {
                  wacthChange(e.target.value, 'operationThsJumpUrl', 'educateConfig');
                }}
              />
            </div>
            <div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>计算器基金链接：
							{ (educateConfig.status && educateConfig.calculatorStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <Input
                style={{ width: 800 }}
                value={educateConfig.calculatorJumpUrl}
                onChange={e => {
                  wacthChange(e.target.value, 'calculatorJumpUrl', 'educateConfig');
                }}
              />
            </div>
						<div style={{ marginBottom: 10 }}>
              <span style={{ marginRight: 10 }}>计算器手炒链接：
							{ (educateConfig.status && educateConfig.calculatorStatus) && <em style={{color: '#fe5d4e'}}>*</em> }
							</span>
              <Input
                style={{ width: 800 }}
                value={educateConfig.calculatorThsJumpUrl}
                onChange={e => {
                  wacthChange(e.target.value, 'calculatorThsJumpUrl', 'educateConfig');
                }}
              />
            </div>
          </div>
        </Panel>
        <Panel header="用户精评" key="3">
					{
						commentList.map((item: ICommentProps, index: number) => (
							<Card key={index} style={{width: 800, marginBottom: 20}}>
								<div style={{ marginBottom: 10 }}>
									<span style={{ marginRight: 10 }}>名称：<em style={{color: '#fe5d4e'}}>*</em></span>
									<Input
										style={{ width: 400 }}
										value={item.title}
										onChange={(e) => {
											wacthChange(e.target.value, 'title', 'commentList', index);
										}}
									/>
								</div>
								<div style={{ marginBottom: 10 }}>
									<span style={{ marginRight: 10 }}>副标题：</span>
									<Input
										style={{ width: 400 }}
										value={item.subTitle}
										onChange={(e) => {
											wacthChange(e.target.value, 'subTitle', 'commentList', index);
										}}
									/>
								</div>
								<div style={{ marginBottom: 10 }}>
									<span style={{ marginRight: 10 }}>描述：<em style={{color: '#fe5d4e'}}>*</em></span>
									<Input
										style={{ width: 600 }}
										value={item.describe}
										onChange={(e) => {
											wacthChange(e.target.value, 'describe', 'commentList', index);
										}}
									/>
								</div>
								<div style={{ marginBottom: 10 }}>
									<span style={{ marginRight: 10 }}>头像：<em style={{color: '#fe5d4e'}}>*</em></span>
									<ImgUpload
										handleChange={(url: string) => {
											wacthChange(url, 'avatar', 'commentList', index);
										}}
										imageUrl={item.avatar}
										limit={0.5}
										isEdit={true}
										title=""
									/>
								</div>
								<Button onClick={() => {deleteComment(index)}} size="small" type="danger">删除</Button>
							</Card>
						))
					}
					<Button onClick={addComment} disabled={commentList.length >= 5} type="primary">新增</Button>
				</Panel>
      </Collapse>
			<div style={{marginTop: 30}}>
				<Button type="primary" onClick={submit}>保存</Button>
			</div>
    </Spin>
  );
}
