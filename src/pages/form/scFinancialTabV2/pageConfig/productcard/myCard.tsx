import React, {useEffect, useState} from 'react';
import { Button, message, Popconfirm } from 'antd';
import styles from '../index.less';
import DesignatedUser from '../../components/DesignatedUser';
import api from 'api';
import { timeFormat2 } from '@/utils/utils';
import Block from './block';
const { updateUserType } = api;
interface dataProps {
    data: any;
    kycTag: any;
    olasTag: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
}
export default function ({data, kycTag, olasTag, position, handleUpdate, handleDelete}: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [designatedData, setDesignatedData] = useState<any>();
    const [formData, setFormState] = useState<any>({});

    useEffect(() => {
        setFormState(data.formData);
        setDesignatedData({
            configData: data.configData,
            relationData: data.relationData
        })
    }, [data]);

    const handleChange = () =>{
        if(isEdit){
            const { type, name, fundCode, fundName, fundManagerName, fundManagerTag, reason, button, buyCount, buyEndTime, buyStartTime,
                fundManagerPage, profitSection, recommendType, fundTag, show, title, secTitle, targetData } = formData;
            if (!type) {
                message.error('请选择产品大卡类型')
                return;
            }
            if (!name) {
                message.error('请填写卡片名称')
                return;
            }
            if (['1', '2', '3', '5'].includes(type)) {
                if (!fundCode) {
                    message.error(`请填写${ type==='3' ? '小目标' : '基金'}代码`)
                    return;
                }
                if (!fundName) {
                    message.error(`请填写${ type==='3' ? '小目标' : '基金'}名称`)
                    return;
                }
            }

            if (['1'].includes(type)) {
                if (!fundManagerName) {
                    message.error('请选择基金经理')
                    return;
                }
                if (!fundManagerTag) {
                    message.error('请填写基金经理标签')
                    return;
                }
                if (!show || show.length === 0) {
                    message.error('请选择展示指标维度')
                    return;
                }
                if (!fundManagerPage) {
                    message.error('请选择基金经理图片')
                    return;
                }
            }
            
            if (['2', '3', '5'].includes(type)) {
                if (!title) {
                    message.error('请填写主标题文案')
                    return;
                }
            }
            if (type === '5') {
                if (!secTitle) {
                    message.error('请填写副标题文案')
                    return;
                }
            }
            if (['1', '2', '5'].includes(type)) {
                if (!fundTag) {
                    message.error('请填写基金标签')
                    return;
                }
            }
            if (['2', '5'].includes(type)) {
                if (!profitSection) {
                    message.error('请选择收益时间区间')
                    return;
                }
            }
            
            if (type === '3') {
                for (let i = 0,len = targetData?.length; i < len; i++) {
                    let obj = targetData[i];
                    if (obj?.name) {
                    } else {
                        message.error(`请填写小目标历史业绩第${i+1}项名称`)
                        return;
                    }
                    if (obj?.profit) {
                    } else {
                        message.error(`请填写小目标历史业绩第${i+1}项止盈时年化收益`)
                        return;
                    }
                }
            }
            if (['1'].includes(type)) {
                if (!buyStartTime) {
                    message.error('请填写认购开始时间')
                    return;
                }
                if (!buyEndTime) {
                    message.error('请填写认购结束时间')
                    return;
                }
                
            }
            if (['1', '3'].includes(type)) {
                if (!buyCount) {
                    message.error(`请填写产品${type === '1' ? '认购' : type === '2' ? '关注' : '购买'}人数`)
                    return;
                }
            }
            if (type === '4') {
                if (!recommendType) {
                    message.error('请选择是否由系统配置')
                    return;
                }
            }
            if (['1', '5'].includes(type)) {
                if (!reason) {
                    message.error('请填写推荐理由')
                    return;
                }
            }
            if (!button) {
                message.error('请填写按钮文案')
                return;
            }
            let realData = {};
            const {configData, relationData} = designatedData;
            if (relationData?.userType === '1') {
                const {blackCustId, whiteCustId, ...other} = relationData;
                realData = {...other}
            } else {
                const {blackUserId, whiteUserId, ...other} = relationData;
                realData = {...other}
            }
            let _data: any = {
                formData,
                configData,
                relationData: realData
            }
            if (configData.platform?.length === 0) {
                message.error('请选择使用平台')
                return;
            }
            if (configData.utype?.length === 0) {
                message.error('请选择用户类型')
                return;
            }
            if (relationData?.id) {
              handleUpdate(_data, position)   
            } else {
                let time = timeFormat2();
                let obj = {
                    ...configData,
                    ...realData,
                    updateTime: time
                };
                updateUserType(obj).then((res: any) => {
                    if (res.code !== '0000') return message.error(res.message);
                    let id = res.data;
                    _data.relationData.id = id;
                    _data.relationData.updateTime = time;
                    handleUpdate(_data, position);
                })
                .catch((e: Error) => {
                    message.error(e.message || '系统错误');
                });
            }
            
        } else {
            setEdit(!isEdit)
        }
    }
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {handleDelete(position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <h1 className="g-fs20 f-bold">配置详情</h1>
                <Block blockData={formData} handleUpdate={setFormState} isEdit={isEdit}/>
                <DesignatedUser
                    data={data}
                    position={position}
                    kycTag={kycTag}
                    olasTag={olasTag}
                    isEdit={isEdit}
                    setDesignatedData={setDesignatedData}
                />
            </div>

}