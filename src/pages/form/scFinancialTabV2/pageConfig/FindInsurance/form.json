{"schema": {"type": "object", "properties": {"tabName": {"title": "tab名称", "type": "string", "description": "", "ui:width": "60%", "ui:options": {}}, "tabImage": {"title": "tab配图", "type": "string", "ui:widget": "uploadImg1"}, "darkTabImage": {"title": "tab配图-黑夜", "type": "string", "ui:widget": "uploadImg3"}, "productImage": {"title": "产品头图", "type": "string", "ui:widget": "uploadImg2"}, "productMainTitle": {"title": "产品主标题", "type": "string", "description": "", "ui:width": "60%", "ui:options": {}}, "productSecTitle": {"title": "产品副标题", "type": "string", "description": "", "ui:width": "60%", "ui:options": {}}, "buyButtonDesc": {"title": "购买按钮文描", "type": "string", "description": "", "ui:width": "60%", "ui:options": {}}, "buyActionUrl": {"title": "产品跳转链接", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "buyVersion": {"title": "版本控制", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "60%", "ui:className": "u-block"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "60%"}}, "required": ["tabName", "tabImage", "darkTabImage", "productImage", "productMainTitle", "productSecTitle", "buyButtonDesc", "buyActionUrl"]}, "displayType": "row", "showDescIcon": true, "labelWidth": 140}