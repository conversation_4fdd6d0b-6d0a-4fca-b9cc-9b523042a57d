import React, {useEffect, useState} from 'react';
import { Button, message, Popconfirm, Select } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../index.less';
import DesignatedUser from '../../components/DesignatedUser';
import api from 'api';
import { timeFormat2 } from '@/utils/utils';
import UploadImg from '../gongge/uploadImg'; 
import UploadImg2 from '../gongge/uploadImg2';

const { updateUserType } = api;
const { Option } = Select;

interface dataProps {
    data: any;
    kycTag: any;
    olasTag: any;
    gonggeClassify: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
}
interface iSelectProps {
    onChange: Function,
    value: string,
    readonly: boolean
}

export default function ({gonggeClassify, data, kycTag, olasTag, position, handleUpdate, handleDelete}: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    const [designatedData, setDesignatedData] = useState<any>();
    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [formData, setFormState] = useState<any>({});

    useEffect(() => {
        setFormState(data.formData);
        setDesignatedData({
            configData: data.configData,
            relationData: data.relationData
        })
    }, [data]);

    const onValidate = (valid: any) => {
        setValid(valid);
    }
    const handleChange = () =>{
        if(isEdit){
            if (!formData.gonggeType) {
                message.error('请选择宫格分类')
                return;
            }
            let exist = false;
            gonggeClassify.forEach((item: any) => {
                if (item.name === formData.gonggeType) {
                    exist = true;
                }
            })
            if (!exist) {
                message.error('该宫格分类不存在，请重新选择')
                return;
            }
            if (!formData.gonggeName) {
                message.error('请填写宫格名称')
                return;
            }
            if (!formData.page) {
                message.error('请上传图标-白天')
                return;
            }
            if (!formData.darkPage) {
                message.error('请上传图标-黑夜')
                return;
            }
            if (!formData.jumpAction) {
                message.error('请填写跳转链接')
                return;
            }
            let realData = {};
            const {configData, relationData} = designatedData;
            if (relationData?.userType === '1') {
                const {blackCustId, whiteCustId, ...other} = relationData;
                realData = {...other}
            } else {
                const {blackUserId, whiteUserId, ...other} = relationData;
                realData = {...other}
            }
            let _data: any = {
                formData,
                configData,
                relationData: realData
            }
            if (configData.platform?.length === 0) {
                message.error('请选择使用平台')
                return;
            }
            if (configData.utype?.length === 0) {
                message.error('请选择用户类型')
                return;
            }
            if (relationData?.id) {
              handleUpdate(_data, position)   
            } else {
                let time = timeFormat2();
                let obj = {
                    ...configData,
                    ...realData,
                    updateTime: time
                };
                updateUserType(obj).then((res: any) => {
                    if (res.code !== '0000') return message.error(res.message);
                    let id = res.data;
                    _data.relationData.id = id;
                    _data.relationData.updateTime = time;
                    handleUpdate(_data, position);
                })
                .catch((e: Error) => {
                    message.error(e.message || '系统错误');
                });
            }
            
        } else {
            setEdit(!isEdit)
        }
    }
    const GongSelect = (props: iSelectProps) => {
        return (
            <div>
                <Select style={{width: 200}} value={props.value} disabled={props.readonly} onChange={(value: string) => props.onChange('gonggeType', value)}>
                    { gonggeClassify?.length > 0 && gonggeClassify.map((item: any, index: number) => {
                        return (
                            <Option value={item.name}>{item.name}</Option>
                        )
                    }) }
                </Select>
            </div>
        )
    }
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {handleDelete(position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <h1 className="g-fs20 f-bold">配置详情</h1>
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={onValidate}
                    onChange={setFormState}
                    readOnly={!isEdit}
                    labelWidth={FORM_CONFIG.labelWidth}
                    widgets={{
                        uploadImg: UploadImg,
                        uploadImg2: UploadImg2,
                        gongSelect: GongSelect
                    }}
                />
                <DesignatedUser
                    data={data}
                    position={position}
                    kycTag={kycTag}
                    olasTag={olasTag}
                    isEdit={isEdit}
                    setDesignatedData={setDesignatedData}
                />
            </div>

}