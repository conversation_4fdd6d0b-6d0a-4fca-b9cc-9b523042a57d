import React from 'react';
import ImgUpload from '../../components/uploadImg/index.jsx';
interface dataProps {
  onChange: Function;
  value: string;
  readonly: boolean;
}

export default function(props: dataProps) {
  return (
    <div style={{ width: 514 }}>
      <ImgUpload
        handleChange={(value: any) => props.onChange('imageSmallSvgaUrl', value)}
        imageUrl={props.value}
        isEdit={!props.readonly}
        title=""
        size={['1125*420']}
        limit={5}
      />
    </div>
  );
}
