import React, { useEffect, useState } from 'react';
import { Button, message, Collapse } from 'antd';
import api from 'api';
import MyCard from './myCard';
import styles from '../index.less';
import ConfigSelect from '../../components/selectModel/index';
import LocationModel from '../../components/locationModel';
import { timeFormat2 } from '@/utils/utils';

const { fetchMixedbanner, fetchCBAS, fetchOLAS, postMixedbanner } = api;
const { Panel } = Collapse;
export default function () {
    const [init, setInit] = useState(true);
    const [originData, setOriginData] = useState<any>([]);
    const [isModify, setModify] = useState(false);
    const [config, setConfig] = useState<{ utype: string[], platform: string[] }>({ utype: [], platform: [] });
    const [kycTag, setKycTag] = useState([]);
    const [olasTag, setOlasTag] = useState([]);
    const [activeKey, setActiveKey] = useState(0);

    const handleChange = (data: any) => {
        console.log('configData', data);
        setConfig(data);
    }
    useEffect(() => {
        fetchCBAS().then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                if (data) {
                    setKycTag(data)
                }
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])
    
    useEffect(() => {
        fetchOLAS().then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                if (data) {
                    let _olasTag: any = [];
                    for (let prop in data) {
                        _olasTag.push({ "groupid": prop, "description": data[prop].description });
                    }
                    console.log('olasTag',_olasTag);
                    setOlasTag(_olasTag);
                }
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])

    useEffect(() => {
        fetchMixedbanner().then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                setInit(true);
                let initData: any = data && JSON.parse(data), _data: any = [], filterIdArr: string[] = [];
                console.log(initData);
                initData?.confs?.forEach((item: any) => {
                    let obj = {
                        formData: {
                            mixName: item.mixName,
                            mixType: item.mixType ?? '1',
                            navColorLeft: item.navColorLeft,
                            navColorRight: item.navColorRight,
                            imageUrl: item.imageUrl,
                            masterTitle: item.masterTitle,
                            masterColor: item.masterColor,
                            subTitle: item.subTitle,
                            subColor: item.subColor,
                            buttonText: item.buttonText,
                            buttonColor: item.buttonColor,
                            buttonTextColor: item.buttonTextColor,
                            jumpAction: item.jumpAction,
                            version: item.version,
                            startTime: item.startTime,
                            endTime: item.endTime
                        },
                        configData: {
                            platform: item.platform || [],
                            utype: item.utype || [],
                        },
                        relationData: {
                            id: item.id,
                            targetType: item.targetType,
                            kycLogic: item.kycLogic,
                            kycs: item.kycs,
                            olasId: item.olasId,
                            olasType: item.olasType,
                            userType: item.userType,
                            blackUserId: item.userType === '1' ? item.blackUserId : '',
                            whiteUserId: item.userType === '1' ? item.blackUserId : '',
                            blackCustId: item.userType === '3' ? item.blackUserId : '',
                            whiteCustId: item.userType === '3' ? item.blackUserId : '',
                            updateTime: item.updateTime
                        }
                    }
                    _data.push(obj);
                })
                setOriginData(_data); 
                console.log('initdata:', _data);
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])

    function onSubmit() {
        let _value: any = [];
        for (let i = 0, len = originData?.length; i < len; i++) {
            let val = originData[i];
            let _data: any = {};
            let realData = {};
            if (val.relationData?.userType === '1') {
                const {blackCustId, whiteCustId, ...other} = val.relationData;
                realData = {...other}
            } else {
                const {blackUserId, whiteUserId, ...other} = val.relationData;
                realData = {...other}
            }
            if (val.formData?.mixType === '1') {
                _data = {
                    mixName: val.formData?.mixName,
                    mixType:  val.formData?.mixType,
                    navColorLeft:  val.formData?.navColorLeft,
                    navColorRight:  val.formData?.navColorRight,
                    imageUrl:  val.formData?.imageUrl,
                    jumpAction:  val.formData?.jumpAction,
                    version:  val.formData?.version,
                    startTime:  val.formData?.startTime,
                    endTime:  val.formData?.endTime
                }
            } else {
                _data = {
                    ...val.formData
                }
            }
            _data = { ..._data, ...val.configData, ...realData};
            _value.push(_data);
        }
        let _sendData = {
            value: JSON.stringify({
                lastEditor: localStorage.name,
                lastEditTime: timeFormat2(),
                confs: _value,
                type: 'OperationPosition'
            }),
        }
        console.log('send', _sendData)
        postMixedbanner(
            _sendData
        ).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.message);
            } else {
                message.success('保存成功！');
                let timer = setTimeout(() => {
                    clearTimeout(timer);
                    location.href = `#/form/scFinancialTabV2/pageConfig`
                }, 1000);
            }
        }).catch((e: Error) => {
            message.error(e?.message);
        })
          
    }
    function addItem() {
        let obj = {
            formData: {
                mixName: '',
                mixType:  '1',
                navColorLeft:  '',
                navColorRight: '',
                imageUrl:  '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            },
            configData: {
                platform: config.platform,
                utype: config.utype,
            },
            relationData: {
                id: '',
                targetType: '',
                kycLogic: '',
                kycs: [],
                olasId: '',
                olasType: '',
                userType: '1', // 1:user   3:cust
                blackUserId: '',
                whiteUserId: '',
                blackCustId: '',
                whiteCustId: '',
                updateTime: ''
            }
        }
        let data = [...originData, obj];
        setOriginData(data);
        setActiveKey(data.length - 1);
    }

    function handleUpdate(data: any, index: number) {
        if (!isModify) setModify(true);
        let _originData: any = [...originData];
        _originData[index] = data;
        setOriginData(_originData);  
    }
    function handleDelete(index: number) {
        if (!isModify) setModify(true);
        let _originData = [...originData];
        _originData.splice(index, 1);
        setOriginData(_originData);
    }
    function handleSelect(item: any) {
        let tag = 0;
        if (item?.configData.platform?.length === 0 && item?.configData.utype?.length === 0) {
            tag = 1;
        } else if (config.utype?.length === 0) {
            for (let data of item?.configData?.platform) {
                if (config.platform?.indexOf(data) != -1) {
                    tag = 1;
                    break;
                }
            }
        } else if (config.platform?.length === 0) {
            for (let data of item?.configData.utype) {
                if (config.utype?.indexOf(data) != -1) {
                    tag = 1;
                    break;
                }
            }
        } else {
            for (let data of item?.configData?.platform) {
                if (config.platform?.indexOf(data) != -1) {
                    for (let data of item?.configData?.utype) {
                        if (config.utype?.indexOf(data) != -1) {
                            tag = 1;
                            break;
                        }
                    }
                    if (tag === 1) {
                        break;
                    }
                }
            }
        }
        return !!tag
    }
    function handleActiveKey (key: any) {
        setActiveKey(key);
    }
    function goUp(e: any, item: any, index: number) {
        e.stopPropagation();

        if (index === 0) {
            message.info('已在最上方');
            return
        }
        let _originData = [...originData];
        _originData.splice(index, 1);
        _originData.splice(index - 1, 0, item);
        console.log(_originData);
        setOriginData(_originData);
        if (!isModify) setModify(true);
    }
    function goDown(e: any, item: any, index: number) {
        e.stopPropagation();
        if (index === originData.length - 1) {
            message.info('已在最下方');
            return
        }
        let _originData = [...originData];
        _originData.splice(index, 1);
        _originData.splice(index + 1, 0, item);
        console.log(_originData);
        setOriginData(_originData);
        if (!isModify) setModify(true);
    }
    if (!init) return '加载中';
    return <div>
        <LocationModel location='融合运营位' />
        <ConfigSelect handleChange={handleChange} isHead={true} />
        <Button type="primary" onClick={onSubmit} disabled={!isModify}>保存</Button>
        <Collapse activeKey={activeKey} onChange={handleActiveKey}>
            {
                originData?.map((item: any, index: number) => {
                    return handleSelect(item) ?
                <Panel 
                    header={(<span style={{height: 22, display: 'inline-block', verticalAlign: 'middle'}}>#{index+1} {item.formData?.mixName}</span>)} 
                    extra={<div className={styles['m-collpase-button']}><Button onClick={(e) => { goUp(e, item, index) }}>上移</Button><Button onClick={(e) => { goDown(e, item, index) }}>下移</Button></div>}
                    key={index}>
                    <MyCard
                        data={item}
                        position={index}
                        kycTag={kycTag}
                        olasTag={olasTag}
                        handleDelete={handleDelete}
                        handleUpdate={handleUpdate}></MyCard>
                </Panel>
                : null

                })
            }
        </Collapse>
        <Button onClick={addItem} type='primary' style={{ marginTop: '20px' }}>添加</Button>
    </div>

}