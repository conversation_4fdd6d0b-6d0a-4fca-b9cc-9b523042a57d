{"schema": {"type": "object", "properties": {"mixName": {"title": "融合运营位名称", "type": "string", "description": "", "ui:width": "60%", "ui:options": {}}, "mixType": {"title": "融合运营位类型", "type": "string", "enum": ["1", "2", "3"], "enumNames": ["自定义图片", "模板图片", "开户/首购大礼包"], "ui:widget": "radio"}, "navColorLeft": {"title": "导航栏色值-左", "type": "string", "ui:width": "51%"}, "navColorRight": {"title": "导航栏色值-右", "type": "string", "ui:width": "70%", "ui:options": {"addonAfter": "若填写，则为渐变效果，请填写与导航栏色值-左不同的色值"}}, "imageUrl": {"title": "{{formData.mixType === '3' ? '开户/首购大礼包图片' : formData.mixType === '1' ? '融合运营位图片' : '背景图片'}}", "type": "string", "ui:widget": "uploadImg"}, "imageDarkUrl": {"title": "{{formData.mixType === '3' ? '开户/首购大礼包图片(黑夜)' : formData.mixType === '1' ? '融合运营位图片(黑夜)' : '背景图片(黑夜)'}}", "type": "string", "ui:widget": "uploadImg2"}, "imageSmallSvgaUrl": {"title": "{{formData.mixType === '3' ? '开户/首购大礼包标清图片' : formData.mixType === '1' ? '融合运营位标清图片' : '背景标清图片'}}", "type": "string", "ui:widget": "uploadImg5"}, "imageSmallSvgaDarkUrl": {"title": "{{formData.mixType === '3' ? '开户/首购大礼包标清图片(黑夜)' : formData.mixType === '1' ? '融合运营位标清图片(黑夜)' : '背景标清图片(黑夜)'}}", "type": "string", "ui:widget": "uploadImg6"}, "imageSvgaUrl": {"title": "{{formData.mixType === '3' ? '开户/首购大礼包svga图片' : formData.mixType === '1' ? '融合运营位svga图片' : '背景svga图片'}}", "type": "string", "ui:widget": "uploadImg3"}, "imageSvgaDarkUrl": {"title": "{{formData.mixType === '3' ? '开户/首购大礼包svga图片(黑夜)' : formData.mixType === '1' ? '融合运营位svga图片(黑夜)' : '背景svga图片(黑夜)'}}", "type": "string", "ui:widget": "uploadImg4"}, "masterTitle": {"title": "主标题", "type": "string", "description": "", "ui:width": "60%", "ui:hidden": "{{formData.mixType !== '2'}}", "ui:options": {"addonAfter": "最多10个字"}, "maxLength": 10}, "masterColor": {"title": "主标题色值", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType !== '2'}}"}, "subTitle": {"title": "副标题", "type": "string", "description": "", "ui:width": "60%", "ui:hidden": "{{formData.mixType !== '2'}}", "ui:options": {"addonAfter": "最多15个字"}, "maxLength": 15}, "subColor": {"title": "副标题色值", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType !== '2'}}"}, "buttonText": {"title": "按钮文描", "type": "string", "description": "", "ui:width": "60%", "ui:hidden": "{{formData.mixType !== '2'}}", "ui:options": {"addonAfter": "最多6个字"}, "maxLength": 6}, "buttonColor": {"title": "按钮色值", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType !== '2'}}"}, "buttonTextColor": {"title": "按钮文字色值", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType !== '2'}}"}, "unreceivedBtnName": {"title": "未领取按钮名称", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType !== '3'}}"}, "receivedUnOpenAccBtnName": {"title": "领取未开户按钮名称", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType !== '3'}}"}, "receivedUnBuyFundBtnName": {"title": "领取未首购按钮名称", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType !== '3'}}"}, "receivedUnBuyFundBtnJumpAction": {"title": "领取未首购按钮跳转", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}, "ui:hidden": "{{formData.mixType !== '3'}}"}, "jumpAction": {"title": "跳转链接", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}, "ui:hidden": "{{formData.mixType === '3'}}"}, "version": {"title": "{{formData.mixType === '3' ? '跳转链接版本控制' : '版本控制'}}", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "equities1": {"type": "object", "title": "权益1", "properties": {"type": {"title": "权益类型", "type": "string", "enum": ["expGold", "coupon", "goldCow", "blank"], "enumNames": ["体验金", "优惠券", "金牛会员", "自定义模块"], "ui:width": "20%", "default": "expGold"}, "id": {"title": "{{formData.equities1.type === 'expGold' ? '体验金活动ID' : formData.equities1.type === 'coupon' ? '优惠券ID' : '活动ID'}}", "type": "string", "ui:width": "50%", "ui:hidden": "{{formData.equities1.type === 'blank'}}", "ui:options": {"addonAfter": "{{formData.equities1.type === 'expGold' ? '只可配置一个活动ID' : formData.equities1.type === 'coupon' ? '如有多个优惠券ID，请用英文逗号隔开' : '只能填写一个活动ID'}}"}}, "jnhyTimeDesc": {"title": "金牛会员权益时间", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.equities1.type !== 'goldCow'}}"}, "equitiesDesc": {"title": "权益内容", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.equities1.type !== 'blank'}}"}}, "required": ["type", "id", "jnhyTimeDesc", "equitiesDesc"], "ui:hidden": "{{formData.mixType !== '3'}}"}, "equities2": {"type": "object", "title": "权益2", "properties": {"type": {"title": "权益类型", "type": "string", "enum": ["expGold", "coupon", "goldCow", "blank"], "enumNames": ["体验金", "优惠券", "金牛会员", "自定义模块"], "ui:width": "20%", "default": "coupon"}, "id": {"title": "{{formData.equities2.type === 'expGold' ? '体验金活动ID' : formData.equities2.type === 'coupon' ? '优惠券ID' : '活动ID'}}", "type": "string", "ui:width": "50%", "ui:hidden": "{{formData.equities2.type === 'blank'}}", "ui:options": {"addonAfter": "{{formData.equities2.type === 'expGold' ? '只可配置一个活动ID' : formData.equities2.type === 'coupon' ? '如有多个优惠券ID，请用英文逗号隔开' : '只能填写一个活动ID'}}"}}, "jnhyTimeDesc": {"title": "金牛会员权益时间", "type": "string", "ui:width": "30%", "ui:hidden": "{{formData.equities2.type !== 'goldCow'}}"}, "equitiesDesc": {"title": "权益内容", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.equities2.type !== 'blank'}}"}}, "required": ["type", "id", "jnhyTimeDesc", "equitiesDesc"], "ui:hidden": "{{formData.mixType !== '3'}}"}, "equities3": {"type": "object", "title": "权益3", "properties": {"type": {"title": "权益类型", "type": "string", "enum": ["expGold", "coupon", "goldCow", "blank"], "enumNames": ["体验金", "优惠券", "金牛会员", "自定义模块"], "ui:width": "20%", "default": "goldCow"}, "id": {"title": "{{formData.equities3.type === 'expGold' ? '体验金活动ID' : formData.equities3.type === 'coupon' ? '优惠券ID' : '活动ID'}}", "type": "string", "ui:width": "50%", "ui:hidden": "{{formData.equities3.type === 'blank'}}", "ui:options": {"addonAfter": "{{formData.equities3.type === 'expGold' ? '只可配置一个活动ID' : formData.equities3.type === 'coupon' ? '如有多个优惠券ID，请用英文逗号隔开' : '只能填写一个活动ID'}}"}}, "jnhyTimeDesc": {"title": "金牛会员权益时间", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.equities3.type !== 'goldCow'}}"}, "equitiesDesc": {"title": "权益内容", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.equities3.type !== 'blank'}}"}}, "required": ["type", "id", "jnhyTimeDesc", "equitiesDesc"], "ui:hidden": "{{formData.mixType !== '3'}}"}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "60%", "ui:className": "u-block"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "60%"}}, "required": ["mixName", "mixType", "navColorLeft", "imageUrl", "imageDarkUrl", "masterTitle", "masterColor", "unreceivedBtnName", "receivedUnOpenAccBtnName", "receivedUnBuyFundBtnName", "receivedUnBuyFundBtnJumpAction"]}, "displayType": "row", "showDescIcon": true, "labelWidth": 140}