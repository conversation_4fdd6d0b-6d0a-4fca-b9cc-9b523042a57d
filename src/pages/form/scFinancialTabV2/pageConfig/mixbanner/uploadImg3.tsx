import React from 'react';
import ImgUpload from '../../components/uploadImgOrSvga/index.jsx';
interface dataProps {
  onChange: Function;
  value: string;
  readonly: boolean;
}

export default function(props: dataProps) {
  return (
    <div style={{ width: 514 }}>
      <ImgUpload
        handleChange={(value: any) => props.onChange('imageSvgaUrl', value)}
        imageUrl={props.value}
        isEdit={!props.readonly}
        title=""
        limit={5}
        isOnlySvga
      />
    </div>
  );
}