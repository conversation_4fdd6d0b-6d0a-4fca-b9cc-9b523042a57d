import React from 'react';
import { FormComponentProps } from 'antd/es/form';
import styles from '../index.less';
import { Form, Input, Select } from 'antd';
import UploadImg from '../../components/wrapperUploadImg'; 
import classNames from 'classnames';

const formItemLayout = {
  labelCol: {
    span: 3,
  },
  wrapperCol: {
    span: 21
  }
};
interface BlockFormProps extends FormComponentProps {
    blockData: any,
    handleUpdate: (data: any, index: number) => void,
    position: number,
    isEdit: boolean
}

interface iState {
  page: string,
  darkPage: string
}

class ListBlock extends React.Component<BlockFormProps, iState> {
    constructor(props: BlockFormProps) {
      super(props);
      this.state = {
        page: props.blockData?.page ?? '',
        darkPage: props.blockData?.darkPage ?? ''
      }
    }
    handleImage = (val: string) => {
      const { position, blockData, handleUpdate } = this.props;
      this.setState({
        page: val
      })
      handleUpdate({
        ...blockData,
        version: blockData?.version ?? '',
        page: val
      }, position);
    }
    handleDarkImage = (val: string) => {
      const { position, blockData, handleUpdate } = this.props;
      this.setState({
        darkPage: val
      })
      handleUpdate({
        ...blockData,
        version: blockData?.version ?? '',
        darkPage: val
      }, position);
    }
    static getDerivedStateFromProps(nextProps: any, prevState: any) {
      if ((nextProps.blockData?.page && nextProps.blockData.page !== prevState.blockData?.page) || 
        (nextProps.blockData?.darkPage && nextProps.blockData.darkPage !== prevState.blockData?.darkPage)) {
        return {
          page: nextProps.blockData.page,
          darkPage: nextProps.blockData.darkPage,
        }; 
      }
      return null;
  }
    render() {
      const { page, darkPage } = this.state;
      const { getFieldDecorator } = this.props.form;
      const { position, blockData, isEdit } = this.props;
      const { mainTitle, secTitle, url, version } = blockData;
      return (
        <div className={classNames(styles['m-card'], styles['m-sc-border'])}>
          <Form {...formItemLayout}>
            <Form.Item label={`榜单${position+1}主标题`} wrapperCol={{span: 12}}>
                {getFieldDecorator('mainTitle', {
                    initialValue: mainTitle,
                    rules: [{ required: true, message: `请填写榜单${position+1}主标题` }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label={`榜单${position+1}副标题`} wrapperCol={{span: 12}}>
                {getFieldDecorator('secTitle', {
                    initialValue: secTitle,
                    rules: [{ required: true, message: `请填写榜单${position+1}副标题` }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label={`榜单${position+1}图片-白天`} required={true}>
              <UploadImg onChange={this.handleImage} imgUrl={page} disabled={!isEdit} size={['1005*198']}></UploadImg>
            </Form.Item>
            <Form.Item label={`榜单${position+1}图片-黑夜`} required={true}>
              <UploadImg onChange={this.handleDarkImage} imgUrl={darkPage} disabled={!isEdit} size={['1005*198']}></UploadImg>
            </Form.Item>
            <Form.Item label={`榜单${position+1}跳转链接`} wrapperCol={{span: 18}}>
                {getFieldDecorator('url', {
                    initialValue: url,
                    rules: [{ required: true, message: `请填写榜单${position+1}跳转链接` }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label="跳转链接版本控制" wrapperCol={{span: 12}}>
                {getFieldDecorator('version', {
                    initialValue: version ?? '',
                })(
                    <Input disabled={!isEdit}/>
                )}
                <span className={styles['m-card-required']}>格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist</span>
            </Form.Item>
          </Form>
        </div>
      )
    }
}

const WrappedListBlock = Form.create<BlockFormProps>({ 
  name: 'listblock',
  onValuesChange: (props, changedValues, allValues) => {
    const { handleUpdate, position, blockData } = props;
    handleUpdate({
      ...allValues,
      page: blockData.page,
      darkPage: blockData.darkPage
    }, position);
  }
 })(ListBlock);
export default WrappedListBlock;