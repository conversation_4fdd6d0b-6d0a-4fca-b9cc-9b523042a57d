import React, {useEffect, useState} from 'react';
import WrappedListBlock from './block';

interface dataProps {
    onChange: Function,
    value: any,
    readonly: boolean
}
export default function FeaturedList({onChange, value, readonly }:dataProps ) {
    
    const handleUpdate = (data: any, index: number) => {
        value[index] = data;
        onChange('lists', value);
    }
    return (
        <div>
            {
                [0, 1, 2].map((item) => {
                    let blockData = value[item] ?? {}
                    return (
                        <div key={item}>
                            <WrappedListBlock position={item} handleUpdate={handleUpdate} isEdit={!readonly} blockData={blockData}></WrappedListBlock>
                        </div>
                        
                    )
                })
            }
            
        </div>
    )

}