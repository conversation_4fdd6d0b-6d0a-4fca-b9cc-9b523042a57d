import React, { useEffect, useState } from 'react';
import { Button, message, Row, Col, Popconfirm } from 'antd';
import styles from './index.less';
import api from 'api';
import { DraggableArea } from '@/components/DragTags/index.js';
const {
	fetchSearchhotwordV2: fetchSearchhotword, 
	fetchMixedbannerV2: fetchMixedbanner, 
	fetchscNoticeV2: fetchscNotice, 
	fetchscGonggeV2: fetchscGongge, 
	fetchscGonggeAllV2: fetchscGonggeAll, 
	fetchscOperationPositionV2: fetchscOperationPosition, 
	fetchscUserCommentV2: fetchscUserComment, 
	fetchscCouponV2: fetchscCoupon,
	fetchscOperationblockV2: fetchscOperationblock, 
	fetchscBiggiftbagV2: fetchscBiggiftbag, 
	fetchscProductcardV2: fetchscProductcard, 
	fetchscOperationcardV2: fetchscOperationcard, 
	fetchscThreestepsV2: fetchscThreesteps, 
	fetchscMultidimensionV2: fetchscMultidimension, 
	fetchscFeaturedListV2: fetchscFeaturedList, 
	fetchscMainOperateV2: fetchscMainOperate,
	fetchscSecOperateV2: fetchscSecOperate,
	fetchscFinancialtabV2: fetchscFinancialtab, 
    fetchscMarketSituationV2: fetchscMarketSituation,
    fetchscCapitalTrendV2: fetchscCapitalTrend,
    fetchscFundTrendV2: fetchscFundTrend,
    fetchscInvestmentOpportunityV2: fetchscInvestmentOpportunity,
    fetchscFindInsuranceV2: fetchscFindInsurance,
    fetchscRobustFinancialV2: fetchscRobustFinancial,
	postscFilterIdV2: postscFilterId,
	postscFinancialtabV2: postscFinancialtab
} = api;
interface iFloorData {
    type: string,
    lastEditor: string | null,
    lastEditTime: string,
    content: string,
    index: number,
    name: string,
    config?: any,
    iconTypeSet?: string[],
    title?: any,
    id?: number,
    secTitle?: string,
    moreTitle?: string;
    moreActionUrl?: string;
    moreVersion?: string;
}
const typeExchange = {
    'HotSearch': 'searchhotword',
    'OperationPosition': 'mixbanner',
    'Notice': 'notice',
    'Icon': 'gongge',
    'AllIcon': 'gonggeall',
    'Holder': 'operationposition',
    'UserComment': 'usercomment',
    'Packet': 'coupon',
    'Block': 'operationblock',
    'customize-BigGift': 'biggiftbag',
    'OperationCard': 'operationCard',
    'customize-NewAccount': 'threesteps',
    'customize-ProductCard': 'productCard',
    'ChooseFund': 'multidimension',
    'SpecialList': 'featuredList',
    'MainOperate': 'mainOperate',
    'SecOperate': 'secOperate',
    'MarketSituation': 'marketSituation',
    'CapitalTrend': 'capitalTrend',
    'FundTrend': 'fundTrend',
    'FindChance': 'FindChance',
    'RobustFinancial': 'RobustFinancial',
    'FindInsurance': 'FindInsurance'
}
//index是列表数序
const init1: iFloorData[] = [
    {type: "HotSearch", index: 0, name: '搜索热词', lastEditor: null, lastEditTime: '', content:''},
    {type: "Holder", index: 1, name: '持仓运营位', lastEditor: null, lastEditTime: '', content:''},
    {type: "OperationPosition", index: 2, name: '融合运营位', lastEditor: null, lastEditTime: '', content:''},
    {type: "Icon", index: 3, name: '宫格', lastEditor: null, lastEditTime: '', content:''},
    {type: "AllIcon", index: 4, name: '宫格-全部页面', lastEditor: null, lastEditTime: '', content:''},
    {type: "Notice", index: 5, name: '公告', lastEditor: null, lastEditTime: '', content:''},
    {type: "UserComment", index: 999, name: '用户精评', lastEditor: null, lastEditTime: '', content:''},
]
//index默认列表顺序，会按照接口返回重新排序，id是拖动排序时的索引
const init2: iFloorData[] = [
    {type: "Packet", index: 7, name: '营销利益点-优惠券', lastEditor: null, lastEditTime: '', content:'', id: 7},
    {type: "Block", index: 8, name: '营销利益点-运营豆腐块', lastEditor: null, lastEditTime: '', content:'', id: 8},
	{type: "MainOperate", index: 9, name: '营销利益点-主运营位', lastEditor: null, lastEditTime: '', content:'', id: 9},
    {type: "SecOperate", index: 10, name: '营销利益点-次运营位', lastEditor: null, lastEditTime: '', content:'', id: 10},
    {type: "customize-BigGift", index: 11, name: '营销利益点-开户/首购大礼包', lastEditor: null, lastEditTime: '', content:'', id: 11},
    {type: "OperationCard", index: 12, name: '营销利益点-运营大卡',lastEditor: null, lastEditTime: '', content:'', id: 12},
    {type: "customize-NewAccount", index: 13, name: '营销利益点-新人三步走', lastEditor: null, lastEditTime: '', content:'', id: 13},
    {type: "MarketSituation", index: 14, name: '市场行情 - 市场情况', lastEditor: null, lastEditTime: '', content:'', id: 14},
    {type: "CapitalTrend", index: 15, name: '市场行情 - 资金动向', lastEditor: null, lastEditTime: '', content:'', id: 15},
    {type: "FundTrend", index: 16, name: '市场行情 - 基金动向', lastEditor: null, lastEditTime: '', content:'', id: 16},
    {type: "customize-ProductCard", index: 17, name: '基金推荐-产品大卡', lastEditor: null, lastEditTime: '', content:'', id: 17},
    {type: "ChooseFund", index: 18, name: '基金推荐-多维度选基', lastEditor: null, lastEditTime: '', content:'', id: 18},
    {type: "SpecialList", index: 19, name: '基金推荐-特色榜单', lastEditor: null, lastEditTime: '', content:'', id: 19},
    {type: "FindChance", index: 20, name: '市场行情 - 投资机会', lastEditor: null, lastEditTime: '', content:'', id: 20},
    {type: "FindInsurance", index: 21, name: '保险板块', lastEditor: null, lastEditTime: '', content:'', id: 21},
    {type: "RobustFinancial", index: 22, name: '基金推荐 - 稳健理财', lastEditor: null, lastEditTime: '', content:'', id: 22},
]

export default function () {
    const [initData1, setData1] = useState(init1);
    const [initData2, setData2] = useState(init2);
    const [init, setInit] = useState(false);
    const [filterIdStr, setFilterIdStr] = useState('');

    useEffect(() => {
        fetchData();
    },[])
    function fetchData() {
        let promise0 = fetchscFinancialtab();
        let promise1 = fetchSearchhotword();
        let promise2 = fetchMixedbanner();
        let promise3 = fetchscNotice();
        let promise4 = fetchscGongge();
        let promise5 = fetchscGonggeAll();
        let promise6 = fetchscOperationPosition();
        let promise7 = fetchscUserComment();
        let promise8 = fetchscCoupon();
        let promise9 = fetchscOperationblock();
        let promise10 = fetchscBiggiftbag();
        let promise11 = fetchscProductcard();
        let promise12 = fetchscOperationcard();
        let promise13 = fetchscThreesteps();
        let promise14 = fetchscMultidimension();
        let promise15 = fetchscFeaturedList();
		let promise16 = fetchscMainOperate();
		let promise17 = fetchscSecOperate();
        let promise18 = fetchscMarketSituation();
        let promise19 = fetchscCapitalTrend();
        let promise20 = fetchscFundTrend();
        let promise21 = fetchscInvestmentOpportunity();
        let promise22 = fetchscFindInsurance();
        let promise23 = fetchscRobustFinancial();
        let promiseArr = [promise0, promise1, promise2, promise3, promise4, promise5, promise6, promise7, promise8, promise9,
            promise10, promise11, promise12, promise13, promise14, promise15, promise16, promise17, promise18, promise19, promise20, promise21, promise22, promise23];
        Promise.all(promiseArr).then((res: any) => {
            let res0 = res[0];
            let _initData1 = [...initData1];
            let _initData2 = [...initData2];
            if (res0?.code === '0000') {
                let data = res0?.data && JSON.parse(res0.data);
                if (data) {
                    Object.keys(data)?.forEach((val: any)=> {
                        let _target1 = _initData1.find((item) => item.type === val)
                        if (_target1) {
                            _target1.lastEditor = data[val]?.lastEditor;
                            _target1.lastEditTime = data[val]?.lastEditTime;
                            _target1.content = data[val]?.content;
                        } else {
                            let _target2 = _initData2.find((item) => item.type === val)
                            if (_target2) {
                                _target2.lastEditor = data[val]?.lastEditor;
                                _target2.lastEditTime = data[val]?.lastEditTime;
                                _target2.content = data[val]?.content;
                                _target2.index = data[val]?.index;
                            }
                        }
                    })
                    _initData2.sort((a,b)=> a.index - b.index)
                }
            } else {
                message.error(res0?.message || '系统繁忙');
            }
            let str = '';
            for (let i = 1; i < promiseArr.length; i++) {
                let result = res[i];
                if (result?.code === '0000') {
                    let data = (result?.data && JSON.parse(result.data)) ?? {};
                    let _target1 = _initData1.find((item) => item.type === data.type);
                    if (_target1) {
                        let content = '', config: any = [];
                        _target1.lastEditor = data.lastEditor;
                        _target1.lastEditTime = data.lastEditTime;
                        if (data.type === 'HotSearch') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { searchWord, jumpAction, version, startTime, endTime, id } = item;
                                content += `${searchWord}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    word: searchWord,
                                    url: jumpAction,
                                    version,
                                    startTime,
                                    endTime,
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'OperationPosition') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { mixName, mixType, navColorLeft, navColorRight, imageUrl, imageDarkUrl, imageSmallSvgaUrl, imageSmallSvgaDarkUrl, imageSvgaUrl, imageSvgaDarkUrl, jumpAction, version, startTime, endTime,
                                masterTitle, masterColor, subTitle, subColor, buttonText, buttonColor, buttonTextColor, id, unreceivedBtnName, receivedUnOpenAccBtnName, receivedUnBuyFundBtnName, receivedUnBuyFundBtnJumpAction, equities1, equities2, equities3 } = item;
                                content += `${mixName}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj: any = {
                                    index,
                                    name: mixName,
                                    pageType: mixType,
                                    barColor: navColorRight ? [navColorLeft, navColorRight] : [navColorLeft],
                                    page: imageUrl,
                                    pageDark: imageDarkUrl,
                                    imageSvgaUrl,
                                    imageSvgaDarkUrl,
                                    imageSmallSvgaUrl,
                                    imageSmallSvgaDarkUrl,
                                    actUrl: jumpAction,
                                    version,
                                    startTime,
                                    endTime,
                                    mainTitle: masterTitle,
                                    mainTitleColor: masterColor,
                                    secTitle: subTitle,
                                    secTitleColor: subColor,
                                    buttonText: buttonText,
                                    buttonColor: buttonColor,
                                    buttonTextColor: buttonTextColor,
                                    filterId: id,
                                    unreceivedBtnName,
                                    receivedUnOpenAccBtnName,
                                    receivedUnBuyFundBtnName,
                                    receivedUnBuyFundBtnJumpAction,
                                    equities: equities1 ? [equities1, equities2, equities3] : undefined,
                                }
								config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'Notice') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { noticeTitle, jumpAction, version, startTime, endTime, id } = item;
                                content += `${noticeTitle}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    title: noticeTitle,
                                    url: jumpAction,
                                    version,
                                    startTime,
                                    endTime,
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'Icon') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { gonggeName, page, darkPage, jumpAction, version, angleMark, startTime, endTime, id } = item;
                                content += `${gonggeName}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    name: gonggeName,
                                    page,
                                    darkPage,
                                    url: jumpAction,
                                    version,
                                    mark: angleMark,
                                    startTime,
                                    endTime,
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'AllIcon') {
                            _target1.iconTypeSet = data.gonggeClassify ?? [];
                            data.confs?.forEach((item: any, index: number) => {
                                const { gonggeType, gonggeName, page, darkPage, jumpAction, version, angleMark, startTime, endTime, id } = item;
                                content += `${gonggeName}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    iconType: gonggeType,
                                    name: gonggeName,
                                    page,
                                    darkPage,
                                    url: jumpAction,
                                    version,
                                    mark: angleMark,
                                    startTime,
                                    endTime,
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'Holder') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { tag, textTitle, jumpAction, version, startTime, endTime, id } = item;
                                content += `${textTitle}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    tag: tag,
                                    title: textTitle,
                                    url: jumpAction,
                                    version,
                                    startTime,
                                    endTime,
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'UserComment') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { userCommentName, commentList, id } = item;
                                content += `${userCommentName}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    name: userCommentName,
                                    commentList: commentList ?? [],
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        }
                    } else {
                        let _target2 = _initData2.find((item) => item.type === data.type);
                        if (_target2) {
                            let content = '', config: any = [];
                            _target2.lastEditor = data.lastEditor;
                            _target2.lastEditTime = data.lastEditTime;
                            if (data.type === 'Packet') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const { couponName, imageUrl, couponText, couponId, achieveText, seeText, jumpAction, version, startTime, endTime, id } = item;
                                    content += `${couponName}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        name: couponName,
                                        page: imageUrl,
                                        content: couponText,
                                        packetId: couponId,
                                        receiveButton: achieveText,
                                        watchButton: seeText,
                                        watchUrl: jumpAction,
                                        version,
                                        startTime,
                                        endTime,
                                        index,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'Block') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const { name, blocks, startTime, endTime, id } = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        name,
                                        blocks: blocks ?? [],
                                        startTime,
                                        endTime,
                                        index,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'customize-BigGift') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const { name, page, title, noneReceiveButton, noneAccountButton, noneBuyButton, noneBuyUrl, version,
                                        gifts, startTime, endTime, id } = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        name,
                                        page,
                                        title,
                                        noneReceiveButton,
                                        noneAccountButton,
                                        noneBuyButton,
                                        noneBuyUrl,
                                        version,
                                        gifts: gifts ?? [],
                                        startTime,
                                        endTime,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'OperationCard') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const { name, page, darkPage, url, version, startTime, endTime, id } = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        name,
                                        page,
                                        darkPage,
                                        content: item.content,
                                        url,
                                        version,
                                        startTime,
                                        endTime,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'customize-NewAccount') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const { name, page, title, steps, url, finishButton, version, startTime, endTime, id } = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        name,
                                        page,
                                        title,
                                        steps: steps ?? [],
                                        finishButton,
                                        url,
                                        version,
                                        startTime,
                                        endTime,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'customize-ProductCard') {
                                _target2.title = data.title;
                                data.confs?.forEach((item: any, index: number) => {
                                    content += `${item.name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let fundManagerId = '';
                                    if (item.type === '1') {
                                        for(let i = 0, len = item.managerInfo?.length; i < len; i++) {
                                            if (item.managerInfo[i]?.name === item.fundManagerName) {
                                                fundManagerId = item.managerInfo[i]?.id;
                                                break;
                                            }
                                        }
                                    }
                                    
                                    let obj = {
                                        type: item.type,
                                        name: item.name,
                                        fundCode: item.fundCode,
                                        fundName: item.fundName,
                                        fundManagerName: item.fundManagerName,
                                        fundManagerId: fundManagerId,
                                        fundManagerTag: item.fundManagerTag,
                                        reason: item.reason,
                                        show: item.show ?? [],
                                        title: item.title,
                                        secTitle: item.secTitle,
                                        fundManagerPage: item.fundManagerPage,
                                        fundTag: item.fundTag,
                                        profitSection: item.profitSection,
                                        buyStartTime: item.buyStartTime,
                                        buyEndTime: item.buyEndTime,
                                        targetData: item.targetData ?? [],
                                        buyCount: item.buyCount,
                                        recommendType: item.recommendType,
                                        button: item.button,
                                        watchUrl: item.watchUrl,
                                        version: item.version,
                                        startTime: item.startTime,
                                        endTime: item.endTime,
                                        filterId: item.id,
                                        index
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'ChooseFund') {
                                _target2.title = data.title;
                                data.confs?.forEach((item: any, index: number) => {
                                    content += `${item.title}${index === data.confs.length - 1 ? '' : ','}`;
                                    let arr: any = [];
                                    let obj = {
                                        tabType: item.tabType,
                                        title: item.title,
                                        secTitle: item.secTitle,
                                        url: item.url,
                                        version: item.version,
                                        recommendType: item.recommendType,
                                        fundConfigList: [],
                                        startTime: item.startTime,
                                        endTime: item.endTime,
                                        filterId: item.id,
                                        index
                                    }
                                    item.fundConfigList?.forEach((list: any) => {
                                        const { profitSection, profitSectionTwo, ...other } = list;
                                        let value = {...other};
                                        if (item.tabType !== 'mainTrack') {
                                            value.range = profitSection;
                                        } else {
                                            value.rangeRateList = [
                                                {range: profitSection},
                                                {range: profitSectionTwo}
                                            ]
                                        }
                                        arr.push(value);
                                    })
                                    obj.fundConfigList = arr;
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'SpecialList') {
                                data.confs?.forEach((item: any, index: number) => {
                                    content += `${item.title}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        title: item.title,
                                        secTitle: item.secTitle,
                                        moreTitle: item.moreTitle,
                                        url: item.url,
                                        version: item.version,
                                        lists: item.lists ?? [],
                                        startTime: item.startTime,
                                        endTime: item.endTime,
                                        filterId: item.id,
                                        index
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'MainOperate') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const {name, page, url, version, startTime, endTime, id} = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj: any = {
                                        index,
                                        name,
                                        url,
                                        page,
                                        version,
                                        startTime,
                                        endTime,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'SecOperate') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const {name, page1, url1, version1, page2, url2, version2, startTime, endTime, id} = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj: any = {
                                        index,
                                        name,
                                        url1,
                                        page1,
                                        version1,
                                        url2,
                                        page2,
                                        version2,
                                        startTime,
                                        endTime,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'MarketSituation') {
                                _target2.title = data.title;
                                data.confs?.forEach((item: any, index: number) => {
                                    const {name, desc, moreText, url, version, startTime, endTime, id, fundPositionUpUrl, fundPositionUpVersion, investorEmotionUrl, investorEmotionVersion, indexValuationUpUrl, indexValuationUpVersion, fundPositionDownUrl, fundPositionDownVersion, indexValuationDownUrl, indexValuationDownVersion} = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj: any = {
                                        index,
                                        name,
                                        desc,
                                        moreText,
                                        url,
                                        version,
                                        startTime,
                                        endTime,
                                        filterId: id,
                                        fundPositionUpUrl,
                                        fundPositionUpVersion,
                                        investorEmotionUrl,
                                        investorEmotionVersion,
                                        indexValuationUpUrl,
                                        indexValuationUpVersion,
                                        fundPositionDownUrl,
                                        fundPositionDownVersion,
                                        indexValuationDownUrl,
                                        indexValuationDownVersion,
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'CapitalTrend') {
                                _target2.title = data.title;
                                data.confs?.forEach((item: any, index: number) => {
                                    const {manager = [], startTime, endTime, id} = item;
                                    let managerName = "";
                                    for (let i = 0; i < manager.length; i++) {
                                        if (i !== 0) managerName += ','
                                        managerName += manager[i].name;
                                    }
                                    content += `${managerName}${index === data.confs.length - 1 ? '' : ';'}`;
                                    let obj: any = {
                                        index,
                                        manager,
                                        startTime,
                                        endTime,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'FundTrend') {
                                _target2.title = data.title;
                                _target2.content = data.title;
                                _target2.config = config;
                            } else if (data.type === 'FindChance') {
                                _target2.title = data.title;
                                _target2.moreTitle = data.moreTitle;
                                _target2.moreActionUrl = data.moreActionUrl;
                                _target2.moreVersion = data.moreVersion
                                data.confs?.forEach((item: any, index: number) => {
                                    const {topicName, topicTag, topicTitle, nodeList, moreDesc, moreActionUrl, moreVersion, fundDesc, fundObj, fundTags, fundBuyDesc, startTime, endTime, id} = item;
                                    content += `${topicName}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj: any = {
                                        index,
                                        topicName,
                                        topicTag,
                                        topicTitle,
                                        nodeList,
                                        moreDesc,
                                        moreActionUrl,
                                        moreVersion,
                                        fundCode: fundObj ? fundObj.code : '',
                                        fundName: fundObj ? fundObj.name : '',
                                        fundDesc,
                                        fundTags,
                                        fundBuyDesc,
                                        startTime,
                                        endTime,
                                        filterId: id,
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'FindInsurance') {
                                _target2.title = data.title;
                                _target2.moreTitle = data.moreTitle;
                                _target2.moreActionUrl = data.moreActionUrl;
                                _target2.moreVersion = data.moreVersion;
                                data.confs?.forEach((item: any, index: number) => {
                                    const {tabName, tabImage, darkTabImage,productImage, productMainTitle, productSecTitle, buyButtonDesc, buyActionUrl, buyVersion, startTime, endTime, id} = item;
                                    content += `${tabName}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj: any = {
                                        index,
                                        tabName,
                                        tabImage,
                                        darkTabImage,
                                        productImage,
                                        productMainTitle,
                                        productSecTitle,
                                        buyButtonDesc,
                                        buyActionUrl,
                                        buyVersion,
                                        startTime,
                                        endTime,
                                        filterId: id,
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'RobustFinancial') {
                                _target2.title = data.title;
                                _target2.secTitle = data.secTitle;
                                _target2.moreTitle = data.moreTitle;
                                _target2.moreActionUrl = data.moreActionUrl;
                                _target2.moreVersion = data.moreVersion;
                                data.confs?.forEach((item: any, index: number) => {
                                    const {productList, startTime, endTime, id} = item;
                                    for (let i = 0; i < productList.length; i++) {
                                        if (productList[i].fundObj) {
                                            content += `${productList[i].fundObj.code} `;
                                        }
                                    }
                                    content += `${index === data.confs.length - 1 ? '' : ','}`;
                                    let _productList = [...productList];
                                    for (let i = 0; i < _productList.length; i++) {
                                        _productList[i].productCode = _productList[i].fundObj ? _productList[i].fundObj.code : '';
                                        _productList[i].productName = _productList[i].fundObj ? _productList[i].fundObj.name : '';
                                        delete _productList[i].fundObj
                                    }
                                    let obj: any = {
                                        index,
                                        productList: _productList,
                                        startTime,
                                        endTime,
                                        filterId: id,
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            }
                        }
                    }
                    if (data?.confs?.length > 0) {
                        data.confs.forEach((item: any) => {
                            if (item?.id) {
                                str += item.id + ','
                            }
                        })
                    }
                } else {
                    message.error(res0?.message || '系统繁忙');
                }
            }
            if (str.length > 0 && str[str.length - 1] === ',') {
                str = str.substr(0, str.length-1);
            }
            setFilterIdStr(str);
            setData1(_initData1)
            setData2(_initData2)
            setInit(true)
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }
    function handleSubmit(flag: boolean){ 
        let _data = [...initData1, ...initData2];
        if (flag) {
            _data.sort((a, b) =>  (a.lastEditTime || b.lastEditTime) ? (+new Date(b.lastEditTime)  - (+new Date(a.lastEditTime))) : 1)
            if(_data[0].lastEditor === localStorage.name){
                message.error('发布者和最后一个编辑楼层的最后编辑人不能为同一人')
                return
            }
        }
        let _sendData: any = {};
        initData1.forEach((item: any) => {
            const { type, ...other } = item;
            _sendData[type] = {
                ...other,
                type: '固定楼层'
            }
        })
        initData2.forEach((item: any, index: number) => {
            const { type, ...other } = item;
            _sendData[type] = {
                ...other,
                type: '可变楼层',
                index: index + 7
            }
        })
        postscFinancialtab({
            value: JSON.stringify(_sendData),
        }).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.message);
            } else {
                message.success('保存并发布成功！');
            }
        }).catch((e: Error) => {
            message.error(e?.message);
        })
        postscFilterId({
            value: JSON.stringify({
                filterIds: filterIdStr
            }),
        }).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.message);
            }
        }).catch((e: Error) => {
            message.error(e?.message);
        })
    } 
    function jumpUrl(tag: iFloorData){
        let hash = typeExchange[tag.type];
        location.href = `#/form/scFinancialTabV2/pageConfig/${hash}`
    }
    function handleFloor(dataFloor: iFloorData[]) {
        setData2(dataFloor);
    } 
    if (!init) return '加载中'
    return <div className={styles['m-drag']}>
            <div className={'g-mb20'}>
                <span className={'g-ml20'}>
                    <Popconfirm
                        title="确定保存并发布？"
                        onConfirm={() => handleSubmit(true)}
                        okText="确定"
                        cancelText="取消"
                    >
                        <Button type="primary">保存并发布</Button>
                    </Popconfirm>
                    
                </span>
                {
                    window.location.hostname.includes('localhost') ||
                    window.location.hostname.includes('febs.') ?
                    <span className={'g-ml20'}>
                        <Popconfirm
                            title="确定保存并发布？"
                            onConfirm={() => handleSubmit(false)}
                            okText="确定"
                            cancelText="取消"
                        >
                            <Button type="primary">保存并发布(测试环境使用，无人员校验)</Button>
                        </Popconfirm>
                    </span> : null
                }
                <span className={'g-ml20'}>二期配置</span>
            </div>
            <div  className={styles['m-head']}>
                <Row className={styles['m-row']}>
                    <Col span={4}>楼层样式</Col>
                    <Col span={2}>楼层类型</Col>
                    <Col span={10}>楼层内容</Col>
                    <Col span={3}>最后编辑人</Col>
                    <Col span={3}>最后编辑时间</Col>
                    <Col span={2}>操作</Col>
                </Row> 
                
            </div>   
            {initData1.map((tag,index) => {
                return <div className={styles['tag']} key={index}>
                    <Row className={styles['m-row-top']}>
                        <Col span={4}>{tag.name}</Col>
                        <Col span={2}>固定楼层</Col>
                        <Col span={10} className={styles['tag-content']}>{tag.content}</Col>
                        <Col span={3}>{tag.lastEditor}</Col>
                        <Col span={3}>{tag.lastEditTime}</Col>
                        <Col span={2}>
                            <Button onClick={()=> jumpUrl(tag)}>编辑</Button>
                        </Col>
                    </Row>
                </div>
            })}            
            <DraggableArea
                isList
                tags={initData2}
                render={({tag}: {tag: iFloorData}) => (
                    <div className={styles["tag"]}>
                        <Row className={styles['m-row']}>
                            <Col span={4}>{tag.name}</Col>
                            <Col span={2}>可变楼层</Col>
                            <Col span={10} className={styles['tag-content']}>{tag.content}</Col>
                            <Col span={3}>{tag.lastEditor}</Col>
                            <Col span={3}>{tag.lastEditTime}</Col>
                            <Col span={2}>
                                <Button  onClick={()=> jumpUrl(tag)}>编辑</Button>
                            </Col>
                        </Row>
                    </div>
                )}
                onChange={(data: iFloorData[]) => {
                    handleFloor(data)
                }}
            />
            </div>

}