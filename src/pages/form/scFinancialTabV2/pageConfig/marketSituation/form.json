{"schema": {"type": "object", "properties": {"name": {"title": "策略名称", "type": "string", "description": "", "ui:width": "60%", "ui:options": {}}, "desc": {"title": "市场解读", "type": "string", "description": "", "ui:width": "60%", "ui:options": {}}, "moreText": {"title": "更多按钮文描", "type": "string", "description": "", "ui:width": "60%", "ui:options": {}}, "url": {"title": "跳转链接", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "version": {"title": "版本控制", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "fundPositionUpUrl": {"title": "基金仓位上跳转链接", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "fundPositionUpVersion": {"title": "基金仓位上跳转版本控制", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "investorEmotionUrl": {"title": "投资者情绪跳转链接", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "investorEmotionVersion": {"title": "投资者情绪跳转版本控制", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "indexValuationUpUrl": {"title": "指数估值上跳转链接", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "indexValuationUpVersion": {"title": "指数估值上跳转版本控制", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "fundPositionDownUrl": {"title": "基金仓位下跳转链接", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "fundPositionDownVersion": {"title": "基金仓位下跳转版本控制", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "indexValuationDownUrl": {"title": "指数估值下跳转链接", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "indexValuationDownVersion": {"title": "指数估值下跳转版本控制", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "60%", "ui:className": "u-block"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "60%"}}, "required": ["name", "desc"]}, "displayType": "row", "showDescIcon": true, "labelWidth": 140}