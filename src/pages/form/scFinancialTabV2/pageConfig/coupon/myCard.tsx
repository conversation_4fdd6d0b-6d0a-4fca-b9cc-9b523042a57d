import React, {useEffect, useState} from 'react';
import { Button, message, Popconfirm } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../index.less';
import DesignatedUser from '../../components/DesignatedUser';
import api from 'api';
import { timeFormat2 } from '@/utils/utils';
import UploadImg from './uploadImg'; 

const { updateUserType, checkCoupon } = api;
interface dataProps {
    data: any;
    kycTag: any;
    olasTag: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
}
export default function ({data, kycTag, olasTag, position, handleUpdate, handleDelete}: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    const [designatedData, setDesignatedData] = useState<any>();
    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [formData, setFormState] = useState<any>({});

    useEffect(() => {
        setFormState(data.formData);
        setDesignatedData({
            configData: data.configData,
            relationData: data.relationData
        })
    }, [data]);

    const onValidate = (valid: any) => {
        setValid(valid);
    }
    const handleChange = async () =>{
        if(isEdit){
            if (!formData.couponName) {
                message.error('请填写优惠券运营位名称')
                return;
            }
            if (!formData.imageUrl) {
                message.error('请上传优惠券图标')
                return;
            }
            if (!formData.couponText) {
                message.error('请填写优惠券展示文案')
                return;
            }
            if (!formData.couponId) {
                message.error('请填写优惠券ID')
                return;
            }
            if (!formData.achieveText) {
                message.error('请填写领取按钮文描')
                return;
            }
            if (!formData.seeText) {
                message.error('请填写查看按钮文描')
                return;
            }
            if (!formData.jumpAction) {
                message.error('请填写跳转链接')
                return;
            }
            if (formData.startTime && formData.endTime) {
                let startTime = formData.startTime.replace(/[^\d]/g, '');
                let endTime = formData.endTime.replace(/[^\d]/g, '');
                if (startTime >= endTime) {
                    message.error('开始时间应早于结束时间');
                    return;
                }
            }   
            try {
                let result = await checkCoupon({}, formData.couponId);
                const { status_code, status_msg } = result;
                if (status_code === 0) {
                    if (result?.data?.length > 0) {
                        for (let i = 0, len = result.data.length; i < len; i++) {
                            const { couponStartDate, couponEndDate, couponId } = result.data[i];
                            if ( couponStartDate && couponEndDate ) {
                                let time = timeFormat2();
                                time = time.replace(/[^\d]/g, '');
                                if (time < couponStartDate || time > couponEndDate) {
                                    message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                    return;
                                }
                                if (formData.startTime) {
                                    let startTime = formData.startTime.replace(/[^\d]/g, '');
                                    if (startTime < couponStartDate) {
                                        message.error('模块的时间范围必须在优惠券的时间范围之内');
                                        return;
                                    }
                                }
                                if (formData.endTime) {
                                    let endTime = formData.endTime.replace(/[^\d]/g, '');
                                    if (endTime >= couponEndDate) {
                                        message.error('模块的时间范围必须在优惠券的时间范围之内');
                                        return;
                                    }
                                }
                            } else {
                                message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                return;
                            }
                        }
                    } else {
                        message.error('请检查优惠券ID是否正确');
                        return;
                    }
                } else {
                    message.error(status_msg || '网络请求错误，请稍后再试');
                    return;
                }
            } catch(e) {
                message.error(e.message);
                return;
            }
            
            let realData = {};
            const {configData, relationData} = designatedData;
            if (relationData?.userType === '1') {
                const {blackCustId, whiteCustId, ...other} = relationData;
                realData = {...other}
            } else {
                const {blackUserId, whiteUserId, ...other} = relationData;
                realData = {...other}
            }
            let _data: any = {
                formData,
                configData,
                relationData: realData
            }
            if (configData.platform?.length === 0) {
                message.error('请选择使用平台')
                return;
            }
            if (configData.utype?.length === 0) {
                message.error('请选择用户类型')
                return;
            }
            if (relationData?.id) {
              handleUpdate(_data, position)   
            } else {
                let time = timeFormat2();
                let obj = {
                    ...configData,
                    ...realData,
                    updateTime: time
                };
                updateUserType(obj).then((res: any) => {
                    if (res.code !== '0000') return message.error(res.message);
                    let id = res.data;
                    _data.relationData.id = id;
                    _data.relationData.updateTime = time;
                    handleUpdate(_data, position);
                })
                .catch((e: Error) => {
                    message.error(e.message || '系统错误');
                });
            }
            
        } else {
            setEdit(!isEdit)
        }
    }
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {handleDelete(position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <h1 className="g-fs20 f-bold">配置详情</h1>
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={onValidate}
                    onChange={setFormState}
                    readOnly={!isEdit}
                    labelWidth={FORM_CONFIG.labelWidth}
                    widgets={{
                        uploadImg: UploadImg,
                    }}
                />
                <DesignatedUser
                    data={data}
                    position={position}
                    kycTag={kycTag}
                    olasTag={olasTag}
                    isEdit={isEdit}
                    setDesignatedData={setDesignatedData}
                />
            </div>

}