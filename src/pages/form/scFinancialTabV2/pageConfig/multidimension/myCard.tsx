import React, {useEffect, useState, useCallback} from 'react';
import { Button, message, Popconfirm } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../index.less';
import DesignatedUser from '../../components/DesignatedUser';
import api from 'api';
import { timeFormat2 } from '@/utils/utils';
import BaseFundConfigList from './blockList';

const { updateUserType } = api;
interface dataProps {
    data: any;
    kycTag: any;
    olasTag: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
}
export default function ({data, kycTag, olasTag, position, handleUpdate, handleDelete}: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    const [designatedData, setDesignatedData] = useState<any>();
    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [formData, setFormState] = useState<any>({});

    useEffect(() => {
        setFormState(data.formData);
        setDesignatedData({
            configData: data.configData,
            relationData: data.relationData
        })
    }, [data]);

    const onValidate = (valid: any) => {
        setValid(valid);
    }
    const handleChange = () =>{
        if(isEdit){
            if (!formData.tabType) {
                message.error('请选择tab类型')
                return;
            }
            if (!formData.title) {
                message.error('请填写tab标题')
                return;
            }
            if (!formData.secTitle) {
                message.error('请填写tab副标题')
                return;
            }
            if (!formData.recommendType) {
                message.error('请选择是否由系统推荐')
                return;
            }
            if (['1', '3', '5'].includes(formData.tabType) && formData.recommendType === '2') {
                for (let i = 0; i < 3; i++) {
                    let obj = formData?.fundConfigList[i] ?? {};
                    const { trackName, code, name, title, secTitle, profitSection, profitSectionTwo, url, rankTag, retracementTag } = obj;
                    if (formData.tabType === 'mainTrack' && !trackName) {
                        message.error(`请填写${i+1}名称`)
                        return;
                    }
                    if (!code) {
                        message.error(`请填写产品${i+1}代码`)
                        return;
                    }
                    if (!name) {
                        message.error(`请填写产品${i+1}名称`)
                        return;
                    }
                    if (!title) {
                        message.error(`请填写产品${i+1}主标题`)
                        return;
                    }
                    if (!secTitle) {
                        message.error(`请填写产品${i+1}副标题`)
                        return;
                    }
                    if (formData.tabType === 'mainTrack') {
                        if (!profitSection) {
                            message.error('请选择收益时间区间1')
                            return;
                        }
                        if (!profitSectionTwo) {
                            message.error('请选择收益时间区间2')
                            return;
                        }
                    } else if (!profitSection) {
                        message.error('请选择收益时间区间')
                        return;
                    }
                    if (['goodFund', 'selfDefine'].includes(formData.tabType)) {
                        if (!retracementTag) {
                            message.error('请选择是否展示近1年最大回撤')
                            return;
                        }
                    }
                    if (!rankTag) {
                        message.error('请选择是否展示近1年收益排名')
                        return;
                    }
                    if (!url) {
                        message.error(`请填写产品${i+1}跳转链接`)
                        return;
                    }
                    
                }
            }
            
            let realData = {};
            const {configData, relationData} = designatedData;
            if (relationData?.userType === '1') {
                const {blackCustId, whiteCustId, ...other} = relationData;
                realData = {...other}
            } else {
                const {blackUserId, whiteUserId, ...other} = relationData;
                realData = {...other}
            }
            let _data: any = {
                formData,
                configData,
                relationData: realData
            }
            if (configData.platform?.length === 0) {
                message.error('请选择使用平台')
                return;
            }
            if (configData.utype?.length === 0) {
                message.error('请选择用户类型')
                return;
            }
            if (relationData?.id) {
              handleUpdate(_data, position)   
            } else {
                let time = timeFormat2();
                let obj = {
                    ...configData,
                    ...realData,
                    updateTime: time
                };
                updateUserType(obj).then((res: any) => {
                    if (res.code !== '0000') return message.error(res.message);
                    let id = res.data;
                    _data.relationData.id = id;
                    _data.relationData.updateTime = time;
                    handleUpdate(_data, position);
                })
                .catch((e: Error) => {
                    message.error(e.message || '系统错误');
                });
            }
            
        } else {
            setEdit(!isEdit)
        }
    }
    const onFormChange = (val: any) => {
        if (['hotSale', 'planTrade'].includes(val.tabType)) {
            val.recommendType = '1'
        } else if (['mainTrack', 'goodFund'].includes(val.tabType)) {
            val.recommendType = '2'
        }
        setFormState(val)
    }
    const FundConfigList = useCallback((props: any) => {
        return (
            <BaseFundConfigList {...props} type={formData.tabType}/>
        )
    }, [formData.tabType])
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {handleDelete(position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <h1 className="g-fs20 f-bold">配置详情</h1>
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={onValidate}
                    onChange={onFormChange}
                    readOnly={!isEdit}
                    labelWidth={FORM_CONFIG.labelWidth}
                    widgets={{
                        fundConfigList: FundConfigList
                    }}
                />
                <DesignatedUser
                    data={data}
                    position={position}
                    kycTag={kycTag}
                    olasTag={olasTag}
                    isEdit={isEdit}
                    setDesignatedData={setDesignatedData}
                />
            </div>

}