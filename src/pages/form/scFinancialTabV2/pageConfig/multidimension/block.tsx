import React from 'react';
import { FormComponentProps } from 'antd/es/form';
import styles from '../index.less';
import { Form, Input, Select, Radio, message } from 'antd';
import classNames from 'classnames';
import api from 'api';
const { fetchFundNameByCode } = api;

const { Option } = Select;
const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20
  }
};
interface BlockFormProps extends FormComponentProps {
    blockData: any,
    handleUpdate: (data: any, index: number) => void,
    handleFocus: (data: any) => void,
    position: number,
    isEdit: boolean,
    type: string
}

class Block extends React.Component<BlockFormProps> {
    constructor(props: BlockFormProps) {
      super(props);
    }
    handleCode = (code: string) => {
        const { position, blockData, handleUpdate, handleFocus } = this.props;
        const { setFieldsValue } = this.props.form;
        handleFocus(true);
        if (code === "") return;
        code = code.trim();
        fetchFundNameByCode({
            fundCode: code
        }).then((data: any) => {
            if (data.code === '0000') {
                data = data.data;
                if (data) {
                    handleUpdate({
                        ...blockData,
                        version: blockData?.version ?? '',
                        name: data.name
                        }, position);
                    setFieldsValue({
                        name: data.name
                    })
                } else {
                    handleUpdate({
                        ...blockData,
                        version: blockData?.version ?? '',
                        code: ''
                    }, position);
                    setFieldsValue({
                        code: ''
                    })
                    message.error('该基金不为基金，请重新输入')
                }
            } else {
                handleUpdate({
                    ...blockData,
                    version: blockData?.version ?? '',
                    code: ''
                }, position);
                setFieldsValue({
                    code: ''
                })
                message.error('请求基金详情接口失败，请稍后重试')
            }
        }).catch((e: Error) => {
            handleUpdate({
                ...blockData,
                version: blockData?.version ?? '',
                code: ''
            }, position);
            setFieldsValue({
                code: ''
            })
            message.error(e?.message || '系统繁忙');
        })
    }
    handleCodeFocus = () => {
        this.props.handleFocus(false)
    }
    render() {
        
      const { getFieldDecorator } = this.props.form;
      const { position, blockData, isEdit, type } = this.props;
      const { trackName, code, name, title, secTitle, profitSection, profitSectionTwo, url, version, rankTag, retracementTag } = blockData;
      return (
        <div className={classNames(styles['m-card'], styles['m-sc-border'])}>
          <Form {...formItemLayout}>
            { type === 'mainTrack' && (
              <Form.Item label={`板块${position+1}名称`} wrapperCol={{span: 12}}>
                  {getFieldDecorator('trackName', {
                      initialValue: trackName,
                      rules: [{ required: true, message: `请填写板块${position+1}名称` }],
                  })(
                      <Input disabled={!isEdit}/>
                  )}
              </Form.Item>
            ) }
            
            <Form.Item label={`产品${position+1}代码`} wrapperCol={{span: 12}}>
                {getFieldDecorator('code', {
                    initialValue: code,
                    rules: [{ required: true, message: `请填写产品${position+1}代码` }],
                })(
                    <Input disabled={!isEdit} onFocus={this.handleCodeFocus} onBlur={(e) => this.handleCode(e.target.value)}/>
                )}
            </Form.Item>
            <Form.Item label={`产品${position+1}名称`} wrapperCol={{span: 12}}>
                {getFieldDecorator('name', {
                    initialValue: name,
                    rules: [{ required: true, message: `请填写产品${position+1}名称` }],
                })(
                    <Input disabled={true}/>
                )}
            </Form.Item>
            <Form.Item label={`产品${position+1}主标题`} wrapperCol={{span: 18}}>
                {getFieldDecorator('title', {
                    initialValue: title,
                    rules: [{ required: true, message: `请填写产品${position+1}主标题` }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label={`产品${position+1}副标题`} wrapperCol={{span: 18}}>
                {getFieldDecorator('secTitle', {
                    initialValue: secTitle,
                    rules: [{ required: true, message: `请填写产品${position+1}副标题` }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label={`收益时间区间${type === 'mainTrack' ? '1' : ''}`} wrapperCol={{span: 12}}>
                {getFieldDecorator('profitSection', {
                      initialValue: profitSection,
                      rules: [{ required: true, message: `请选择产品收益时间区间${type === 'mainTrack' ? '1' : ''}` }],
                  })(
                    <Select style={{width: 200}} disabled={!isEdit}>
                      <Option value="month">近1月</Option>
                      <Option value="threeMonth">近3月</Option>
                      <Option value="halfYear">近6月</Option>
                      <Option value="year">近1年</Option>
                      <Option value="threeYear">近3年</Option>
                      <Option value="fiveYear">近5年</Option>
                      <Option value="startBuild">成立来</Option>
                  </Select>
                )}
            </Form.Item>
            { type === 'mainTrack' && (
                <Form.Item label={`产品${position+1}收益时间区间2`} wrapperCol={{span: 12}}>
                    {getFieldDecorator('profitSectionTwo', {
                          initialValue: profitSectionTwo,
                          rules: [{ required: true, message: '请选择产品收益时间区间2' }],
                      })(
                        <Select style={{width: 200}} disabled={!isEdit}>
                          <Option value="month">近1月</Option>
                          <Option value="threeMonth">近3月</Option>
                          <Option value="halfYear">近6月</Option>
                          <Option value="year">近1年</Option>
                          <Option value="threeYear">近3年</Option>
                          <Option value="fiveYear">近5年</Option>
                          <Option value="startBuild">成立来</Option>
                        </Select>
                    )}
                </Form.Item>
            ) }
            { ['goodFund', 'selfDefine'].includes(type) && (
                <Form.Item label="是否展示近1年最大回撤" wrapperCol={{span: 12}}>
                  {getFieldDecorator('retracementTag', {
                        initialValue: retracementTag,
                        rules: [{ required: true, message: '是否展示近1年最大回撤' }],
                    })(
                      <Radio.Group disabled={!isEdit}>
                          <Radio value={'1'}>是</Radio>
                          <Radio value={'0'}>否</Radio>
                      </Radio.Group>
                  )}
              </Form.Item>
            ) }
            <Form.Item label="是否展示近1年收益排名" wrapperCol={{span: 12}}>
                {getFieldDecorator('rankTag', {
                      initialValue: rankTag,
                      rules: [{ required: true, message: '是否展示近1年收益排名' }],
                  })(
                    <Radio.Group disabled={!isEdit}>
                        <Radio value={'1'}>是</Radio>
                        <Radio value={'0'}>否</Radio>
                    </Radio.Group>
                )}
            </Form.Item>
            <Form.Item label={`产品${position+1}跳转链接`} wrapperCol={{span: 12}}>
                {getFieldDecorator('url', {
                    initialValue: url,
                    rules: [{ required: true, message: `请填写产品${position+1}跳转链接` }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label="跳转链接版本控制" wrapperCol={{span: 12}}>
                {getFieldDecorator('version', {
                    initialValue: version ?? '',
                })(
                    <Input disabled={!isEdit}/>
                )}
                <span className={styles['m-card-required']}>格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist</span>
            </Form.Item>
          </Form>
        </div>
      )
    }
}

const WrappedBlock = Form.create<BlockFormProps>({ 
  name: 'multiblock',
  onValuesChange: (props, changedValues, allValues) => {
    const { handleUpdate, position, blockData } = props;
    handleUpdate({
      ...allValues,
    }, position);
  }
 })(Block);
export default WrappedBlock;