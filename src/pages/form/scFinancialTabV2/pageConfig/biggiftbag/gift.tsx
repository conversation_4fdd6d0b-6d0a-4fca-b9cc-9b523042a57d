import React from 'react';
import { FormComponentProps } from 'antd/es/form';
import styles from '../index.less';
import {Button, Popconfirm, Form, Input, Select, message, Switch, DatePicker, Checkbox, Radio, Modal } from 'antd';
import UploadImg from '../../components/wrapperUploadImg'; 
import classNames from 'classnames';

const { Option } = Select;

const formItemLayout = {
  labelCol: {
    span: 3,
  },
  wrapperCol: {
    span: 21
  }
};
interface GiftFormProps extends FormComponentProps {
    giftData: any,
    handleUpdate: (data: any, index: number) => void,
    position: number,
    isEdit: boolean
}

interface iState {
  page: string
}

class BigGift extends React.Component<GiftFormProps, iState> {
    constructor(props: GiftFormProps) {
      super(props);
      this.state = {
        page: props.giftData?.page ?? ''
      }
    }
    handleImage = (val: string) => {
      const { position, giftData, handleUpdate } = this.props;
      this.setState({
        page: val
      })
      handleUpdate({
        ...giftData,
        version: giftData?.version ?? '',
        page: val
      }, position);
    }
    static getDerivedStateFromProps(nextProps: any, prevState: any) {
      if (nextProps.giftData?.page && nextProps.giftData.page !== prevState.giftData?.page) {
        return {
          page: nextProps.giftData.page,
        }; 
      }
      return null;
  }
    render() {
      const { page } = this.state;
      const { getFieldDecorator } = this.props.form;
      const { position, giftData, isEdit } = this.props;
      const { rewardType, activityId, receiveButton, watchButton, watchUrl, version, rightContent, rightTime } = giftData;
      return (
        <div className={classNames(styles['m-card'], styles['m-sc-border'])}>
          <h1 className="g-fs16 f-bold">权益{position+1}</h1>
          <Form {...formItemLayout}>
            <Form.Item label="权益类型" wrapperCol={{span: 8}}>
                {getFieldDecorator('rewardType', {
                    initialValue: rewardType,
                    rules: [{ required: true, message: '请选择权益类型' }],
                })(
                  <Select style={{width: 200}} disabled={!isEdit}>
                    <Option value="1">体验金</Option>
                    <Option value="2">优惠券</Option>
                    <Option value="3">金牛会员</Option>
                    <Option value="4">自定义模块</Option>
                  </Select>
                )}
            </Form.Item>
            <Form.Item label={`权益${position+1}图片`} required={true}>
              <UploadImg onChange={this.handleImage} imgUrl={page} disabled={!isEdit} size={['270*300']}></UploadImg>
            </Form.Item>
            { 
              ['1', '2', '3'].includes(rewardType) && (
                <Form.Item label={ rewardType === '1' ? '体验金活动ID' : rewardType === '2' ? '优惠券ID' : '活动ID' }>
                    {getFieldDecorator('activityId', {
                        initialValue: activityId,
                        rules: [{ required: true, message: `请填写${rewardType === '1' ? '体验金活动ID' : rewardType === '2' ? '优惠券ID' : '活动ID'}` }],
                    })(
                        <Input disabled={!isEdit} style={{width: '150px', marginRight: '10px'}}/>
                    )}
                    { ['1', '3'].includes(rewardType) && <span className={styles['m-required']}>只可配置一个活动ID</span> }
                    { rewardType === '2' && <span className={styles['m-required']}>如有多个优惠券ID，请用英文逗号隔开</span> }
                </Form.Item>
              )
            }
            { rewardType === '3' && (
              <Form.Item label="金牛会员权益时间" wrapperCol={{span: 12}}>
                {getFieldDecorator('rightTime', {
                    initialValue: rightTime,
                    rules: [{ required: true, message: '请填写金牛会员权益时间' }]
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            ) }
            { rewardType === '4' && (
              <Form.Item label="权益内容" wrapperCol={{span: 12}}>
                {getFieldDecorator('rightContent', {
                    initialValue: rightContent,
                    rules: [{ required: true, message: '请填写权益内容' }]
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            ) }
            { 
              rewardType !== '4' && (
                <Form.Item label="领取按钮文描" wrapperCol={{span: 12}}>
                    {getFieldDecorator('receiveButton', {
                        initialValue: receiveButton,
                        rules: [{ required: true, message: '请填写领取按钮文描' }]
                    })(
                        <Input disabled={!isEdit}/>
                    )}
                </Form.Item>
              )
            }
            
            <Form.Item label={ rewardType === '4' ? '按钮文描' : '查看按钮文描' } wrapperCol={{span: 12}}>
                {getFieldDecorator('watchButton', {
                    initialValue: watchButton,
                    rules: [{ required: true, message: `请填写${rewardType === '4' ? '查看' : ''}按钮文描` }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label={ rewardType === '4' ? '跳转链接' : '查看跳转链接' } wrapperCol={{span: 19}}>
                {getFieldDecorator('watchUrl', {
                    initialValue: watchUrl,
                    rules: [{ required: true, message: `请填写${rewardType === '4' ? '查看' : ''}跳转链接` }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label="跳转链接版本控制" wrapperCol={{span: 12}}>
                {getFieldDecorator('version', {
                    initialValue: version ?? '',
                })(
                    <Input disabled={!isEdit}/>
                )}
                <span className={styles['m-card-required']} s-cr="#f5222d">格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist</span>
            </Form.Item>
          </Form>
        </div>
      )
    }
}

const WrappedGift = Form.create<GiftFormProps>({ 
  name: 'biggift',
  onValuesChange: (props, changedValues, allValues) => {
    const { handleUpdate, position, giftData } = props;
    handleUpdate({
      ...allValues,
      page: giftData.page,
      
    }, position);
  }
 })(BigGift);
export default WrappedGift;