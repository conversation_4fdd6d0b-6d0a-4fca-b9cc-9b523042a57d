import React, {useEffect, useState} from 'react';
import WrappedGift from './gift';

interface dataProps {
    onChange: Function,
    value: any,
    readonly: boolean
}
export default function ({onChange, value, readonly }:dataProps ) {
    
    const handleUpdate = (data: any, index: number) => {
        value[index] = data;
        console.log(value);
        onChange('gifts', value);
    }
    return (
        <div>
            {
                [0, 1, 2].map((item) => {
                    let giftData = value[item] ?? {}
                    return (
                        <div key={item}>
                            <WrappedGift position={item} handleUpdate={handleUpdate} isEdit={!readonly} giftData={giftData}></WrappedGift>
                        </div>
                        
                    )
                })
            }
            
        </div>
    )

}