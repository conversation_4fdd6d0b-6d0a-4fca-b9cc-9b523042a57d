import React, {useEffect, useState} from 'react';
import { Button, message, Popconfirm } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../index.less';
import DesignatedUser from '../../components/DesignatedUser';
import api from 'api';
import { timeFormat2 } from '@/utils/utils';
import UploadImg from './uploadImg'; 
import StepList from './stepList'; 

const { updateUserType, checkCoupon, postActivityDetails } = api;
interface dataProps {
    data: any;
    kycTag: any;
    olasTag: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
}
export default function ({data, kycTag, olasTag, position, handleUpdate, handleDelete}: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    const [designatedData, setDesignatedData] = useState<any>();
    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [formData, setFormState] = useState<any>({});

    useEffect(() => {
        setFormState(data.formData);
        setDesignatedData({
            configData: data.configData,
            relationData: data.relationData
        })
    }, [data]);

    const onValidate = (valid: any) => {
        setValid(valid);
    }
    const handleChange = async() =>{
        if(isEdit){
            if (!formData.name) {
                message.error('请填写三步走策略名称')
                return;
            }
            if (!formData.page) {
                message.error('请上传小图标')
                return;
            }
            if (!formData.title) {
                message.error('请填写模块标题')
                return;
            }

            let activityIdObj: any = {
                coupon: '',
                gold: []
            }
            for (let i = 0; i < 3; i++) {
                let obj = formData?.steps[i] ?? {};
                const { type, activityId, page, status, url, button, buttonUrl } = obj;
                if (!type) {
                    message.error('请选择权益类型')
                    return;
                }
                if (['1','2', '3'].includes(type) && !activityId) {
                    message.error(`请填写${type === '1' ? '体验金活动' : type === '2' ? '优惠券' : '活动'}ID`)
                    return;
                }
                if (!page) {
                    message.error(`请填写步骤${i+1}图片`)
                    return;
                }
                if (!status) {
                    message.error(`请填写步骤${i+1}状态文描`)
                    return;
                }
                if (!url) {
                    message.error('请填写权益查看跳转链接')
                    return;
                }
                if (!button) {
                    message.error(`请填写步骤${i+1}按钮文描`)
                    return;
                }
                if (i === 2 && !buttonUrl) {
                    message.error(`请填写跳转链接`)
                    return;
                } 
                if (type === '1') {
                    activityIdObj.gold.push(activityId);
                }
                if (type === '2') {
                    activityIdObj.coupon += activityId + ',';
                }
            }
            if (!formData.finishButton) {
                message.error('请填写完成按钮文描')
                return;
            }
            if (formData.startTime && formData.endTime) {
                let startTime = formData.startTime.replace(/[^\d]/g, '');
                let endTime = formData.endTime.replace(/[^\d]/g, '');
                if (startTime >= endTime) {
                    message.error('开始时间应早于结束时间');
                    return;
                }
            } 
            try {
                let promiseAll = []
                if (activityIdObj.coupon) {
                    let promiseFace = await checkCoupon({}, activityIdObj.coupon);
                    promiseAll.push(promiseFace);
                }
                if (activityIdObj.gold.length > 0) {
                    for (let i = 0, len = activityIdObj.gold.length; i < len; i++ ) {
                        let promiseFace = await postActivityDetails({
                            type: 'query',
                            activityIndex: activityIdObj.gold[i]?.trim()
                        });
                        promiseAll.push(promiseFace);
                    }
                }
                let resArr = await Promise.all(promiseAll);
                if (activityIdObj.coupon) {
                    let res0 = resArr[0];
                    const { status_code, status_msg } = res0;
                    if (status_code === 0) {
                        if (res0?.data?.length > 0) {
                            for (let i = 0, len = res0.data.length; i < len; i++) {
                                const { couponStartDate, couponEndDate, couponId } = res0.data[i];
                                if ( couponStartDate && couponEndDate ) {
                                    let time = timeFormat2();
                                    time = time.replace(/[^\d]/g, '');
                                    if (time < couponStartDate || time > couponEndDate) {
                                        message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                        return;
                                    }
                                    if (formData.startTime) {
                                        let startTime = formData.startTime.replace(/[^\d]/g, '');
                                        if (startTime < couponStartDate) {
                                            message.error('模块的时间范围必须在优惠券的时间范围之内');
                                            return;
                                        }
                                    }
                                    if (formData.endTime) {
                                        let endTime = formData.endTime.replace(/[^\d]/g, '');
                                        if (endTime >= couponEndDate) {
                                            message.error('模块的时间范围必须在优惠券的时间范围之内');
                                            return;
                                        }
                                    }
                                } else {
                                    message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                    return;
                                }
                            }
                        } else {
                            message.error('请检查优惠券ID是否正确');
                            return;
                        }
                    } else {
                        message.error(status_msg || '网络请求错误，请稍后再试');
                        return;
                    }
                }
                if (activityIdObj.gold.length > 0) {
                    let start = activityIdObj.coupon ? 1 : 0;
                    for (let i = start, len = resArr.length; i < len; i++ ) {
                        const { code } = resArr[i];
                        if (code === '0000') {
                            if (resArr[i]?.data) {
                                let { startTime, endTime, indexStr } = resArr[i]?.data;
                                if ( startTime && endTime ) {
                                    let time = timeFormat2();
                                    time = time.replace(/[^\d]/g, '');
                                    startTime = startTime.replace(/[^\d]/g, '');
                                    endTime = endTime.replace(/[^\d]/g, '');
                                    if (time < startTime || time > endTime) {
                                        message.error(`当前体验金活动ID-${indexStr}未处于生效状态`);
                                        return;
                                    }
                                    if (formData.startTime) {
                                        let formStartTime = formData.startTime.replace(/[^\d]/g, '');
                                        if (formStartTime < startTime) {
                                            message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                            return;
                                        }
                                    }
                                    if (formData.endTime) {
                                         let formEndTime = formData.endTime.replace(/[^\d]/g, '');
                                        if (formEndTime >= endTime) {
                                            message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                            return;
                                        }
                                    }
                                } else {
                                    message.error(`接口未返回体验金活动ID-${indexStr}生效时间`);
                                    return;
                                }
                            } else {
                                message.error('请检查体验金活动ID是否正确');
                                return;
                            }
                        } else {
                            message.error(resArr[i]?.message || '网络请求错误，请稍后再试');
                            return;
                        }
                    }
                }
            }catch(err) {
                message.error(err.message);
                return;
            }
            let realData = {};
            const {configData, relationData} = designatedData;
            if (relationData?.userType === '1') {
                const {blackCustId, whiteCustId, ...other} = relationData;
                realData = {...other}
            } else {
                const {blackUserId, whiteUserId, ...other} = relationData;
                realData = {...other}
            }
            let _data: any = {
                formData,
                configData,
                relationData: realData
            }
            if (configData.platform?.length === 0) {
                message.error('请选择使用平台')
                return;
            }
            if (configData.utype?.length === 0) {
                message.error('请选择用户类型')
                return;
            }
            if (relationData?.id) {
              handleUpdate(_data, position)   
            } else {
                let time = timeFormat2();
                let obj = {
                    ...configData,
                    ...realData,
                    updateTime: time
                };
                updateUserType(obj).then((res: any) => {
                    if (res.code !== '0000') return message.error(res.message);
                    let id = res.data;
                    _data.relationData.id = id;
                    _data.relationData.updateTime = time;
                    handleUpdate(_data, position);
                })
                .catch((e: Error) => {
                    message.error(e.message || '系统错误');
                });
            }
            
        } else {
            setEdit(!isEdit)
        }
    }
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {handleDelete(position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <h1 className="g-fs20 f-bold">配置详情</h1>
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={onValidate}
                    onChange={setFormState}
                    readOnly={!isEdit}
                    labelWidth={FORM_CONFIG.labelWidth}
                    widgets={{
                        uploadImg: UploadImg,
                        stepList: StepList
                    }}
                />
                <DesignatedUser
                    data={data}
                    position={position}
                    kycTag={kycTag}
                    olasTag={olasTag}
                    isEdit={isEdit}
                    setDesignatedData={setDesignatedData}
                />
            </div>

}