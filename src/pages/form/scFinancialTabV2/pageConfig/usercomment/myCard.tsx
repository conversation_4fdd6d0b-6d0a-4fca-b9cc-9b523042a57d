import React, {useEffect, useState} from 'react';
import { Button, message, Popconfirm, Row, Col, Input } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../index.less';
import DesignatedUser from '../../components/DesignatedUser';
import api from 'api';
import { timeFormat2 } from '@/utils/utils';
import classnames from 'classnames';

const { updateUserType } = api;
interface dataProps {
    data: any;
    kycTag: any;
    olasTag: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
}
interface iUserComment {
    onChange: Function,
    value: string,
    readonly: boolean
}
export default function ({data, kycTag, olasTag, position, handleUpdate, handleDelete}: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    const [designatedData, setDesignatedData] = useState<any>();
    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [formData, setFormState] = useState<any>({});
    const [commentList, setCommentList] = useState([]);

    useEffect(() => {
        setFormState(data.formData);
        setCommentList(data.commentList);
        setDesignatedData({
            configData: data.configData,
            relationData: data.relationData
        })
    }, [data]);

    const onValidate = (valid: any) => {
        setValid(valid);
    }
    const handleChange = () =>{
        if(isEdit){
            if (!formData.userCommentName) {
                message.error('请填写用户精评名称')
                return;
            }
            for (let i = 0,len = commentList?.length; i < len; i++) {
                const { userName, userTag, startPoint, content } =  commentList[i];
                if (!userName) {
                    message.error(`请填写第${i+1}项用户昵称`)
                    return;
                }
                if (!userTag) {
                    message.error(`请填写第${i+1}项用户标签`)
                    return;
                }
                if (!startPoint) {
                    message.error(`请填写第${i+1}项初始点赞数`)
                    return;
                }
                if (!content) {
                    message.error(`请填写第${i+1}项精评内容`)
                    return;
                }
            }
            let realData = {};
            const {configData, relationData} = designatedData;
            if (relationData?.userType === '1') {
                const {blackCustId, whiteCustId, ...other} = relationData;
                realData = {...other}
            } else {
                const {blackUserId, whiteUserId, ...other} = relationData;
                realData = {...other}
            }
            let _data: any = {
                formData,
                commentList,
                configData,
                relationData: realData
            }
            if (configData.platform?.length === 0) {
                message.error('请选择使用平台')
                return;
            }
            if (configData.utype?.length === 0) {
                message.error('请选择用户类型')
                return;
            }
            if (relationData?.id) {
              handleUpdate(_data, position)   
            } else {
                let time = timeFormat2();
                let obj = {
                    ...configData,
                    ...realData,
                    updateTime: time
                };
                updateUserType(obj).then((res: any) => {
                    if (res.code !== '0000') return message.error(res.message);
                    let id = res.data;
                    _data.relationData.id = id;
                    _data.relationData.updateTime = time;
                    handleUpdate(_data, position);
                })
                .catch((e: Error) => {
                    message.error(e.message || '系统错误');
                });
            }
            
        } else {
            setEdit(!isEdit)
        }
    }
    const addCommentItem = () => {
        let arr: any = [...commentList];
        let obj = {
            userName: '',
            userTag: '',
            startPoint: '',
            content: ''
        }
        arr.push(obj);
        setCommentList(arr);
    }
    const deleteCommentItem = (index: number) => {
        let arr: any = [...commentList];
        arr.splice(index, 1)
        setCommentList(arr);
    }
    const handleComment = (e: any, num: number, type: string) => {
        let arr: any = [...commentList];
        arr[num][type] = e.target.value;
        setCommentList(arr);
    }
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {handleDelete(position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <h1 className="g-fs20 f-bold">配置详情</h1>
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={onValidate}
                    onChange={setFormState}
                    readOnly={!isEdit}
                    labelWidth={FORM_CONFIG.labelWidth}
                />
                <div className="g-mb10">
                    <ul className={classnames(styles['m-user-content-ul'], 'g-mb20')}>
                        { commentList?.map((item: any, index: number) => {
                            return (
                                <li style={{width: 1150, marginBottom: 20}} key={index}>
                                    <div>
                                        <div className="u-flex">
                                            <div className="u-l-middle">
                                                <span className={classnames(styles['m-card-required'])} style={{width: 110}}>用户昵称</span>
                                                <Input value={item.userName} disabled={!isEdit} className="g-ml20" style={{width: 200}} onChange={(e) => handleComment(e, index, 'userName')}></Input>
                                            </div>
                                            <div className="u-l-middle g-ml30">
                                                <span className={classnames(styles['m-card-required'])} style={{width: 110}}>用户标签</span>
                                                <Input value={item.userTag} disabled={!isEdit} className="g-ml20" style={{width: 200}} onChange={(e) => handleComment(e, index, 'userTag')}></Input>
                                            </div>
                                            <div className="u-l-middle g-ml30">
                                                <span className={classnames(styles['m-card-required'])} style={{width: 110}}>初始点赞数</span>
                                                <Input value={item.startPoint} disabled={!isEdit} className="g-ml20" style={{width: 200}} onChange={(e) => handleComment(e, index, 'startPoint')}></Input>
                                            </div>
                                        </div>
                                        <div className="u-l-middle g-mt20">
                                            <span className={classnames(styles['m-card-required'])} style={{width: 110}}>精评内容</span>
                                            <Input value={item.content} disabled={!isEdit} className="g-ml20" onChange={(e) => handleComment(e, index, 'content')}></Input>
                                        </div>
                                    </div>
                                    <Button type="danger" ghost disabled={!isEdit} onClick={() => deleteCommentItem(index)}>删除</Button>
                                </li>
                            )
                        }) }
                        
                    </ul>
                    <Button disabled={!isEdit} onClick={addCommentItem}>添加</Button>
                </div>
                <DesignatedUser
                    data={data}
                    position={position}
                    kycTag={kycTag}
                    olasTag={olasTag}
                    isEdit={isEdit}
                    setDesignatedData={setDesignatedData}
                />
            </div>

}