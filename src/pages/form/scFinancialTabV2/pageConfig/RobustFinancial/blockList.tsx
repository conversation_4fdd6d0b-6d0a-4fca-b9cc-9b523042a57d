import React, {useEffect, useState, useCallback} from 'react';
import WrappedBlock from './block';

interface dataProps {
    onChange: Function,
    value: any,
    readonly: boolean,
    type: any,
    handleFocus: (data: any) => void
}
function BlockList({onChange, value, readonly, type, handleFocus }:dataProps ) {
    const handleUpdate = (data: any, index: number) => {
        value = data;
        onChange('fundObj', value);
    }
    return (
        <div>
            {
                [0].map((item) => {
                    let blockData = value ?? {}
                    return (
                        <div key={item}>
                            <WrappedBlock type={type} position={item} handleFocus={handleFocus} handleUpdate={handleUpdate} isEdit={!readonly} blockData={blockData}></WrappedBlock>
                        </div>
                        
                    )
                })
            }
            
        </div>
    )

}
export default React.memo(BlockList);