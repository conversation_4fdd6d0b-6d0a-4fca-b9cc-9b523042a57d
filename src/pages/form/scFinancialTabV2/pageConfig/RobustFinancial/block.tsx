import React from 'react';
import { FormComponentProps } from 'antd/es/form';
import styles from '../index.less';
import { Form, Input, message } from 'antd';
import classNames from 'classnames';
import api from 'api';
const { fetchFundNameByCode } = api;

const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20
  }
};
interface BlockFormProps extends FormComponentProps {
    blockData: any,
    handleUpdate: (data: any, index: number) => void,
    handleFocus: (data: any) => void,
    position: number,
    isEdit: boolean,
    type: string
}

class Block extends React.Component<BlockFormProps> {
    constructor(props: BlockFormProps) {
      super(props);
    }
    handleCode = (code: string) => {
        const { position, blockData, handleUpdate, handleFocus } = this.props;
        const { setFieldsValue } = this.props.form;
        handleFocus(true);
        if (code === "") return;
        code = code.trim();
        fetchFundNameByCode({
            fundCode: code
        }).then((data: any) => {
            if (data.code === '0000') {
                data = data.data;
                if (data) {
                    handleUpdate({
                        ...blockData,
                        version: blockData?.version ?? '',
                        name: data.name
                        }, position);
                    setFieldsValue({
                        name: data.name
                    })
                } else {
                    handleUpdate({
                        ...blockData,
                        version: blockData?.version ?? '',
                        code: ''
                    }, position);
                    setFieldsValue({
                        code: ''
                    })
                    message.error('该基金不为基金，请重新输入')
                }
            } else {
                handleUpdate({
                    ...blockData,
                    version: blockData?.version ?? '',
                    code: ''
                }, position);
                setFieldsValue({
                    code: ''
                })
                message.error('请求基金详情接口失败，请稍后重试')
            }
        }).catch((e: Error) => {
            handleUpdate({
                ...blockData,
                version: blockData?.version ?? '',
                code: ''
            }, position);
            setFieldsValue({
                code: ''
            })
            message.error(e?.message || '系统繁忙');
        })
    }
    handleCodeFocus = () => {
        this.props.handleFocus(false)
    }
    render() {
        
      const { getFieldDecorator } = this.props.form;
      const { position, blockData, isEdit } = this.props;
      const { code, name, } = blockData;
      return (
        <div className={classNames(styles['m-card'], styles['m-sc-border'])}>
          <Form {...formItemLayout}>
            <Form.Item label={`基金代码`} wrapperCol={{span: 12}}>
                {getFieldDecorator('code', {
                    initialValue: code,
                    rules: [{ required: true, message: `请填写产品${position+1}代码` }],
                })(
                    <Input disabled={!isEdit} onFocus={this.handleCodeFocus} onBlur={(e) => this.handleCode(e.target.value)}/>
                )}
            </Form.Item>
            <Form.Item label={`基金名称`} wrapperCol={{span: 12}}>
                {getFieldDecorator('name', {
                    initialValue: name,
                    rules: [{ required: true, message: `请填写产品${position+1}名称` }],
                })(
                    <Input disabled={true}/>
                )}
            </Form.Item>
          </Form>
        </div>
      )
    }
}

const WrappedBlock = Form.create<BlockFormProps>({ 
  name: 'multiblock',
  onValuesChange: (props, changedValues, allValues) => {
    const { handleUpdate, position, blockData } = props;
    handleUpdate({
      ...allValues,
    }, position);
  }
})(Block);
export default WrappedBlock;