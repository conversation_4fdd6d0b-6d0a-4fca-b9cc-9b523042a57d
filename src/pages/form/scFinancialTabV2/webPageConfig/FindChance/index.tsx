import React, { useEffect, useState } from 'react';
import { Button, Collapse, Popconfirm, Icon, message } from 'antd';
import api from 'api';
import SimpleConfig from './components/SimpleConfig';
const { fetchscTabPageFindChance, postscTabPageFindChance } = api;

const { Panel } = Collapse;

export default function() {
  const [listData, setListData] = useState<any[]>([]);
  const [canUpload, setCanUpload] = useState(false);

  /**
   * 检测 url 的有效性
   */
  const checkUrl = (str: string) => {
    if (str) {
      if (!/^(http:\/\/|https:\/\/|client.html).+/.test(str)) {
        return false;
      }
      if (str.length !== str.trim().length) {
        return false;
      }
    }
    return true;
  };

  /**
   * 新增数据
   */
  const addData = () => {
    let _listData = [...listData];
    _listData.push({
      name: '',
      picUrl: '',
      mainTitle: '',
      secTitle: '',
      fundCode: '',
      fundName: '',
      fundTag: '',
      buyText: '',
      jumpUrl: '',
      startTime: '',
      endTime: '',
      newsId: '',
      taId: '',
      hugeNewsTitle: '',
      hugeNewsContent: '',
      hugeNewsJumpUrl: '',
      articleAvatar: '',
      articleName: '',
      articleDesc: '',
      articleContent: '',
      relateFundList: [],
    });
    setListData(_listData);
  };

  /**
   * 修改数据
   */
  const editData = (data: any, index: number) => {
    let _listData = [...listData];
    _listData[index] = data;
    setListData(_listData);
    setCanUpload(true);
  };

  /**
   * 删除数据
   */
  const deleteData = (index: number) => {
    let _listData = [...listData];
    _listData.splice(index, 1);
    setListData(_listData);
    setCanUpload(true);
  };

  /**
   * 上移
   */
  const goUp = (item: any, index: number) => {
    if (index === 0) {
      message.error('顺序已在第一位');
      return;
    }
    let _listData = [...listData];
    _listData.splice(index, 1);
    _listData.splice(index - 1, 0, item);
    setListData(_listData);
    setCanUpload(true);
  };

  /**
   * 下移
   */
  const goDonw = (item: any, index: number) => {
    if (index === listData.length - 1) {
      message.error('顺序已在最后一位');
      return;
    }
    let _listData = [...listData];
    _listData.splice(index, 1);
    _listData.splice(index + 1, 0, item);
    setListData(_listData);
    setCanUpload(true);
  };

  /**
   * 发布
   */
  const uploadData = () => {
    let ids: string[] = [];
    let taids: string[] = [];
    for (let i = 0; i < listData.length; i++) {
      const {
        name,
        picUrl,
        mainTitle,
        secTitle,
        fundCode,
        fundName,
        fundTag,
        buyText,
        jumpUrl,
        newsId,
        taId,
        hugeNewsTitle,
        hugeNewsContent,
        hugeNewsJumpUrl,
        articleAvatar,
        articleName,
        articleDesc,
        articleContent,
        relateFundList,
      } = listData[i];
      if (!name) {
        message.error(`请填写第${i + 1}项的投资逻辑名称`);
        return;
      }
      if (!picUrl) {
        message.error(`请上传第${i + 1}项的标题图片`);
        return;
      }
      if (!mainTitle) {
        message.error(`请填写第${i + 1}项的主标题`);
        return;
      }
      if (!secTitle) {
        message.error(`请填写第${i + 1}项的副标题`);
        return;
      }
      if (!fundCode) {
        message.error(`请填写第${i + 1}项的基金代码`);
        return;
      } else if (fundCode.includes(' ')) {
        message.error(`请检查第${i + 1}项的基金代码，存在空格`);
        return;
      }
      if (!fundName) {
        message.error(`请重新填写第${i + 1}项的基金代码获取基金名称`);
        return;
      }
      if (!fundTag) {
        message.error(`请填写第${i + 1}项的基金标签`);
        return;
      }
      if (!buyText) {
        message.error(`请填写第${i + 1}项的购买按钮文描`);
        return;
      }
      if (!checkUrl(jumpUrl)) {
        message.error(`第${i + 1}项的跳转链接填写有误`);
        return;
      }
      if (!newsId) {
        message.error(`请填写第${i + 1}项的资讯ID`);
        return;
      } else if (ids.indexOf(newsId) > -1) {
        message.error(`第${i + 1}项的资讯ID已重复，请修改`);
        return;
      } else {
        ids.push(newsId);
      }
      if (!taId) {
        message.error(`请填写第${i + 1}项的埋点标识`);
        return;
      } else if (taids.indexOf(taId) > -1) {
        message.error(`第${i + 1}项的埋点标识已重复，请修改`);
        return;
      } else {
        taids.push(taId);
      }
      if (!hugeNewsTitle) {
        message.error(`请填写第${i + 1}项的重磅热点标题`);
        return;
      }
      if (!hugeNewsContent) {
        message.error(`请填写第${i + 1}项的重磅热点内容`);
        return;
      }
      if (!checkUrl(hugeNewsJumpUrl)) {
        message.error(`第${i + 1}项的重磅热点跳转链接填写有误`);
        return;
      }
      if (!articleAvatar) {
        message.error(`请上传第${i + 1}项的投顾头像`);
        return;
      }
      if (!articleName) {
        message.error(`请填写第${i + 1}项的投顾名称`);
        return;
      }
      if (!articleDesc) {
        message.error(`请填写第${i + 1}项的投顾简介`);
        return;
      }
      if (!articleContent) {
        message.error(`请填写第${i + 1}项的文章内容`);
        return;
      }
      for (let j = 0; j < relateFundList.length; j++) {
        const item = relateFundList[j];
        if (!item.fundCode) {
          message.error(`请填写第${i + 1}项的第${j + 1}相关基金-基金代码`);
          return;
        } else if (item.fundCode.includes(' ')) {
          message.error(`请检查第${i + 1}项的第${j + 1}相关基金-基金代码，存在空格`);
          return;
        }
        if (!item.fundName) {
          message.error(`请重新填写第${i + 1}项的第${j + 1}相关基金-基金代码获取基金名称`);
          return;
        }
        if (!item.fundDesc) {
          message.error(`请填写第${i + 1}项的第${j + 1}相关基金-基金简介`);
          return;
        }
        if (!item.fundTag) {
          message.error(`请填写第${i + 1}项的第${j + 1}相关基金-基金标签`);
          return;
        }
        if (!item.fundIndustry) {
          message.error(`请填写第${i + 1}项的第${j + 1}相关基金-基金行业`);
          return;
        }
        if (!checkUrl(item.jumpUrl)) {
          message.error(`第${i + 1}项的第${j + 1}相关基金-跳转链接填写有误`);
          return;
        }
      }
    }
    postscTabPageFindChance({
      value: JSON.stringify(listData),
    })
      .then((res: any) => {
        const { code } = res;
        if (code === '0000') {
          message.success('发布成功');
          setCanUpload(false);
        } else {
          message.error(res.msg || '发布失败，请稍后再试');
        }
      })
      .catch(() => {
        message.error('发布失败，请稍后再试');
      });
  };

  /**
   * 获取数据
   */
  const getData = () => {
    fetchscTabPageFindChance()
      .then((data: any) => {
        console.log(data);
        if (data.code === '0000') {
          if (data.data) {
            setListData(JSON.parse(data.data));
          }
        } else {
          message.error(data.message || '系统繁忙，请稍后再试');
        }
      })
      .catch(() => {
        message.error('系统繁忙，请稍后再试');
      });
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <div>
      <Popconfirm
        title="确定保存并发布？"
        onConfirm={() => {
          uploadData();
        }}
        okText="确定"
        cancelText="取消"
        disabled={!canUpload}
      >
        <Button disabled={!canUpload} style={{ marginBottom: 10 }} type="primary">
          发布
        </Button>
      </Popconfirm>
      <Collapse>
        {listData.map((item: any, index: number) => (
          <Panel
            header={`${index + 1} ${item.name}`}
            key={`${index}`}
            extra={
              <>
                <Icon
                  type="arrow-up"
                  onClick={e => {
                    e.stopPropagation();
                    goUp(item, index);
                  }}
                />
                <Icon
                  style={{ marginLeft: 10 }}
                  type="arrow-down"
                  onClick={e => {
                    e.stopPropagation();
                    goDonw(item, index);
                  }}
                />
              </>
            }
          >
            <SimpleConfig
              baseData={item}
              saveFn={(data: any) => {
                editData(data, index);
              }}
              deleteFn={() => {
                deleteData(index);
              }}
            />
          </Panel>
        ))}
      </Collapse>
      <div style={{ marginTop: 20 }}>
        <Button onClick={addData} type="primary">
          新增
        </Button>
      </div>
    </div>
  );
}
