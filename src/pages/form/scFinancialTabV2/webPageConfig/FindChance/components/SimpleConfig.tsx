import React, { useEffect, useState } from 'react';
import { Button, Input, Popconfirm, message, DatePicker, Card, Row, Col, Select } from 'antd';
import moment from 'moment';
import UploadImg from './UploadImg';
import api from 'api';

const { fetchFundNameByCode } = api;
const { Option } = Select;

function InputTitle({ title, isRequired = true }: { title: string; isRequired?: boolean }) {
  return (
    <>
      {isRequired && (
        <span
          style={{
            color: '#ff330a',
            fontSize: 14,
            fontFamily: 'SimSun, sans-serif',
            margin: '1px 4px 0 0',
          }}
        >
          *
        </span>
      )}
      {title}：
    </>
  );
}

interface IFundProps {
  fundCode: string;
  fundName: string;
  timeInterval: string;
  fundTag: string;
  fundDesc: string;
  fundIndustry: string;
  jumpUrl: string;
}

function SimpleConfig({
  saveFn,
  deleteFn,
  baseData,
}: {
  saveFn: Function;
  deleteFn: Function;
  baseData: any;
}) {
  const [name, setName] = useState('');
  const [picUrl, setPicUrl] = useState('');
  const [mainTitle, setMainTitle] = useState('');
  const [secTitle, setSecTitle] = useState('');
  const [fundCode, setFundCode] = useState('');
  const [fundName, setFundName] = useState('');
  const [fundTag, setFundTag] = useState('');
  const [buyText, setBuyText] = useState('');
  const [jumpUrl, setJumpUrl] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');

  const [newsId, setNewsId] = useState('');
  const [taId, setTaId] = useState('');
  const [hugeNewsTitle, setHugNewsTitle] = useState('');
  const [hugeNewsContent, setHugNewsContent] = useState('');
  const [hugeNewsJumpUrl, setHugeNewsJumpUrl] = useState('');
  const [articleAvatar, setArticleAvatar] = useState('');
  const [articleName, setArticleName] = useState('');
  const [articleDesc, setArticleDesc] = useState('');
  const [articleContent, setArticleContent] = useState('');
  const [relateFundList, setRelateFundList] = useState<IFundProps[]>([]);

  const [isEdit, setIsEdit] = useState(false);

  /**
   * 检测 url 的有效性
   */
  const checkUrl = (str: string) => {
    if (str) {
      if (!/^(http:\/\/|https:\/\/|client.html).+/.test(str)) {
        return false;
      }
      if (str.length !== str.trim().length) {
        return false;
      }
    }
    return true;
  };

  /**
   * 保存数据
   */
  const saveData = () => {
    if (!name) {
      message.error(`请填写投资逻辑名称`);
      return;
    }
    if (!picUrl) {
      message.error(`请上传标题图片`);
      return;
    }
    if (!mainTitle) {
      message.error(`请填写主标题`);
      return;
    }
    if (!secTitle) {
      message.error(`请填写副标题`);
      return;
    }
    if (!fundCode) {
      message.error(`请填写基金代码`);
      return;
    } else if (fundCode.includes(' ')) {
      message.error(`请检查基金代码，存在空格`);
      return;
    }
    if (!fundName) {
      message.error(`请重新填写基金代码获取基金名称`);
      return;
    }
    if (!fundTag) {
      message.error(`请填写基金标签`);
      return;
    }
    if (!buyText) {
      message.error(`请填写购买按钮文描`);
      return;
    }
    if (!checkUrl(jumpUrl)) {
      message.error(`跳转链接填写有误`);
      return;
    }
    if (!newsId) {
      message.error(`请填写资讯ID`);
      return;
    }
    if (!taId) {
      message.error(`请填写埋点标识`);
      return;
    }
    if (!hugeNewsTitle) {
      message.error(`请填写重磅热点标题`);
      return;
    }
    if (!hugeNewsContent) {
      message.error(`请填写重磅热点内容`);
      return;
    }
    if (!checkUrl(hugeNewsJumpUrl)) {
      message.error(`重磅热点跳转链接填写有误`);
      return;
    }
    if (!articleAvatar) {
      message.error(`请上传投顾头像`);
      return;
    }
    if (!articleName) {
      message.error(`请填写投顾名称`);
      return;
    }
    if (!articleDesc) {
      message.error(`请填写投顾简介`);
      return;
    }
    if (!articleContent) {
      message.error(`请填写文章内容`);
      return;
    }
    for (let j = 0; j < relateFundList.length; j++) {
      const item = relateFundList[j];
      if (!item.fundCode) {
        message.error(`请填写第${j + 1}相关基金-基金代码`);
        return;
      } else if (item.fundCode.includes(' ')) {
        message.error(`请检查第${j + 1}相关基金-基金代码，存在空格`);
        return;
      }
      if (!item.fundName) {
        message.error(`请重新填写第${j + 1}相关基金-基金代码获取基金名称`);
        return;
      }
      if (!item.fundDesc) {
        message.error(`请填写第${j + 1}相关基金-基金简介`);
        return;
      }
      if (!item.fundTag) {
        message.error(`请填写第${j + 1}相关基金-基金标签`);
        return;
      }
      if (!item.fundIndustry) {
        message.error(`请填写第${j + 1}相关基金-基金行业`);
        return;
      }
      if (!checkUrl(item.jumpUrl)) {
        message.error(`第${j + 1}相关基金-跳转链接填写有误`);
        return;
      }
    }
    setIsEdit(false);
    saveFn({
      name,
      picUrl,
      mainTitle,
      secTitle,
      fundCode,
      fundName,
      fundTag,
      buyText,
      jumpUrl,
      startTime,
      endTime,
      newsId,
      taId,
      hugeNewsTitle,
      hugeNewsContent,
      hugeNewsJumpUrl,
      articleAvatar,
      articleName,
      articleDesc,
      articleContent,
      relateFundList,
    });
  };

  /**
   * 删除数据
   */
  const deleteData = () => {
    deleteFn();
  };

  /**
   * 获取基金名称
   */
  const getFundName = (code: string, key: string, index?: number) => {
    if (code === '') return;
    code = code.trim();
    fetchFundNameByCode({
      fundCode: code,
    })
      .then((data: any) => {
        if (data.code === '0000') {
          if (data.data) {
            let name = data.data.name;
            if (key === 'fundCode') {
              setFundName(name);
            } else if (key === 'relateFundList' && (index || index === 0)) {
              editRelateFund('fundName', name, index);
            }
          } else {
            message.error('该基金不存在，请重新输入');
          }
        } else {
          message.error('调用基金详情接口失败，请稍后再试');
        }
      })
      .catch(() => {
        message.error('调用基金详情接口失败，请稍后再试');
      });
  };

  /**
   * 新增相关基金
   */
  const addRelateFund = () => {
    let _relateFundList = [...relateFundList];
    _relateFundList.push({
      fundCode: '',
      fundName: '',
      timeInterval: 'year',
      fundDesc: '',
      fundTag: '',
      fundIndustry: '',
      jumpUrl: '',
    });
    setRelateFundList(_relateFundList);
  };

  /**
   * 修改相关基金配置项
   */
  const editRelateFund = (key: string, value: string, index: number) => {
    let _relateFundList = [...relateFundList];
    if (_relateFundList[index]) {
      _relateFundList[index][key] = value;
    }
    setRelateFundList(_relateFundList);
  };

  /**
   * 删除相关基金
   */
  const deleteRelateFund = (index: number) => {
    let _relateFundList = [...relateFundList];
    _relateFundList.splice(index, 1);
    setRelateFundList(_relateFundList);
  };

  useEffect(() => {
    if (baseData) {
      setName(baseData.name || '');
      setPicUrl(baseData.picUrl || '');
      setMainTitle(baseData.mainTitle || '');
      setSecTitle(baseData.secTitle || '');
      setFundCode(baseData.fundCode || '');
      setFundName(baseData.fundName || '');
      setFundTag(baseData.fundTag || '');
      setBuyText(baseData.buyText || '');
      setJumpUrl(baseData.jumpUrl || '');
      setStartTime(baseData.startTime || '');
      setEndTime(baseData.endTime || '');
      setNewsId(baseData.newsId || '');
      setTaId(baseData.taId || '');
      setHugNewsTitle(baseData.hugeNewsTitle || '');
      setHugNewsContent(baseData.hugeNewsContent || '');
      setHugeNewsJumpUrl(baseData.hugeNewsJumpUrl || '');
      setArticleAvatar(baseData.articleAvatar || '');
      setArticleName(baseData.articleName || '');
      setArticleDesc(baseData.articleDesc || '');
      setArticleContent(baseData.articleContent || '');
      setRelateFundList(baseData.relateFundList || []);
    }
  }, []);

  return (
    <div>
      <div
        style={{ height: 40, backgroundColor: '#95badd', marginBottom: 20, paddingRight: 20 }}
        className="u-r-middle"
      >
        <Button
          onClick={() => {
            if (isEdit) {
              saveData();
            } else {
              setIsEdit(true);
            }
          }}
        >
          {isEdit ? '保存' : '编辑'}
        </Button>
        <Popconfirm title="确定删除？" onConfirm={deleteData} okText="确定" cancelText="取消">
          <Button type="danger" style={{ marginLeft: 10 }}>
            删除
          </Button>
        </Popconfirm>
      </div>
      <p style={{ color: '#000', fontSize: 20 }}>列表页配置</p>
      <div style={{ width: 600 }}>
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="投资逻辑名称" />}
          value={name}
          onChange={e => {
            setName(e.target.value);
          }}
          disabled={!isEdit}
        />
        <UploadImg
          value={picUrl}
          size={['176*176']}
          title="标题图片"
          onChange={setPicUrl}
          disabled={!isEdit}
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="主标题" />}
          value={mainTitle}
          onChange={e => {
            setMainTitle(e.target.value);
          }}
          disabled={!isEdit}
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="副标题" />}
          value={secTitle}
          onChange={e => {
            setSecTitle(e.target.value);
          }}
          disabled={!isEdit}
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="基金代码" />}
          value={fundCode}
          onChange={e => {
            setFundCode(e.target.value);
          }}
          onBlur={e => {
            getFundName(e.target.value, 'fundCode');
          }}
          disabled={!isEdit}
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="基金名称" />}
          value={fundName}
          disabled
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="基金标签" />}
          value={fundTag}
          onChange={e => {
            setFundTag(e.target.value);
          }}
          disabled={!isEdit}
          placeholder="多个请用英文逗号分开，仅前三个生效"
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="购买按钮文描" />}
          value={buyText}
          onChange={e => {
            setBuyText(e.target.value);
          }}
          disabled={!isEdit}
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="跳转链接" isRequired={false} />}
          placeholder="不填默认跳转基金购买页"
          value={jumpUrl}
          onChange={e => {
            setJumpUrl(e.target.value);
          }}
          disabled={!isEdit}
        />
        <div style={{ marginBottom: 10 }}>
          <span>开始时间：</span>
          <DatePicker
            showTime
            placeholder="请选择开始时间"
            value={startTime ? moment(startTime, 'YYYY-MM-DD HH:mm:ss') : null}
            onChange={(data, dateString) => {
              setStartTime(dateString);
            }}
            disabled={!isEdit}
          />
        </div>
        <div style={{ marginBottom: 10 }}>
          <span>结束时间：</span>
          <DatePicker
            showTime
            placeholder="请选择开始时间"
            value={endTime ? moment(endTime, 'YYYY-MM-DD HH:mm:ss') : null}
            onChange={(data, dateString) => {
              setEndTime(dateString);
            }}
            disabled={!isEdit}
          />
        </div>
      </div>
      <p style={{ color: '#000', fontSize: 20 }}>详情页配置</p>
      <div style={{ width: 600 }}>
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="资讯ID" />}
          value={newsId}
          onChange={e => {
            setNewsId(e.target.value);
          }}
          disabled={!isEdit}
        />
        <Input
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<InputTitle title="埋点标识" />}
          value={taId}
          onChange={e => {
            setTaId(e.target.value);
          }}
          disabled={!isEdit}
        />
        <span style={{ marginLeft: 10, paddingTop: 2 }}>fund_licai_zhaojihui_{taId}</span>
        <p>重磅热点</p>
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="重磅热点标题" />}
          value={hugeNewsTitle}
          onChange={e => {
            setHugNewsTitle(e.target.value);
          }}
          disabled={!isEdit}
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="重磅热点内容" />}
          value={hugeNewsContent}
          onChange={e => {
            setHugNewsContent(e.target.value);
          }}
          disabled={!isEdit}
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="重磅热点跳转链接" isRequired={false} />}
          value={hugeNewsJumpUrl}
          onChange={e => {
            setHugeNewsJumpUrl(e.target.value);
          }}
          disabled={!isEdit}
        />
        <p>投资逻辑</p>
        <UploadImg
          value={articleAvatar}
          size={['80*80']}
          title="投顾头像"
          onChange={setArticleAvatar}
          disabled={!isEdit}
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="投顾名称" />}
          value={articleName}
          onChange={e => {
            setArticleName(e.target.value);
          }}
          disabled={!isEdit}
        />
        <Input
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title="投顾简介" />}
          value={articleDesc}
          onChange={e => {
            setArticleDesc(e.target.value);
          }}
          disabled={!isEdit}
        />
        <InputTitle title="文章内容" />
        <Input.TextArea
          style={{ marginBottom: 10 }}
          value={articleContent}
          onChange={e => {
            setArticleContent(e.target.value);
          }}
          disabled={!isEdit}
          rows={6}
        />
      </div>
      <p>相关基金</p>
      <Row>
        {relateFundList.map((item, index) => (
          <Col key={index} span={8}>
            <Card style={{ margin: 10 }}>
              <Input
                style={{ marginBottom: 10 }}
                addonBefore={<InputTitle title="基金代码" />}
                value={item.fundCode || ''}
                onChange={e => {
                  editRelateFund('fundCode', e.target.value, index);
                }}
                onBlur={e => {
                  getFundName(e.target.value, 'relateFundList', index);
                }}
                disabled={!isEdit}
              />
              <Input
                style={{ marginBottom: 10 }}
                addonBefore={<InputTitle title="基金名称" />}
                value={item.fundName || ''}
                disabled
              />
              <div style={{ marginBottom: 10 }}>
                <span>收益区间：</span>
                <Select
                  style={{ width: 200 }}
                  value={item.timeInterval || 'year'}
                  onChange={(value: string) => {
                    editRelateFund('timeInterval', `${value}`, index);
                  }}
                  disabled={!isEdit}
                >
                  <Option value="month">近1月</Option>
                  <Option value="tmonth">近3月</Option>
                  <Option value="hyear">近6月</Option>
                  <Option value="year">近1年</Option>
                  <Option value="tyear">近3年</Option>
                  <Option value="fyear">近5年</Option>
                  <Option value="nowyear">今年来</Option>
                  <Option value="now">成立以来</Option>
                </Select>
              </div>
              <Input
                style={{ marginBottom: 10 }}
                addonBefore={<InputTitle title="基金简介" />}
                value={item.fundDesc || ''}
                onChange={e => {
                  editRelateFund('fundDesc', e.target.value, index);
                }}
                disabled={!isEdit}
              />
              <Input
                style={{ marginBottom: 10 }}
                addonBefore={<InputTitle title="基金标签" />}
                value={item.fundTag || ''}
                onChange={e => {
                  editRelateFund('fundTag', e.target.value, index);
                }}
                placeholder="多个请用英文逗号分开，仅前两个生效"
                disabled={!isEdit}
              />
              <Input
                style={{ marginBottom: 10 }}
                addonBefore={<InputTitle title="基金行业" />}
                value={item.fundIndustry || ''}
                onChange={e => {
                  editRelateFund('fundIndustry', e.target.value, index);
                }}
                placeholder="多个请用英文逗号分开，仅前三个生效"
                disabled={!isEdit}
              />
              <Input
                style={{ marginBottom: 10 }}
                addonBefore={<InputTitle title="跳转链接" isRequired={false} />}
                placeholder="不填默认跳转基金购买页"
                value={item.jumpUrl}
                onChange={e => {
                  editRelateFund('jumpUrl', e.target.value, index);
                }}
                disabled={!isEdit}
              />
              <Popconfirm
                title="确定删除该相关基金？"
                onConfirm={() => {
                  deleteRelateFund(index);
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button disabled={!isEdit} size="small">
                  删除
                </Button>
              </Popconfirm>
            </Card>
          </Col>
        ))}
      </Row>
      <Button onClick={addRelateFund} disabled={!isEdit || relateFundList.length >= 3} size="small">
        增加相关基金
      </Button>
    </div>
  );
}

export default SimpleConfig;
