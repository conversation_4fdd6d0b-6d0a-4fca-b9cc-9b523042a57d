import React, { useEffect, useState } from 'react';
import { Button, message, Collapse, Input, Popconfirm } from 'antd';
import api from 'api';
import MyCard from '../cardItem';
import styles from '../../pageConfig/index.less';
import ConfigSelect from '../selectModel/index';
import LocationModel from '../locationModel';
import { timeFormat2 } from '@/utils/utils';
import { fetchData, addData, handleApi, modelName } from '../../script/utils';
import { checkUrl } from '../../script/utils'; 

const { fetchCBAS, fetchOLAS, checkCoupon, postActivityDetails } = api;
const { Panel } = Collapse;

interface iProps {
    floorType: string
}

export default function ({floorType}: iProps) {
    const [init, setInit] = useState(true);
    const [originData, setOriginData] = useState<any>([]);
    const [isModify, setModify] = useState(false);
    const [config, setConfig] = useState<{ utype: string[], platform: string[] }>({ utype: [], platform: [] });
    const [kycTag, setKycTag] = useState([]);
    const [olasTag, setOlasTag] = useState([]);
    const [activeKey, setActiveKey] = useState(0);
    const [gonggeClassify, setGonggeClassify] = useState<any>([]); // 宫格分类
    const [inputArr, setInputArr] = useState<any>([]);  // 输入内容
    const [floorInfo, setFloorInfo] = useState({
        floorTitle: '',
        isEdit: false
    }); // 楼层标题
    const [floorTitle, setFloorTitle] = useState(''); // 楼层标题

    const [floorInfo2, setFloorInfo2] = useState({
        floorTitle: '',
        floorSubTitle: '',
        moreBtnText: '',
        moreBtnUrl: '',
        moreBtnVersion: '',
        isEdit: false
    });
    const [floorTitle2, setFloorTitle2] = useState(''); // 楼层标题
    const [floorSubTitle2, setfloorSubTitle2] = useState(''); // 楼层副标题
    const [floorMoreBtnText2, setfloorMoreBtnText2] = useState(''); // 楼层更多按钮文描
    const [floorMoreBtnUrl2, setfloorMoreBtnUrl2] = useState(''); // 楼层跳转链接
    const [floorMoreBtnVersion2, setfloorMoreBtnVersion2] = useState(''); // 楼层副标题

    const handleChange = (data: any) => {
        console.log('configData', data);
        setConfig(data);
    }
    useEffect(() => {
        fetchCBAS().then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                if (data) {
                    setKycTag(data)
                }
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])
    
    useEffect(() => {
        fetchOLAS().then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                if (data) {
                    let _olasTag: any = [];
                    for (let prop in data) {
                        _olasTag.push({ "groupid": prop, "description": data[prop].description });
                    }
                    setOlasTag(_olasTag);
                }
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, []) 

    useEffect(() => {
        api[handleApi(floorType).get]().then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                setInit(true);
                let initData: any = data && JSON.parse(data), _data: any = [];
                console.log(initData);
                initData?.confs?.forEach((item: any) => {
                    let obj: any = {
                        formData: fetchData(item, floorType),
                        configData: {
                            platform: item.platform || [],
                            utype: item.utype || [],
                        },
                        relationData: {
                            id: item.id,
                            targetType: item.targetType,
                            kycLogic: item.kycLogic,
                            kycs: item.kycs,
                            olasId: item.olasId,
                            olasType: item.olasType,
                            userType: item.userType,
                            blackUserId: item.userType === '1' ? item.blackUserId : '', 
                            whiteUserId: item.userType === '1' ? item.whiteUserId : '',
                            blackCustId: item.userType === '3' ? item.blackUserId : '',
                            whiteCustId: item.userType === '3' ? item.whiteUserId : '',
                            updateTime: item.updateTime
                        }
                    }
                    if (floorType === 'usercomment') {
                        obj.commentList = item.commentList;
                    }
                    _data.push(obj);
                })
                if (floorType === 'gonggeall') {
                    if (initData?.gonggeClassify?.length > 0) {
                        let iArr: string[] = [], gArr: any = [];
                        initData.gonggeClassify.forEach((str: string) => {
                            let obj = {
                                name: str,
                                isEdit: false
                            }
                            iArr.push(str);
                            gArr.push(obj)
                        })
                        setInputArr(iArr);
                        setGonggeClassify(gArr);
                    }
                }
                if (floorType === 'productCard' || 
                    floorType === 'marketSituation' || 
                    floorType === 'capitalTrend' || 
                    floorType === 'fundTrend' ||
                    floorType === 'multidimension') {
                    if (initData?.title) {
                        let obj = {
                            floorTitle: initData.title,
                            isEdit: false
                        }
                        setFloorInfo(obj);
                        setFloorTitle(initData.title);
                    }
                } else if (floorType === 'FindChance' || floorType === 'FindInsurance') {
                    if (initData) {
                        let obj = {
                            floorTitle: initData.title || '',
                            floorSubTitle: '',
                            moreBtnText: initData.moreTitle || '',
                            moreBtnUrl: initData.moreActionUrl || '',
                            moreBtnVersion: initData.moreVersion || '',
                            isEdit: false
                        }
                        setFloorInfo2(obj);
                        setFloorTitle2(initData.title || '');
                        setfloorMoreBtnText2(initData.moreTitle || '');
                        setfloorMoreBtnUrl2(initData.moreActionUrl || '');
                        setfloorMoreBtnVersion2(initData.moreVersion || '');
                    }
                } else if (floorType === 'RobustFinancial') {
                    if (initData) {
                        let obj = {
                            floorTitle: initData.title || '',
                            floorSubTitle: initData.secTitle || '',
                            moreBtnText: initData.moreTitle || '',
                            moreBtnUrl: initData.moreActionUrl || '',
                            moreBtnVersion: initData.moreVersion || '',
                            isEdit: false
                        }
                        setFloorInfo2(obj);
                        setFloorTitle2(initData.title || '');
                        setfloorSubTitle2(initData.secTitle || '')
                        setfloorMoreBtnText2(initData.moreTitle || '');
                        setfloorMoreBtnUrl2(initData.moreActionUrl || '');
                        setfloorMoreBtnVersion2(initData.moreVersion || '');
                    }
                }
                setOriginData(_data); 
                console.log('initdata:', _data);
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])

    const onSubmit = async () => {
        let _value: any = [];
        let _sendData: any = {};
        if (floorType === 'gonggeall' && gonggeClassify?.length > 0) {
            for(let i = 0, len = gonggeClassify.length; i < len; i++) {
                if (!gonggeClassify[i]?.name) {
                    message.error(`第${i+1}个宫格分类为空，请重新编辑后保存`);
                    return;
                }
            }
        }
        for (let i = 0, len = originData?.length; i < len; i++) {
            const { formData, configData, relationData, commentList } = originData[i];
            if (floorType === 'searchhotword') {
                if (!formData.searchWord) {
                    message.error(`请填写第${i+1}项搜索热词`)
                    return;
                }
            } else if (floorType === 'mixbanner') {
                if (!formData.mixName) {
                    message.error(`请填写第${i+1}项融合运营位名称`)
                    return;
                }
                if (!formData.mixType) {
                    message.error(`请填写第${i+1}项融合运营位类型`)
                    return;
                }
                if (!formData.navColorLeft) {
                    message.error(`请填写第${i+1}项导航栏颜色-左`)
                    return;
                }
                if (!formData.imageUrl) {
                    message.error(`请上传第${i+1}项图片`)
                    return;
                }
                if (!formData.imageDarkUrl) {
                    message.error(`请上传第${i+1}项黑夜图片`)
                    return;
                }
                if (formData.mixType === '2') {
                    if (!formData.masterTitle) {
                        message.error(`请填写第${i+1}项主标题`)
                        return;
                    }
                    if (!formData.masterColor) {
                        message.error(`请填写第${i+1}项主标题颜色`)
                        return;
                    }
                } else if (formData.mixType === '3') {
                    if (!formData.unreceivedBtnName) {
                        message.error(`请填写第${i+1}项未领取按钮名称`)
                        return;
                    }
                    if (!formData.receivedUnOpenAccBtnName) {
                        message.error(`请填写第${i+1}项领取未开户按钮名称`)
                        return;
                    }
                    if (!formData.receivedUnBuyFundBtnName) {
                        message.error(`请填写第${i+1}项领取未首购按钮名称`)
                        return;
                    }
                    if (!formData.receivedUnBuyFundBtnJumpAction) {
                        message.error(`请填写第${i+1}项未首购按钮跳转`)
                        return;
                    }
                    let result = checkUrl(formData.receivedUnBuyFundBtnJumpAction, `第${i+1}项`);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }
                    const { equities1, equities2, equities3 } = formData;
                    let equities = [equities1, equities2, equities3];
                    for (let i = 0; i < equities.length; i++) {
                        if (equities[i].type === 'expGold') {
                            if (!equities[i].id) {
                                message.error(`请填写第${i+1}项权益${i + 1}体验金活动ID`)
                                return;
                            }
                            if (equities[i].id.indexOf(',') > -1 || equities[i].id.indexOf(' ') > -1) {
                                message.error(`第${i+1}项权益${i + 1}体验金活动ID只能配置一个`)
                                return;
                            }
                        } else if (equities[i].type === 'coupon') {
                            if (!equities[i].id) {
                                message.error(`请填写第${i+1}项权益${i + 1}优惠券ID`)
                                return;
                            }
                            if (equities[i].id.indexOf(' ') > -1) {
                                message.error(`第${i+1}项权益${i + 1}多个优惠券ID请用英文逗号隔开`)
                                return;
                            }
                        } else if (equities[i].type === 'goldCow') {
                            if (!equities[i].id) {
                                message.error(`请填写第${i+1}项权益${i + 1}活动ID`)
                                return;
                            }
                            if (equities[i].id.indexOf(',') > -1 || equities[i].id.indexOf(' ') > -1) {
                                message.error(`第${i+1}项权益${i + 1}多个活动ID只能配置一个`)
                                return;
                            }
                            if (!equities[i].jnhyTimeDesc) {
                                message.error(`请填写第${i+1}项权益${i + 1}金牛会员权益时间`)
                                return;
                            }
                        } else if (equities[i].type === 'blank') {
                            if (!equities[i].equitiesDesc) {
                                message.error(`请填写第${i+1}项权益${i + 1}权益内容`)
                                return;
                            }
                        }
                    }
                }
            } else if (floorType === 'notice') {
                if (!formData.noticeTitle) {
                    message.error(`请填写第${i+1}项公告标题`)
                    return;
                }
                if (!formData.startTime) {
                    message.error(`请填写第${i+1}项开始时间`)
                    return;
                }
                if (!formData.endTime) {
                    message.error(`请填写第${i+1}项结束时间`)
                    return;
                }
            } else if (floorType === 'gongge') {
                if (!formData.gonggeName) {
                    message.error(`请填写第${i+1}项宫格名称`)
                    return;
                }
                if (!formData.page) {
                    message.error(`请上传第${i+1}项图标-白天`)
                    return;
                }
                if (!formData.darkPage) {
                    message.error(`请上传第${i+1}项图标-黑夜`)
                    return;
                }
                if (!formData.jumpAction) {
                    message.error(`请填写第${i+1}项跳转链接`)
                    return;
                }
            } else if (floorType === 'gonggeall') {
                if (!formData.gonggeType) {
                    message.error(`请选择第${i+1}项宫格分类`)
                    return;
                }
                let exist = false;
                gonggeClassify.forEach((item: any) => {
                    if (item.name === formData.gonggeType) {
                        exist = true;
                    }
                })
                if (!exist) {
                    message.error(`第${i+1}项宫格分类不存在，请重新选择`)
                    return;
                }
                if (!formData.gonggeName) {
                    message.error(`请填写第${i+1}项宫格名称`)
                    return;
                }
                if (!formData.page) {
                    message.error(`请上传第${i+1}项图标-白天`)
                    return;
                }
                if (!formData.darkPage) {
                    message.error(`请上传第${i+1}项图标-黑夜`)
                    return;
                }
                if (!formData.jumpAction) {
                    message.error(`请填写第${i+1}项跳转链接`)
                    return;
                }
            } else if (floorType === 'operationposition') {
                if (!formData.tag) {
                    message.error(`请填写第${i+1}项标签`)
                    return;
                }
                if (!formData.textTitle) {
                    message.error(`请填写第${i+1}项文字链标题`)
                    return;
                }
                if (!formData.jumpAction) {
                    message.error(`请填写第${i+1}项跳转链接`)
                    return;
                }
            } else if (floorType === 'usercomment') {
                if (!formData.userCommentName) {
                    message.error(`请填写第${i+1}项用户精评名称`)
                    return;
                }
                for (let j = 0,len = commentList?.length; j < len; j++) {
                    const { userName, userTag, startPoint, content } =  commentList[j];
                    if (!userName) {
                        message.error(`请填写第${i+1}项第${j+1}个用户昵称`)
                        return;
                    }
                    if (!userTag) {
                        message.error(`请填写第${i+1}项第${j+1}个用户标签`)
                        return;
                    }
                    if (!startPoint) {
                        message.error(`请填写第${i+1}项第${j+1}个初始点赞数`)
                        return;
                    }
                    if (!content) {
                        message.error(`请填写第${i+1}项第${j+1}个精评内容`)
                        return;
                    }
                }
            } else if (floorType === 'coupon') {
                if (!formData.couponName) {
                    message.error(`请填写第${i+1}项优惠券运营位名称`)
                    return;
                }
                if (!formData.imageUrl) {
                    message.error(`请上传第${i+1}项优惠券图标`)
                    return;
                }
                if (!formData.couponText) {
                    message.error(`请填写第${i+1}项优惠券展示文案`)
                    return;
                }
                if (!formData.couponId) {
                    message.error(`请填写第${i+1}项优惠券ID`)
                    return;
                }
                if (!formData.achieveText) {
                    message.error(`请填写第${i+1}项领取按钮文描`)
                    return;
                }
                if (!formData.seeText) {
                    message.error(`请填写第${i+1}项查看按钮文描`)
                    return;
                }
                if (!formData.jumpAction) {
                    message.error(`请填写第${i+1}项跳转链接`)
                    return;
                }
                try {
                    let result = await checkCoupon({}, formData.couponId);
                    const { status_code, status_msg } = result;
                    if (status_code === 0) {
                        if (result?.data?.length > 0) {
                            for (let i = 0, len = result.data.length; i < len; i++) {
                                const { couponStartDate, couponEndDate, couponId } = result.data[i];
                                if ( couponStartDate && couponEndDate ) {
                                    let time = timeFormat2();
                                    time = time.replace(/[^\d]/g, '');
                                    if (time < couponStartDate || time > couponEndDate) {
                                        message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                        return;
                                    }
                                    if (formData.startTime) {
                                        let startTime = formData.startTime.replace(/[^\d]/g, '');
                                        if (startTime < couponStartDate) {
                                            message.error('模块的时间范围必须在优惠券的时间范围之内');
                                            return;
                                        }
                                    }
                                    if (formData.endTime) {
                                        let endTime = formData.endTime.replace(/[^\d]/g, '');
                                        if (endTime >= couponEndDate) {
                                            message.error('模块的时间范围必须在优惠券的时间范围之内');
                                            return;
                                        }
                                    }
                                } else {
                                    message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                    return;
                                }
                            }
                        } else {
                            message.error('请检查优惠券ID是否正确');
                            return;
                        }
                    } else {
                        message.error(status_msg || '网络请求错误，请稍后再试');
                        return;
                    }
                } catch(e) {
                    message.error(e.message);
                    return;
                }
            } else if (floorType === 'operationblock') {
                if (!formData.name) {
                    message.error(`请填写第${i+1}项运营豆腐块名称`)
                    return;
                }
                for (let j = 0; j < 2; j++) {
                    let obj = formData?.blocks[j] ?? {};
                    const { mainTitle, secTitle, page, url } = obj;
                    if (!mainTitle) {
                        message.error(`请填写第${i+1}项豆腐块${j+1}主标题`)
                        return;
                    }
                    if (!secTitle) {
                        message.error(`请填写第${i+1}项豆腐块${j+1}副标题`)
                        return;
                    }
                    if (!page) {
                        message.error(`请填写第${i+1}项豆腐块${j+1}图片`)
                        return;
                    }
                    if (!url) {
                        message.error(`请填写第${i+1}项豆腐块${j+1}跳转链接`)
                        return;
                    }
                    let result = checkUrl(url, `第${i+1}项豆腐块${j+1}`);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }
                    
                }
            } else if (floorType === 'biggiftbag') {
                if (!formData.name) {
                    message.error(`请填写第${i+1}项大礼包策略名称`)
                    return;
                }
                if (!formData.page) {
                    message.error(`请上传第${i+1}项小图标`)
                    return;
                }
                if (!formData.title) {
                    message.error(`请填写第${i+1}项礼包标题`)
                    return;
                }
                if (!formData.noneReceiveButton) {
                    message.error(`请填写第${i+1}项未领取按钮名称`)
                    return;
                }
                if (!formData.noneAccountButton) {
                    message.error(`请填写第${i+1}项领取未开户按钮名称`)
                    return;
                }
                
                if (!formData.noneBuyButton) {
                    message.error(`请填写第${i+1}项领取未首购按钮名称`)
                    return;
                }
                if (!formData.noneBuyUrl) {
                    message.error(`请填写第${i+1}项领取未首购按钮跳转`)
                    return;
                }
                let result = checkUrl(formData.noneBuyUrl, `第${i+1}项`);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
                let activityIdObj: any = {
                    coupon: '',
                    gold: []
                }
                for (let j = 0; j < 3; j++) {
                    let obj = formData?.gifts[j] ?? {};
                    const { rewardType, page, activityId, watchButton, watchUrl, receiveButton, rightContent, rightTime } = obj;
                    if (!rewardType) {
                        message.error(`请选择第${i+1}项权益${j+1}权益类型`)
                        return;
                    }
                    if (!page) {
                        message.error(`请填写第${i+1}项权益${j+1}图片`)
                        return;
                    }
                    if (rewardType === '4' && !rightContent) {
                        message.error(`请填写第${i+1}项权益内容`)
                        return;
                    }
                    if (rewardType === '3' && !rightTime) {
                        message.error(`请填写第${i+1}项金牛会员权益时间`)
                        return;
                    }
                    if (['1', '2', '3'].includes(rewardType) && !activityId) {
                        message.error(`请填写第${i+1}项权益${j+1}${rewardType === '1' ? '体验金活动' : rewardType === '2' ? '优惠券' : '活动'}ID`)
                        return;
                    }
                    if (rewardType !== '4' && !receiveButton) {
                        message.error(`请填写第${i+1}项权益${j+1}领取按钮文描`)
                        return;
                    }
                    if (!watchButton) {
                        message.error(`请填写第${i+1}项权益${j+1}${rewardType === '4' ? '' : '查看'}按钮文描`)
                        return;
                    }
                    if (!watchUrl) {
                        message.error(`请填写第${i+1}项权益${j+1}${rewardType === '4' ? '' : '查看'}跳转链接`)
                        return;
                    }
                    let result1 = checkUrl(watchUrl, `第${i+1}项权益${j+1}`);
                    if (result1.isError) {
                        message.error(result1.msg);
                        return;
                    }
                    if (rewardType === '1') {
                        activityIdObj.gold.push(activityId);
                    }
                    if (rewardType === '2') {
                        activityIdObj.coupon += activityId + ',';
                    }
                }
                
                
                try {
                    let promiseAll = []
                    if (activityIdObj.coupon) {
                        let promiseFace = await checkCoupon({}, activityIdObj.coupon);
                        promiseAll.push(promiseFace);
                    }
                    if (activityIdObj.gold.length > 0) {
                        for (let i = 0, len = activityIdObj.gold.length; i < len; i++ ) {
                            let promiseFace = await postActivityDetails({
                                type: 'query',
                                activityIndex: activityIdObj.gold[i]?.trim()
                            });
                            promiseAll.push(promiseFace);
                        }
                    }
                    let resArr = await Promise.all(promiseAll);
                    if (activityIdObj.coupon) {
                        let res0 = resArr[0];
                        const { status_code, status_msg } = res0;
                        if (status_code === 0) {
                            if (res0?.data?.length > 0) {
                                for (let i = 0, len = res0.data.length; i < len; i++) {
                                    const { couponStartDate, couponEndDate, couponId } = res0.data[i];
                                    if ( couponStartDate && couponEndDate ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        if (time < couponStartDate || time > couponEndDate) {
                                            message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let startTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (startTime < couponStartDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                            let endTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (endTime >= couponEndDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                        return;
                                    }
                                }
                            } else {
                                message.error('请检查优惠券ID是否正确');
                                return;
                            }
                        } else {
                            message.error(status_msg || '网络请求错误，请稍后再试');
                            return;
                        }
                    }
                    if (activityIdObj.gold.length > 0) {
                        let start = activityIdObj.coupon ? 1 : 0;
                        for (let i = start, len = resArr.length; i < len; i++ ) {
                            const { code } = resArr[i];
                            if (code === '0000') {
                                if (resArr[i]?.data) {
                                    let { startTime, endTime, indexStr } = resArr[i]?.data;
                                    if ( startTime && endTime ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        startTime = startTime.replace(/[^\d]/g, '');
                                        endTime = endTime.replace(/[^\d]/g, '');
                                        if (time < startTime || time > endTime) {
                                            message.error(`当前体验金活动ID-${indexStr}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let formStartTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (formStartTime < startTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                            let formEndTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (formEndTime >= endTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回体验金活动ID-${indexStr}生效时间`);
                                        return;
                                    }
                                } else {
                                    message.error('请检查体验金活动ID是否正确');
                                    return;
                                }
                            } else {
                                message.error(resArr[i]?.message || '网络请求错误，请稍后再试');
                                return;
                            }
                        }
                    }
                }catch(err) {
                    message.error(err.message);
                    return;
                }
            } else if (floorType === 'operationCard') {
                if (!formData.name) {
                    message.error(`请填写第${i+1}项运营大卡名称`)
                    return;
                }
                if (!formData.page) {
                    message.error(`请上传第${i+1}项运营位图片-白天`)
                    return;
                }
                if (!formData.darkPage) {
                    message.error(`请上传第${i+1}项运营位图片-黑夜`)
                    return;
                }
                if (!formData.content) {
                    message.error(`请填写第${i+1}项按钮文描`)
                    return;
                }
                if (!formData.url) {
                    message.error(`请填写第${i+1}项跳转链接`)
                    return;
                }
            } else if (floorType === 'threesteps') {
                if (!formData.name) {
                    message.error(`请填写第${i+1}项三步走策略名称`)
                    return;
                }
                if (!formData.page) {
                    message.error(`请上传第${i+1}项小图标`)
                    return;
                }
                if (!formData.title) {
                    message.error(`请填写第${i+1}项模块标题`)
                    return;
                }

                let activityIdObj: any = {
                    coupon: '',
                    gold: []
                }
                for (let j = 0; j < 3; j++) {
                    let obj = formData?.steps[j] ?? {};
                    const { type, activityId, page, status, url, button, buttonUrl,rightTime } = obj;
                    if (!type) {
                        message.error(`请选择第${i+1}项步骤${j+1}权益类型`)
                        return;
                    }
                    if (['1','2', '3'].includes(type) && !activityId) {
                        message.error(`请填写第${i+1}项步骤${j+1}${type === '1' ? '体验金活动' : type === '2' ? '优惠券' : '活动'}ID`)
                        return;
                    }
                    if (!page) {
                        message.error(`请填写第${i+1}项步骤${j+1}图片`)
                        return;
                    }
                    if (type === '3' && !rightTime) {
                        message.error(`请填写第${i+1}项步骤${j+1}金牛会员权益时间`)
                        return;
                    }
                    if (!status) {
                        message.error(`请填写第${i+1}项步骤${j+1}状态文描`)
                        return;
                    }
                    if (!url) {
                        message.error(`请填写第${i+1}项步骤${j+1}权益查看跳转链接`)
                        return;
                    }
                    let result = checkUrl(url, `第${i+1}项步骤${j+1}`);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }
                    if (!button) {
                        message.error(`请填写第${i+1}项步骤${j+1}按钮文描`)
                        return;
                    }
                    if (j === 2 ) {
                        if (!buttonUrl) {
                            message.error(`请填写第${i+1}项步骤${j+1}跳转链接`)
                            return;
                        } else {
                            let result = checkUrl(buttonUrl, `第${i+1}项步骤${j+1}`);
                            if (result.isError) {
                                message.error(result.msg);
                                return;
                            }
                        }
                        
                    }
                    if (type === '1') {
                        activityIdObj.gold.push(activityId);
                    }
                    if (type === '2') {
                        activityIdObj.coupon += activityId + ',';
                    }
                }
                if (!formData.finishButton) {
                    message.error(`请填写第${i+1}项完成按钮文描`)
                    return;
                }
                try {
                    let promiseAll = []
                    if (activityIdObj.coupon) {
                        let promiseFace = await checkCoupon({}, activityIdObj.coupon);
                        promiseAll.push(promiseFace);
                    }
                    if (activityIdObj.gold.length > 0) {
                        for (let i = 0, len = activityIdObj.gold.length; i < len; i++ ) {
                            let promiseFace = await postActivityDetails({
                                type: 'query',
                                activityIndex: activityIdObj.gold[i]?.trim()
                            });
                            promiseAll.push(promiseFace);
                        }
                    }
                    let resArr = await Promise.all(promiseAll);
                    if (activityIdObj.coupon) {
                        let res0 = resArr[0];
                        const { status_code, status_msg } = res0;
                        if (status_code === 0) {
                            if (res0?.data?.length > 0) {
                                for (let i = 0, len = res0.data.length; i < len; i++) {
                                    const { couponStartDate, couponEndDate, couponId } = res0.data[i];
                                    if ( couponStartDate && couponEndDate ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        if (time < couponStartDate || time > couponEndDate) {
                                            message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let startTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (startTime < couponStartDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                            let endTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (endTime >= couponEndDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                        return;
                                    }
                                }
                            } else {
                                message.error('请检查优惠券ID是否正确');
                                return;
                            }
                        } else {
                            message.error(status_msg || '网络请求错误，请稍后再试');
                            return;
                        }
                    }
                    if (activityIdObj.gold.length > 0) {
                        let start = activityIdObj.coupon ? 1 : 0;
                        for (let i = start, len = resArr.length; i < len; i++ ) {
                            const { code } = resArr[i];
                            if (code === '0000') {
                                if (resArr[i]?.data) {
                                    let { startTime, endTime, indexStr } = resArr[i]?.data;
                                    if ( startTime && endTime ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        startTime = startTime.replace(/[^\d]/g, '');
                                        endTime = endTime.replace(/[^\d]/g, '');
                                        if (time < startTime || time > endTime) {
                                            message.error(`当前体验金活动ID-${indexStr}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let formStartTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (formStartTime < startTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                            let formEndTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (formEndTime >= endTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回体验金活动ID-${indexStr}生效时间`);
                                        return;
                                    }
                                } else {
                                    message.error('请检查体验金活动ID是否正确');
                                    return;
                                }
                            } else {
                                message.error(resArr[i]?.message || '网络请求错误，请稍后再试');
                                return;
                            }
                        }
                    }
                }catch(err) {
                    message.error(err.message);
                    return;
                }
            } else if (floorType === 'productCard') {
                if (!floorTitle) {
                    message.error(`请填写楼层标题`);
                    return;
                }
                const { type, name, fundCode, fundName, fundManagerName, fundManagerTag, reason, button, buyCount, buyEndTime, buyStartTime,
                    fundManagerPage, profitSection, recommendType, fundTag, show, title, secTitle, targetData } = formData;
                if (!type) {
                    message.error(`请选择第${i+1}项产品大卡类型`)
                    return;
                }
                if (!name) {
                    message.error(`请填写第${i+1}项卡片名称`)
                    return;
                }
                if (['1', '2', '3', '5'].includes(type)) {
                    if (!fundCode) {
                        message.error(`请填写第${i+1}项${ type==='3' ? '小目标' : '基金'}代码`)
                        return;
                    }
                    if (!fundName) {
                        message.error(`请填写第${i+1}项${ type==='3' ? '小目标' : '基金'}名称`)
                        return;
                    }
                }

                if (['1'].includes(type)) {
                    if (!fundManagerName) {
                        message.error(`请选择第${i+1}项基金经理`)
                        return;
                    }
                    if (!fundManagerTag) {
                        message.error(`请填写第${i+1}项基金经理标签`)
                        return;
                    }
                    if (!show || show.length === 0) {
                        message.error(`请选择第${i+1}项展示指标维度`)
                        return;
                    }
                    if (!fundManagerPage) {
                        message.error(`请选择第${i+1}项基金经理图片`)
                        return;
                    }
                }
                
                if (['2', '3', '5'].includes(type)) {
                    if (!title) {
                        message.error(`请填写第${i+1}项主标题文案`)
                        return;
                    }
                }
                if (type === '5') {
                    if (!secTitle) {
                        message.error(`请填写第${i+1}项副标题文案`)
                        return;
                    }
                }
                if (['1', '2'].includes(type)) {
                    if (!fundTag) {
                        message.error(`请填写第${i+1}项基金标签`)
                        return;
                    }
                }
                if (['2', '5'].includes(type)) {
                    if (!profitSection) {
                        message.error(`请选择第${i+1}项收益时间区间`)
                        return;
                    }
                }
                
                if (type === '3') {
                    for (let j = 0,len = targetData?.length; j < len; j++) {
                        let obj = targetData[j];
                        if (obj?.name) {
                        } else {
                            message.error(`请填写第${i+1}项小目标历史业绩第${j+1}项名称`)
                            return;
                        }
                        if (obj?.profit) {
                        } else {
                            message.error(`请填写第${i+1}项小目标历史业绩第${j+1}项止盈时年化收益`)
                            return;
                        }
                    }
                }
                if (['1'].includes(type)) {
                    if (!buyStartTime) {
                        message.error(`请填写第${i+1}项认购开始时间`)
                        return;
                    }
                    if (!buyEndTime) {
                        message.error(`请填写第${i+1}项认购结束时间`)
                        return;
                    }
                    
                }
                if (['1', '3'].includes(type)) {
                    if (!buyCount) {
                        message.error(`请填写第${i+1}项产品${type === '1' ? '认购' : type === '2' ? '关注' : '购买'}人数`)
                        return;
                    }
                }
                if (type === '4') {
                    if (!recommendType) {
                        message.error(`请选择第${i+1}项是否由系统配置`)
                        return;
                    }
                }
                if (['1', '5'].includes(type)) {
                    if (!reason) {
                        message.error(`请填写第${i+1}项推荐理由`)
                        return;
                    }
                }
                if (!button) {
                    message.error(`请填写第${i+1}项按钮文案`)
                    return;
                }
            } else if (floorType === 'multidimension') {
                if (!floorTitle) {
                    message.error(`请填写楼层标题`);
                    return;
                }
                if (!formData.tabType) {
                    message.error(`请选择第${i+1}项tab类型`)
                    return;
                }
                if (!formData.title) {
                    message.error(`请填写第${i+1}项tab标题`)
                    return;
                }
                if (!formData.secTitle) {
                    message.error(`请填写第${i+1}项tab副标题`)
                    return;
                }
                if (!formData.recommendType) {
                    message.error(`请选择第${i+1}项是否由系统推荐`)
                    return;
                }
                if (['mainTrack', 'goodFund', 'selfDefine'].includes(formData.tabType) && formData.recommendType === '2') {
                    for (let j = 0; j < 3; j++) {
                        let obj = formData?.fundConfigList[j] ?? {};
                        const { trackName, code, name, title, secTitle, profitSection, profitSectionTwo, url, rankTag, retracementTag } = obj;
                        if (formData.tabType === 'mainTrack' && !trackName) {
                            message.error(`请填写第${i+1}项板块${j+1}名称`)
                            return;
                        }
                        if (!code) {
                            message.error(`请填写第${i+1}项产品${j+1}代码`)
                            return;
                        }
                        if (!name) {
                            message.error(`请填写第${i+1}项产品${j+1}名称`)
                            return;
                        }
                        if (!title) {
                            message.error(`请填写第${i+1}项产品${j+1}主标题`)
                            return;
                        }
                        if (!secTitle) {
                            message.error(`请填写产品${j+1}副标题`)
                            return;
                        }
                        if (formData.tabType === 'mainTrack') {
                            if (!profitSection) {
                                message.error(`请选择第${i+1}项收益时间区间1`)
                                return;
                            }
                            if (!profitSectionTwo) {
                                message.error(`请选择第${i+1}项收益时间区间2`)
                                return;
                            }
                        } else if (!profitSection) {
                            message.error(`请选择第${i+1}项收益时间区间`)
                            return;
                        }
                        if (['goodFund', 'selfDefine'].includes(formData.tabType)) {
                            if (!retracementTag) {
                                message.error(`请选择第${i+1}项是否展示近1年最大回撤`)
                                return;
                            }
                        }
                        if (!rankTag) {
                            message.error(`请选择第${i+1}项是否展示近1年收益排名`)
                            return;
                        }
                        if (!url) {
                            message.error(`请填写第${i+1}项产品${j+1}跳转链接`)
                            return;
                        }
                        let result = checkUrl(url, `第${i+1}项产品${j+1}`);
                        if (result.isError) {
                            message.error(result.msg);
                            return;
                        }
                    }
                }
            } else if (floorType === 'featuredList') {
                if (!formData.title) {
                    message.error(`请填写第${i+1}项tab标题`)
                    return;
                }
                if (!formData.secTitle) {
                    message.error(`请填写第${i+1}项tab副标题`)
                    return;
                }
                for (let j = 0; j < 3; j++) {
                    let obj = formData?.lists[j] ?? {};
                    const { mainTitle, secTitle, page, url, darkPage } = obj;
                    if (!mainTitle) {
                        message.error(`请填写第${i+1}项榜单${j+1}主标题`)
                        return;
                    }
                    if (!secTitle) {
                        message.error(`请填写第${i+1}项榜单${j+1}副标题`)
                        return;
                    }
                    if (!page) {
                        message.error(`请填写第${i+1}项榜单${j+1}图片-白天`)
                        return;
                    }
                    if (!darkPage) {
                        message.error(`请填写第${i+1}项榜单${j+1}图片-黑夜`)
                        return;
                    }
                    if (!url) {
                        message.error(`请填写第${i+1}项榜单${j+1}跳转链接`)
                        return;
                    }
                    let result = checkUrl(url, `第${i+1}项榜单${j+1}`);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }
                }
            } else if (floorType === 'mainOperate') {
                if (!formData.name) {
                    message.error(`请填写第${i+1}项主运营位名称`)
                    return;
                }
                if (!formData.page) {
                    message.error(`请上传第${i+1}项主运营位图片`)
                    return;
                }
            } else if (floorType === 'secOperate') {
                if (!formData.name) {
                    message.error(`请填写第${i+1}项次运营位名称`)
                    return;
                }
                if (!formData.page1) {
                    message.error(`请上传第${i+1}项次运营位1图片`)
                    return;
                }
                if (!formData.page2) {
                    message.error(`请上传第${i+1}项次运营位2图片`)
                    return;
                }
            } else if(floorType === 'marketSituation') {
                if (!floorTitle) {
                    message.error(`请填写楼层标题`);
                    return;
                }
                if (!formData.name) {
                    message.error(`请填写第${i+1}项策略名称`)
                    return;
                }
                if (!formData.desc) {
                    message.error(`请填写第${i+1}项市场解读`)
                    return;
                }
            } else if(floorType === 'capitalTrend') {
                if (!floorTitle) {
                    message.error(`请填写楼层标题`);
                    return;
                }
                if (!formData.manager || formData.manager.length !== 3) {
                    message.error(`第${i+1}项基金经理数据错误，请重新添加`);
                    return;
                }
                for (let j = 0; j < formData.manager.length; j++) {
                    if (!formData.manager[j].name) {
                        message.error(`请填写第${i+1}项基金经理${j+1}姓名`)
                        return;
                    }
                    if (!formData.manager[j].avatar) {
                        message.error(`请上传第${i+1}项基金经理${j+1}头像`)
                        return;
                    }
                    if (!formData.manager[j].opinion) {
                        message.error(`请填写第${i+1}项基金经理${j+1}观点`)
                        return;
                    }
                }   
            } else if (floorType === "fundTrend") {
                if (!floorTitle) {
                    message.error(`请填写楼层标题`);
                    return;
                }
			} else if (floorType === 'FindChance') {
                if (!floorTitle2) {
                    message.error(`请填写楼层标题`);
                    return;
                }
                if (!formData.topicName) {
                    message.error(`请填写第${i+1}项tab名称`)
                    return;
                }
                if (!formData.topicTag) {
                    message.error(`请填写第${i+1}项标签`)
                    return;
                }
                if (!formData.topicTitle) {
                    message.error(`请填写第${i+1}项主标题`)
                    return;
                }
                if (formData.nodeList.length === 0) {
                    message.error(`第${i+1}项至少添加一个节点`)
                    return;
                }
                for (let j = 0; j < formData.nodeList.length; j++) {
                    if (!formData.nodeList[j].timeNode) {
                        message.error(`请填写第${i+1}项节点${j+1}时间点`)
                        return;
                    }
                    if (!formData.nodeList[j].nodeTitle) {
                        message.error(`请填写第${i+1}项节点${j+1}标题`)
                        return;
                    }
                    if (!formData.nodeList[j].nodeContent) {
                        message.error(`请填写第${i+1}项节点${j+1}内容`)
                        return;
                    }
                }
                if (!formData.fundDesc) {
                    message.error(`请填写第${i+1}项推荐基金文描`)
                    return;
                }
                if (!formData.fundObj) {
                    message.error(`请填写第${i+1}项基金代码`)
                    return;
                }
                if (!formData.fundObj.code) {
                    message.error(`请填写第${i+1}项基金代码`)
                    return;
                } else if (formData.fundObj.code.indexOf(' ') > -1) {
                    message.error(`请注意第${i+1}项基金代码有空格`)
                    return;
                }
                if (!formData.fundObj.name) {
                    message.error(`请重新填写第${i+1}项基金代码来获取基金名称`)
                    return;
                }
                if (!formData.fundTags) {
                    message.error(`请填写第${i+1}项基金亮点标签`)
                    return;
                }
                if (!formData.fundBuyDesc) {
                    message.error(`请填写第${i+1}项购买按钮文描`)
                    return;
                }
            } else if (floorType === 'FindInsurance') {
                if (!floorTitle2) {
                    message.error(`请填写楼层标题`);
                    return;
                }
                if (!formData.tabName) {
                    message.error(`请填写第${i+1}项tab名称`)
                    return;
                }
                if (!formData.tabImage) {
                    message.error(`请上传第${i+1}项tab配图`)
                    return;
                }
                if (!formData.darkTabImage) {
                    message.error(`请上传第${i+1}项tab黑夜配图`)
                    return;
                }
                if (!formData.productImage) {
                    message.error(`请上传第${i+1}项产品头图`)
                    return;
                }
                if (!formData.productMainTitle) {
                    message.error(`请填写第${i+1}项产品主标题`)
                    return;
                }
                if (!formData.productSecTitle) {
                    message.error(`请填写第${i+1}项产品副标题`)
                    return;
                }
                if (!formData.buyButtonDesc) {
                    message.error(`请填写第${i+1}项购买按钮文描`)
                    return;
                }
                if (!formData.buyActionUrl) {
                    message.error(`请填写第${i+1}项产品跳转链接`)
                    return;
                }
                let result = checkUrl(formData.buyActionUrl, `第${i+1}项`);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (floorType === 'RobustFinancial') {
                for (let j = 0; j < formData.productList.length; j++) {
                    let item = formData.productList[j];
                    if (!item.fundObj) {
                        message.error(`请填写第${i+1}项产品${j + 1}产品代码`)
                        return;
                    }
                    if (!item.fundObj.code) {
                        message.error(`请填写第${i+1}项产品${j + 1}产品代码`)
                        return;
                    } else if (item.fundObj.code.indexOf(' ') > -1) {
                        message.error(`请注意第${i+1}项产品${j + 1}产品代码有空格`)
                        return;
                    }
                    if (!item.fundObj.name) {
                        message.error(`请重新填写第${i+1}项产品${j + 1}产品代码来获取产品名称`)
                        return;
                    }
                    if (!item.title) {
                        message.error(`请填写第${i+1}项产品${j + 1}主标题`)
                        return;
                    }
                    if (!item.secTitle) {
                        message.error(`请填写第${i+1}项产品${j + 1}副标题`)
                        return;
                    }
                    if (!item.timeInterval) {
                        message.error(`请填写第${i+1}项产品${j + 1}时间区间`)
                        return;
                    }
                    let result = checkUrl(formData.actionUrl, `第${i+1}项`);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }
                }
            }
            if (formData.startTime && formData.endTime) {
                let startTime = formData.startTime.replace(/[^\d]/g, '');
                let endTime = formData.endTime.replace(/[^\d]/g, '');
                if (startTime >= endTime) {
                    message.error(`第${i+1}项开始时间应早于结束时间`);
                    return;
                }
            } 
            if (['searchhotword', 'mixbanner', 'notice', 'gongge', 'gonggeall', 'operationposition', 'coupon'].includes(floorType)) {
                let result = checkUrl(formData.jumpAction, `第${i+1}项`);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (['operationCard', 'threesteps', 'multidimension', 'featuredList', 'mainOperate', 'capitalTrend', 'FindChance', 'FindInsurance'].includes(floorType)) {
                let result = checkUrl(formData.url, `第${i+1}项`);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (['productCard'].includes(floorType)) {
                let result = checkUrl(formData.watchUrl, `第${i+1}项`);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (['secOperate'].includes(floorType)) {
                let result1 = checkUrl(formData.url1, `第${i+1}项`);
                if (result1.isError) {
                    message.error(result1.msg);
                    return;
                }
                let result2 = checkUrl(formData.url2, `第${i+1}项`);
                if (result2.isError) {
                    message.error(result2.msg);
                    return;
                }
            } else if (['marketSituation'].includes(floorType)) {
                let result = checkUrl(formData.url, `第${i+1}项`);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
                
                let result1 = checkUrl(formData.fundPositionUpUrl, `第${i+1}项基金仓位上`);
                if (result1.isError) {
                    message.error(result1.msg);
                    return;
                }

                let result2 = checkUrl(formData.investorEmotionUrl, `第${i+1}项投资者情绪`);
                if (result2.isError) {
                    message.error(result2.msg);
                    return;
                }

                let result3 = checkUrl(formData.indexValuationUpUrl, `第${i+1}项指数估值上`);
                if (result3.isError) {
                    message.error(result3.msg);
                    return;
                }

                let result4 = checkUrl(formData.fundPositionDownUrl, `第${i+1}项基金仓位下`);
                if (result4.isError) {
                    message.error(result4.msg);
                    return;
                }

                let result5 = checkUrl(formData.indexValuationDownUrl, `第${i+1}项指数估值下`);
                if (result5.isError) {
                    message.error(result5.msg);
                    return;
                }
            }
            if (configData.platform.length === 0) {
                message.error(`请选择第${i+1}项适用平台`)
                return
            } else if (configData.utype.length === 0) {
                message.error(`请选择第${i+1}项用户类型`)
                return
            }
            if (!relationData || !relationData.id) {
                message.error(`请先保存第${i+1}项指定用户数据配置`)
                return
            }
        }
        for (let i = 0, len = originData?.length; i < len; i++) {
            let val = originData[i];
            let realData: any = {};
            let _data: any = {};
            if (val.relationData?.userType === '1') {
                const {blackCustId, whiteCustId, ...other} = val.relationData;
                realData = {...other}
            } else {
                const {blackUserId, whiteUserId, blackCustId, whiteCustId, ...other} = val.relationData;;
                realData = {
                    ...other,
                    blackUserId: blackCustId,
                    whiteUserId: whiteCustId
                }
            }
            if (val.relationData?.targetType === 'kyc') {
                realData.olasId = '';
                realData.olasType = '';
            } else if (val.relationData?.targetType === 'olas') {
                realData.kycLogic = '';
                realData.kycs = [];
            }
            if (floorType === 'mixbanner') {
                if (val.formData?.mixType === '1') {
                    _data = {
                        mixName: val.formData?.mixName,
                        mixType:  val.formData?.mixType,
                        navColorLeft:  val.formData?.navColorLeft,
                        navColorRight:  val.formData?.navColorRight,
                        imageUrl:  val.formData?.imageUrl,
                        imageDarkUrl:  val.formData?.imageDarkUrl,
                        imageSvgaUrl:  val.formData?.imageSvgaUrl,
                        imageSvgaDarkUrl:  val.formData?.imageSvgaDarkUrl,
                        imageSmallSvgaUrl:  val.formData?.imageSmallSvgaUrl,
                        imageSmallSvgaDarkUrl:  val.formData?.imageSmallSvgaDarkUrl,
                        jumpAction:  val.formData?.jumpAction,
                        version:  val.formData?.version,
                        startTime:  val.formData?.startTime,
                        endTime:  val.formData?.endTime
                    }
                } else if (val.formData?.mixType === '2') {
                    _data = {
                        mixName: val.formData?.mixName,
                        mixType: val.formData?.mixType,
                        navColorLeft: val.formData?.navColorLeft,
                        navColorRight: val.formData?.navColorRight,
                        imageUrl: val.formData?.imageUrl,
                        imageDarkUrl:  val.formData?.imageDarkUrl,
                        imageSvgaUrl:  val.formData?.imageSvgaUrl,
                        imageSvgaDarkUrl:  val.formData?.imageSvgaDarkUrl,
                        imageSmallSvgaUrl:  val.formData?.imageSmallSvgaUrl,
                        imageSmallSvgaDarkUrl:  val.formData?.imageSmallSvgaDarkUrl,
                        masterTitle: val.formData?.masterTitle,
                        masterColor: val.formData?.masterColor,
                        subTitle: val.formData?.subTitle,
                        subColor: val.formData?.subColor,
                        buttonText: val.formData?.buttonText,
                        buttonColor: val.formData?.buttonColor,
                        buttonTextColor: val.formData?.buttonTextColor,
                        jumpAction: val.formData?.jumpAction,
                        version: val.formData?.version,
                        startTime: val.formData?.startTime,
                        endTime: val.formData?.endTime
                    }
                } else if (val.formData?.mixType === '3') {
                    _data = {
                        mixName: val.formData?.mixName,
                        mixType: val.formData?.mixType,
                        navColorLeft: val.formData?.navColorLeft,
                        navColorRight: val.formData?.navColorRight,
                        imageUrl: val.formData?.imageUrl,
                        imageDarkUrl:  val.formData?.imageDarkUrl,
                        imageSvgaUrl:  val.formData?.imageSvgaUrl,
                        imageSvgaDarkUrl:  val.formData?.imageSvgaDarkUrl,
                        imageSmallSvgaUrl:  val.formData?.imageSmallSvgaUrl,
                        imageSmallSvgaDarkUrl:  val.formData?.imageSmallSvgaDarkUrl,
                        unreceivedBtnName: val.formData?.unreceivedBtnName,
                        receivedUnOpenAccBtnName: val.formData?.receivedUnOpenAccBtnName,
                        receivedUnBuyFundBtnName: val.formData?.receivedUnBuyFundBtnName,
                        receivedUnBuyFundBtnJumpAction: val.formData?.receivedUnBuyFundBtnJumpAction,
                        version: val.formData?.version,
                        equities1: val.formData?.equities1,
                        equities2: val.formData?.equities2,
                        equities3: val.formData?.equities3,
                        startTime: val.formData?.startTime,
                        endTime: val.formData?.endTime
                    }
                    
                }
                _data = { ..._data, ...val.configData, ...realData};
            } else if (floorType === 'usercomment'){
                val.commentList?.forEach((item: any, index: number) => {
                    item.index = index;
                })
                _data = { commentList: val.commentList, ...val.formData, ...val.configData, ...realData};
            } else {
                _data = { ...val.formData, ...val.configData, ...realData};
            }
            _value.push(_data);
        }
        if (floorType === 'gonggeall') {
            let arr: string[] = [];
            gonggeClassify?.forEach((item: any) => {
                arr.push(item.name);
            })
            _sendData = {
                value: JSON.stringify({
                    lastEditor: localStorage.name,
                    lastEditTime: timeFormat2(),
                    gonggeClassify: arr,
                    confs: _value,
                    type: modelName(floorType).type
                }),
            }
        } else if (floorType === 'productCard' || 
            floorType === 'marketSituation' || 
            floorType === 'capitalTrend' || 
            floorType === 'fundTrend' ||
            floorType === 'multidimension') {
            _sendData = {
                value: JSON.stringify({
                    lastEditor: localStorage.name,
                    lastEditTime: timeFormat2(),
                    confs: _value,
                    title: floorTitle,
                    type: modelName(floorType).type
                }),
            }
        } else if (floorType === 'FindChance' || floorType === 'FindInsurance') {
            _sendData = {
                value: JSON.stringify({
                    lastEditor: localStorage.name,
                    lastEditTime: timeFormat2(),
                    confs: _value,
                    title: floorTitle2,
                    moreTitle: floorMoreBtnText2,
                    moreActionUrl: floorMoreBtnUrl2,
                    moreVersion: floorMoreBtnVersion2,
                    type: modelName(floorType).type
                }),
            }
        } else if (floorType === 'RobustFinancial') {
            _sendData = {
                value: JSON.stringify({
                    lastEditor: localStorage.name,
                    lastEditTime: timeFormat2(),
                    confs: _value,
                    title: floorTitle2,
                    secTitle: floorSubTitle2,
                    moreTitle: floorMoreBtnText2,
                    moreActionUrl: floorMoreBtnUrl2,
                    moreVersion: floorMoreBtnVersion2,
                    type: modelName(floorType).type
                }),
            }
        } else {
            _sendData = {
                value: JSON.stringify({
                    lastEditor: localStorage.name,
                    lastEditTime: timeFormat2(),
                    confs: _value,
                    type: modelName(floorType).type
                }),
            }
        }
        
        console.log('send', _sendData)
        api[handleApi(floorType).post](
            _sendData
        ).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.message);
            } else {
                message.success('保存成功！');
                let timer = setTimeout(() => {
                    clearTimeout(timer);
                    location.href = `#/form/scFinancialTabV2/pageConfig`
                }, 1000);
            }
        }).catch((e: Error) => {
            message.error(e?.message);
        })
    }
    function addItem() {
        let obj:any = {
            formData: addData(floorType),
            configData: {
                platform: config.platform,
                utype: config.utype,
            },
            relationData: {
                id: '',
                targetType: '',
                kycLogic: '',
                kycs: [],
                olasId: '',
                olasType: '',
                userType: '1', // 1:user   3:cust
                blackUserId: '',
                whiteUserId: '',
                blackCustId: '',
                whiteCustId: '',
                updateTime: ''
            }
        }
        if (floorType === 'usercomment') {
            obj.commentList = [];
        }
        let data = [...originData, obj];
        setOriginData(data);
        setActiveKey(data.length - 1);
    }

    function handleUpdate(data: any, index: number) {
        if (!isModify) setModify(true);
        let _originData: any = [...originData];
        _originData[index] = data;
        setOriginData(_originData);  
    }
    function handleDelete(index: number) {
        if (!isModify) setModify(true);
        let _originData = [...originData];
        _originData.splice(index, 1);
        setOriginData(_originData);
    }
    function handleSelect(item: any) {
        let tag = 0;
        if (item?.configData.platform?.length === 0 && item?.configData.utype?.length === 0) {
            tag = 1;
        } else if (config.utype?.length === 0) {
            for (let data of item?.configData?.platform) {
                if (config.platform?.indexOf(data) != -1) {
                    tag = 1;
                    break;
                }
            }
        } else if (config.platform?.length === 0) {
            for (let data of item?.configData.utype) {
                if (config.utype?.indexOf(data) != -1) {
                    tag = 1;
                    break;
                }
            }
        } else {
            for (let data of item?.configData?.platform) {
                if (config.platform?.indexOf(data) != -1) {
                    for (let data of item?.configData?.utype) {
                        if (config.utype?.indexOf(data) != -1) {
                            tag = 1;
                            break;
                        }
                    }
                    if (tag === 1) {
                        break;
                    }
                }
            }
        }
        return !!tag
    }
    function handleActiveKey (key: any) {
        setActiveKey(key);
    }
    function goUp(e: any, item: any, index: number) {
        e.stopPropagation();

        if (index === 0) {
            message.info('已在最上方');
            return
        }
        let _originData = [...originData];
        _originData.splice(index, 1);
        _originData.splice(index - 1, 0, item);
        setOriginData(_originData);
        if (!isModify) setModify(true);
    }
    function goDown(e: any, item: any, index: number) {
        e.stopPropagation();
        if (index === originData.length - 1) {
            message.info('已在最下方');
            return
        }
        let _originData = [...originData];
        _originData.splice(index, 1);
        _originData.splice(index + 1, 0, item);
        setOriginData(_originData);
        if (!isModify) setModify(true);
    }
    // 新增宫格分类
    function addClassify() {
        let gArr: any = [...gonggeClassify];
        let iArr: any = [...inputArr];
        let obj = {
            name: '',
            isEdit: true
        }
        gArr.push(obj);
        iArr.push('');
        setGonggeClassify(gArr);
        setInputArr(iArr);
    }
    // 删除宫格分类
    function handleDeleteClasify(num: number) {
        console.log(gonggeClassify, inputArr);
        let _originData = [...originData];
        let icon = gonggeClassify[num]?.name;
        for(let i = 0, len = _originData?.length; i < len; i++ ) {
            if (_originData[i]?.formData?.gonggeType === icon) {
                message.error('该分类下还存在未删除的宫格！');
                return;
            }
        }
        if (!isModify) setModify(true);
        let gArr: any = [...gonggeClassify];
        let iArr: any = [...inputArr];
        gArr.splice(num, 1);
        iArr.splice(num, 1);
        setGonggeClassify(gArr);
        setInputArr(iArr);
    }
    // 编辑宫格分类
    function handleClassifyEdit(obj: any, num: number) {
        console.log(obj, gonggeClassify, inputArr);
        let arr: any = [...gonggeClassify];
        if (obj.isEdit) {
            let name = inputArr[num];
            if (!name) {
                message.error('该宫格分类为空，请重新输入后保存');
                return;
            }
            for(let i = 0, len = gonggeClassify.length; i < len; i++) {
                if (i === num) {
                    break;
                } else {
                    if (name === gonggeClassify[i]?.name) {
                        message.error('该宫格分类已存在！');
                        return;
                    }
                }
            } 
            let _originData = [...originData];
            let icon = gonggeClassify[num]?.name;
            for(let i = 0, len = _originData?.length; i < len; i++ ) {
                if (_originData[i]?.formData?.gonggeType === icon && icon !== name){
                    message.error('该分类下还存在未删除的宫格, 无法修改！');
                    return;
                }
                
            }
            obj.name = name;
        }
        if (!isModify && obj.isEdit) setModify(true);
        obj.isEdit = !obj.isEdit;
        
        arr[num] = obj;
        setGonggeClassify(arr);
    }
    // 输入宫格内容
    function handleInput(e: any, num: number) {
        let arr: any = [...inputArr];
        arr[num] = e.target.value;
        setInputArr(arr);
    }
    function saveFloor () {
        if (floorInfo.isEdit) {
            if (!floorInfo.floorTitle) {
                message.error('请填写楼层标题');
                return;
            }
            setFloorTitle(floorInfo.floorTitle);
            setModify(true);
        }
        let obj = {...floorInfo};
        obj.isEdit = !obj.isEdit;
        setFloorInfo(obj);
    }
    function saveFloor2 () {
        if (floorInfo2.isEdit) {
            if (!floorInfo2.floorTitle) {
                message.error('请填写楼层标题');
                return;
            }
            if (floorType === 'RobustFinancial' && !floorInfo2.floorSubTitle) {
                message.error('请填写楼层副标题');
                return;
            }
            let result = checkUrl(floorInfo2.moreBtnUrl);
            if (result.isError) {
                message.error(result.msg);
                return;
            }
            setFloorTitle2(floorInfo2.floorTitle);
            setfloorSubTitle2(floorInfo2.floorSubTitle);
            setfloorMoreBtnText2(floorInfo2.moreBtnText);
            setfloorMoreBtnUrl2(floorInfo2.moreBtnUrl);
            setfloorMoreBtnVersion2(floorInfo2.moreBtnVersion);
            setModify(true);
        }
        let obj = {...floorInfo2};
        obj.isEdit = !obj.isEdit;
        setFloorInfo2(obj);
    }

    function handleFloorInput (e: any) {
        setFloorInfo({
            floorTitle: e.target.value,
            isEdit: true
        });
    }
    function handleFloorInput2 (e: any, type: string) {
        let _floorInfo2 = {...floorInfo2};
        _floorInfo2[type] = e.target.value;
        _floorInfo2.isEdit = true;
        setFloorInfo2(_floorInfo2);
    }

    if (!init) return (
        <div>加载中</div>
    );
    return <div>
        <LocationModel location={modelName(floorType).location} />
        <ConfigSelect handleChange={handleChange} isHead={true} />
        <Button type="primary" style={{marginBottom: 20}} onClick={onSubmit} disabled={!isModify}>保存</Button>
        { floorType === 'gonggeall' && (
            <div className={styles['m-gongge-classify']}>
                <header>宫格分类</header>
                <div className={styles['m-classify-content']}>
                    <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>宫格分类：</p>
                    <ul>
                        { gonggeClassify?.length > 0 && gonggeClassify.map((item: any, index: number) => {
                            return (
                                <li key={index}>
                                    <Input style={{marginRight: 20}} disabled={!item.isEdit} value={inputArr[index]} onChange={(e) => handleInput(e, index)} />
                                    <Button style={{marginRight: 20}} onClick={() => handleClassifyEdit(item, index)}>{item.isEdit ? '保存' : '编辑'}</Button>
                                    <Popconfirm
                                        title="确定删除?"
                                        onConfirm={() => {handleDeleteClasify(index)}}
                                        okText="是"
                                        cancelText="否"
                                        disabled={!item.isEdit}
                                    >
                                        <Button type="danger" ghost disabled={!item.isEdit}>删除</Button>
                                    </Popconfirm>
                                    
                                </li>
                            )
                        }) }
                    </ul>
                </div>
                <Button style={{marginLeft: 20, marginBottom: 20}} onClick={addClassify}>添加</Button>
            </div> 
        ) }
        { (floorType === 'productCard' || floorType === 'marketSituation' || floorType === 'capitalTrend' || floorType === 'fundTrend' || floorType === 'multidimension') && (
            <div className={styles['m-gongge-classify']}>
                <header>楼层信息</header>
                <div className={styles['m-classify-content']}>
                    <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>楼层标题：</p>
                    <Input style={{marginRight: 20, width: 300}} disabled={!floorInfo.isEdit} value={floorInfo.floorTitle} onChange={handleFloorInput} />
                    <Button style={{marginLeft: 20, marginBottom: 20}} onClick={saveFloor}>{floorInfo.isEdit ? '保存' : '编辑'}</Button>
                </div>
            </div> 
        )}
        { (floorType === 'FindChance' || floorType === 'RobustFinancial' || floorType === 'FindInsurance') && (
            <div className={styles['m-gongge-classify']}>
                <header>楼层信息</header>
                <div className={styles['m-classify-content']}>
                    <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>楼层标题：</p>
                    <Input style={{marginRight: 20, width: 300}} disabled={!floorInfo2.isEdit} value={floorInfo2.floorTitle} onChange={(e) => {handleFloorInput2(e, 'floorTitle')}} />
                </div>
                {
                    floorType === 'RobustFinancial' &&
                    <div className={styles['m-classify-content']}>
                        <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>楼层副标题：</p>
                        <Input style={{marginRight: 20, width: 300}} disabled={!floorInfo2.isEdit} value={floorInfo2.floorSubTitle} onChange={(e) => {handleFloorInput2(e, 'floorSubTitle')}} />
                    </div>
                }
                <div className={styles['m-classify-content']}>
                    <p className={styles['m-card-label']}>更多按钮文描：</p>
                    <Input style={{marginRight: 20, width: 300}} disabled={!floorInfo2.isEdit} value={floorInfo2.moreBtnText} onChange={(e) => {handleFloorInput2(e, 'moreBtnText')}} />
                </div>
                <div className={styles['m-classify-content']}>
                    <p className={styles['m-card-label']}>跳转链接：</p>
                    <Input style={{marginRight: 20, width: 300}} disabled={!floorInfo2.isEdit} value={floorInfo2.moreBtnUrl} onChange={(e) => {handleFloorInput2(e, 'moreBtnUrl')}} />
                </div>
                <div className={styles['m-classify-content']}>
                    <p className={styles['m-card-label']}>版本控制：</p>
                    <Input style={{marginRight: 20, width: 300}} disabled={!floorInfo2.isEdit} value={floorInfo2.moreBtnVersion} onChange={(e) => {handleFloorInput2(e, 'moreBtnVersion')}} />
										<p>*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist</p>
                </div>
                <Button style={{marginLeft: 20, marginBottom: 20}} onClick={saveFloor2}>{floorInfo2.isEdit ? '保存' : '编辑'}</Button>
            </div> 
        )}
        <Collapse activeKey={activeKey} onChange={handleActiveKey}>
            {
                originData?.map((item: any, index: number) => {
                    return handleSelect(item) ?
                <Panel 
                    header={(<span style={{height: 22, display: 'inline-block', verticalAlign: 'middle'}}>#{index+1} {item.formData?.[modelName(floorType).name]}</span>)} 
                    extra={<div className={styles['m-collpase-button']}><Button onClick={(e) => { goUp(e, item, index) }}>上移</Button><Button onClick={(e) => { goDown(e, item, index) }}>下移</Button></div>}
                    key={index}>
                    <MyCard
                        data={item}
                        gonggeClassify={gonggeClassify}
                        position={index}
                        floorType={floorType}
                        kycTag={kycTag}
                        olasTag={olasTag}
                        handleDelete={handleDelete}
                        handleUpdate={handleUpdate}></MyCard>
                </Panel>
                : null

                })
            }
        </Collapse>
        <Button onClick={addItem} type='primary' style={{ marginTop: '20px' }}>添加</Button>
    </div>

}