import React from 'react';
import ImgUpload from '../uploadImg/index.jsx';
interface dataProps {
    onChange: Function,
    imgUrl: string,
    disabled: boolean,
    size?: Array<string>,
    title?: string,
}

export default function (props:dataProps) {

    return <div style={{width: 514}}>
        <ImgUpload 
            handleChange={(value: any) => props.onChange(value)}
            imageUrl={props.imgUrl}
            isEdit={!props.disabled}
            title={props.title}
            size={props.size}
        />
    </div>

}