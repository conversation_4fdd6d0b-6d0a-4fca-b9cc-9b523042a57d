import React, {useEffect, useState, useCallback, useRef} from 'react';
import { Button, message, Popconfirm, Select, Input, InputNumber } from 'antd';
import FormRender from "form-render/lib/antd";
import styles from '../../pageConfig/index.less';
import DesignatedUser from '../designatedUser';
import api from 'api';
import { timeFormat2 } from '@/utils/utils';
import { checkUrl } from '../../script/utils'; 
import SEARCH_FORM_CONFIG from '../../pageConfig/searchhotword/form.json';
import BANNER_FORM_CONFIG from '../../pageConfig/mixbanner/form.json';
import NOTICE_FORM_CONFIG from '../../pageConfig/notice/form.json';
import ICON_FORM_CONFIG from '../../pageConfig/gongge/form.json';
import ICON_ALL_FORM_CONFIG from '../../pageConfig/gonggeall/form.json';
import POSITION_FORM_CONFIG from '../../pageConfig/operationposition/form.json';
import USER_FORM_CONFIG from '../../pageConfig/usercomment/form.json';
import COUPON_FORM_CONFIG from '../../pageConfig/coupon/form.json';
import BLOCK_FORM_CONFIG from '../../pageConfig/operationblock/form.json';
import BAG_FORM_CONFIG from '../../pageConfig/biggiftbag/form.json';
import CARD_FORM_CONFIG from '../../pageConfig/operationCard/form.json';
import STEP_FORM_CONFIG from '../../pageConfig/threesteps/form.json';
import MULT_FORM_CONFIG from '../../pageConfig/multidimension/form.json';
import FEATURED_FORM_CONFIG from '../../pageConfig/featuredlist/form.json';
import MAINOPERATE_FORM_CONFIG from '../../pageConfig/mainOperate/form.json';
import SECOPERATE_FORM_CONFIG from '../../pageConfig/secOperate/form.json';
import MARKETSITUATION_FORM_CONFIG from '../../pageConfig/marketSituation/form.json';
import CAPITALTREND_FORM_CONFIG from '../../pageConfig/capitalTrend/form.json';
import INVESTMENTOPORTUNITY_FORM_CONFIG from '../../pageConfig/FindChance/form.json';
import FINDINSURANCE_FORM_CONFIG from '../../pageConfig/FindInsurance/form.json';
import ROBUSTFINANCIAL_FORM_CONFIG from '../../pageConfig/RobustFinancial/form.json';
import classnames from 'classnames';
import ImgUploadImg from '../../pageConfig/mixbanner/uploadImg';
import MixBannerImgUploadImg from '../../pageConfig/mixbanner/uploadImg2';
import MixBannerImgUploadImg3 from '../../pageConfig/mixbanner/uploadImg3';
import MixBannerImgUploadImg4 from '../../pageConfig/mixbanner/uploadImg4';
import MixBannerImgUploadImg5 from '../../pageConfig/mixbanner/uploadImg5';
import MixBannerImgUploadImg6 from '../../pageConfig/mixbanner/uploadImg6';
import PageUploadImg from '../../pageConfig/gongge/uploadImg';
import DarkPageUploadImg from '../../pageConfig/gongge/uploadImg2';
import CouponUploadImg from '../../pageConfig/coupon/uploadImg';
import BagUploadImg from '../../pageConfig/biggiftbag/uploadImg';
import CardUploadImg from '../../pageConfig/operationCard/uploadImg';
import CardUploadImg2 from '../../pageConfig/operationCard/uploadImg2';
import StepUploadImg from '../../pageConfig/threesteps/uploadImg';
import BlockList from '../../pageConfig/operationblock/blockList';
import BagGiftList from '../../pageConfig/biggiftbag/giftList';
import StepList from '../../pageConfig/threesteps/stepList';
import BaseFundConfigList from '../../pageConfig/multidimension/blockList';
import FundCodeNameConfig from '../../pageConfig/FindChance/blockList';
import RobustFinancialFundNameConfig from '../../pageConfig/RobustFinancial/blockList';
import FeaturedList from '../../pageConfig/featuredlist/blockList';
import Block from '../../pageConfig/productcard/block';
import mainOperateImg from '../../pageConfig/mainOperate/uploadImg';
import secOperateImg1 from '../../pageConfig/secOperate/uploadImg1';
import secOperateImg2 from '../../pageConfig/secOperate/uploadImg2';
import CapitalTrendImg from '../../pageConfig/capitalTrend/uploadImg';
import InsuranceImg1 from '../../pageConfig/FindInsurance/uploadImg1';
import InsuranceImg2 from '../../pageConfig/FindInsurance/uploadImg2';
import InsuranceImg3 from '../../pageConfig/FindInsurance/uploadImg3';

const { updateUserType, checkCoupon, postActivityDetails } = api;
const { Option } = Select;
interface dataProps {
    data: any;
    kycTag: any;
    olasTag: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
    gonggeClassify?: any;
    floorType: string;
}
interface iSelectProps {
    onChange: Function,
    value: string,
    readonly: boolean
}

export default function ({floorType, gonggeClassify, data, kycTag, olasTag, position, handleUpdate, handleDelete}: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    const [designatedData, setDesignatedData] = useState<any>();
    const [formConfig, setFormConfig] = useState<any>(null);
    const [formData, setFormState] = useState<any>({});
    const [commentList, setCommentList] = useState([]);
    const [codeEdit, setCodeEdit] = useState(true);
    useEffect(() => {
        let json: any = '';
        switch(floorType) {
            case 'searchhotword':
                json = SEARCH_FORM_CONFIG;
                break;
            case 'mixbanner':
                json = BANNER_FORM_CONFIG;
                break;
            case 'notice':
                json = NOTICE_FORM_CONFIG;
                break;
            case 'gongge':
                json = ICON_FORM_CONFIG;
                break;
            case 'gonggeall':
                json = ICON_ALL_FORM_CONFIG;
                break;
            case 'operationposition':
                json = POSITION_FORM_CONFIG;
                break;
            case 'usercomment':
                json = USER_FORM_CONFIG;
                break;
            case 'coupon':
                json = COUPON_FORM_CONFIG;
                break;
            case 'operationblock':
                json = BLOCK_FORM_CONFIG;
                break;
            case 'biggiftbag':
                json = BAG_FORM_CONFIG;
                break;
            case 'operationCard':
                json = CARD_FORM_CONFIG;
                break;
            case 'threesteps':
                json = STEP_FORM_CONFIG;
                break;
            case 'multidimension':
                json = MULT_FORM_CONFIG;
                break;
            case 'featuredList':
                json = FEATURED_FORM_CONFIG;
                break;
            case 'mainOperate': 
                json = MAINOPERATE_FORM_CONFIG;
                break;
            case 'secOperate':
                json = SECOPERATE_FORM_CONFIG;
                break;
            case 'marketSituation':
                json = MARKETSITUATION_FORM_CONFIG;
                break;
            case 'capitalTrend':
                json = CAPITALTREND_FORM_CONFIG;
                break;
            case 'FindChance':
                json = INVESTMENTOPORTUNITY_FORM_CONFIG;
                break;
            case 'FindInsurance':
                json = FINDINSURANCE_FORM_CONFIG;
                break;
            case 'RobustFinancial':
                json = ROBUSTFINANCIAL_FORM_CONFIG;
                break;
        }
        setFormConfig(json);
    }, [])
    useEffect(() => {
        setFormState(data.formData);
        if (floorType === 'usercomment') {
            setCommentList(data.commentList);
        }   
        setDesignatedData({
            configData: data.configData,
            relationData: data.relationData
        })
    }, [data]);

    const onValidate = (valid: any) => {
        setValid(valid);
    }
    const handleChange = async () => {
        if(isEdit){
            if (floorType === 'searchhotword') {
                if (!formData.searchWord) {
                    message.error('请填写搜索热词')
                    return;
                }
            } else if (floorType === 'mixbanner') {
                if (!formData.mixName) {
                    message.error('请填写融合运营位名称')
                    return;
                }
                if (!formData.mixType) {
                    message.error('请填写融合运营位类型')
                    return;
                }
                if (!formData.navColorLeft) {
                    message.error('请填写导航栏颜色-左')
                    return;
                }
                if (!formData.imageUrl) {
                    message.error('请上传图片')
                    return;
                }
                if (!formData.imageDarkUrl) {
                    message.error('请上传黑夜模式图片')
                    return;
                }
                if (formData.mixType === '2') {
                    if (!formData.masterTitle) {
                        message.error('请填写主标题')
                        return;
                    }
                    if (!formData.masterColor) {
                        message.error('请填写主标题颜色')
                        return;
                    }
                } else if (formData.mixType === '3') {
                    if (!formData.unreceivedBtnName) {
                        message.error('请填写未领取按钮名称')
                        return;
                    }
                    if (!formData.receivedUnOpenAccBtnName) {
                        message.error('请填写领取未开户按钮名称')
                        return;
                    }
                    if (!formData.receivedUnBuyFundBtnName) {
                        message.error('请填写领取未首购按钮名称')
                        return;
                    }
                    if (!formData.receivedUnBuyFundBtnJumpAction) {
                        message.error('请填写未首购按钮跳转')
                        return;
                    }
                    let result = checkUrl(formData.receivedUnBuyFundBtnJumpAction);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }
                    const { equities1, equities2, equities3 } = formData;
                    let equities = [equities1, equities2, equities3];
                    for (let i = 0; i < equities.length; i++) {
                        if (equities[i].type === 'expGold') {
                            if (!equities[i].id) {
                                message.error(`请填写权益${i + 1}体验金活动ID`)
                                return;
                            }
                            if (equities[i].id.indexOf(',') > -1 || equities[i].id.indexOf(' ') > -1) {
                                message.error(`权益${i + 1}体验金活动ID只能配置一个`)
                                return;
                            }
                        } else if (equities[i].type === 'coupon') {
                            if (!equities[i].id) {
                                message.error(`请填写权益${i + 1}优惠券ID`)
                                return;
                            }
                            if (equities[i].id.indexOf(' ') > -1) {
                                message.error(`权益${i + 1}多个优惠券ID请用英文逗号隔开`)
                                return;
                            }
                        } else if (equities[i].type === 'goldCow') {
                            if (!equities[i].id) {
                                message.error(`请填写权益${i + 1}活动ID`)
                                return;
                            }
                            if (equities[i].id.indexOf(',') > -1 || equities[i].id.indexOf(' ') > -1) {
                                message.error(`权益${i + 1}多个活动ID只能配置一个`)
                                return;
                            }
                            if (!equities[i].jnhyTimeDesc) {
                                message.error(`请填写权益${i + 1}金牛会员权益时间`)
                                return;
                            }
                        } else if (equities[i].type === 'blank') {
                            if (!equities[i].equitiesDesc) {
                                message.error(`请填写权益${i + 1}权益内容`)
                                return;
                            }
                        }
                    }
                }
            } else if (floorType === 'notice') {
                if (!formData.noticeTitle) {
                    message.error('请填写公告标题')
                    return;
                }
                if (!formData.startTime) {
                    message.error('请填写开始时间')
                    return;
                }
                if (!formData.endTime) {
                    message.error('请填写结束时间')
                    return;
                }
            } else if (floorType === 'gongge') {
                if (!formData.gonggeName) {
                    message.error('请填写宫格名称')
                    return;
                }
                if (!formData.page) {
                    message.error('请上传图标-白天')
                    return;
                }
                if (!formData.darkPage) {
                    message.error('请上传图标-黑夜')
                    return;
                }
                if (!formData.jumpAction) {
                    message.error('请填写跳转链接')
                    return;
                }
            } else if (floorType === 'gonggeall') {
                if (!formData.gonggeType) {
                    message.error('请选择宫格分类')
                    return;
                }
                let exist = false;
                gonggeClassify.forEach((item: any) => {
                    if (item.name === formData.gonggeType) {
                        exist = true;
                    }
                })
                if (!exist) {
                    message.error('该宫格分类不存在，请重新选择')
                    return;
                }
                if (!formData.gonggeName) {
                    message.error('请填写宫格名称')
                    return;
                }
                if (!formData.page) {
                    message.error('请上传图标-白天')
                    return;
                }
                if (!formData.darkPage) {
                    message.error('请上传图标-黑夜')
                    return;
                }
                if (!formData.jumpAction) {
                    message.error('请填写跳转链接')
                    return;
                }
            } else if (floorType === 'operationposition') {
                if (!formData.tag) {
                    message.error('请填写标签')
                    return;
                }
                if (!formData.textTitle) {
                    message.error('请填写文字链标题')
                    return;
                }
                if (!formData.jumpAction) {
                    message.error('请填写跳转链接')
                    return;
                }
            } else if (floorType === 'usercomment') {
                if (!formData.userCommentName) {
                    message.error('请填写用户精评名称')
                    return;
                }
                for (let i = 0,len = commentList?.length; i < len; i++) {
                    const { userName, userTag, startPoint, content } =  commentList[i];
                    if (!userName) {
                        message.error(`请填写第${i+1}项用户昵称`)
                        return;
                    }
                    if (!userTag) {
                        message.error(`请填写第${i+1}项用户标签`)
                        return;
                    }
                    if (!startPoint) {
                        message.error(`请填写第${i+1}项初始点赞数`)
                        return;
                    }
                    if (!content) {
                        message.error(`请填写第${i+1}项精评内容`)
                        return;
                    }
                }
            } else if (floorType === 'coupon') {
                if (!formData.couponName) {
                    message.error('请填写优惠券运营位名称')
                    return;
                }
                if (!formData.imageUrl) {
                    message.error('请上传优惠券图标')
                    return;
                }
                if (!formData.couponText) {
                    message.error('请填写优惠券展示文案')
                    return;
                }
                if (!formData.couponId) {
                    message.error('请填写优惠券ID')
                    return;
                }
                if (!formData.achieveText) {
                    message.error('请填写领取按钮文描')
                    return;
                }
                if (!formData.seeText) {
                    message.error('请填写查看按钮文描')
                    return;
                }
                if (!formData.jumpAction) {
                    message.error('请填写跳转链接')
                    return;
                }  
                try {
                    let result = await checkCoupon({}, formData.couponId);
                    const { status_code, status_msg } = result;
                    if (status_code === 0) {
                        if (result?.data?.length > 0) {
                            for (let i = 0, len = result.data.length; i < len; i++) {
                                const { couponStartDate, couponEndDate, couponId } = result.data[i];
                                if ( couponStartDate && couponEndDate ) {
                                    let time = timeFormat2();
                                    time = time.replace(/[^\d]/g, '');
                                    if (time < couponStartDate || time > couponEndDate) {
                                        message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                        return;
                                    }
                                    if (formData.startTime) {
                                        let startTime = formData.startTime.replace(/[^\d]/g, '');
                                        if (startTime < couponStartDate) {
                                            message.error('模块的时间范围必须在优惠券的时间范围之内');
                                            return;
                                        }
                                    }
                                    if (formData.endTime) {
                                        let endTime = formData.endTime.replace(/[^\d]/g, '');
                                        if (endTime >= couponEndDate) {
                                            message.error('模块的时间范围必须在优惠券的时间范围之内');
                                            return;
                                        }
                                    }
                                } else {
                                    message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                    return;
                                }
                            }
                        } else {
                            message.error('请检查优惠券ID是否正确');
                            return;
                        }
                    } else {
                        message.error(status_msg || '网络请求错误，请稍后再试');
                        return;
                    }
                } catch(e) {
                    message.error(e.message);
                    return;
                }
            } else if (floorType === 'operationblock') {
                if (!formData.name) {
                    message.error('请填写运营豆腐块名称')
                    return;
                }
                for (let i = 0; i < 2; i++) {
                    let obj = formData?.blocks[i] ?? {};
                    const { mainTitle, secTitle, page, url } = obj;
                    if (!mainTitle) {
                        message.error(`请填写豆腐块${i+1}主标题`)
                        return;
                    }
                    if (!secTitle) {
                        message.error(`请填写豆腐块${i+1}副标题`)
                        return;
                    }
                    if (!page) {
                        message.error(`请填写豆腐块${i+1}图片`)
                        return;
                    }
                    if (!url) {
                        message.error(`请填写豆腐块${i+1}跳转链接`)
                        return;
                    }
                    let result = checkUrl(url, `豆腐块${i+1}`);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }
                }
            } else if (floorType === 'biggiftbag') {
                if (!formData.name) {
                    message.error('请填写大礼包策略名称')
                    return;
                }
                if (!formData.page) {
                    message.error('请上传小图标')
                    return;
                }
                if (!formData.title) {
                    message.error('请填写礼包标题')
                    return;
                }
                if (!formData.noneReceiveButton) {
                    message.error('请填写未领取按钮名称')
                    return;
                }
                if (!formData.noneAccountButton) {
                    message.error('请填写领取未开户按钮名称')
                    return;
                }
                
                if (!formData.noneBuyButton) {
                    message.error('请填写领取未首购按钮名称')
                    return;
                }
                if (!formData.noneBuyUrl) {
                    message.error('请填写领取未首购按钮跳转')
                    return;
                }
                let result = checkUrl(formData.noneBuyUrl);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }

                let activityIdObj: any = {
                    coupon: '',
                    gold: []
                }
                for (let i = 0; i < 3; i++) {
                    let obj = formData?.gifts[i] ?? {};
                    const { rewardType, page, activityId, watchButton, watchUrl, receiveButton, rightContent, rightTime } = obj;
                    if (!rewardType) {
                        message.error(`请选择权益${i+1}权益类型`)
                        return;
                    }
                    if (!page) {
                        message.error(`请填写权益${i+1}图片`)
                        return;
                    }
                    if (rewardType === '4' && !rightContent) {
                        message.error(`请填写权益内容`)
                        return;
                    }
                    if (rewardType === '3' && !rightTime) {
                        message.error(`请填写金牛会员权益时间`)
                        return;
                    }
                    if (['1', '2', '3'].includes(rewardType) && !activityId) {
                        message.error(`请填写权益${i+1}${rewardType === '1' ? '体验金活动' : rewardType === '2' ? '优惠券' : '活动'}ID`)
                        return;
                    }
                    if (rewardType !== '4' && !receiveButton) {
                        message.error(`请填写权益${i+1}领取按钮文描`)
                        return;
                    }
                    if (!watchButton) {
                        message.error(`请填写权益${i+1}${rewardType === '4' ? '' : '查看'}按钮文描`)
                        return;
                    }
                    if (!watchUrl) {
                        message.error(`请填写权益${i+1}${rewardType === '4' ? '' : '查看'}跳转链接`)
                        return;
                    }
                    let result1 = checkUrl(watchUrl, `权益${i+1}`);
                    if (result1.isError) {
                        message.error(result1.msg);
                        return;
                    }
                    if (rewardType === '1') {
                        activityIdObj.gold.push(activityId);
                    }
                    if (rewardType === '2') {
                        activityIdObj.coupon += activityId + ',';
                    }
                }
                
                try {
                    let promiseAll = []
                    if (activityIdObj.coupon) {
                        let promiseFace = await checkCoupon({}, activityIdObj.coupon);
                        promiseAll.push(promiseFace);
                    }
                    if (activityIdObj.gold.length > 0) {
                        for (let i = 0, len = activityIdObj.gold.length; i < len; i++ ) {
                            let promiseFace = await postActivityDetails({
                                type: 'query',
                                activityIndex: activityIdObj.gold[i]?.trim()
                            });
                            promiseAll.push(promiseFace);
                        }
                    }
                    let resArr = await Promise.all(promiseAll);
                    if (activityIdObj.coupon) {
                        let res0 = resArr[0];
                        const { status_code, status_msg } = res0;
                        if (status_code === 0) {
                            if (res0?.data?.length > 0) {
                                for (let i = 0, len = res0.data.length; i < len; i++) {
                                    const { couponStartDate, couponEndDate, couponId } = res0.data[i];
                                    if ( couponStartDate && couponEndDate ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        if (time < couponStartDate || time > couponEndDate) {
                                            message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let startTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (startTime < couponStartDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                            let endTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (endTime >= couponEndDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                        return;
                                    }
                                }
                            } else {
                                message.error('请检查优惠券ID是否正确');
                                return;
                            }
                        } else {
                            message.error(status_msg || '网络请求错误，请稍后再试');
                            return;
                        }
                    }
                    if (activityIdObj.gold.length > 0) {
                        let start = activityIdObj.coupon ? 1 : 0;
                        for (let i = start, len = resArr.length; i < len; i++ ) {
                            const { code } = resArr[i];
                            if (code === '0000') {
                                if (resArr[i]?.data) {
                                    let { startTime, endTime, indexStr } = resArr[i]?.data;
                                    if ( startTime && endTime ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        startTime = startTime.replace(/[^\d]/g, '');
                                        endTime = endTime.replace(/[^\d]/g, '');
                                        if (time < startTime || time > endTime) {
                                            message.error(`当前体验金活动ID-${indexStr}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let formStartTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (formStartTime < startTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                             let formEndTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (formEndTime >= endTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回体验金活动ID-${indexStr}生效时间`);
                                        return;
                                    }
                                } else {
                                    message.error('请检查体验金活动ID是否正确');
                                    return;
                                }
                            } else {
                                message.error(resArr[i]?.message || '网络请求错误，请稍后再试');
                                return;
                            }
                        }
                    }
                }catch(err) {
                    message.error(err.message);
                    return;
                }
            } else if (floorType === 'operationCard') {
                if (!formData.name) {
                    message.error('请填写运营大卡名称')
                    return;
                }
                if (!formData.page) {
                    message.error('请上传运营位图片-白天')
                    return;
                }
                if (!formData.darkPage) {
                    message.error('请上传运营位图片-黑夜')
                    return;
                }
                if (!formData.content) {
                    message.error('请填写按钮文描')
                    return;
                }
                if (!formData.url) {
                    message.error('请填写跳转链接')
                    return;
                }
            } else if (floorType === 'threesteps') {
                if (!formData.name) {
                    message.error('请填写三步走策略名称')
                    return;
                }
                if (!formData.page) {
                    message.error('请上传小图标')
                    return;
                }
                if (!formData.title) {
                    message.error('请填写模块标题')
                    return;
                }
                
                let activityIdObj: any = {
                    coupon: '',
                    gold: []
                }
                for (let i = 0; i < 3; i++) {
                    let obj = formData?.steps[i] ?? {};
                    const { type, activityId, page, status, url, button, buttonUrl, rightTime } = obj;
                    if (!type) {
                        message.error('请选择权益类型')
                        return;
                    }
                    if (['1','2', '3'].includes(type) && !activityId) {
                        message.error(`请填写${type === '1' ? '体验金活动' : type === '2' ? '优惠券' : '活动'}ID`)
                        return;
                    }
                    if (!page) {
                        message.error(`请填写步骤${i+1}图片`)
                        return;
                    }
                    if (type === '3' && !rightTime) {
                        message.error(`请填写金牛会员权益时间`)
                        return;
                    }
                    if (!status) {
                        message.error(`请填写步骤${i+1}状态文描`)
                        return;
                    }
                    if (!url) {
                        message.error('请填写权益查看跳转链接')
                        return;
                    }
                    let result = checkUrl(url, `步骤${i+1}`);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }

                    if (!button) {
                        message.error(`请填写步骤${i+1}按钮文描`)
                        return;
                    }
                    if (i === 2 ) {
                        if (!buttonUrl) {
                            message.error(`请填写跳转链接`)
                            return;
                        } else {
                            let result = checkUrl(buttonUrl, `步骤${i+1}`);
                            if (result.isError) {
                                message.error(result.msg);
                                return;
                            }
                        }
                        
                    } 
                    
                    if (type === '1') {
                        activityIdObj.gold.push(activityId);
                    }
                    if (type === '2') {
                        activityIdObj.coupon += activityId + ',';
                    }
                }
                if (!formData.finishButton) {
                    message.error('请填写完成按钮文描')
                    return;
                }
                try {
                    let promiseAll = []
                    if (activityIdObj.coupon) {
                        let promiseFace = await checkCoupon({}, activityIdObj.coupon);
                        promiseAll.push(promiseFace);
                    }
                    if (activityIdObj.gold.length > 0) {
                        for (let i = 0, len = activityIdObj.gold.length; i < len; i++ ) {
                            let promiseFace = await postActivityDetails({
                                type: 'query',
                                activityIndex: activityIdObj.gold[i]?.trim()
                            });
                            promiseAll.push(promiseFace);
                        }
                    }
                    let resArr = await Promise.all(promiseAll);
                    if (activityIdObj.coupon) {
                        let res0 = resArr[0];
                        const { status_code, status_msg } = res0;
                        if (status_code === 0) {
                            if (res0?.data?.length > 0) {
                                for (let i = 0, len = res0.data.length; i < len; i++) {
                                    const { couponStartDate, couponEndDate, couponId } = res0.data[i];
                                    if ( couponStartDate && couponEndDate ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        if (time < couponStartDate || time > couponEndDate) {
                                            message.error(`当前优惠券ID-${couponId}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let startTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (startTime < couponStartDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                            let endTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (endTime >= couponEndDate) {
                                                message.error('模块的时间范围必须在优惠券的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回优惠券ID-${couponId}生效时间`);
                                        return;
                                    }
                                }
                            } else {
                                message.error('请检查优惠券ID是否正确');
                                return;
                            }
                        } else {
                            message.error(status_msg || '网络请求错误，请稍后再试');
                            return;
                        }
                    }
                    if (activityIdObj.gold.length > 0) {
                        let start = activityIdObj.coupon ? 1 : 0;
                        for (let i = start, len = resArr.length; i < len; i++ ) {
                            const { code } = resArr[i];
                            if (code === '0000') {
                                if (resArr[i]?.data) {
                                    let { startTime, endTime, indexStr } = resArr[i]?.data;
                                    if ( startTime && endTime ) {
                                        let time = timeFormat2();
                                        time = time.replace(/[^\d]/g, '');
                                        startTime = startTime.replace(/[^\d]/g, '');
                                        endTime = endTime.replace(/[^\d]/g, '');
                                        if (time < startTime || time > endTime) {
                                            message.error(`当前体验金活动ID-${indexStr}未处于生效状态`);
                                            return;
                                        }
                                        if (formData.startTime) {
                                            let formStartTime = formData.startTime.replace(/[^\d]/g, '');
                                            if (formStartTime < startTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                        if (formData.endTime) {
                                             let formEndTime = formData.endTime.replace(/[^\d]/g, '');
                                            if (formEndTime >= endTime) {
                                                message.error('模块的时间范围必须在体验金活动的时间范围之内');
                                                return;
                                            }
                                        }
                                    } else {
                                        message.error(`接口未返回体验金活动ID-${indexStr}生效时间`);
                                        return;
                                    }
                                } else {
                                    message.error('请检查体验金活动ID是否正确');
                                    return;
                                }
                            } else {
                                message.error(resArr[i]?.message || '网络请求错误，请稍后再试');
                                return;
                            }
                        }
                    }
                }catch(err) {
                    message.error(err.message);
                    return;
                }
            } else if (floorType === 'productCard') {
                const { type, name, fundCode, fundName, fundManagerName, fundManagerTag, reason, button, buyCount, buyEndTime, buyStartTime,
                    fundManagerPage, profitSection, recommendType, fundTag, show, title, secTitle, targetData } = formData;
                if (!type) {
                    message.error('请选择产品大卡类型')
                    return;
                }
                if (!name) {
                    message.error('请填写卡片名称')
                    return;
                }
                if (['1', '2', '3', '5'].includes(type)) {
                    if (!fundCode) {
                        message.error(`请填写${ type==='3' ? '小目标' : '基金'}代码`)
                        return;
                    }
                    if (!fundName) {
                        message.error(`请填写${ type==='3' ? '小目标' : '基金'}名称`)
                        return;
                    }
                }
    
                if (['1'].includes(type)) {
                    if (!fundManagerName) {
                        message.error('请选择基金经理')
                        return;
                    }
                    if (!fundManagerTag) {
                        message.error('请填写基金经理标签')
                        return;
                    }
                    if (!show || show.length === 0) {
                        message.error('请选择展示指标维度')
                        return;
                    }
                    if (!fundManagerPage) {
                        message.error('请选择基金经理图片')
                        return;
                    }
                }
                
                if (['2', '3', '5'].includes(type)) {
                    if (!title) {
                        message.error('请填写主标题文案')
                        return;
                    }
                }
                if (type === '5') {
                    if (!secTitle) {
                        message.error('请填写副标题文案')
                        return;
                    }
                }
                if (['1', '2'].includes(type)) {
                    if (!fundTag) {
                        message.error('请填写基金标签')
                        return;
                    }
                }
                if (['2', '5'].includes(type)) {
                    if (!profitSection) {
                        message.error('请选择收益时间区间')
                        return;
                    }
                }
                
                if (type === '3') {
                    for (let i = 0,len = targetData?.length; i < len; i++) {
                        let obj = targetData[i];
                        if (obj?.name) {
                        } else {
                            message.error(`请填写小目标历史业绩第${i+1}项名称`)
                            return;
                        }
                        if (obj?.profit) {
                        } else {
                            message.error(`请填写小目标历史业绩第${i+1}项止盈时年化收益`)
                            return;
                        }
                    }
                }
                if (['1'].includes(type)) {
                    if (!buyStartTime) {
                        message.error('请填写认购开始时间')
                        return;
                    }
                    if (!buyEndTime) {
                        message.error('请填写认购结束时间')
                        return;
                    }
                    
                }
                if (['1', '3'].includes(type)) {
                    if (!buyCount) {
                        message.error(`请填写产品${type === '1' ? '认购' : type === '2' ? '关注' : '购买'}人数`)
                        return;
                    }
                }
                if (type === '4') {
                    if (!recommendType) {
                        message.error('请选择是否由系统配置')
                        return;
                    }
                }
                if (['1', '5'].includes(type)) {
                    if (!reason) {
                        message.error('请填写推荐理由')
                        return;
                    }
                }
                if (!button) {
                    message.error('请填写按钮文案')
                    return;
                }
            } else if (floorType === 'multidimension') {
                if (!formData.tabType) {
                    message.error('请选择tab类型')
                    return;
                }
                if (!formData.title) {
                    message.error('请填写tab标题')
                    return;
                }
                if (!formData.secTitle) {
                    message.error('请填写tab副标题')
                    return;
                }
                if (!formData.recommendType) {
                    message.error('请选择是否由系统推荐')
                    return;
                }
                if (['mainTrack', 'goodFund', 'selfDefine'].includes(formData.tabType) && formData.recommendType === '2') {
                    for (let i = 0; i < 3; i++) {
                        let obj = formData?.fundConfigList[i] ?? {};
                        const { trackName, code, name, title, secTitle, profitSection, profitSectionTwo, url, rankTag, retracementTag } = obj;
                        if (formData.tabType === 'mainTrack' && !trackName) {
                            message.error(`请填写板块${i+1}名称`)
                            return;
                        }
                        if (!code) {
                            message.error(`请填写产品${i+1}代码`)
                            return;
                        }
                        if (!name) {
                            message.error(`请填写产品${i+1}名称`)
                            return;
                        }
                        if (!title) {
                            message.error(`请填写产品${i+1}主标题`)
                            return;
                        }
                        if (!secTitle) {
                            message.error(`请填写产品${i+1}副标题`)
                            return;
                        }
                        if (formData.tabType === 'mainTrack') {
                            if (!profitSection) {
                                message.error('请选择收益时间区间1')
                                return;
                            }
                            if (!profitSectionTwo) {
                                message.error('请选择收益时间区间2')
                                return;
                            }
                        } else if (!profitSection) {
                            message.error('请选择收益时间区间')
                            return;
                        }
                        if (['goodFund', 'selfDefine'].includes(formData.tabType)) {
                            if (!retracementTag) {
                                message.error('请选择是否展示近1年最大回撤')
                                return;
                            }
                        }
                        if (!rankTag) {
                            message.error('请选择是否展示近1年收益排名')
                            return;
                        }
                        if (!url) {
                            message.error(`请填写产品${i+1}跳转链接`)
                            return;
                        }
                        let result = checkUrl(url, `产品${i+1}`);
                        if (result.isError) {
                            message.error(result.msg);
                            return;
                        }
                    }
                }
            } else if (floorType === 'featuredList') {
                if (!formData.title) {
                    message.error('请填写tab标题')
                    return;
                }
                if (!formData.secTitle) {
                    message.error('请填写tab副标题')
                    return;
                }
                for (let i = 0; i < 3; i++) {
                    let obj = formData?.lists[i] ?? {};
                    const { mainTitle, secTitle, page, url, darkPage } = obj;
                    if (!mainTitle) {
                        message.error(`请填写榜单${i+1}主标题`)
                        return;
                    }
                    if (!secTitle) {
                        message.error(`请填写榜单${i+1}副标题`)
                        return;
                    }
                    if (!page) {
                        message.error(`请填写榜单${i+1}图片-白天`)
                        return;
                    }
                    if (!darkPage) {
                        message.error(`请填写榜单${i+1}图片-黑夜`)
                        return;
                    }
                    if (!url) {
                        message.error(`请填写榜单${i+1}跳转链接`)
                        return;
                    }
                    let result = checkUrl(url, `榜单${i+1}`);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }
                }
            } else if (floorType === 'mainOperate') {
                if (!formData.name) {
                    message.error('请填写主运营位名称')
                    return;
                }
                if (!formData.page) {
                    message.error('请上传主运营位图片')
                    return;
                }
            } else if (floorType === 'secOperate') {
                if (!formData.name) {
                    message.error('请填写次运营位名称')
                    return;
                }
                if (!formData.page1) {
                    message.error('请上传次运营位1图片')
                    return;
                }
                if (!formData.page2) {
                    message.error('请上传次运营位2图片')
                    return;
                }
            } else if (floorType === 'marketSituation') {
                if (!formData.name) {
                    message.error('请填写策略名称')
                    return;
                }
                if (!formData.desc) {
                    message.error('请填写市场解读')
                    return;
                }
            } else if (floorType === 'capitalTrend') {
                if (!formData.manager || formData.manager.length !== 3) {
                    message.error(`基金经理数据错误，请重新添加`);
                    return;
                }
                for (let j = 0; j < formData.manager.length; j++) {
                    if (!formData.manager[j].name) {
                        message.error(`请填写基金经理${j+1}姓名`)
                        return;
                    }
                    if (!formData.manager[j].avatar) {
                        message.error(`请上传基金经理${j+1}头像`)
                        return;
                    }
                    if (!formData.manager[j].opinion) {
                        message.error(`请填写基金经理${j+1}观点`)
                        return;
                    }
                }   
            } else if (floorType === 'FindChance') {
                if (!formData.topicName) {
                    message.error(`请填写tab名称`)
                    return;
                }
                if (!formData.topicTag) {
                    message.error(`请填写标签`)
                    return;
                }
                if (!formData.topicTitle) {
                    message.error(`请填写主标题`)
                    return;
                }
                if (formData.nodeList.length === 0) {
                    message.error(`至少添加一个节点`)
                    return;
                }
                for (let j = 0; j < formData.nodeList.length; j++) {
                    if (!formData.nodeList[j].timeNode) {
                        message.error(`请填写节点${j+1}时间点`)
                        return;
                    }
                    if (!formData.nodeList[j].nodeTitle) {
                        message.error(`请填写节点${j+1}标题`)
                        return;
                    }
                    if (!formData.nodeList[j].nodeContent) {
                        message.error(`请填写节点${j+1}内容`)
                        return;
                    }
                }
                if (!formData.fundDesc) {
                    message.error(`请填写推荐基金文描`)
                    return;
                }
                if (!formData.fundObj) {
                    message.error(`请填写基金代码`)
                    return;
                }
                if (!formData.fundObj.code) {
                    message.error(`请填写基金代码`)
                    return;
                } else if (formData.fundObj.code.indexOf(' ') > -1) {
                    message.error(`请注意基金代码有空格`)
                    return;
                }
                if (!formData.fundObj.name) {
                    message.error(`请重新填写基金代码来获取基金名称`)
                    return;
                }
                if (!formData.fundTags) {
                    message.error(`请填写基金亮点标签`)
                    return;
                }
                if (!formData.fundBuyDesc) {
                    message.error(`请填写购买按钮文描`)
                    return;
                }
            } else if (floorType === 'FindInsurance') {
                if (!formData.tabName) {
                    message.error(`请填写tab名称`)
                    return;
                }
                if (!formData.tabImage) {
                    message.error(`请上传tab配图`)
                    return;
                }
                if (!formData.darkTabImage) {
                    message.error(`请上传tab黑夜配图`)
                    return;
                }
                if (!formData.productImage) {
                    message.error(`请上传产品头图`)
                    return;
                }
                if (!formData.productMainTitle) {
                    message.error(`请填写产品主标题`)
                    return;
                }
                if (!formData.productSecTitle) {
                    message.error(`请填写产品副标题`)
                    return;
                }
                if (!formData.buyButtonDesc) {
                    message.error(`请填写购买按钮文描`)
                    return;
                }
                if (!formData.buyActionUrl) {
                    message.error(`请填写产品跳转链接`)
                    return;
                }
                let result = checkUrl(formData.buyActionUrl);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (floorType === 'RobustFinancial') {
                for (let j = 0; j < formData.productList.length; j++) {
                    let item = formData.productList[j];
                    if (!item.fundObj) {
                        message.error(`请填写产品${j + 1}产品代码`)
                        return;
                    }
                    if (!item.fundObj.code) {
                        message.error(`请填写产品${j + 1}产品代码`)
                        return;
                    } else if (item.fundObj.code.indexOf(' ') > -1) {
                        message.error(`请注意产品${j + 1}产品代码有空格`)
                        return;
                    }
                    if (!item.fundObj.name) {
                        message.error(`请重新填写产品${j + 1}产品代码来获取产品名称`)
                        return;
                    }
                    if (!item.title) {
                        message.error(`请填写产品${j + 1}主标题`)
                        return;
                    }
                    if (!item.secTitle) {
                        message.error(`请填写产品${j + 1}副标题`)
                        return;
                    }
                    if (!item.timeInterval) {
                        message.error(`请填写产品${j + 1}时间区间`)
                        return;
                    }
                    let result = checkUrl(formData.actionUrl);
                    if (result.isError) {
                        message.error(result.msg);
                        return;
                    }
                }
            }
            if (formData.startTime && formData.endTime) {
                let startTime = formData.startTime.replace(/[^\d]/g, '');
                let endTime = formData.endTime.replace(/[^\d]/g, '');
                if (startTime >= endTime) {
                    message.error('开始时间应早于结束时间');
                    return;
                }
            }
            if (['searchhotword', 'mixbanner', 'notice', 'gongge', 'gonggeall', 'operationposition', 'coupon'].includes(floorType)) {
                let result = checkUrl(formData.jumpAction);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (['operationCard', 'threesteps', 'multidimension', 'featuredList', 'mainOperate', 'capitalTrend'].includes(floorType)) {
                let result = checkUrl(formData.url);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (['productCard'].includes(floorType)) {
                let result = checkUrl(formData.watchUrl);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (['secOperate'].includes(floorType)) {
                let result1 = checkUrl(formData.url1);
                if (result1.isError) {
                    message.error(result1.msg);
                    return;
                }

                let result2 = checkUrl(formData.url2);
                if (result2.isError) {
                    message.error(result2.msg);
                    return;
                }
            } else if (['FindChance'].includes(floorType)) {
                let result = checkUrl(formData.moreActionUrl);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
            } else if (['marketSituation'].includes(floorType)) {
                let result = checkUrl(formData.url);
                if (result.isError) {
                    message.error(result.msg);
                    return;
                }
                
                let result1 = checkUrl(formData.fundPositionUpUrl, '基金仓位上');
                console.log(result1);
                if (result1.isError) {
                    message.error(result1.msg);
                    return;
                }

                let result2 = checkUrl(formData.investorEmotionUrl, '投资者情绪');
                if (result2.isError) {
                    message.error(result2.msg);
                    return;
                }

                let result3 = checkUrl(formData.indexValuationUpUrl, '指数估值上');
                if (result3.isError) {
                    message.error(result3.msg);
                    return;
                }

                let result4 = checkUrl(formData.fundPositionDownUrl, '基金仓位下');
                if (result4.isError) {
                    message.error(result4.msg);
                    return;
                }

                let result5 = checkUrl(formData.indexValuationDownUrl, '指数估值下');
                if (result5.isError) {
                    message.error(result5.msg);
                    return;
                }
            }

            let realData: any = {};
            const {configData, relationData} = designatedData;
            if (relationData?.userType === '1') {
                const {blackCustId, whiteCustId, ...other} = relationData;
                realData = {...other}
            } else {
                const {blackUserId, whiteUserId, blackCustId, whiteCustId, ...other} = relationData;
                realData = {
                    ...other,
                    blackUserId: blackCustId,
                    whiteUserId: whiteCustId
                }
            }
            if (relationData?.targetType === 'kyc') {
                realData.olasId = '';
                realData.olasType = '';
            } else if (relationData?.targetType === 'olas') {
                realData.kycLogic = '';
                realData.kycs = [];
            }

            let _data: any = {
                formData,
                configData,
                relationData,
            }
            if (floorType === 'usercomment') {
                _data.commentList = commentList;
            }
            if (configData.platform?.length === 0) {
                message.error('请选择使用平台')
                return;
            }
            if (configData.utype?.length === 0) {
                message.error('请选择用户类型')
                return;
            }
            console.log('saved', _data);
            if (relationData?.id) {
                handleUpdate(_data, position)
                setEdit(!isEdit)   
            } else {
                let time = timeFormat2();
                let obj = {
                    ...configData,
                    ...realData,
                    updateTime: time
                };
                updateUserType(obj).then((res: any) => {
                    if (res.code !== '0000') return message.error(res.message);
                    let id = res.data;
                    _data.relationData.id = id;
                    _data.relationData.updateTime = time;
                    handleUpdate(_data, position);
                    setEdit(!isEdit)
                })
                .catch((e: Error) => {
                    message.error(e.message || '系统错误');
                });
            }
            
        } else {
            setEdit(!isEdit)
        }
    }
    const GongSelect = (props: iSelectProps) => {
        return (
            <div>
                <Select style={{width: 200}} value={props.value} disabled={props.readonly} onChange={(value: string) => props.onChange('gonggeType', value)}>
                    { gonggeClassify?.length > 0 && gonggeClassify.map((item: any, index: number) => {
                        return (
                            <Option value={item.name}>{item.name}</Option>
                        )
                    }) }
                </Select>
            </div>
        )
    }
    const addCommentItem = () => {
        let arr: any = [...commentList];
        let obj = {
            userName: '',
            userTag: '',
            startPoint: '',
            content: ''
        }
        arr.push(obj);
        setCommentList(arr);
    }
    const deleteCommentItem = (index: number) => {
        let arr: any = [...commentList];
        arr.splice(index, 1)
        setCommentList(arr);
    }
    const handleComment = (e: any, num: number, type: string) => {
        let arr: any = [...commentList];
        if (type === 'startPoint') {
            arr[num][type] = String(e);
        } else {
            arr[num][type] = e.target.value;
        }
        
        setCommentList(arr);
    }
    const returnWidgets = () => {
        let widgets = {};
        switch(floorType) {
            case 'mixbanner': 
                widgets = {
                    uploadImg: ImgUploadImg,
                    uploadImg2: MixBannerImgUploadImg,
                    uploadImg3: MixBannerImgUploadImg3,
                    uploadImg4: MixBannerImgUploadImg4,
                    uploadImg5: MixBannerImgUploadImg5,
                    uploadImg6: MixBannerImgUploadImg6
                }
                break;
            case 'gongge': 
                widgets = {
                    uploadImg: PageUploadImg,
                    uploadImg2: DarkPageUploadImg,
                }
                break;
            case 'gonggeall': 
                widgets = {
                    uploadImg: PageUploadImg,
                    uploadImg2: DarkPageUploadImg,
                    gongSelect: GongSelect
                }
                break;
            case 'operationblock': 
                widgets = {
                    blockList: BlockList
                }
                break;
            case 'coupon': 
                widgets = {
                    uploadImg: CouponUploadImg
                }
                break;
            case 'biggiftbag': 
                widgets = {
                    uploadImg: BagUploadImg,
                    giftList: BagGiftList
                }
                break;
            case 'operationCard': 
                widgets = {
                    uploadImg: CardUploadImg,
                    uploadImg2: CardUploadImg2
                }
                break;
            case 'threesteps': 
                widgets = {
                    uploadImg: StepUploadImg,
                    stepList: StepList
                }
                break;
            case 'multidimension': 
                widgets = {
                    fundConfigList: FundConfigList
                }
                break;
            case 'featuredList': 
                widgets = {
                    featuredList: FeaturedList
                }
                break;
            case 'mainOperate':
                widgets = {
                    uploadImg: mainOperateImg
                }
                break;
            case 'secOperate':
                widgets = {
                    uploadImg1: secOperateImg1,
                    uploadImg2: secOperateImg2
                }
                break;
            case 'capitalTrend':
                widgets = {
                    uploadImg: CapitalTrendImg
                }
                break;
            case 'FindChance':
                widgets = {
                    fundObj: FundNameConfig
                }
                break;
            case 'FindInsurance':
                widgets = {
                    uploadImg1: InsuranceImg1,
                    uploadImg2: InsuranceImg2,
                    uploadImg3: InsuranceImg3
                }
                break;
            case 'RobustFinancial':
                widgets = {
                    fundObj: RobustFinancialFundName
                };
                break;
        }
        return widgets
    }
    const onFormChange = (val: any) => {
        if (['multidimension'].includes(floorType)) {
            if (['hotSale', 'planTrade'].includes(val.tabType)) {
                val.recommendType = '1'
            }
            else if (['selfDefine'].includes(val.tabType)) {
                val.recommendType = '2'
            }
        }
        setFormState(val)
    }
    const FundConfigList = useCallback((props: any) => {
        return (
            <BaseFundConfigList {...props} type={formData.tabType} handleFocus={setCodeEdit}/>
        )
    }, [formData.tabType])
    const FundNameConfig = useCallback((props: any) => {
        return (
            <FundCodeNameConfig  {...props} type={formData.tabType} handleFocus={setCodeEdit} />
        )
    }, [formData.tabType]);
	const RobustFinancialFundName = useCallback((props: any) => {
        return (
            <RobustFinancialFundNameConfig  {...props} type={formData.tabType} handleFocus={setCodeEdit} />
        )
	}, [formData.tabType]);
    const limitDecimals = (value: any) => {
        value = String(value).replace(/[^\d]/g, '');
        if (value === '') {
            return ''
        }
        value = Number(value);
        return value.toString();
    }
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} disabled={!codeEdit} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {handleDelete(position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <h1 className="g-fs20 f-bold">配置详情</h1>
                { floorType === 'productCard' ? (
                    <Block blockData={formData} handleUpdate={setFormState} handleFocus={setCodeEdit} isEdit={isEdit}/>
                ) : formConfig ? (
                    <FormRender
                        propsSchema={formConfig.schema}
                        displayType='row'
                        formData={formData}
                        onValidate={onValidate}
                        onChange={onFormChange}
                        readOnly={!isEdit}
                        labelWidth={135}
                        widgets={returnWidgets()}
                    />
                ) : null}
                { floorType === 'usercomment' && (
                    <div className="g-mb10">
                        <ul className={classnames(styles['m-user-content-ul'], 'g-mb20')}>
                            { commentList?.map((item: any, index: number) => {
                                return (
                                    <li style={{width: 1150, marginBottom: 20}} key={index}>
                                        <div>
                                            <div className="u-flex">
                                                <div className="u-l-middle">
                                                    <span className={classnames(styles['m-card-required'])} style={{width: 110}}>用户昵称</span>
                                                    <Input value={item.userName} disabled={!isEdit} className="g-ml20" style={{width: 200}} onChange={(e) => handleComment(e, index, 'userName')}></Input>
                                                </div>
                                                <div className="u-l-middle g-ml30">
                                                    <span className={classnames(styles['m-card-required'])} style={{width: 110}}>用户标签</span>
                                                    <Input value={item.userTag} disabled={!isEdit} className="g-ml20" style={{width: 200}} onChange={(e) => handleComment(e, index, 'userTag')}></Input>
                                                </div>
                                                <div className="u-l-middle g-ml30">
                                                    <span className={classnames(styles['m-card-required'])} style={{width: 110}}>初始点赞数</span>
                                                    <InputNumber value={item.startPoint} formatter={limitDecimals} parser={limitDecimals} disabled={!isEdit} className="g-ml20" style={{width: 200}} onChange={(e) => handleComment(e, index, 'startPoint')}></InputNumber>
                                                </div>
                                            </div>
                                            <div className="u-l-middle g-mt20">
                                                <span className={classnames(styles['m-card-required'])} style={{width: 110}}>精评内容</span>
                                                <Input value={item.content} disabled={!isEdit} className="g-ml20" onChange={(e) => handleComment(e, index, 'content')}></Input>
                                            </div>
                                        </div>
                                        <Button type="danger" ghost disabled={!isEdit} onClick={() => deleteCommentItem(index)}>删除</Button>
                                    </li>
                                )
                            }) }
                            
                        </ul>
                        <Button disabled={!isEdit} onClick={addCommentItem}>添加</Button>
                    </div>
                ) }
                <DesignatedUser
                    data={data}
                    position={position}
                    kycTag={kycTag}
                    olasTag={olasTag}
                    isEdit={isEdit}
                    setDesignatedData={setDesignatedData}
                />
            </div>

}