import React, { useState, useEffect } from 'react';
import { Radio, Select, Input, Button, Popconfirm, message, Upload, Row, Col } from 'antd';
import api from 'api';
import styles from '../../pageConfig/index.less';
import classNames from 'classnames';

const { uploadUserList, downloadUserList } = api;

interface iProps {
    name: string,
    position: number,
    relationData: any,
    userType: '1' | '3',
    fileType: 'black' | 'white',
    saveFile: (obj: any) => void,
    isEdit: boolean,
    ifHidden?: boolean,
    describe?: string,
    isExist?: boolean //是否存在与可下载
}
function getFileName(type: string) {
    switch (type) {
        case 'black':
            return '用户黑名单文件'
        case 'holdFundFile':
            return '持仓-自定义基金列表文件'
        case 'holdDetailFundFile':
            return '持仓详情-自定义基金列表文件'
        case 'myFundFile':
            return '我的-自定义基金列表文件'
        case 'selectFundFile':
            return '自选-自定义基金列表文件'
        case 'singleFundFile':
            return '个基-自定义基金列表文件'
        case 'white':
            return '用户白名单文件'
        default:
            return '未知文件'
    }
}

export default React.memo(function ({ relationData, name, userType, fileType, saveFile, isEdit, ifHidden, describe, isExist, position }: iProps) {
    const [uploadFileId, setUploadFileId] = useState({
        '1': {
            blackUserId: '',
            whiteUserId: ''
        },
        '3': {
            blackUserId: '',
            whiteUserId: ''
        }
    })
    useEffect(() => {
        setUploadFileId({
            '3': {
                blackUserId: relationData.blackCustId,
                whiteUserId: relationData.whiteCustId
            },
            '1': {
                blackUserId: relationData.blackUserId,
                whiteUserId: relationData.whiteUserId
            }
        })
    }, [relationData, userType])

    const addFile = (File: File, fileList: File[]) => {
        if (fileList.length > 1) {
          message.error('只能上传一个文件，请先删除原先文件');
          return false;
        }
        return true;
    };
    const download = () => {
        if (!isEdit) {
            return;
        }
        let randomKey = '';
        switch (fileType) {
            case 'black':
              randomKey = uploadFileId[userType]['blackUserId'];
              break;
            case 'white':
              randomKey = uploadFileId[userType]['whiteUserId'];
              break;
          }
        
        downloadUserList({}, `randomKey=${randomKey}&listType=${fileType}&userType=${userType}`, null, { responseType: 'blob' }).then((data: any) => {
            if (data.data && data.data.size === 0 && data.message !== 'OK' && data.message)
            return message.error('下载失败');
            if (data.code !== '0000' && data.message !== 'OK' && data.message)
            return message.error(data.message);
        }).catch((e: Error) => {
            message.error('下载失败');
        });
    }
    const uploadList = ({file}: any) => {
        let _data = new FormData();
        _data.append('multipartFile', file);
        _data.append('userType', userType);
        _data.append('listType', fileType);
        console.log(file, _data);
        uploadUserList(_data)
          .then((res: Record<string, string>) => {
            if (res.code !== '0000') return message.error(res.message);
            message.success('上传成功');
            let obj = {...uploadFileId};
            
            switch (fileType) {
              case 'black':
                obj[userType]['blackUserId'] = res.data;
                break;
              case 'white':
                obj[userType]['whiteUserId'] = res.data;
                break;
            }
            setUploadFileId(obj);
            saveFile(obj[userType]);
          })
          .catch((e: Error) => {
            message.error(e.message || '系统错误');
          });
    };
    return (
        <section>
            <div key='file' className={classNames(styles['m-tagModel-row'], ifHidden ? 'z-hide' : '')}>
                <Row style={{width: '100%'}}>
                    <Col span={3}><p className={styles['m-card-label']}>{name}:</p></Col>
                    <Col span={3} className={isExist ? '' : 'z-hide'}>
                        <a onClick={download} className={classNames(isEdit ? '' : styles['m-not-active'], 'f-unl')}>{getFileName(fileType)}</a>
                    </Col>
                    <Col span={2} offset={1}>
                        <Upload
                            accept=".xls,.xlsx"
                            beforeUpload={addFile}
                            customRequest={uploadList} 
                            showUploadList={false}
                        >
                            <Button disabled={!isEdit}>上传文件</Button>
                        </Upload>

                    </Col>
                    {/* <Col span={2}>
                        <Button disabled={!isEdit} onClick={downloadModel}>下载模板</Button>
                    </Col> */}
                    <Col span={7} offset={1}>
                        <p style={{ color: "red" }}>{describe}</p>
                    </Col>
                </Row>
            </div>
        </section>
    )
})

