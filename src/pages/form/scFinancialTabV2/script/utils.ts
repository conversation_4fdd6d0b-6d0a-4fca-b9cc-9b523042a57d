export const handleApi = (hash: string) => {
    let get =  '';
    let post = ''
    switch(hash) {
        case 'searchhotword':
            get = 'fetchSearchhotwordV2';
            post = 'postSearchhotwordV2';
            break;
        case 'mixbanner':
            get = 'fetchMixedbannerV2';
            post = 'postMixedbannerV2';
            break;
        case 'notice':
            get = 'fetchscNoticeV2';
            post = 'postscNoticeV2';
            break;
        case 'gongge':
            get = 'fetchscGonggeV2';
            post = 'postscGonggeV2';
            break;
        case 'gonggeall':
            get = 'fetchscGonggeAllV2';
            post = 'postscGonggeAllV2';
            break;
        case 'operationposition':
            get = 'fetchscOperationPositionV2';
            post = 'postscOperationPositionV2';
            break;
        case 'usercomment':
            get = 'fetchscUserCommentV2';
            post = 'postscUserCommentV2';
            break;
        case 'coupon':
            get = 'fetchscCouponV2';
            post = 'postscCouponV2';
            break;
        case 'operationblock':
            get = 'fetchscOperationblockV2';
            post = 'postscOperationblockV2';
            break;
        case 'biggiftbag':
            get = 'fetchscBiggiftbagV2';
            post = 'postscBiggiftbagV2';
            break;
        case 'operationCard':
            get = 'fetchscOperationcardV2';
            post = 'postscOperationcardV2';
            break;
        case 'threesteps':
            get = 'fetchscThreestepsV2';
            post = 'postscThreestepsV2';
            break;
        case 'productCard':
            get = 'fetchscProductcardV2';
            post = 'postscProductcardV2';
            break;
        case 'multidimension':
            get = 'fetchscMultidimensionV2';
            post = 'postscMultidimensionV2';
            break;
        case 'featuredList':
            get = 'fetchscFeaturedListV2';
            post = 'postscFeaturedListV2';
            break;
        case 'mainOperate':
            get = 'fetchscMainOperateV2';
            post = 'postscMainOperateV2';
            break;
        case 'secOperate':
            get = 'fetchscSecOperateV2';
            post = 'postscSecOperateV2';
            break;
        case 'marketSituation':
            get = 'fetchscMarketSituationV2';
            post = 'postscMarketSituationV2';
            break;
        case 'capitalTrend':
            get = 'fetchscCapitalTrendV2';
            post = 'postscCapitalTrendV2';
            break;
        case 'fundTrend':
            get = 'fetchscFundTrendV2';
            post = 'postscFundTrendV2';
            break;
        case 'FindChance':
            get = 'fetchscInvestmentOpportunityV2';
            post = 'postscInvestmentOpportunityV2';
            break;
        case 'FindInsurance':
            get = 'fetchscFindInsuranceV2';
            post = 'postscFindInsuranceV2';
            break;
        case 'RobustFinancial':
            get = 'fetchscRobustFinancialV2';
            post = 'postscRobustFinancialV2';
            break;
    }
    return {
        get,
        post
    };
}
export const modelName = (hash: string) => {
    const obj = {
        'searchhotword': {
            location: '搜索热词',
            name: 'searchWord',
            type: 'HotSearch'
        },
        'mixbanner': {
            location: '融合运营位',
            name: 'mixName',
            type: 'OperationPosition'
        },
        'notice': {
            location: '公告',
            name: 'noticeTitle',
            type: 'Notice'
        },
        'gongge': {
            location: '宫格',
            name: 'gonggeName',
            type: 'Icon'
        },
        'gonggeall': {
            location: '宫格-全部页面',
            name: 'gonggeName',
            type: 'AllIcon'
        },
        'operationposition': {
            location: '持仓运营位',
            name: 'textTitle',
            type: 'Holder'
        },
        'usercomment': {
            location: '用户精评',
            name: 'userCommentName',
            type: 'UserComment'
        },
        'coupon': {
            location: '营销利益点-优惠券',
            name: 'couponName',
            type: 'Packet'
        },
        'operationblock': {
            location: '营销利益点-运营豆腐块',
            name: 'name',
            type: 'Block'
        },
        'biggiftbag': {
            location: '营销利益点-开户/首购大礼包',
            name: 'name',
            type: 'customize-BigGift'
        },
        'operationCard': {
            location: '营销利益点-运营大卡',
            name: 'name',
            type: 'OperationCard'
        },
        'threesteps': {
            location: '营销利益点-新人三步走',
            name: 'name',
            type: 'customize-NewAccount'
        },
        'productCard': {
            location: '基金推荐-产品大卡',
            name: 'name',
            type: 'customize-ProductCard'
        },
        'multidimension': {
            location: '基金推荐-多维度选基',
            name: 'title',
            type: 'ChooseFund'
        },
        'featuredList': {
            location: '基金推荐-特色榜单',
            name: 'title',
            type: 'SpecialList'
        },
        'mainOperate': {
            location: '营销利益点-主运营位',
            name: 'name', 
            type: 'MainOperate'
        },
        'secOperate': {
            location: '营销利益点-次运营位',
            name: 'name',
            type: 'SecOperate'
        },
        'marketSituation': {
            location: '市场行情 - 市场情况',
            name: 'name',
            type: 'MarketSituation'
        },
        'capitalTrend': {
            location: '市场行情 - 资金动向',
            name: 'name',
            type: 'CapitalTrend'
        },
        'fundTrend': {
            location: '市场行情 - 基金动向',
            name: '',
            type: 'FundTrend'
        },
        'FindChance': {
            location: '市场行情 - 投资机会',
            name: 'topicName',
            type: 'FindChance'
        },
        'FindInsurance': {
            location: '保险板块',
            name: 'tabName',
            type: 'FindInsurance'
        },
        'RobustFinancial': {
            location: '基金推荐 - 稳健理财',
            name: '',
            type: 'RobustFinancial'
        }
    }
    return obj[hash]
}

export const fetchData = (item: any, hash: string) => {
    let formData: any = {};
    switch(hash) {
        case 'searchhotword':
            formData = {
                searchWord: item.searchWord,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime
            }
            break;
        case 'mixbanner':
            formData = {
                mixName: item.mixName,
                mixType: item.mixType ?? '1',
                navColorLeft: item.navColorLeft,
                navColorRight: item.navColorRight,
                imageUrl: item.imageUrl,
                imageDarkUrl: item.imageDarkUrl,
                imageSvgaUrl: item.imageSvgaUrl,
                imageSvgaDarkUrl: item.imageSvgaDarkUrl,
                imageSmallSvgaUrl: item.imageSmallSvgaUrl,
                imageSmallSvgaDarkUrl: item.imageSmallSvgaDarkUrl,
                masterTitle: item.masterTitle,
                masterColor: item.masterColor,
                subTitle: item.subTitle,
                subColor: item.subColor,
                buttonText: item.buttonText,
                buttonColor: item.buttonColor,
                buttonTextColor: item.buttonTextColor,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
                unreceivedBtnName: item.unreceivedBtnName,
                receivedUnOpenAccBtnName: item.receivedUnOpenAccBtnName,
                receivedUnBuyFundBtnName: item.receivedUnBuyFundBtnName,
                receivedUnBuyFundBtnJumpAction: item.receivedUnBuyFundBtnJumpAction,
                equities1: item.equities1,
                equities2: item.equities2,
                equities3: item.equities3,
            }
            break;
        case 'notice':
            formData = {
                noticeTitle: item.noticeTitle,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime
            }
            break;
        case 'gongge':
            formData = {
                gonggeName: item.gonggeName,
                page: item.page,
                darkPage: item.darkPage,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
                angleMark: item.angleMark
            }
            break;
        case 'gonggeall':
            formData = {
                gonggeType: item.gonggeType,
                gonggeName: item.gonggeName,
                page: item.page,
                darkPage: item.darkPage, 
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
                angleMark: item.angleMark
            }
            break;
        case 'operationposition':
            formData = {
                tag: item.tag,
                textTitle: item.textTitle,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime
            }
            break;
        case 'usercomment':
            formData = {
                userCommentName: item.userCommentName,
            }
            break;
        case 'coupon':
            formData = {
                couponName: item.couponName,
                imageUrl: item.imageUrl,
                couponText: item.couponText,
                couponId: item.couponId,
                achieveText: item.achieveText,
                seeText: item.seeText,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'operationblock':
            formData = {
                name: item.name,
                blocks: item.blocks ?? [],
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'biggiftbag':
            formData = {
                name: item.name,
                page: item.page,
                title: item.title,
                noneReceiveButton: item.noneReceiveButton,
                noneAccountButton: item.noneAccountButton,
                noneBuyButton: item.noneBuyButton,
                noneBuyUrl: item.noneBuyUrl,
                version: item.version,
                gifts: item.gifts ?? [],
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'operationCard':
            formData = {
                name: item.name,
                page: item.page,
                darkPage: item.darkPage,
                content: item.content,
                url: item.url,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime
            }
            break;
        case 'threesteps':
            formData = {
                name: item.name,
                page: item.page,
                title: item.title,
                steps: item.steps ?? [],
                finishButton: item.finishButton,
                url: item.url,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'productCard':
            formData = {
                type: item.type,
                name: item.name,
                fundCode: item.fundCode,
                fundName: item.fundName,
                fundManagerName: item.fundManagerName,
                managerInfo: item.managerInfo ?? [],
                fundManagerTag: item.fundManagerTag,
                reason: item.reason,
                show: item.show ?? [],
                title: item.title,
                secTitle: item.secTitle,
                fundManagerPage: item.fundManagerPage,
                fundTag: item.fundTag,
                profitSection: item.profitSection,
                buyStartTime: item.buyStartTime,
                buyEndTime: item.buyEndTime,
                targetData: item.targetData ?? [],
                buyCount: item.buyCount,
                recommendType: item.recommendType,
                button: item.button,
                watchUrl: item.watchUrl,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'multidimension':
            formData = {
                tabType: item.tabType,
                title: item.title,
                secTitle: item.secTitle,
                url: item.url,
                version: item.version,
                recommendType: item.recommendType,
                fundConfigList: item.fundConfigList ?? [],
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'featuredList':
            formData = {
                title: item.title,
                secTitle: item.secTitle,
                moreTitle: item.moreTitle,
                url: item.url,
                version: item.version,
                lists: item.lists ?? [],
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'mainOperate':
            formData = {
                name: item.name,
                page: item.page,
                url: item.url,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
            };
            break;
        case 'secOperate':
            formData = {
                name: item.name,
                page1: item.page1,
                url1: item.url1,
                version1: item.version1,
                page2: item.page2,
                url2: item.url2,
                version2: item.version2,
                startTime: item.startTime,
                endTime: item.endTime,
            };
            break;
        case 'marketSituation':
            formData = {
                name: item.name,
                desc: item.desc,
                moreText: item.moreText,
				fundPositionUpUrl: item.fundPositionUpUrl,
                fundPositionUpVersion: item.fundPositionUpVersion,
                investorEmotionUrl: item.investorEmotionUrl,
                investorEmotionVersion: item.investorEmotionVersion,
                indexValuationUpUrl: item.indexValuationUpUrl,
                indexValuationUpVersion: item.indexValuationUpVersion,
                fundPositionDownUrl: item.fundPositionDownUrl,
                fundPositionDownVersion: item.fundPositionDownVersion,
                indexValuationDownUrl: item.indexValuationDownUrl,
                indexValuationDownVersion: item.indexValuationDownVersion,
                url: item.url,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
            };
            break;
        case 'capitalTrend':
            formData = {
                manager: item.manager,
                startTime: item.startTime,
                endTime: item.endTime,
            };
            break;
        case 'fundTrend':
            formData = {};
            break;
        case 'FindChance':
            formData = {
                topicName: item.topicName,
                topicTag: item.topicTag,
                topicTitle: item.topicTitle,
                nodeList: item.nodeList,
                moreDesc: item.moreDesc,
                moreActionUrl: item.moreActionUrl,
                moreVersion: item.moreVersion,
                fundDesc: item.fundDesc,
                fundObj: item.fundObj,
                fundTags: item.fundTags,
                fundBuyDesc: item.fundBuyDesc,
                startTime: item.startTime,
                endTime:item.endTime,
            };
            break;
        case 'FindInsurance':
            formData = {
                tabName: item.tabName,
                tabImage: item.tabImage,
                darkTabImage: item.darkTabImage,
                productImage: item.productImage,
                productMainTitle: item.productMainTitle,
                productSecTitle: item.productSecTitle,
                buyButtonDesc: item.buyButtonDesc,
                buyActionUrl: item.buyActionUrl,
                buyVersion: item.buyVersion,
                startTime: item.startTime,
                endTime: item.endTime,
            };
            break;
        case 'RobustFinancial':
            formData = {
                productList: item.productList || [{
                    fundObj: {
                        code: '',
                        name: ''
                    },
                    title: '',
                    secTitle: '',
                    timeInterval: 'year',
                    actionUrl: '',
                    version: ''
                }, {
                    fundObj: {
                        code: '',
                        name: ''
                    },
                    title: '',
                    secTitle: '',
                    timeInterval: 'year',
                    actionUrl: '',
                    version: ''
                }, {
                    fundObj: {
                        code: '',
                        name: ''
                    },
                    title: '',
                    secTitle: '',
                    timeInterval: 'year',
                    actionUrl: '',
                    version: ''
                }],
                startTime: item.startTime,
                endTime: item.endTime,
            };
            break;
    }          
    return formData;        
}
export const addData = (hash: string) => {
    let formData: any = {};
    switch(hash) {
        case 'searchhotword':
            formData = {
                ssearchWord: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'mixbanner':
            formData = {
                mixName: '',
                mixType:  '1',
                navColorLeft:  '',
                navColorRight: '',
                imageUrl:  '',
                imageDarkUrl: '',
                imageSvgaUrl:  '',
                imageSmallSvgaUrl: '',
                imageSmallSvgaDarkUrl: '',
                imageSvgaDarkUrl: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'notice':
            formData = {
                noticeTitle: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'gongge':
            formData = {
                gonggeName: '',
                page: '',
                darkPage: '',
                angleMark: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'gonggeall':
            formData = {
                gonggeType: '',
                gonggeName: '',
                page: '',
                darkPage: '',
                angleMark: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'operationposition':
            formData = {
                tag: '',
                textTitle: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'usercomment':
            formData = {
                userCommentName: '',
            }
            break;
        case 'coupon':
            formData = {
                couponName: '',
                imageUrl: '',
                couponText: '',
                couponId: '',
                achieveText: '',
                seeText: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: '',
            }
            break;
        case 'operationblock':
            formData = {
                name: '',
                blocks: [],
                startTime: '',
                endTime: '',
            }
            break;
        case 'biggiftbag':
            formData = {
                name: '',
                page: '',
                title: '',
                noneReceiveButton: '',
                noneAccountButton: '',
                noneBuyButton: '',
                noneBuyUrl: '',
                version: '',
                gifts: [],
                startTime: '',
                endTime: '',
            }
            break;
        case 'operationCard':
            formData = {
                name: '',
                page: '',
                darkPage: '',
                content: '',
                url: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'threesteps':
            formData = {
                name: '',
                page: '',
                title: '',
                steps: [],
                finishButton: '',
                url: '',
                version: '',
                startTime: '',
                endTime: '',
            }
            break;
        case 'productCard':
            formData = {
                type: '1',
                name: '',
                fundCode: '',
                fundName: '',
                fundManagerName: '',
                managerInfo: [],
                fundManagerTag: '',
                reason: '',
                show: [],
                fundManagerPage: '',
                fundTag: '',
                buyStartTime: '',
                buyEndTime: '',
                buyCount: '',
                button: '',
                watchUrl: '',
                version: '',
                startTime: '',
                endTime: '',
            }
            break;
        case 'multidimension':
            formData = {
                tabType: 'selfDefine',
                title: '',
                secTitle: '',
                url: '',
                version: '',
                recommendType: '1',
                fundConfigList: [],
                startTime: '',
                endTime: '',
            }
            break;
        case 'featuredList':
            formData = {
                title: '',
                secTitle: '',
                moreTitle: '',
                url: '',
                version: '',
                lists: [],
                startTime: '',
                endTime: '',
            }
            break;
        case 'mainOperate':
            formData = {
                name: '',
                page: '',
                url: '',
                version: '',
                startTime: '',
                endTime: '',
            };
            break;
        case 'secOperate':
            formData = {
                name: '',
                page1: '',
                url1: '',
                version1: '',
                page2: '',
                url2: '',
                version2: '',
                startTime: '',
                endTime: '',
            };
            break;
        case 'marketSituation':
            formData = {
                name: "",
                desc: "",
                moreText: "",
                url: '',
                version: '',
                startTime: '',
                endTime: '',
                fundPositionUpUrl: '',
                fundPositionUpVersion: '',
                investorEmotionUrl: '',
                investorEmotionVersion: '',
                indexValuationUpUrl: '',
                indexValuationUpVersion: '',
                fundPositionDownUrl: '',
                fundPositionDownVersion: '',
                indexValuationDownUrl: '',
                indexValuationDownVersion: '',
            };
            break;
        case 'capitalTrend':
            formData = {
                manager: [{
                    name: '',
                    avatar: '',
                    opinion: '',
                    url: '',
                    version: '',
                }, {
                    name: '',
                    avatar: '',
                    opinion: '',
                    url: '',
                    version: '',
                }, {
                    name: '',
                    avatar: '',
                    opinion: '',
                    url: '',
                    version: '',
                }],
                startTime: '',
                endTime: '',
            };
            break;
        case 'fundTrend':
            formData = {};
            break;
        case 'FindChance':
            formData = {
				topicName: '',
                topicTag: '',
                topicTitle: '',
                nodeList: [],
                moreDesc: '',
                moreActionUrl: '',
                moreVersion: '',
                fundDesc: '',
                fundObj: {
                    code: '',
                    name: ''
                },
                fundTags: '',
                fundBuyDesc: '',
                startTime: '',
                endTime: ''
            };
            break;
        case 'FindInsurance':
            formData = {
                tabName: '',
                tabImage: '',
                darkTabImage: '',
                productImage: '',
                productMainTitle: '',
                productSecTitle: '',
                buyButtonDesc: '',
                buyActionUrl: '',
                buyVersion: '',
                startTime: '',
                endTime: ''
            };
            break;
        case 'RobustFinancial':
            formData = {
                productList: [{
                    fundObj: {
                        code: '',
                        name: ''
                    },
                    title: '',
                    secTitle: '',
                    timeInterval: 'year',
                    actionUrl: '',
                    version: ''
                }, {
                    fundObj: {
                        code: '',
                        name: ''
                    },
                    title: '',
                    secTitle: '',
                    timeInterval: 'year',
                    actionUrl: '',
                    version: ''
                }, {
                    fundObj: {
                        code: '',
                        name: ''
                    },
                    title: '',
                    secTitle: '',
                    timeInterval: 'year',
                    actionUrl: '',
                    version: ''
                }],
                startTime: '',
                endTime: ''
            }
            break;
    }          
    return formData;        
}
export const checkUrl = (str: string, sort: string = '') => {
    if (str) {
        if (!/^(http:\/\/|https:\/\/|client.html).+/.test(str)) {
            return {
                isError: true,
                msg: `请填写${sort}正确的跳转链接`
            }
        }
        if (str.length !== str.trim().length) {
            return {
                isError: true,
                msg: `${sort}跳转链接前后不能有空格`
            }
        } else {
            return {
                isError: false,
                msg: ''
            }
        }
    } else {
        return {
            isError: false,
            msg: ''
        }
    }
}