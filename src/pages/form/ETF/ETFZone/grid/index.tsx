import React, { useEffect, useState } from 'react';
import api from 'api';
import Activity from './activity';
import { Button, message, Drawer, Table } from 'antd';
import { KEYS, TITLES, PROPKEYS } from './config';

const { getEtfEarningConfig, postEtfEarningConfig } = api;

const statusInfo = {
	1: '未启用',
	2: '启用中',
	3: '已驳回',
}

const statusInfoColor = {
	1: '',
	2: '#1890ff',
	3: 'red',
}

export default function(props: any) {
  const isCardtype = props.match.params.card === 'card';
  const isPagetype = props.match.params.card === 'page';
  const type = isCardtype ? 'card' : (isPagetype ? 'page' : 'grid');
  const [activities, setActivities] = useState([] as ETFActivityItem[]); //列表
  const [isShowAdd, setIsShowAdd] = useState(false); //是否显示modal
  const [selectedActivity, setSelectedActivity] = useState({}); //选中的活动
  const [modify, setModifyStatus] = useState('0'); // 0代表新增 1 代表查看 2 代表修改 3代表审核
  const columns = [
    {
      title: TITLES[type].title,
      dataIndex: PROPKEYS[type],
      key: 'activeId',
    },
    {
      title: TITLES[type].name,
      dataIndex: TITLES[type].nameKey,
      key: 'activeName',
    },
    {
      title: '最后修改人',
      dataIndex: 'person',
      key: 'person',
    },
    {
      title: '审核人',
      key: 'activeId',
      render:(item: any, record, index) => {
        return (<>
          <span>{record.auditPerson || '-'}</span>
        </>)
      }
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      key: 'endTime',
    },
    {
      title: '状态',
      dataIndex: 'beginTime',
      key: 'beginTime',
      render: (row, record, index) => {
        if (record.status === '2') {
          return (
            <>
              <span>未启用</span>
            </>
          )
        } 
        return (
          <>
            <span style={{color: statusInfoColor[record.audit]}}>{statusInfo[record.audit]}</span>
          </>
        )
      },
    },
    {
      title: '生效时间',
      key: 'endTime',
      render: (item: any, record, index) => {
        return (<>
          <div>
            {record.beginTime ? record.beginTime : ''} - {record.endTime ? record.endTime : ''}
          </div>
        </>)
      }
    },
    {
      title: '操作',
      key: 'button',
      render: (item: any, record, index) => {
        return (
          <>
            <Button
              type="primary"
              style={{ marginRight: 20 }}
              onClick={() => {
                queryActivity(record, '2');
              }}
            >
              编辑
            </Button>
            <Button
              type="primary"
              style={{ marginRight: 20 }}
              onClick={() => {
                queryActivity(record, '1');
              }}
            >
              查看全部
            </Button>
            <Button
              type="primary"
              style={{ marginRight: 20 }}
              onClick={() => {
                queryActivity(record, '3');
              }}
            >
              审批
            </Button>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    // postEtfEarningConfig({
    // 	value: JSON.stringify([])
    // })
    handleFetchETFActivity();
  }, [props.location]);

  /**
   * 查询活动列表
   */

  function handleFetchETFActivity() {
    _.fundLoading();
    getEtfEarningConfig({ key: KEYS[type] })
      .then((data: any) => {
        _.hideFundLoading();
        if (data.code === '0000') {
          if (!data.data) {
            return;
          }
          const hashObj = data.data;
          const copyKeys = [];
          const onlineKey = [];
          Object.keys(hashObj).forEach(key => {
            if (key.indexOf('@') !== 0) {
              onlineKey.push(key)
            } else {
              copyKeys.push(key);
            }
          });
          const listMap = onlineKey.map(key => {
            const copyKey = `@${key}`;
            if (copyKeys.indexOf(copyKey) > -1) {
              return JSON.parse(hashObj[copyKey]);
            } else {
              return JSON.parse(hashObj[key]);
            }
          })
          listMap.sort(function(a,b){
            return a.modifyTime < b.modifyTime ? 1 : -1
            });
          // const list = Object.values(hashObj).map(item => JSON.parse(item));
          setActivities(data.data ? listMap : []);
        } else {
          message.error(data.message);
        }
      })
      .catch(() => {
        _.hideFundLoading();
      });
  }

  /**
   * 查询单个
   * @param activityId
   */
  function queryActivity(record, status) {
    _.fundLoading();

    setSelectedActivity(record);
    setModifyStatus(status);
    setIsShowAdd(true);
    _.hideFundLoading();
  }

  function showAdd() {
    setModifyStatus('0');
    setSelectedActivity({});
    setIsShowAdd(true);
  }

  return (
    <div>
      <Table columns={columns} dataSource={activities}></Table>
      <Button type="primary" style={{ marginTop: '20px' }} onClick={showAdd}>
        {TITLES[type].add}
      </Button>

      <Drawer
        title={TITLES[type].drawerTitle}
        width={1200}
        onClose={() => {
          setIsShowAdd(false);
        }}
        visible={isShowAdd}
        bodyStyle={{ paddingBottom: 80 }}
      >
        {isShowAdd && <Activity
          type={type}
          isCardtype={isCardtype}
          modify={modify}
          isShowAdd={isShowAdd}
          activities={activities}
          activity={selectedActivity}
          setActivity={setSelectedActivity}
          setIsShowAdd={setIsShowAdd}
          handleFetchETFActivity={handleFetchETFActivity}
        />}
      </Drawer>
    </div>
  );
}
