import React, {useEffect, useState} from 'react'
import Form<PERSON><PERSON> from 'form-render/lib/antd';
import moment from 'moment';
import {Button, Popconfirm, message,  Select, Row, Col} from 'antd';
import {getSchema, KEY, PROPNAME } from './config';
import api from 'api';
const { Option } = Select;
const {
    postEtfEarningConfig,
} = api;


export default function({
    activity,
    setIsShowAdd = () => {},
    handleFetchETFActivity = () => {}
}: {
    activity?: any
    setIsShowAdd: Function
    handleFetchETFActivity: Function
}) {
    const [valid, setValid] = useState([]);
    const [formData, setFormData] = useState<any>({});
    const [optionList, setOptionList] = useState<any>([]);

    // 深拷贝对象/数组
    const deepClone = (obj) => {
        return JSON.parse(JSON.stringify(obj));
    };

    /**
     * 提交
     */
    function postConfig() {
        if (valid.length > 0) return message.error('必填项未填');
        let _form: any = {
            ...formData,
            modifyTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        }
        console.log(_form, '_form')
        saveConfig(_form);
    }

    function saveConfig(_form, audited) {
        if (!_form.name) {
            return message.error('请选择板块');
        }
        postEtfEarningConfig({
            key: KEY,
            propName: _form[PROPNAME],
            value: JSON.stringify(_form)
        }).then((res: any) => {
            _.hideFundLoading()
            if (res.code === '0000') {
                setIsShowAdd(false);
                message.success('保存成功');
                handleFetchETFActivity();
            }
        }).catch(() => {
            _.hideFundLoading()
        })
    }

    useEffect(() => {
        setFormData({ ...activity });
    }, [activity]);

    function onChangeForm(activityN: any) : any{
        setFormData({...formData, ...activityN})
    }

    /** 板块 */
    const SelectPlate = () => {
        const {thscodeHq, name} = formData;
        const value = name ? `${name} ${thscodeHq}` : undefined;
        const options = optionList.map(item => <Option value={`${item.name} ${item.thscodeHq}`} label={`${item.name} ${item.thscodeHq}`} key={item.thscodeHq}>
        {item.name} {item.thscodeHq}
        </Option>);
        return (<Row type="flex" align="middle" style={{width: '100%'}}>
            <Col span={16}>
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    placeholder="查询板块"
                    showArrow={false}
                    defaultActiveFirstOption={false}
                    value={value}
                    optionLabelProp="label"
                    filterOption={false}
                    onSearch={onSearch}
                    onSelect={onSelect}
                >
                    {options}
                </Select>
            </Col>
        </Row>)
    }

    let timeout = null;
    const onSearch = (value) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            api.cooperatePlateGet({}, value)
            .then((res: any) => {
            if (res?.code !== '0000') {
                return;
            }
            res.data && setOptionList([res.data]);
            })
            .finally(() => {
            _.hideFundLoading()
            });
        }, 400)
    }
    
      const onSelect = (value) => {
        const selectItem = optionList.find(item => value.indexOf(item.thscodeHq) > -1);
        setFormData((preForm: any) => ({ ...preForm, ...selectItem }));
      }


    return (
        <div>
            <FormRender
                propsSchema={getSchema(false)}
                onValidate={setValid}
                formData={formData}
                onChange={onChangeForm}
                showDescIcon={true}
            />
            {SelectPlate()}
            {  <div className="u-r-middle" style={{margin: 20}}>
                <Button type="primary" onClick={() => {setIsShowAdd(false)}} style={{marginRight: 20}}>取消</Button>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交么'}
                    onConfirm={postConfig}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="danger" 
                    >
                        提交
                    </Button>
                </Popconfirm>
            </div>}

        </div>
    )
}