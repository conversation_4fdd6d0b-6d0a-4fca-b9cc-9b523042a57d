import React, { useEffect, useState } from 'react';
import api from 'api';
import Activity from './activity';
import { Button, message, Drawer, Table } from 'antd';
import { KEY } from './config';

const { getEtfEarningConfig } = api;

export default function(props: any) {
  const [activities, setActivities] = useState([]); //列表
  const [isShowAdd, setIsShowAdd] = useState(false); //是否显示modal
  const [selectedActivity, setSelectedActivity] = useState({}); //选中的活动
  const [modify, setModifyStatus] = useState('0'); // 0代表新增 1 代表查看 2 代表修改 3代表审核
  const columns = [
    {
      title: '序号',
      key: 'index',
      render:(item, record, index) => {
        return (<>
          <span>{index + 1}</span>
        </>)
      }
    },
    {
      title: '名称',
      key: 'activeId',
      render:(item: any, record, index) => {
        return (<>
          <span>{`${record.name} ${record.thscodeHq}`}</span>
        </>)
      }
    },
    {
      title: '链接',
      key: 'link',
      render: (row, record, index) => {
        return (
          <>
            <span>{`https://fund.10jqka.com.cn/fefund/etf-collection/scym_scsy/public/plate.html?code=${record.thscodeHq}&marketid=${record.marketid}&release=true#/`}</span>
          </>
        )
      },
    },
    {
      title: '操作',
      key: 'button',
      render: (item: any, record, index) => {
        return (
          <>
            <Button
              type="primary"
              style={{ marginRight: 20 }}
              onClick={() => {
                queryActivity(record, '2');
              }}
            >
              编辑
            </Button>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    handleFetchETFActivity();
  }, []);

  /**
   * 查询活动列表
   */

  function handleFetchETFActivity() {
    _.fundLoading();
    getEtfEarningConfig({ key: KEY })
      .then((data: any) => {
        _.hideFundLoading();
        if (data.code === '0000') {
          if (!data.data) {
            return;
          }
          const hashObj = data.data;
          const listMap = Object.values(hashObj).map(item => JSON.parse(item));
          listMap.sort(function(a,b){
            return a.modifyTime < b.modifyTime ? 1 : -1
          });
          setActivities(listMap);
        } else {
          message.error(data.message);
        }
      })
      .catch(() => {
        _.hideFundLoading();
      });
  }

  /**
   * 查询单个
   * @param activityId
   */
  function queryActivity(record, status) {
    _.fundLoading();

    setSelectedActivity(record);
    setModifyStatus(status);
    setIsShowAdd(true);
    _.hideFundLoading();
  }

  function showAdd() {
    setModifyStatus('0');
    setSelectedActivity({});
    setIsShowAdd(true);
  }

  return (
    <div>
      <Button type="primary" style={{ marginBottom: '20px' }} onClick={showAdd}>
        新增
      </Button>
      <Table columns={columns} dataSource={activities} pagination={false}></Table>

      <Drawer
        title="板块链接配置"
        width={1200}
        onClose={() => {
          setIsShowAdd(false);
        }}
        visible={isShowAdd}
        bodyStyle={{ paddingBottom: 80 }}
      >
        {isShowAdd && <Activity
          modify={modify}
          isShowAdd={isShowAdd}
          activities={activities}
          activity={selectedActivity}
          setActivity={setSelectedActivity}
          setIsShowAdd={setIsShowAdd}
          handleFetchETFActivity={handleFetchETFActivity}
        />}
      </Drawer>
    </div>
  );
}
