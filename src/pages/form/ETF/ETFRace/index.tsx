import * as XLSX from 'xlsx';
import React, { useState, useEffect, useCallback } from 'react';
import { Input, Button, message, Popconfirm, Upload, Icon } from 'antd';
import api from 'api';
const { postEtfRaceAwardAdd } = api;

interface iResponse {
  code: number;
  data?: string;
  message: string;
}

export default function() {
  const [fileList, setFileList] = useState<any>([]);
  const [formData, setFormData] = useState([]);
  const confirmData = () => {
    if (formData.length === 0) {
      return message.warn('上传名单为空，请重新上传');
    }
    // const value = formData[0];
    const params = {
      orderId: `${new Date().getTime()}`,
      awards: formData,
    };
    postEtfRaceAwardAdd(params)
      .then((res: iResponse) => {
        if (res?.code !== 0) {
          message.error(res?.message);
        } else {
          message.success('保存成功！');
        }
      })
      .catch((e: Error) => {
        message.error(e.message);
      });
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const fileReader = new FileReader();
      fileReader.onload = (event: any) => {
        try {
          const { result } = event.target;
          // 以二进制流方式读取得到整份excel表格对象
          const workbook = XLSX.read(result, { type: 'binary' });
          let data: any = []; // 存储获取到的数据
          // 遍历每张工作表进行读取（这里默认只读取第一张表）
          for (const sheet in workbook.Sheets) {
            // 利用 sheet_to_json 方法将 excel 转成 json 数据
            data = XLSX.utils.sheet_to_json(workbook.Sheets[sheet], {
              header: ['userId', 'userName', 'awardType', 'notes', 'amount'], // 如果header被指定，第一行就会被当做数据行；如果header未指定，第一行是header并且不作为数据。
            });
            break;
          }
          console.log(data);
          // 是否为数字
          const ifNumber = (value: any) => {
            const ifString = typeof value === 'string';
            let ifAllNumber = true;
            if (ifString) {
              ifAllNumber = /^\d+$/.test(value);
            }
            return ifAllNumber;
          };
          if (
            file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.type === 'application/vnd.ms-excel'
          ) {
            console.log('file excel');
            // 第一行为一些数据时数据会失效，所以第一行不能为数据
            if (data[0].__rowNum__ === 0 && ifNumber(data[0].userId)) {
              console.log('检测id');
              return message.error('请上传正确格式的excel');
            } else {
              console.log(file, 'file');
              setFileList([
                {
                  uid: file.uid,
                  status: 'done',
                  name: file.name,
                  size: file.size,
                  type: file.type,
                },
              ]);
              setFormData(data.slice(1));
              // props.onChange(file);
            }
          } else {
            return message.error('请上传正确格式的excel');
          }
        } catch (e) {
          return message.error('请上传正确文件类型');
        }
      };
      fileReader.readAsBinaryString(file);
      return false;
    },
    onRemove: (e: File) => {
      console.log(e, 're');
      setFileList([]);
      setFormData([]);
    },
  };
  return (
    <div>
      <p style={{ width: 900, marginBottom: 30, textAlign: 'center', fontSize: 30 }}>ETF大赛发奖</p>
      <div style={{ width: 400, display: 'flex' }}>
        {/* <TextArea
          autoSize={{ minRows: 2, maxRows: 10 }}
          onChange={e => {
            setFormData('');
          }}
        /> */}
        <Upload {...uploadProps} fileList={fileList}>
          <Button style={{ marginRight: '100px' }}>
            <Icon type="upload" /> 选择文件
          </Button>
        </Upload>
      </div>
      <div style={{ marginBottom: '20px', marginTop: '20px' }}>
        <span style={{ color: 'red', marginBottom: '30px', marginTop: '6px' }}>
          请上传xlsx文档，每行依次填入用户id、用户名称、奖品类型、奖励描述、奖励金额，第一行是标题栏，不要填数据
        </span>
      </div>
      <Popconfirm
        title="请问是否保存"
        okText="确定"
        cancelText="取消"
        onConfirm={confirmData}
        placement="bottom"
      >
        <Button type="danger">保存</Button>
      </Popconfirm>
    </div>
  );
}
