import React, { useEffect, useState } from 'react';
import FormRender from 'form-render/lib/antd';
import { Button, Popconfirm, message, Modal } from 'antd';
import UploadImg from '../../components/uploadFile';
import getSchema from './configV2';
import api from 'api';
import styles from './index.less';
import { keyV2, BgImgHeightObj } from './constants';
const SCHEMA1 = getSchema(false);
const SCHEMA2 = getSchema(true);
const { postEtfBuyGiveActivity } = api;

export default function({
  isAdd = false,
  isShowAdd,
  activities = [],
  activity,
  setActivity,
  setIsShowAdd = () => {},
  handleFetchETFActivity = () => {},
}: {
  isAdd?: boolean;
  isShowAdd: boolean;
  activities: any;
  activity?: any;
  setActivity: any;
  setIsShowAdd: Function;
  handleFetchETFActivity: Function;
}) {
  const [valid, setValid] = useState([]);

  /**
   * 提交
   */
  function postConfig() {
    console.log(activity, activities, valid, 'activity');
    if (activities.filter(item => item.activeId === activity.activeId).length > (isAdd ? 0 : 1))
      return message.error('activeId重复，需为唯一');
    if (valid.length > 0) return message.error('必填项未填');
    if (activity.activityRules.length === 0) return message.error('请填写规则');
    let _form: any = { ...activity };
    postEtfBuyGiveActivity({
      key: keyV2,
      propName: _form.activeId,
      value: JSON.stringify(_form),
    })
      .then((res: any) => {
        console.log(res, 'res');
        _.hideFundLoading();
        if (res.code === '0000') {
          setIsShowAdd(false);
          message.success(isAdd ? '添加完成' : '修改完成');
          handleFetchETFActivity();
        }
      })
      .catch(() => {
        _.hideFundLoading();
      });
  }

  function onChangeForm(activity: any): any {
    setActivity(activity);
  }

  const LogoUpload = ({ value, onChange, name }) => {
    const uploadCallback = (fileName: string, size: number, url: string) => {
      onChange(name, url);
    };
    return (
      <>
        {value && <img style={{ width: `300px`, height: '40px' }} src={value} />}
        <span className={styles['upload-logo']}>
          <UploadImg
            text="选择文件"
            callback={uploadCallback}
            // beforeUpload={file => beforeUpload(file, imgObj.width, imgObj.height)}
          />
        </span>
        <span>图片要求：1. 格式为.png 2.底色透明 3、尺寸推荐长度718* {BgImgHeightObj[name]}</span>
      </>
    );
  };

  return (
    <div>
      <FormRender
        propsSchema={isAdd ? SCHEMA1 : SCHEMA2}
        onValidate={setValid}
        formData={activity}
        onChange={activity => onChangeForm(activity)}
        widgets={{ uploadImg: LogoUpload }}
        displayType="row"
        showDescIcon={true}
      />

      <div className="u-r-middle" style={{ margin: 20 }}>
        <Button
          type="primary"
          onClick={() => {
            setIsShowAdd(false);
          }}
          style={{ marginRight: 20 }}
        >
          取消
        </Button>
        <Popconfirm
          placement="rightBottom"
          title={'你确定要提交么'}
          onConfirm={postConfig}
          okText="确认"
          cancelText="取消"
        >
          <Button type="danger">提交</Button>
        </Popconfirm>
      </div>
    </div>
  );
}
