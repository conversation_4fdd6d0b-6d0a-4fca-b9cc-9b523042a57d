import React, { useEffect, useState } from 'react';
import api from 'api';
import Activity from './activity';
import ActivityV2 from './activityV2';
import { Button, message, Drawer, Table } from 'antd';
import { formatUrlParams } from '../../abtest/util';
import { keyV2 } from './constants';

const { getEtfBuyGiveActivity, postEtfBuyGiveActivity } = api;

interface ETFActivityItem {
  activityId: string;
  type: string;
  cardType: string;
  activityName: string;
  startDate: string;
  endDate: string;
  status: string;
  title: string;
  subTitle: string;
  tipTitle: string;
  tipContext: string;
  recommendTitle: string;
  recommendCode: string;
  recommendName: string;
  profitDescribe: string;
  productTitle: string;
  purchaseContext: string;
  windowText: string;
  activityRules: string | any;
  codeRate: string;
  cashVoucherList: any;
}

export default function() {
  const [activities, setActivities] = useState([] as ETFActivityItem[]); //列表
  const [isShowAdd, setIsShowAdd] = useState(false); //是否显示modal
  const [selectedActivity, setSelectedActivity] = useState({}); //选中的活动
  const [isAdd, setIsAdd] = useState(true); //是否为添加
  const [awardList, setAwardList] = useState([]); //名单
  const columns = [
    {
      title: '活动id',
      dataIndex: 'activeId',
      key: 'activeId',
    },
    {
      title: '活动名称',
      dataIndex: 'activeName',
      key: 'activeName',
    },
    {
      title: '起始时间',
      dataIndex: 'beginTime',
      key: 'beginTime',
    },
    {
      title: '中止时间',
      dataIndex: 'endTime',
      key: 'endTime',
    },
    {
      title: '按钮',
      key: 'button',
      render: (item: any, record, index) => {
        return (
          <>
            <Button
              type="primary"
              style={{ marginRight: 20 }}
              onClick={() => {
                queryActivity(record);
              }}
            >
              查看全部
            </Button>
          </>
        );
      },
    },
  ];
  // 使用type用于区分新的ETF种子基金增加活动，不影响老配置
  const typePage = formatUrlParams().type;
  useEffect(() => {
    handleFetchETFActivity();
  }, []);

  /**
   * 查询活动列表
   */

  function handleFetchETFActivity() {
    _.fundLoading();
    getEtfBuyGiveActivity({ key: typePage ? keyV2 : 'etf-buygiveactivity' })
      .then((data: any) => {
        _.hideFundLoading();
        if (data.code === '0000') {
          if (!data.data) {
            return;
          }
          const list = Object.values(data.data).map(item => JSON.parse(item));
          setActivities(data.data ? list : []);
        } else {
          message.error(data.message);
        }
      })
      .catch(() => {
        _.hideFundLoading();
      });
  }

  /**
   * 查询单个
   * @param activityId
   */
  function queryActivity(record) {
    _.fundLoading();
    setSelectedActivity(record);
    setAwardList(record.award);
    setIsAdd(false);
    setIsShowAdd(true);
    _.hideFundLoading();
  }

  function showAdd() {
    setSelectedActivity({});
    setIsAdd(true);
    setIsShowAdd(true);
  }

  return (
    <div>
      <Table columns={columns} dataSource={activities}></Table>
      <Button type="primary" style={{ marginTop: '20px' }} onClick={showAdd}>
        增加活动
      </Button>

      <Drawer
        title="增加活动"
        width={1200}
        onClose={() => {
          setIsShowAdd(false);
        }}
        visible={isShowAdd}
        bodyStyle={{ paddingBottom: 80 }}
      >
        {isShowAdd &&
          (typePage ? (
            <ActivityV2
              isAdd={isAdd}
              isShowAdd={isShowAdd}
              activities={activities}
              activity={selectedActivity}
              setActivity={setSelectedActivity}
              setIsShowAdd={setIsShowAdd}
              handleFetchETFActivity={handleFetchETFActivity}
            />
          ) : (
            <Activity
              isAdd={isAdd}
              isShowAdd={isShowAdd}
              activities={activities}
              activity={selectedActivity}
              setActivity={setSelectedActivity}
              setIsShowAdd={setIsShowAdd}
              awardList={awardList}
              setAwardList={setAwardList}
              handleFetchETFActivity={handleFetchETFActivity}
            />
          ))}
      </Drawer>
    </div>
  );
}
