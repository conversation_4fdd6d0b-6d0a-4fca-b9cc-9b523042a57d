export default function(isEdit) {
  return {
    type: 'object',
    required: [
      'activeId',
      'activeName',
      'status',
      'beginTime',
      'endTime',
      'awardEndTime',
      'activityRules',
      'sunMaoAward',
      'etfGroupFunds',
      'pushConfig',
    ],
    properties: {
      activeId: {
        title: '活动id',
        type: 'string',
        pattern: /^\w+$/,
        description: '需要用英文输入，唯一标识符，不能重复',
        'ui:options': {},
        'ui:disabled': isEdit ? true : false,
      },
      activeName: {
        title: '活动名称',
        type: 'string',
        'ui:options': {},
      },
      status: {
        title: '活动状态（紧急状态下线使用）',
        type: 'string',
        enum: ['1', '0'],
        enumNames: ['开始', '结束'],
        'ui:widget': 'radio',
      },
      etfGroupFunds: {
        title: '行业ETF列表',
        type: 'array',
        'ui:options': {},
        items: {
          type: 'object',
          properties: {
            groupName: {
              title: 'ETF类型名称',
              type: 'string',
              'ui:options': {},
            },
            groupDesc: {
              title: 'ETF类型描述(选填)',
              type: 'string',
              'ui:options': {},
            },
            topBgImg: {
              title: '首页ETF类型头图背景(选填)',
              type: 'string',
              'ui:widget': 'uploadImg',
              'ui:options': {},
            },
            popTopBgImg: {
              title: 'pop弹窗类型头图背景(选填)',
              type: 'string',
              'ui:widget': 'uploadImg',
              'ui:options': {},
            },
            riseType: {
              title: '选择ETF涨幅',
              type: 'string',
              enum: [
                'Today',
                'ThreeDay',
                'OneWeek',
                'oneMonth',
                'ThreeMonth',
                'HalfYear',
                'OneYear',
              ],
              enumNames: [
                '实时涨幅',
                '近3日涨幅',
                '近一周涨幅',
                '近一月涨幅',
                '近三月涨幅',
                '近半年涨幅',
                '近一年涨幅',
              ],
              'ui:widget': 'select',
              'ui:width': '30%',
              // "ui:labelWidth": 122,
            },
            etfFunds: {
              title: 'ETF列表',
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  etfCode: {
                    title: 'etf产品code',
                    type: 'string',
                    pattern: /^[a-zA-Z0-9]+$/,
                    description: '仅允许输入字母或数字',
                    'ui:options': {},
                  },
                  etfMarket: {
                    title: 'etf市场code',
                    type: 'string',
                    pattern: /^[a-zA-Z0-9]+$/,
                    description: '仅允许输入字母或数字',
                    'ui:options': {},
                  },
                  companyCode: {
                    title: '基金公司code',
                    type: 'string',
                    'ui:options': {},
                  },
                },
              },
            },
          },
        },
      },
      activityRules: {
        title: '活动规则',
        type: 'array',
        items: {
          type: 'object',
          properties: {
            text: {
              title: '',
              type: 'string',
              'ui:options': {},
            },
          },
        },
        'ui:options': {},
      },

      beginTime: {
        'ui:labelWidth': 120,
        title: '活动开始时间',
        type: 'string',
        format: 'date',
        // 'ui:width': '46%',
      },
      endTime: {
        'ui:labelWidth': 120,
        title: '活动结束时间',
        type: 'string',
        format: 'date',
        // 'ui:width': '46%',
      },
      awardEndTime: {
        'ui:labelWidth': 120,
        title: '活动兑奖时间',
        type: 'string',
        format: 'date',
        // 'ui:width': '46%',
      },
      sunMaoAward: {
        title: '榫卯奖励配置',
        type: 'object',
        properties: {
          sunMaoId: {
            title: '榫卯id',
            type: 'string',
            'ui:options': {},
          },
          sunMaoRightId: {
            title: '榫卯权益id',
            type: 'string',
            'ui:options': {},
          },
        },
      },
      pushConfig: {
        title: '获奖提示push配置',
        type: 'object',
        properties: {
          pushTitle: {
            title: '获奖标题',
            type: 'string',
            'ui:options': {},
          },
          pushContent: {
            title: '获奖内容',
            type: 'string',
            'ui:options': {},
          },
          pushUrl: {
            title: '链接',
            type: 'string',
            'ui:options': {},
          }
        },
      },
    },
  };
}
