{"type": "page", "regions": ["body", "header"], "id": "u:dc588a95fe28", "body": [{"type": "form", "id": "u:b93c32851bb2", "api": {"method": "post", "url": "/common_config/hash_data_save", "messages": {}, "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "notice", "value": "{\"noticeContent\": \"${noticeContent}\", \"noticeUrl\": \"${noticeUrl}\"}"}, "dataType": "form"}, "body": [{"type": "input-text", "id": "u:e7111d7b71d3", "label": "公告滚动文案", "name": "noticeContent", "value": "${config.noticeContent}", "mode": "horizontal"}, {"type": "input-text", "label": "公告跳转链接", "name": "noticeUrl", "id": "u:0974be856a23", "mode": "horizontal", "validations": {}, "validationErrors": {}, "showCounter": false, "value": "${config.noticeUrl}"}], "title": "顶部公告配置", "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "notice"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存", "actions": [{"type": "submit", "label": "保存", "primary": true, "id": "u:721b9f5495d2"}]}, {"type": "form", "body": [{"type": "combo", "label": "", "name": "popupList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b615a96d7747"}, "items": [{"type": "input-text", "name": "id", "id": "u:9b2a3d350667", "label": "id", "mode": "horizontal", "required": true, "unique": true, "value": "${id || TIMESTAMP(NOW)}", "disabled": true}, {"type": "input-text", "name": "name", "id": "u:582896be78a9", "label": "名称", "mode": "horizontal", "required": true, "unique": false}, {"type": "input-datetime-range", "name": "validTime", "placeholder": "请选择日期时间范围", "id": "u:21addd91c1ad", "label": "日期范围", "mode": "horizontal", "inputFormat": "YYYY-MM-DD HH:mm:ss", "format": "X", "minDate": "", "maxDate": "", "value": "", "ranges": [], "hidden": false, "required": true}, {"type": "input-image", "label": "图片上传(560:440)", "name": "imageUrl", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:6b0d8e94e484", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "mode": "horizontal", "required": true, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"aspectRatio": "1.2727"}}, {"type": "input-text", "label": "跳转链接", "name": "jumpUrl", "id": "u:136669ea1f27", "mode": "horizontal"}, {"type": "select", "label": "人群类型", "name": "userType", "options": [{"label": "全部用户", "value": 0}, {"label": "进入页面未报名", "value": 1}, {"label": "完成报名未绑定", "value": 2}, {"label": "完成绑定未交易", "value": 3}, {"label": "完成绑定已交易", "value": 4}], "id": "u:68934c578318", "multiple": false, "mode": "horizontal", "required": true, "value": 0}, {"type": "select", "label": "疲劳度", "name": "fatigue", "options": [{"label": "每次都有", "value": 0}, {"label": "每天一次", "value": 1}, {"label": "每个策略周期一次", "value": 2}], "id": "u:06ab01f52f23", "multiple": false, "mode": "horizontal", "required": true, "value": ""}], "id": "u:0d1fbf05d5f8", "strictMode": true, "syncFields": [], "multiLine": true, "value": "${config}"}], "id": "u:3ec481ba144d", "api": {"url": "/common_config/hash_data_save", "method": "post", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "popup", "value": "${ENCODEJSON(popupList)}"}, "dataType": "form"}, "title": "弹窗配置", "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "popup"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存", "actions": [{"type": "submit", "label": "保存", "primary": true, "id": "u:8b7183717799"}]}, {"type": "form", "body": [{"type": "combo", "id": "u:0da70f9167e6", "label": "", "name": "popupList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:3c7d6b0b4c9a"}, "items": [{"type": "input-text", "name": "id", "id": "u:9b2a3d350667", "label": "id", "mode": "horizontal", "required": true, "unique": true, "value": "${id || TIMESTAMP(NOW)}", "disabled": true}, {"type": "input-text", "name": "name", "id": "u:9b2a3d350667", "label": "名称", "mode": "horizontal", "required": true, "unique": false}, {"type": "input-datetime-range", "name": "validTime", "placeholder": "请选择日期时间范围", "id": "u:7995a5f24c1a", "label": "日期范围", "mode": "horizontal", "inputFormat": "YYYY-MM-DD HH:mm:ss", "format": "X", "minDate": "", "maxDate": "", "value": "", "ranges": [], "hidden": false, "required": true}, {"type": "input-image", "label": "图片上传（750:184）", "name": "imageUrl", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:747ccf1d5bff", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "mode": "horizontal", "required": true, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"aspectRatio": "4.076"}}, {"type": "input-text", "label": "跳转链接", "name": "jumpUrl", "id": "u:8beb9162e816", "mode": "horizontal"}, {"type": "select", "label": "人群类型", "name": "userType", "options": [{"label": "全部用户", "value": 0}, {"label": "进入页面未报名", "value": 1}, {"label": "完成报名未绑定", "value": 2}, {"label": "完成绑定未交易", "value": 3}, {"label": "完成绑定已交易", "value": 4}], "id": "u:6584a7ee0fd6", "multiple": false, "mode": "horizontal", "required": true, "value": 0}, {"type": "select", "label": "疲劳度", "name": "fatigue", "options": [{"label": "每次都有", "value": 0}, {"label": "每天一次", "value": 1}, {"label": "每个策略周期一次", "value": 2}], "id": "u:45752e089247", "multiple": false, "mode": "horizontal", "required": true, "value": ""}, {"type": "select", "label": "展示时间", "name": "durationType", "options": [{"label": "永久", "value": 0}, {"label": "固定秒数", "value": 1}], "id": "u:7333d77dec8d", "multiple": false, "mode": "horizontal", "required": true, "value": ""}, {"type": "input-number", "label": "持续秒数", "name": "durationTime", "keyboard": true, "props": {}, "id": "u:79bb8cb67c65", "step": 1, "required": true, "size": "full", "mode": "horizontal", "visibleOn": "${durationType === 1}"}], "strictMode": true, "syncFields": [], "multiLine": true, "value": "${config}"}], "id": "u:2afcc0603255", "title": "浮窗配置", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "floatingWindow", "value": "${ENCODEJSON(popupList)}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}", "requestAdaptor": ""}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "floatingWindow"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};", "requestAdaptor": ""}, "submitText": "保存", "actions": [{"type": "submit", "label": "保存", "primary": true, "id": "u:70d57ffac9ee"}], "feat": "Edit", "dsType": "api"}], "title": "ETF大赛公告弹窗"}