import React, { useEffect } from 'react';
import 'moment/locale/zh-cn';
import amisJSON from './amis.json';
import amisEnv from 'functions/amisEnv';
import {env} from 'config';
import { formatUrlParams} from '../../../abtest/util';
import { defaultActivityId } from '../index';
import axios from 'axios'
import api from 'api';

export default function() {
    let amisScoped:any;
    let khPrefix:any;
    let activityId:any;
    let voucherList:any;
    khPrefix = env === 'prod' ? 'https://mams.10jqka.com.cn/' : 'https://khtest.10jqka.com.cn/tenon_genius/';
    activityId = formatUrlParams()?.activityId ? formatUrlParams().activityId: defaultActivityId
    const init = () => {
      axios.post(khPrefix + '/assembly/center/rights/center/list/v1/' + activityId, null, {
        headers: {
          'token': 'manta',
        }
      }).then((res: any) => {
        let rightsConfig:any;
        if (res && res.status === 200 && res.data.status_code === 0) {
          rightsConfig = res.data.data.rightsList;
        }
        if (rightsConfig && Array.isArray(rightsConfig)) {
          rightsConfig.forEach((config, index) => {
            config["rightsDescShow"] = config["rightsDesc"] + config["target"];
            config["rightsData"] = {};
            config.rightsData["rightsDesc"] = config["rightsDesc"];
            config.rightsData["rightsId"] = config["rightsId"];
            config.rightsData["target"] = config["target"];
          })
        }

        api.fetchHash({
          'key': 'etf_competition_activity_config_' + activityId,
          'propName': 'voucherList'
        }).then((res:any) => {
          if (res && res.statusCode === 200 && res.code === '0000') {
            voucherList = JSON.parse(res.data);
          }

          let amis = amisRequire('amis/embed');
          amisScoped = amis.embed('#etfAwardList', {
          ...amisJSON,
          data:{
            activityId: activityId,
            rightsConfig: rightsConfig,
            voucherList: voucherList
          }}, {}, amisEnv());
        })
      })
    }
    useEffect(() => {
      init();
      return () => {
        amisScoped.unmount();
      }
    }, [])
    
    return (
      <div id='etfAwardList'></div>
    )
}

