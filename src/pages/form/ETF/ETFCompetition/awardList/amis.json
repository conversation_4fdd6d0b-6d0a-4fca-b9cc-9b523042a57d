{"type": "page", "body": [{"type": "form", "id": "u:e9ceb24a32bf", "api": {"method": "post", "url": "/common_config/hash_data_save", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "voucherList", "value": "${ENCODEJSON(voucherList)}"}, "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "title": "凭证配置", "body": [{"type": "combo", "label": "", "name": "voucherList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:2e5790da7d16"}, "items": [{"type": "input-text", "name": "voucherId", "placeholder": "", "id": "u:e1ebc0216501", "label": "凭证ID", "required": true}, {"type": "select", "name": "useType", "options": [{"label": "计数", "value": "COUNT"}], "id": "u:5336e582dd17", "multiple": false, "label": "使用类型", "value": "COUNT", "required": true}, {"type": "input-text", "label": "凭证描述", "name": "voucherDesc", "id": "u:ae36fa8b4199", "required": true}], "id": "u:946ae60d136c", "strictMode": true, "syncFields": [], "value": "${voucherList}"}], "submitText": "保存"}, {"type": "form", "title": "抽奖配置", "body": [{"type": "combo", "label": "", "name": "drawConfig", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:6a8166d9a893"}, "items": [{"type": "input-text", "label": "奖池ID", "name": "poolId", "id": "u:b5a7c3b48bdz", "mode": "horizontal", "required": true}, {"label": "抽奖所需凭证ID", "type": "select", "name": "voucherId", "id": "u:8f6a8944093e", "multiple": false, "required": true, "mode": "horizontal", "source": "${voucherList}", "labelField": "voucherDesc", "valueField": "voucherId"}, {"type": "select", "label": "兜底权益ID", "name": "worstRightsId", "id": "u:4bedee1989d5", "multiple": false, "mode": "horizontal", "options": [{"label": "无（默认）", "value": "-1"}], "value": "-1"}, {"type": "select", "label": "发放类型", "name": "provideType", "id": "u:4bedee1989d5", "multiple": false, "mode": "horizontal", "options": [{"label": "立即发奖", "value": "IMMEDIATELY"}, {"label": "延迟发奖", "value": "DELAY"}], "required": true}, {"type": "select", "label": "延迟触发事件", "name": "provideEvent", "id": "u:4bedee1989d5", "multiple": false, "mode": "horizontal", "options": [{"label": "报名", "value": "SIGN"}, {"label": "绑定账户", "value": "BINDING"}]}, {"type": "combo", "label": "抽奖奖池配置", "name": "poolConfig", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:6e332cc97d31"}, "items": [{"type": "select", "name": "rightsId", "id": "u:2064c5526cc4", "source": "${rightsConfig}", "labelField": "rightsDescShow", "valueField": "rightsId", "multiple": false, "label": "权益ID", "required": true}, {"type": "input-text", "name": "inventory", "placeholder": "", "id": "u:6f2dd9cbd581", "label": "库存", "required": true}, {"type": "input-text", "label": "概率", "name": "probability", "id": "u:b5a7c3b48bdf", "required": true}, {"type": "combo", "label": "领取条件", "name": "conditions", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:dc0672689962"}, "items": [{"type": "select", "name": "conditionType", "options": [{"label": "外部触发", "value": "EXTERNAL_TRIGGER"}, {"label": "抽奖次数", "value": "DRAW_COUNT"}, {"label": "基金开户", "value": "FUND_ACCOUNT"}, {"label": "比赛交易一笔", "value": "COMPETITION_TRADE"}, {"label": "用户收益率", "value": "USER_RATE"}, {"label": "用户报名", "value": "USER_SIGN"}, {"label": "基金交易", "value": "FUND_TRADE"}], "multiple": false, "value": "EXTERNAL_TRIGGER", "id": "u:2064c5526cc4", "label": "条件类型", "required": true}, {"draggable": true, "type": "input-kv", "label": "条件参数", "name": "conditionParam", "id": "u:90447bf4a9e5", "multiple": true, "items": [{"placeholder": "Key", "type": "input-text", "unique": true, "name": "key", "required": true, "validateOnChange": true}, {"placeholder": "Value", "type": "input-text", "name": "value"}], "required": true}], "id": "u:69dea52e24c8", "syncFields": [], "required": true, "multiLine": true}], "id": "u:aad0d0fdb17d", "strictMode": true, "syncFields": [], "mode": "horizontal", "required": true}], "id": "u:2372051c466c", "syncFields": [], "multiLine": true, "value": "${config}"}], "id": "u:6d72b9301396", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "drawConfig", "value": "${ENCODEJSON(drawConfig)}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "drawConfig"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}, {"type": "form", "id": "u:e9ceb24a32bf", "api": {"method": "post", "url": "/common_config/hash_data_save", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "actionConfig", "value": "${ENCODEJSON(actionConfig)}"}, "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "actionConfig"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "title": "事件配置", "body": [{"type": "combo", "label": "", "name": "actionConfig", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:2e5790da7d16"}, "items": [{"type": "select", "label": "事件", "name": "eventType", "id": "u:4bedee1989d6", "multiple": false, "mode": "horizontal", "options": [{"label": "报名", "value": "SIGN"}, {"label": "绑定账户", "value": "BINDING"}], "required": true}, {"type": "input-text", "label": "奖池ID", "name": "poolId", "id": "u:b5a7c3b48bdl", "mode": "horizontal", "required": true}], "id": "u:946ae60d136c", "strictMode": true, "syncFields": [], "value": "${config}"}], "submitText": "保存"}, {"type": "form", "title": "瓜分配置", "body": [{"type": "combo", "label": "", "name": "carveUpList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b21965fabc5b"}, "items": [{"type": "select", "name": "rightsId", "id": "u:2064c5526cc5", "source": "${rightsConfig}", "labelField": "rightsDescShow", "valueField": "rightsId", "multiple": false, "label": "权益ID", "required": true}, {"type": "select", "name": "voucherId", "id": "u:9b2c2d4bb2aa", "source": "${voucherList}", "labelField": "voucherDesc", "valueField": "voucherId", "multiple": true, "label": "凭证ID", "required": true, "checkAll": false, "joinValues": false, "extractValue": true}, {"type": "input-text", "name": "totalAmount", "id": "u:a01d34fdbf6e", "placeholder": "", "label": "总奖金", "required": true}, {"type": "input-text", "label": "单人最高限额", "name": "topSingleAmount", "id": "u:a740c12b1c98", "required": true}], "id": "u:0d63ccb1174f", "strictMode": true, "syncFields": [], "value": "${config}"}], "id": "u:e0529a35d221", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "carveUpList", "value": "${ENCODEJSON(carveUpList)}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "carveUpList"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}, {"type": "form", "title": "榜单奖励配置", "body": [{"type": "combo", "label": "周榜", "name": "weekConfig", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:888b7778a36e"}, "items": [{"type": "input-number", "label": "名次起始", "name": "startIndex", "keyboard": true, "id": "u:7990b5093a53", "step": 1, "required": true}, {"type": "input-number", "name": "endIndex", "id": "u:7990b5093a54", "label": "名次结束", "keyboard": true, "step": 1, "required": true}, {"type": "combo", "label": "", "name": "rightsList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:4cc00e3d170f"}, "items": [{"type": "select", "name": "rightsData", "id": "u:2064c5526cc6", "source": "${rightsConfig}", "labelField": "rightsDescShow", "valueField": "rightsData", "multiple": false, "label": "权益ID", "required": true, "checkAll": false}, {"type": "input-number", "name": "count", "id": "u:7990b5093a55", "label": "数量", "keyboard": true, "step": 1, "required": true}], "id": "u:1f4b05e876fb", "strictMode": true, "syncFields": [], "required": true}], "id": "u:e029e207b35c", "strictMode": true, "syncFields": [], "value": "${config.weekConfig}"}, {"type": "combo", "label": "月榜", "name": "monthConfig", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:888b7778a37e"}, "items": [{"type": "input-number", "label": "名次起始", "name": "startIndex", "keyboard": true, "id": "u:7990b5093a63", "step": 1, "required": true}, {"type": "input-number", "name": "endIndex", "id": "u:7990b5093a64", "label": "名次结束", "keyboard": true, "step": 1, "required": true}, {"type": "combo", "label": "", "name": "rightsList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:4cc00e3d171f"}, "items": [{"type": "select", "name": "rightsData", "id": "u:2064c5526cd6", "source": "${rightsConfig}", "labelField": "rightsDescShow", "valueField": "rightsData", "multiple": false, "label": "权益ID", "required": true, "checkAll": false}, {"type": "input-number", "name": "count", "id": "u:7990b5093a65", "label": "数量", "keyboard": true, "step": 1, "required": true}], "id": "u:1f4b05e876eb", "strictMode": true, "syncFields": [], "required": true}], "id": "u:e029e207b36c", "strictMode": true, "syncFields": [], "value": "${config.monthConfig}"}, {"type": "combo", "label": "总榜", "name": "totalConfig", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:988b7778a36e"}, "items": [{"type": "input-number", "label": "名次起始", "name": "startIndex", "keyboard": true, "id": "u:8990b5093a53", "step": 1, "required": true}, {"type": "input-number", "name": "endIndex", "id": "u:8990b5093a54", "label": "名次结束", "keyboard": true, "step": 1, "required": true}, {"type": "combo", "label": "", "name": "rightsList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:5cc00e3d170f"}, "items": [{"type": "select", "name": "rightsData", "id": "u:3064c5526cc6", "source": "${rightsConfig}", "labelField": "rightsDescShow", "valueField": "rightsData", "multiple": false, "label": "权益ID", "required": true, "checkAll": false}, {"type": "input-number", "name": "count", "id": "u:8990b5093a55", "label": "数量", "keyboard": true, "step": 1, "required": true}], "id": "u:2f4b05e876fb", "strictMode": true, "syncFields": [], "required": true}], "id": "u:f029e207b35c", "strictMode": true, "syncFields": [], "value": "${config.totalConfig}"}], "id": "u:5aa6a67bdf37", "submitText": "保存", "api": {"method": "post", "url": "/common_config/hash_data_save", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "rankAward", "value": "{\"weekConfig\": ${ENC<PERSON><PERSON><PERSON><PERSON><PERSON>(weekConfig)}, \"monthConfig\": ${ENCODEJSON(monthConfig)}, \"totalConfig\": ${ENCODEJSON(totalConfig)}}"}, "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "rankAward"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}}], "title": "ETF大赛奖励配置", "regions": ["body", "header"], "id": "u:e8b7b451e41b"}