{"type": "page", "regions": ["body", "header"], "id": "u:1329990c527a", "body": [{"id": "u:da2ee3ae6024", "type": "form", "body": [{"id": "u:7b910ca6ead3", "type": "input-text", "label": "活动名称", "name": "activityName", "required": true, "value": "${config.activityName}", "mode": "horizontal"}, {"type": "input-text", "label": "活动ID", "name": "newActivityId", "id": "u:7b910ca6ead3", "required": true, "mode": "horizontal", "value": "${activityId}", "disabled": true}, {"type": "input-text", "label": "安全部活动ID", "name": "securityId", "id": "u:7b910ca6ead4", "required": true, "mode": "horizontal", "value": "${config.securityId}"}, {"type": "input-datetime", "label": "报名开始时间", "name": "signStartTime", "id": "u:eeceff72cb72", "required": true, "size": "lg", "mode": "horizontal", "inputFormat": "YYYYMMDDHHmmss", "placeholder": "请选择日期以及时间", "format": "YYYYMMDDHHmmss", "minDate": "", "maxDate": "", "value": "${config.signStartTime}"}, {"type": "input-datetime", "label": "报名结束时间", "name": "signEndTime", "id": "u:b18b31bfe5e3", "mode": "horizontal", "size": "lg", "required": true, "inputFormat": "YYYYMMDDHHmmss", "placeholder": "请选择日期以及时间", "format": "YYYYMMDDHHmmss", "minDate": "", "maxDate": "", "value": "${config.signEndTime}"}, {"type": "input-datetime", "label": "比赛开始时间", "name": "competitionStartTime", "id": "u:eed042727e38", "inputFormat": "YYYYMMDDHHmmss", "placeholder": "请选择日期以及时间", "format": "YYYYMMDDHHmmss", "minDate": "", "maxDate": "", "value": "${config.competitionStartTime}", "required": true, "mode": "horizontal", "size": "lg"}, {"type": "input-datetime", "label": "比赛结束时间", "name": "competitionEndTime", "id": "u:c028ce71c06c", "mode": "horizontal", "size": "lg", "inputFormat": "YYYYMMDDHHmmss", "placeholder": "请选择日期以及时间", "format": "YYYYMMDDHHmmss", "minDate": "", "maxDate": "", "value": "${config.competitionEndTime}", "required": true}, {"type": "input-datetime", "label": "领奖截止时间", "name": "receiveEndTime", "id": "u:288f3fe4e758", "mode": "horizontal", "inputFormat": "YYYYMMDDHHmmss", "placeholder": "请选择日期以及时间", "format": "YYYYMMDDHHmmss", "minDate": "", "maxDate": "", "value": "${config.receiveEndTime}", "required": true, "size": "lg"}, {"type": "input-number", "label": "报名累加基数", "name": "signBaseNum", "keyboard": true, "id": "u:c178f0720b27", "step": 1, "mode": "horizontal", "required": true, "value": "${config.signBaseNum}"}, {"type": "input-number", "label": "报名系数", "name": "signRatio", "keyboard": true, "id": "u:0edf1a4a03b7", "step": 1, "mode": "horizontal", "required": true, "value": "${config.signRatio}"}, {"type": "input-url", "label": "比赛规则链接", "name": "ruleUrl", "id": "u:55228a554002", "mode": "horizontal", "required": true, "validations": {}, "validationErrors": {}, "value": "${config.ruleUrl}"}, {"type": "combo", "label": "投教配置", "name": "advertiseConfig", "value": "${config.advertiseConfig}", "multiple": true, "addable": true, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:607e11369258"}, "items": [{"type": "input-text", "name": "adTitle", "placeholder": "文本", "id": "u:5c9a14eddbaa", "label": "投教标题", "required": true}, {"type": "input-url", "label": "查看更多链接", "name": "moreUrl", "props": {}, "id": "u:5dfe38338d5f", "required": true}, {"type": "combo", "label": "图片配置（需配置4条）", "name": "cardList", "value": "cardList", "multiple": true, "addable": true, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:70615ba9a1ef"}, "items": [{"type": "input-image", "name": "adImage", "id": "u:c25fc81bbdcd", "label": "投教图片(1.75:1)", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "limit": {"aspectRatio": "1.75"}, "fixedSize": false, "mode": "horizontal", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    value: _url\r\n  }\r\n}", "method": "post", "requestAdaptor": "", "messages": {}}, "required": true}, {"type": "input-url", "label": "图片跳转链接", "name": "adJumpUrl", "props": {}, "id": "u:802b739eacd5", "required": true}], "props": {}, "id": "u:f235dd810ff8", "minLength": 4, "maxLength": 4, "strictMode": true, "syncFields": []}], "props": {}, "id": "u:543c353ee0da", "syncFields": [], "multiLine": true, "strictMode": true, "maxLength": 1, "minLength": 1}], "title": "大赛相关信息", "mode": "normal", "dsType": "api", "feat": "Edit", "actions": [{"type": "submit", "label": "保存", "id": "u:89b803bb7c55", "primary": true}], "api": {"method": "post", "url": "/common_config/hash_data_save", "data": {"&": "$$", "activityId": "${activityId}"}, "requestAdaptor": "const {\n activityName,\n  newActivityId,\n  activityId,\n  securityId,\n  signStartTime,\n  signEndTime,\n  competitionStartTime,\n  competitionEndTime,\n  receiveEndTime,\n  signBaseNum,\n  signRatio,\n  ruleUrl,\n  advertiseConfig\n} = api.body;\n\nconst targetItem = {\n  activityName,\n  activityId: newActivityId,\n  securityId,\n  signStartTime,\n  signEndTime,\n  competitionStartTime,\n  competitionEndTime,\n  receiveEndTime,\n  signBaseNum,\n  signRatio,\n  ruleUrl,\n  advertiseConfig\n};\n\nreturn {\n  ...api,\n  data: {\n    key: `etf_competition_activity_config_${activityId}`,\n    propName: 'info',\n    value: JSON.stringify(targetItem)\n  }\n}", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}", "messages": {}, "dataType": "form"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "info"}}, "submitText": "保存"}], "title": "ETF大赛信息配置"}