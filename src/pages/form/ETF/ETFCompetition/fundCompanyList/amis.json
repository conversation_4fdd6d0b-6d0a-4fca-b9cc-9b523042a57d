{"type": "page", "title": "ETF大赛基金公司配置", "body": [{"type": "form", "title": "基金公司列表", "body": [{"type": "combo", "label": "", "name": "companyList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:2a061f1c67be"}, "items": [{"type": "input-text", "name": "companyName", "placeholder": "", "id": "u:09d96b0426fa", "mode": "horizontal", "label": "基金公司名称", "required": true}, {"type": "input-text", "label": "简介", "name": "companyDesc", "id": "u:972a490bf4d7", "mode": "horizontal", "validations": {"maxLength": 26}, "required": true}, {"type": "input-number", "label": "排序权重", "name": "sortWeight", "keyboard": true, "id": "u:f4ce68ffcc1c", "step": 1, "mode": "horizontal", "required": true, "value": 0}, {"type": "input-image", "label": "基金公司Logo（144*144）", "name": "companyLogo", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:89ae7c3dec92", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "mode": "horizontal", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "required": true, "limit": {"height": 144, "width": 144}}, {"type": "combo", "label": "参赛ETF", "name": "etfList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:c43b21d73693"}, "items": [{"type": "input-text", "name": "fundCode", "placeholder": "", "id": "u:09d96b0426fa", "label": "基金CODE", "mode": "horizontal", "size": "full", "required": true}, {"type": "input-text", "label": "市场ID", "name": "marketId", "id": "u:9986897e6f6e", "required": true}, {"type": "input-text", "label": "交易code", "name": "tradeCode", "required": true}, {"required": true, "type": "select", "label": "类别", "name": "fundType", "options": [{"label": "行业", "value": "industry"}, {"label": "宽基", "value": "index"}, {"label": "T+0", "value": "qdii"}]}], "id": "u:eafe008578b2", "syncFields": [], "required": true, "strictMode": true, "tabsMode": false, "mode": "horizontal", "size": "full"}], "id": "u:ff68f4d4be76", "strictMode": true, "syncFields": [], "multiLine": true, "value": "${config}"}], "id": "u:462957127cee", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "companyList", "value": "${ENCODEJSON(companyList)}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "submitText": "保存", "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "companyList"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}}], "regions": ["body", "header"], "id": "u:fa7b4e843698"}