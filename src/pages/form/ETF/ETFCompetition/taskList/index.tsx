import React, { useEffect } from 'react';
import 'moment/locale/zh-cn';
import amisJSON from './amis.json';
import amisEnv from 'functions/amisEnv';
import { formatUrlParams} from '../../../abtest/util';
import { defaultActivityId } from '../index';
import api from 'api';

export default function() {
    let amisScoped:any;
    let voucherList:any;
    let activityId:any;
    activityId = formatUrlParams()?.activityId ? formatUrlParams().activityId: defaultActivityId
    const init = () => {
      api.fetchHash({
        'key': 'etf_competition_activity_config_' + activityId,
        'propName': 'voucherList'
      }).then((res:any) => {
        if (res && res.statusCode === 200 && res.code === '0000') {
          voucherList = JSON.parse(res.data);
        }
        let amis = amisRequire('amis/embed');
        amisScoped = amis.embed('#etfTaskList', {
          ...amisJSON,
          data:{
            activityId: activityId,
            voucherList: voucherList
          }}, {}, amisEnv());
        })
    }
    useEffect(() => {
      init();
      return () => {
        amisScoped.unmount();
      }
    }, [])
    
    return (
      <div id='etfTaskList'></div>
    )
}

