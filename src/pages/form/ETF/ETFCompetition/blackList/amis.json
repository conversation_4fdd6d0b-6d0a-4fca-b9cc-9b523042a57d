{"type": "page", "id": "u:6e0f950b8676", "body": [{"type": "crud", "syncLocation": false, "api": {"method": "get", "url": "/common_config/hash_data_get_all", "requestAdaptor": "", "adaptor": "let blacklist = payload.data;\nif (!blacklist) {\n  blacklist = {};\n}\n\nlet items = Object.keys(blacklist).map(key => JSON.parse(blacklist[key]));\nconsole.log('blacklist', blacklist, items);\nreturn {\n  data: {\n    items,\n  },\n};", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}_blacklist"}}, "columns": [{"name": "groupName", "label": "用户群名称", "type": "text", "id": "u:a072d1fa0ac8"}, {"label": "人数", "type": "tpl", "id": "u:bfeeb53bd8e0", "tpl": "${userIdList.length}"}, {"type": "operation", "label": "操作", "buttons": [{"type": "button", "label": "删除", "actionType": "ajax", "level": "link", "className": "text-danger", "confirmText": "确定要删除？", "api": {"method": "post", "url": "/common_config/hash_data_del", "interval": 0, "requestAdaptor": "const { groupName, activityId } = api.body;\n\nconsole.log('delete')\nreturn {\n    ...api,\n    data: {\n        key: `etf_competition_activity_config_${activityId}_blacklist`,\n        propName: groupName\n    }\n}", "adaptor": "const isSuccess = payload.code === '0000'\nreturn {\n  code: isSuccess ? 0 : 1,\n  msg: isSuccess ? '成功' : '失败'\n}", "messages": {}, "dataType": "form-data", "data": {"&": "$$", "activityId": "${activityId}"}}, "id": "u:7ec60a5f7b57", "editorSetting": {"behavior": "delete"}}, {"id": "u:dc91975aaa6b", "type": "button", "level": "link", "label": "下载名单", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "url", "args": {"url": "${excelUrl}"}}]}}}], "id": "u:64c5b6ecb148"}], "bulkActions": [], "itemActions": [], "features": ["create", "delete"], "headerToolbar": [{"label": "新增", "type": "button", "actionType": "dialog", "level": "primary", "dialog": {"title": "新增", "body": [{"type": "form", "api": {"method": "post", "url": "/common_config/hash_data_save", "requestAdaptor": "const { activityId, groupName, userIdList, excelUrl, items } = api.body\nconsole.log('add', api.body);\nif (items.find(item => item.groupName === groupName)) {\n  throw new Error('用户群名称重复');\n}\n\nreturn {\n  ...api,\n  data: {\n    key: `etf_competition_activity_config_${activityId}_blacklist`,\n    propName: groupName,\n    value: JSON.stringify({\n      groupName,\n      userIdList,\n      excelUrl\n    })\n  }\n}", "adaptor": "const isSuccess = payload.code === '0000'\nreturn {\n  code: isSuccess ? 0 : 1,\n  msg: isSuccess ? '成功' : '失败'\n}", "messages": {}, "dataType": "form-data", "data": {"&": "$$", "activityId": "${activityId}", "items": "${items}"}}, "body": [{"type": "input-text", "name": "groupName", "label": "用户群名称", "id": "u:d28cf723d904", "required": true}, {"type": "button-group", "id": "u:cac85ad462f6", "buttons": [{"type": "input-excel", "label": "用户群名单", "name": "userIdList", "id": "u:d1f13f45d90f", "parseMode": "object", "inline": false, "mode": "inline", "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "u:613629fdf121", "groupType": "component", "actionType": "setValue", "args": {"value": "${JOIN(ARRAYFILTER(ARRAYMAP(event.data.value, item => TRIM(item.custId)), item => LEN(item) > 0),\"\\n\")}"}}]}}, "required": true}, {"label": "下载模板", "type": "button", "level": "primary", "className": "mr-5", "id": "u:5feee631463b", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "url", "args": {"blank": true, "url": "https://o.thsi.cn/thsjj-yytj-docker-cn.yytj-resource/************************************.xlsx"}}]}}}]}, {"type": "input-file", "label": "保存excel", "name": "excelUrl", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:c25fc81bbdcd", "accept": ".xlsx", "multiple": false, "hideUploadButton": false, "fixedSize": false, "mode": "horizontal", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    value: _url\r\n  }\r\n}", "method": "post", "requestAdaptor": "", "messages": {}}, "required": true}], "id": "u:0b7f4c9825a2", "actions": [{"type": "submit", "label": "提交", "primary": true}], "feat": "Insert", "dsType": "api"}], "id": "u:c34a3d762f29", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:ff4c08507510"}, {"type": "button", "actionType": "confirm", "label": "确定", "primary": true, "id": "u:4e184e38324b"}]}, "id": "u:99ddad6fdb65"}, "bulkActions"], "id": "u:59f88830af40", "perPageAvailable": [10], "messages": {}}], "asideResizor": false, "pullRefresh": {"disabled": true}, "title": "黑名单配置"}