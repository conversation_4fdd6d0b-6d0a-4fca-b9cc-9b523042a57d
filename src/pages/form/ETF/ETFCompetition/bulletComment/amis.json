{"type": "page", "regions": ["body", "header"], "id": "u:9e10aa293e97", "body": [{"id": "u:043c6826ba32", "type": "form", "body": [{"type": "input-text", "label": "跳转链接", "name": "jumpUrl", "id": "u:a983120c5792", "mode": "horizontal", "required": true}, {"type": "combo", "label": "弹幕", "name": "commentList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:cd3d7f176276"}, "items": [{"type": "input-text", "name": "comment", "placeholder": "文本", "id": "u:ca5980e48712", "required": true}, {"type": "input-image", "label": "头像", "name": "avatar", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:c25fc81bbdcd", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "mode": "horizontal", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    value: _url\r\n  }\r\n}", "method": "post", "requestAdaptor": "", "messages": {}}, "required": true}], "id": "u:1ffa28d1fc50", "syncFields": [], "required": true}], "title": "圈子", "submitText": "保存", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"&": "$$", "activityId": "${activityId}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}", "requestAdaptor": "const { title, desc, jumpUrl, commentList, activityId } = api.body;\nconst res = {\n  title,\n  desc,\n  jumpUrl,\n  commentList\n}\nreturn {\n  ...api,\n  data: {\n    key: `etf_competition_activity_config_${activityId}`,\n    propName: 'bulletComment',\n    value: JSON.stringify(res)\n  }\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "bulletComment"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: config\r\n};"}, "actions": [{"type": "submit", "label": "保存", "primary": true, "id": "u:d31da8cc36a9"}], "feat": "Edit", "dsType": "api"}], "title": "ETF大赛圈子配置"}