{"type": "page", "id": "u:6d39f81e173d", "body": [{"type": "form", "id": "u:a0b0d052e284", "body": [{"type": "combo", "label": "玩法顺序Ab", "name": "gameOrderConfig", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm"}, "items": [{"type": "select", "name": "userIdEndList", "placeholder": "选项", "options": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}, {"label": "9", "value": "9"}, {"label": "0", "value": "0"}], "id": "u:5d24df4e26db", "multiple": true, "label": "用户尾号", "required": true, "value": "", "checkAll": false, "clearable": true}, {"type": "combo", "id": "u:696e1aa032e5", "label": "玩法顺序排列", "name": "order", "multiple": true, "addable": false, "removable": false, "removableMode": "icon", "items": [{"type": "select", "id": "u:20a3e018ad83", "name": "type", "placeholder": "选项", "options": [{"label": "ETF赛马", "value": "horse"}, {"label": "ETF涨跌连猜", "value": "link"}, {"label": "ETF轮盘竞猜", "value": "roulette"}], "multiple": false, "disabled": true}], "props": {}, "syncFields": [], "value": [{"type": "horse"}, {"type": "link"}, {"type": "roulette"}], "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:0bd745978ef5"}, "required": true, "draggable": true}], "props": {}, "id": "u:80c0fbfc698b", "strictMode": true, "syncFields": [], "required": true}, {"type": "combo", "label": "ETF列表", "name": "ETFList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:a395894649f1"}, "items": [{"type": "input-text", "name": "ETFCode", "placeholder": "", "id": "u:4bcd4a8679d5", "label": "ETF代码"}, {"type": "input-text", "label": "市场代码", "name": "marketCode", "id": "u:fdfc18156ea9", "multiple": false}], "props": {}, "id": "u:19b03d1f099b", "strictMode": true, "syncFields": [], "required": true, "draggable": true}, {"type": "input-number", "label": "首日底池数量", "name": "firstDayIntegral", "props": {}, "id": "u:480ffd0fce62", "required": true}, {"type": "input-text", "label": "底池败方抽取比例", "name": "proportion", "props": {}, "id": "u:4da611a531c1", "required": true}, {"type": "combo", "label": "底池加码", "name": "addChips", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:4d14f61d069c"}, "items": [{"type": "input-date", "label": "手动底池加码日期", "name": "date", "id": "u:9549248b02bc", "multiple": false}, {"type": "input-number", "name": "amount", "placeholder": "", "id": "u:f2465cf153ca", "label": "底池加码数量", "keyboard": true, "step": 1}], "props": {}, "id": "u:0e487529c0bf", "strictMode": true, "syncFields": []}], "api": {"method": "post", "url": "/common_config/hash_data_save", "messages": {}, "requestAdaptor": "const { gameOrderConfig, ETFList, firstDayIntegral, proportion, addChips, activityId } = api.body\n\nreturn {\n  ...api,\n  data: {\n    key: `etf_competition_activity_config_${activityId}`,\n    propName: \"etfGuessGame\",\n    value: JSON.stringify({\n      gameOrderConfig,\n      ETFList,\n      firstDayIntegral,\n      proportion,\n      addChips\n    })\n  }\n}", "adaptor": "const isSuccess = payload.code === '0000'\nreturn {\n  code: isSuccess ? 0 : 1,\n  msg: isSuccess ? '成功' : '失败'\n}", "data": {"&": "$$", "activityId": "${activityId}"}, "dataType": "form-data"}, "title": "", "mode": "horizontal", "dsType": "api", "feat": "Edit", "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:a0b0d052e284"}]}}, "id": "u:7bfc6fbb26a9", "level": "primary"}], "resetAfterSubmit": false, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "adaptor": "let config = payload.data;\r\nif (!config) {\r\n  config = {};\r\n} else {\r\n  config = JSON.parse(config);\r\n}\r\nconsole.log('config', config);\r\nreturn {\r\n  data: {\r\n    ...config,\r\n  },\r\n};", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "etfGuessGame"}, "requestAdaptor": ""}}], "regions": ["body", "header"], "title": "每日一猜配置"}