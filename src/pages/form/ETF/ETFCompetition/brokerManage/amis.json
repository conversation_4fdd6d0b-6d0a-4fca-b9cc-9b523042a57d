{"type": "page", "id": "u:6503fa8e89be", "body": [{"type": "crud", "id": "u:cf2a0f418d27", "api": {"method": "get", "url": "/common_config/hash_data_get_all?key=brokerManage", "messages": {}, "requestAdaptor": "", "adaptor": "let itemsObj = payload.data;\nlet items = [];\n\n// 如果没有数据，则为空数组\nif (!items) {\n  items = {};\n}\n\nitems = Object.keys(itemsObj).map(key => {\n  return JSON.parse(itemsObj[key]);\n})\n\n\nreturn {\n  status: payload.code === '0000' ? '0' : '1',\n  data: {\n    items\n  }\n}"}, "syncLocation": false, "columns": [{"name": "brokerId", "label": "券商id", "type": "text", "id": "u:731d8803887c"}, {"name": "brokerName", "label": "券商名称", "type": "text", "id": "u:cfa8786d32d3"}, {"type": "image", "label": "券商图标", "name": "brokerImage", "id": "u:d9b411e60046"}, {"type": "text", "label": "简介", "name": "brokerDesc", "id": "u:270a8d6351c9"}, {"type": "text", "label": "创建人", "name": "creater", "id": "u:b81f9dff14d1"}, {"type": "operation", "label": "操作", "buttons": [{"type": "button", "label": "删除", "actionType": "ajax", "level": "link", "className": "text-danger", "confirmText": "确定要删除？", "api": {"method": "post", "url": "/common_config/hash_data_del", "interval": 0, "requestAdaptor": "const { brokerId } = api.body;\n\nconsole.log('delete')\nreturn {\n    ...api,\n    data: {\n        key: 'brokerManage',\n        propName: brokerId\n    }\n}", "adaptor": "const isSuccess = payload.code === '0000'\nreturn {\n  code: isSuccess ? 0 : 1,\n  msg: isSuccess ? '成功' : '失败'\n}", "messages": {}, "dataType": "form-data", "data": {"&": "$$"}}, "editorSetting": {"behavior": "delete"}, "id": "u:7ec60a5f7b57"}, {"label": "编辑", "type": "button", "actionType": "dialog", "level": "link", "editorSetting": {"behavior": "update"}, "dialog": {"title": "编辑", "size": "lg", "body": [{"type": "form", "horizontal": {"left": 3, "right": 7}, "api": {"method": "post", "url": "/common_config/hash_data_save", "data": {"&": "$$", "items": "${items}"}, "requestAdaptor": "const { activityId, items, brokerDesc, brokerId, brokerImage, brokerName, decryption, encryption, isShowAccount, isShowPosition, isNeedThsDevelopBindPage, isNeedThsDevelopHoldAdjustPage, brokerPingYinName} = api.body;\nconst creater = localStorage.getItem('name') || '';\nconst editItem = {\n  brokerDesc,\n  brokerId,\n  brokerImage,\n  brokerName,\n  decryption,\n  encryption,\n  isShowAccount,\n  isShowPosition,\n  creater,\n  isNeedThsDevelopBindPage,\n  isNeedThsDevelopHoldAdjustPage,\n  brokerPingYinName\n}\n\nif (items.find(item => item.brokerPingYinName === brokerPingYinName && item.brokerId !== brokerId)) {\n  throw new Error('券商拼音名称不能重复重复');\n}\n\nreturn {\n  ...api,\n  data: {\n    key: 'brokerManage',\n    propName: brokerId,\n    value: JSON.stringify(editItem)\n  }\n}", "adaptor": "const isSuccess = payload.code === '0000'\nreturn {\n  code: isSuccess ? 0 : 1,\n  msg: isSuccess ? '成功' : '失败'\n}", "messages": {}, "dataType": "form-data"}, "body": [{"name": "brokerId", "label": "券商id", "type": "input-text", "id": "u:ebb2631e6251", "required": true, "disabled": true}, {"name": "brokerName", "label": "券商名称", "type": "input-text", "id": "u:e595c20433f8", "required": true}, {"name": "brokerPingYinName", "label": "券商拼音唯一名称（例如：xingye）", "type": "input-text", "id": "u:e595c20433f9", "required": true, "disabledOn": "${brokerPingYinName ? true : false}"}, {"type": "input-image", "label": "券商图标", "name": "brokerImage", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:c25fc81bbdcd", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "mode": "horizontal", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    value: _url\r\n  }\r\n}", "method": "post", "requestAdaptor": "", "messages": {}}, "required": true}, {"type": "input-text", "label": "简介", "name": "brokerDesc", "props": {}, "id": "u:d41beca475a9", "required": true}, {"type": "radios", "label": "是否需要同花顺开发绑定页面", "name": "isNeedThsDevelopBindPage", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "props": {}, "id": "u:f91a74095d15", "value": "", "required": true}, {"type": "radios", "label": "是否需要同花顺开发持仓调仓页面", "name": "isNeedThsDevelopHoldAdjustPage", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "props": {}, "id": "u:f91a74095d17", "value": "", "required": true}, {"type": "radios", "label": "是否支持展示脱敏账号", "name": "isShowAccount", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "props": {}, "id": "u:f91a74095d16", "value": "", "required": true}, {"type": "radios", "label": "是否允许查看牛人调仓", "name": "isShowPosition", "options": [{"label": "是", "value": "1"}, {"label": "否", "value": "0"}, {"label": "用户授权", "value": "2"}], "props": {}, "id": "u:89ef3ca744ce", "value": "", "required": true}, {"type": "input-text", "label": "加密器", "name": "encryption", "props": {}, "id": "u:f038f333c353", "required": true}, {"type": "input-text", "label": "解密器", "name": "decryption", "props": {}, "id": "u:aa70a39d5eaf", "required": true}], "id": "u:d59ef448492f", "actions": [{"type": "submit", "label": "提交", "primary": true, "id": "u:af6c1883f32f"}], "feat": "Insert", "dsType": "api"}], "id": "u:678ef419d67e", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:387d183150ba"}, {"type": "button", "actionType": "confirm", "label": "确定", "primary": true, "id": "u:7be5d5895ac5"}]}, "id": "u:f9975f1e4912"}], "id": "u:78b0fbbbd3a1"}], "bulkActions": [], "itemActions": [], "headerToolbar": [{"label": "新增", "type": "button", "actionType": "dialog", "level": "primary", "editorSetting": {"behavior": "create"}, "dialog": {"title": "新增", "size": "lg", "body": [{"type": "form", "horizontal": {"left": 3, "right": 7}, "api": {"method": "post", "url": "/common_config/hash_data_save", "data": {"items": "${items}", "&": "$$"}, "requestAdaptor": "const { activityId, items, brokerDesc, brokerId, brokerImage, brokerName, decryption, encryption, isShowAccount, isShowPosition, isNeedThsDevelopBindPage, isNeedThsDevelopHoldAdjustPage, brokerPingYinName } = api.body;\nconst creater = localStorage.getItem('name') || '';\nconst addItem = {\n  brokerDesc,\n  brokerId,\n  brokerImage,\n  brokerName,\n  decryption,\n  encryption,\n  isShowAccount,\n  isShowPosition,\n  creater,\n  isNeedThsDevelopBindPage,\n  isNeedThsDevelopHoldAdjustPage,\n  brokerPingYinName\n}\n\nif (items.find(item => item.brokerId === brokerId)) {\n  throw new Error('券商id不能重复');\n}\n\nif (items.find(item => item.brokerName === brokerName)) {\n  throw new Error('券商名称不能重复');\n}\n\nif (items.find(item => item.brokerPingYinName === brokerPingYinName)) {\n  throw new Error('券商拼音名称不能重复重复');\n}\n\nreturn {\n  ...api,\n  data: {\n    key: 'brokerManage',\n    propName: brokerId,\n    value: JSON.stringify(addItem)\n  }\n}", "adaptor": "const isSuccess = payload.code === '0000'\nreturn {\n  code: isSuccess ? 0 : 1,\n  msg: isSuccess ? '成功' : '失败'\n}", "messages": {}, "dataType": "form-data"}, "body": [{"type": "input-text", "name": "brokerId", "label": "券商id", "id": "u:ebb2631e6251", "required": true}, {"type": "input-text", "name": "brokerName", "label": "券商名称", "id": "u:e595c20433f8", "required": true}, {"name": "brokerPingYinName", "label": "券商拼音名称（例如：xingye）", "type": "input-text", "id": "u:e595c20423f9", "required": true}, {"type": "input-image", "label": "券商图标", "name": "brokerImage", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:c25fc81bbdcd", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "mode": "horizontal", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    value: _url\r\n  }\r\n}", "method": "post", "requestAdaptor": "", "messages": {}}, "required": true}, {"type": "input-text", "label": "简介", "name": "brokerDesc", "props": {}, "id": "u:d41beca475a9", "required": true}, {"type": "radios", "label": "是否需要同花顺开发绑定页面", "name": "isNeedThsDevelopBindPage", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "props": {}, "id": "u:f91a73095d15", "value": "", "required": true}, {"type": "radios", "label": "是否需要同花顺开发持仓调仓页面", "name": "isNeedThsDevelopHoldAdjustPage", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "props": {}, "id": "u:f91a74094d17", "value": "", "required": true}, {"type": "radios", "label": "是否支持展示脱敏账号", "name": "isShowAccount", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "props": {}, "id": "u:f91a74095d16", "value": "", "required": true}, {"type": "radios", "label": "是否允许查看牛人调仓", "name": "isShowPosition", "options": [{"label": "是", "value": "1"}, {"label": "否", "value": "0"}, {"label": "用户授权", "value": "2"}], "props": {}, "id": "u:89ef3ca744ce", "value": "", "required": true}, {"type": "input-text", "label": "加密器", "name": "encryption", "props": {}, "id": "u:f038f333c353", "required": true}, {"type": "input-text", "label": "解密器", "name": "decryption", "props": {}, "id": "u:aa70a39d5eaf", "required": true}], "id": "u:d59ef448492f", "actions": [{"type": "submit", "label": "提交", "primary": true, "id": "u:af6c1883f32f"}], "feat": "Insert", "dsType": "api", "rules": []}], "id": "u:3bfa7b10a5a2", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:a8c78e3cc792"}, {"type": "button", "actionType": "confirm", "label": "确定", "primary": true, "id": "u:c92d1669db0c"}]}, "id": "u:bb807641615c"}, "bulkActions"], "perPageAvailable": [10], "messages": {}, "primaryField": "broker_id"}], "title": "券商管理", "asideResizor": false}