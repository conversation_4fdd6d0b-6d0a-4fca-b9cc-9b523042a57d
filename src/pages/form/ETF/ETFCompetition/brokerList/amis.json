{"type": "page", "id": "u:6e0f950b8676", "body": [{"type": "form", "id": "u:a0b0d052e284", "body": [{"type": "checkboxes", "id": "u:c0614506f354", "label": "复选框", "name": "brokerList", "multiple": true, "props": {}, "checkAll": true, "joinValues": false, "defaultCheckAll": false, "source": "${brokerManage}", "labelField": "brokerName", "valueField": "brokerId", "onEvent": {"change": {"weight": 0, "actions": [{"ignoreError": false, "script": "const data = event.data;\nconst { brokerList, brokerInfo } = data;\n\n\nconst newBrokerInfo = brokerList.map(brokerItem => {\n  const brokerInfoItem = brokerInfo.find(item => item.brokerId === brokerItem.brokerId);\n  return {\n    ...brokerItem,\n    ...brokerInfoItem\n  }\n})\n\n\nevent.setData({\n  ...event.data,\n  newBrokerInfo\n})", "actionType": "custom"}, {"componentId": "u:a0b0d052e284", "groupType": "component", "actionType": "setValue", "args": {"value": {"brokerInfo": "${newBrokerInfo}"}}}]}}}, {"type": "combo", "id": "u:9d95f36a7179", "label": "券商信息", "name": "brokerInfo", "multiple": true, "addable": false, "removable": false, "removableMode": "icon", "items": [{"type": "input-text", "props": {}, "id": "u:9e72cb21869d", "disabled": true, "label": "券商名称", "name": "brokerName"}, {"class": "input", "type": "input-url", "props": {}, "id": "u:ad3e196b225e", "label": "绑定链接：", "name": "bindUrl", "validations": {}, "validationErrors": {}, "required": true}, {"class": "input", "type": "input-url", "props": {}, "id": "u:ad3e196b225e", "label": "自己持仓详情页面：", "name": "holdUrl", "validations": {}, "validationErrors": {}, "required": true}, {"class": "input", "type": "input-url", "props": {}, "id": "u:ad3e196b225e", "label": "他人持仓详情页面：", "name": "otherHoldUrl", "validations": {}, "validationErrors": {}, "required": true}, {"class": "input", "type": "input-url", "props": {}, "id": "u:ad3e196b225e", "label": "调仓详情页面：", "name": "adjustUrl", "validations": {}, "validationErrors": {}, "required": true}, {"type": "input-text", "label": "顺序：", "name": "order", "props": {}, "id": "u:cc653cfcd06a", "required": true, "value": 0}], "props": {}, "multiLine": false, "strictMode": true, "syncFields": [], "size": "full"}], "api": {"method": "post", "url": "/common_config/hash_data_save", "messages": {}, "requestAdaptor": "const { brokerInfo, brokerList, activityId } = api.body\n\nreturn {\n  ...api,\n  data: {\n    key: `etf_competition_activity_config_${activityId}`,\n    propName: \"brokerSetting\",\n    value: JSON.stringify({\n      brokerInfo,\n      brokerList\n    })\n  }\n}", "adaptor": "const isSuccess = payload.code === '0000'\nreturn {\n  code: isSuccess ? 0 : 1,\n  msg: isSuccess ? '成功' : '失败'\n}", "data": {"&": "$$", "activityId": "${activityId}"}, "dataType": "form-data"}, "title": "", "mode": "horizontal", "dsType": "api", "feat": "Edit", "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:a0b0d052e284"}]}}, "id": "u:7bfc6fbb26a9", "level": "primary"}], "resetAfterSubmit": true, "initApi": "", "onEvent": {"submitSucc": {"weight": 0, "actions": [{"actionType": "refresh", "ignoreError": false, "args": {}}]}}}], "onEvent": {"init": {"weight": 0, "actions": [{"ignoreError": false, "outputVar": "brokerSetting", "actionType": "ajax", "options": {}, "args": {"api": {"url": "/common_config/hash_data_get?key=etf_competition_activity_config_${activityId}&propName=brokerSetting", "method": "get", "requestAdaptor": "", "adaptor": "let brokerSetting = payload.data;\n\n// 如果没有数据，则为空数组\nif (!brokerSetting) {\n  brokerSetting = {\n    brokerList: [],\n    brokerInfo: []\n  };\n} else {\n  brokerSetting = JSON.parse(brokerSetting);\n}\nconsole.log('brokerSetting', brokerSetting, payload.data)\nreturn {\n  status: payload.code === '0000' ? '0' : '1',\n  data: {\n    brokerSetting\n  }\n}", "messages": {}}}}, {"ignoreError": false, "outputVar": "brokerManage", "actionType": "ajax", "options": {}, "args": {"api": {"url": "/common_config/hash_data_get_all?key=brokerManage", "method": "get", "requestAdaptor": "", "adaptor": "let brokerManage = payload.data;\nlet brokerList = [];\n\n// 如果没有数据，则为空数组\nif (!brokerManage) {\n  brokerManage = {};\n}\nbrokerList = Object.keys(brokerManage).map(key => {\n  return JSON.parse(brokerManage[key]);\n})\nconsole.log('brokerManage', brokerList);\nreturn {\n  status: payload.code === '0000' ? '0' : '1',\n  data: {\n    brokerManage: brokerList\n  }\n}", "messages": {}}}}, {"ignoreError": false, "actionType": "custom", "script": "const { brokerManage } = event.data.brokerManage;\n;\nconst { brokerSetting } = event.data.brokerSetting;\nconst { brokerInfo, brokerList } = brokerSetting;\n\nconsole.log('brokerSetting', brokerSetting);\n\nconst _brokerInfo = brokerInfo.filter(brokerInfoItem => brokerManage.find(brokerManageItem => brokerInfoItem.brokerId === brokerManageItem.brokerId));\n\nconst _brokerList = brokerList.filter(brokerListItem => brokerManage.find(brokerManageItem => brokerListItem.brokerId === brokerManageItem.brokerId));\n\nevent.setData({\n  ...event.data,\n  brokerList: _brokerList,\n  brokerInfo: _brokerInfo,\n  brokerManage\n})"}, {"actionType": "setValue", "groupType": "component", "componentId": "u:6e0f950b8676", "args": {"value": {"brokerInfo": "${brokerInfo}", "brokerManage": "${brokerManage}", "brokerList": "${brokerList}"}}}]}}, "asideResizor": false, "pullRefresh": {"disabled": true}, "regions": ["body", "header"], "css": {".input": "width: 200px"}}