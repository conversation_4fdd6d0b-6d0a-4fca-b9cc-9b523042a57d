{"type": "page", "id": "u:6d39f81e173d", "body": [{"type": "form", "api": {"method": "post", "url": "/common_config/hash_data_save", "requestAdaptor": "", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "etfPool", "value": "{\"listOrder\": ${ENCODEJSON(listOrder)}, \"bannerImage\": \"${bannerImage}\", \"jumpUrl\": \"${jumpUrl}\", \"noSelfSelectedRecommend\": ${ENCODEJSON(noSelfSelectedRecommend)}, \"hasSelfSelectedRecommend\": ${ENCODEJSON(hasSelfSelectedRecommend)}}"}, "dataType": "form"}, "id": "u:3dc8398b9a81", "body": [{"type": "input-image", "id": "u:e5cf95b59c5b", "label": "banner图（686*172）", "name": "bannerImage", "value": "${config.bannerImage}", "accept": ".jpeg, .jpg, .png", "uploadType": "fileReceptor", "proxy": true, "multiple": false, "hideUploadButton": false, "autoUpload": true, "fixedSize": false, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"height": 172, "width": 686}}, {"type": "input-text", "label": "跳转链接", "name": "jumpUrl", "id": "u:24bee6519f5b", "value": "${config.jumpUrl}"}, {"type": "combo", "label": "ETF榜单顺序", "name": "listOrder", "multiple": true, "addable": false, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:ef5024af08cc"}, "items": [{"type": "select", "label": "", "name": "type", "options": [{"label": "跨境T+0市场", "value": "T+0"}, {"label": "行业主题", "value": "theme"}, {"label": "宽基指数", "value": "index"}], "id": "u:aa4154def0ec", "multiple": false, "disabled": true, "value": ""}], "id": "u:db22bc56af11", "syncFields": [], "draggable": true, "required": true, "strictMode": true, "value": "${config.listOrder || [{\"type\":\"T+0\"},{\"type\":\"theme\"},{\"type\":\"index\"}]}"}, {"type": "combo", "label": "无自选推荐", "name": "noSelfSelectedRecommend", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:dc20a72f231c"}, "items": [{"type": "input-text", "name": "title", "placeholder": "文本", "id": "u:8a0533b1331d", "label": "标题", "required": true}, {"type": "combo", "label": "推荐", "name": "recommendList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:ec87db7b15ea"}, "items": [{"type": "input-text", "name": "title", "placeholder": "", "id": "u:8a0533b1331d", "label": "主标题", "required": true, "mode": "horizontal"}, {"type": "input-text", "label": "副标题", "name": "subTitle", "id": "u:abc8532c3a7c", "required": true, "mode": "horizontal"}, {"type": "input-text", "label": "ETF代码", "name": "fundCode", "id": "u:63c1d5909dc3", "mode": "horizontal", "required": true}, {"type": "input-text", "label": "市场代码", "name": "marketId", "id": "u:63c1d5909dc4", "mode": "horizontal", "required": true}], "id": "u:7487717033f8", "strictMode": true, "syncFields": [], "multiLine": true, "required": true, "minLength": 3, "maxLength": 3}], "id": "u:b861869b1ba5", "syncFields": [], "strictMode": true, "required": true, "value": "${config.noSelfSelectedRecommend}"}, {"type": "combo", "label": "有自选推荐", "name": "hasSelfSelectedRecommend", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:e490f592d9c6"}, "items": [{"type": "input-text", "name": "topContent", "placeholder": "", "id": "u:8a0533b1331d", "label": "顶部文字链文案", "mode": "horizontal"}, {"type": "input-text", "label": "跳转链接", "name": "jumpUrl", "id": "u:75c9675a82e4", "mode": "horizontal"}, {"type": "input-text", "label": "角标文案", "name": "conerMarkContect", "id": "u:75c9675a82e5", "mode": "horizontal"}, {"type": "combo", "label": "推荐", "name": "recommendList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:76c3c8a2800d"}, "items": [{"type": "input-text", "name": "fundCode", "placeholder": "", "id": "u:8a0533b1331d", "label": "ETF代码", "mode": "horizontal", "required": true}, {"type": "input-text", "name": "marketId", "placeholder": "", "id": "u:8a0533b1331e", "label": "市场代码", "mode": "horizontal", "required": true}, {"type": "input-text", "label": "推荐理由", "name": "recommendDesc", "id": "u:bab0420a1cea", "mode": "horizontal", "required": true}], "id": "u:de652907a842", "syncFields": [], "required": true, "strictMode": true, "multiLine": true, "mode": "horizontal"}], "id": "u:81a8fc95cb84", "syncFields": [], "required": true, "strictMode": true, "maxLength": 1, "minLength": 1, "multiLine": true, "value": "${config.hasSelfSelectedRecommend}"}], "title": "比赛ETF池", "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "etfPool"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存", "mode": "horizontal", "actions": [{"type": "submit", "label": "保存", "primary": true, "id": "u:70ba5a77289f"}], "feat": "Edit", "dsType": "api"}], "title": "ETF大赛比赛ETF池配置", "regions": ["body", "header"]}