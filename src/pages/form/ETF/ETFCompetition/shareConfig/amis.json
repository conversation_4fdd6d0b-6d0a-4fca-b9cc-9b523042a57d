{"type": "page", "id": "u:6d39f81e173d", "body": [{"type": "form", "id": "u:1865f6f554a4", "api": {"url": "/common_config/hash_data_save", "method": "post", "adaptor": "const isSuccess = payload.code === '0000'\nreturn {\n  code: isSuccess ? 0 : 1,\n  msg: isSuccess ? '成功' : '失败'\n}", "messages": {}, "data": {"&": "$$", "activityId": "${activityId}"}, "requestAdaptor": "const { shareTitle, shareContent, shareImage, recordImage, recordList, activityId } = api.body\n\nreturn {\n  ...api,\n  data: {\n    key: `etf_competition_activity_config_${activityId}`,\n    propName: \"shareConfig\",\n    value: JSON.stringify({\n      shareTitle,\n      shareContent,\n      shareImage,\n      recordImage,\n      recordList\n    })\n  }\n}", "dataType": "form-data"}, "title": "表单", "mode": "horizontal", "dsType": "api", "feat": "Edit", "body": [{"type": "tpl", "tpl": "页面分享", "inline": true, "wrapperComponent": "h3", "props": {}, "id": "u:1d96a3382e98"}, {"type": "input-text", "label": "分享标题", "name": "shareTitle", "props": {}, "id": "u:548c88cb3975", "required": true}, {"type": "input-text", "label": "分享内容", "name": "shareContent", "props": {}, "id": "u:31190383ee82", "required": true}, {"type": "input-image", "label": "分享图片", "name": "shareImage", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:f913034d9537", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "mode": "horizontal", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    value: _url\r\n  }\r\n}", "method": "post", "requestAdaptor": "", "messages": {}}, "required": true}], "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:1865f6f554a4"}]}}, "level": "primary", "id": "u:0f67b928ed5c"}], "resetAfterSubmit": false, "initApi": {"method": "get", "url": "/common_config/hash_data_get", "requestAdaptor": "", "adaptor": "let config = payload.data;\nif (!config) {\n  config = {};\n} else {\n  config = JSON.parse(config);\n}\nconsole.log('config', config);\nreturn {\n  data: {\n    ...config,\n  },\n};", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "shareConfig"}}}], "title": "分享配置", "regions": ["body", "header"]}