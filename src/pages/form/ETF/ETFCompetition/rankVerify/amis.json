{"type": "page", "regions": ["body", "header"], "id": "u:0b8f9cb113f7", "body": [{"type": "service", "id": "u:126bc6c3bf10", "api": {"method": "get", "url": "/common_config/hash_data_get", "messages": {}, "adaptor": "let config = payload.data;\nif (!config) {\n  config = {};\n} else {\n  config = JSON.parse(config);\n}\nconsole.log('config', config);\nreturn {\n  data: {\n    config,\n  },\n};", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "info"}, "requestAdaptor": "", "interval": 0}, "dsType": "api", "onEvent": {"fetchInited": {"weight": 0, "actions": [{"componentId": "u:0b8f9cb113f7", "ignoreError": false, "actionType": "setValue", "args": {"value": {"id": "${config.activityId}"}}}]}}, "props": {}, "interval": 0, "kamis:customName": "获取大赛基本信息"}, {"type": "service", "body": [], "id": "u:be66da559e1c", "api": {"url": "/common_config/hash_data_get_all?key=brokerManage", "method": "get", "adaptor": "let brokerManage = payload.data;\nlet brokerList = [];\n\n// 如果没有数据，则为空数组\nif (!brokerManage) {\n  brokerManage = {};\n}\nbrokerList = Object.keys(brokerManage).map(key => {\n  return JSON.parse(brokerManage[key]);\n})\nconsole.log('brokerManage', brokerList);\nreturn {\n  status: payload.code === '0000' ? '0' : '1',\n  data: {\n    brokerManage: brokerList\n  }\n}", "messages": {}, "requestAdaptor": ""}, "props": {}, "dsType": "api", "interval": 0, "onEvent": {"fetchInited": {"weight": 0, "actions": [{"componentId": "u:0b8f9cb113f7", "groupType": "component", "actionType": "setValue", "args": {"value": {"brokerManage": "\\${brokerManage}"}}}]}}, "kamis:customName": "获取券商信息"}, {"type": "collapse-group", "body": [{"type": "collapse", "id": "u:6913a0900ca5", "key": "1", "active": false, "header": "查询券商漏推信息-Step1", "body": [{"type": "crud", "syncLocation": false, "api": {"url": "${fundPrefix}marketing/etf_competition/v2/check/push_lose", "method": "get", "messages": {}, "data": {"activityId": "${id}", "date": "${date}", "brokerManage": "${brokerManage}", "fundPrefix": "${fundPrefix}"}, "adaptor": "const { brokerManage, date } = api.body;\r\n\r\nconst pushRes = payload.data;\r\nconst pushOk = pushRes.pushOk;\r\n\r\nconst data = pushRes[date];\r\nconst list = [];\r\n\r\nObject.keys(data).forEach(brokerId => {\r\n  const target = brokerManage.find(item => item.brokerId === brokerId);\r\n  const obj = {\r\n    brokerId: target.brokerId,\r\n    brokerName: target.brokerName,\r\n    missedPushCount: data[brokerId]\r\n  }\r\n  target && list.push(obj);\r\n})\r\n\r\nreturn {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    items: list,\r\n    pushOk \r\n  }\r\n}", "requestAdaptor": "const { activityId, date, brokerManage, fundPrefix } = api.body;\n\napi.url = `${fundPrefix}marketing/etf_competition/v2/check/push_lose?activityId=${activityId}&date=${date}`\n\nreturn api;", "sendOn": "${id && brokerManage}", "trackExpression": "${id},${brokerManage}"}, "columns": [{"name": "brokerId", "label": "券商id", "type": "text", "id": "u:32e539baac36"}, {"name": "brokerName", "label": "券商名称", "type": "text", "id": "u:06ac6ac2f9e5"}, {"type": "text", "label": "漏推人数", "name": "missed<PERSON><PERSON><PERSON>ount", "id": "u:7282696f9a60"}], "bulkActions": [], "itemActions": [{"label": "查看详情", "type": "button", "id": "u:12aaab8ec8dc", "hiddenOnHover": false, "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "drawer", "drawer": {"type": "drawer", "title": "${brokerName}-漏推用户列表", "body": [{"type": "crud", "syncLocation": false, "api": {"method": "post", "url": "${fundPrefix}marketing/etf_competition/v2/user/bind_info/lose", "requestAdaptor": "", "adaptor": "const res = payload.data;\nconsole.log('res', res);\nconst { userBindBrokerInfos, total } = res;\n\napi.data = {\n  items: userBindBrokerInfos,\n  total\n}\n\nreturn api;", "messages": {}, "data": {"activityId": "${activityId}", "date": "${date}", "brokerId": "${brokerId}", "pageNo": "${page}", "pageSize": "${perPage}"}}, "columns": [{"name": "accountId", "label": "资金账号Id", "type": "text", "id": "u:9eb9febadb60"}, {"name": "accountShow", "label": "加密资金账号Id", "type": "text", "id": "u:0e179511a964"}, {"type": "text", "label": "userId", "name": "userId", "id": "u:b13ced8ce9de"}, {"type": "text", "label": "加密userId", "name": "userIdCipher", "id": "u:54a190e4b77c"}, {"type": "text", "label": "是否能换绑", "name": "canReBind", "id": "u:7cee3133d8ed"}, {"type": "text", "label": "绑定时间", "name": "timestamp", "id": "u:f895519ea942"}], "bulkActions": [], "itemActions": [], "props": {}, "id": "u:ed353d657007", "perPageAvailable": [10], "messages": {}}], "className": "app-popover", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:51a6612a07e7"}, {"type": "button", "actionType": "confirm", "label": "确认", "primary": true, "id": "u:181960a710e0"}], "id": "u:502e1bb6f86d", "resizable": true, "size": "lg", "closeOnOutside": true, "hideActions": true}}]}}}], "features": ["filter"], "filterColumnCount": 3, "id": "u:3d1db5a04ac7", "headerToolbar": [], "perPageAvailable": [10], "messages": {}, "filter": {"title": "查询条件", "body": [{"type": "input-date", "label": "日期", "name": "date", "id": "u:c7e414a5ac49", "valueFormat": "YYYYMMDD", "value": "${DATETOSTR(TODAY(), 'YYYYMMDD')}", "required": true}], "id": "u:7369c5d873cf", "actions": [{"type": "submit", "label": "搜索", "primary": true, "id": "u:623662ebc99a"}]}}]}, {"type": "collapse", "header": "查询券商推送的收益率列表-Step2", "body": [{"type": "crud", "syncLocation": false, "api": {"method": "post", "url": "${fundPrefix}marketing/etf_competition/v2/user/push_eaning/info", "requestAdaptor": "", "adaptor": "const items = payload.data || [];\n\nreturn {\n  ...api,\n  msg: payload.status_msg,\n  data: {\n    items\n  }\n}", "messages": {}, "data": {"activityId": "${id}", "date": "${date}", "asc": "${isAsc}", "pageNo": "${page}", "pageSize": "${perPage}"}, "sendOn": "${id}", "dataType": "json", "trackExpression": "${id}"}, "columns": [{"name": "userId", "label": "用户ID", "type": "text", "id": "u:99f22385ac9d"}, {"name": "earning", "label": "收益率", "type": "text", "id": "u:108a40e83eb7"}], "bulkActions": [], "itemActions": [], "props": {}, "id": "u:c4f9e34669f2", "perPageAvailable": [10], "messages": {}, "filter": {"title": "查询条件", "body": [{"type": "input-date", "name": "date", "label": "日期", "id": "u:b93ea9a7d007", "required": true, "props": {}, "value": "${DATETOSTR(TODAY(), 'YYYYMMDD')}", "valueFormat": "YYYYMMDD"}, {"type": "select", "label": "榜单顺序", "name": "isAsc", "options": [{"label": "正序", "value": true}, {"label": "倒序", "value": false}], "value": true, "required": true, "id": "u:dffc08b124a3"}], "id": "u:d62f2ba089f1", "actions": [{"type": "submit", "label": "搜索", "primary": true, "id": "u:2c8482efc8bf"}]}, "primaryField": "userId"}], "key": "2", "id": "u:23ac30e4db39"}, {"type": "collapse", "header": "指标计算-Step3", "body": [{"type": "form", "api": {"method": "post", "url": "${fundPrefix}marketing/etf_competition/v2/compute/index", "messages": {}, "requestAdaptor": "", "adaptor": "const isSucc = payload.status_code === 0;\nreturn {\n  ...api,\n  status: isSucc ? 0 : -1,\n  msg: isSucc ? \"成功\" : payload.status_msg\n}", "data": {"date": "${date}", "activityId": "${activityId}", "taskParam": 1}}, "id": "u:1c8c6e556e26", "title": "执行指标计算", "mode": "horizontal", "dsType": "api", "feat": "Insert", "body": [{"type": "input-date", "label": "日期", "name": "date", "id": "u:a6da5b33f312", "valueFormat": "YYYYMMDD", "value": "${DATETOSTR(TODAY(), 'YYYYMMDD')}", "required": true, "size": "sm"}], "actions": [{"type": "button", "label": "立即执行", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:1c8c6e556e26"}]}}, "level": "primary", "id": "u:a14444e3267b", "confirmText": "确定要执行${date}的指标计算吗"}], "resetAfterSubmit": false}, {"id": "u:25d70bbf34b5", "type": "form", "title": "查询指标计算状态", "mode": "horizontal", "dsType": "api", "feat": "Insert", "body": [{"type": "input-date", "label": "日期", "name": "date", "id": "u:b6caa5750edb", "valueFormat": "YYYYMMDD", "value": "${DATETOSTR(TODAY(), 'YYYYMMDD')}", "required": true, "size": "sm"}, {"type": "combo", "label": "", "name": "status", "multiple": false, "addable": false, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:7527c0be8e80"}, "items": [{"type": "mapping", "id": "u:b1634037a2ef", "map": {"0": "<span class='label label-warning'>未开始</span>", "1": "<span class='label label-info'>计算中</span>", "2": "<span class='label label-success'>计算完成</span>", "3": "<span class='label label-danger'>计算失败</span>", "*": "<span class='label label-default'>未查询</span>"}, "value": "${index}", "placeholder": ""}], "id": "u:c3b2219eb762", "strictMode": true, "syncFields": [], "static": false, "draggable": false, "flat": false}], "api": {"url": "${fundPrefix}marketing/etf_competition/v2/compute/status", "method": "get", "requestAdaptor": "", "adaptor": "const isSucc = payload.status_code === 0;\nconst { index, rank } = payload.data;\nreturn {\n  ...api,\n  data: {\n    status: {\n      index,\n      rank\n    }\n  },\n  status: isSucc ? 0 : -1,\n  msg: isSucc ? \"成功\" : payload.status_msg\n}", "messages": {}, "data": {"activityId": "${activityId}", "date": "${date}"}}, "actions": [{"type": "button", "label": "查询", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:25d70bbf34b5"}]}}, "level": "primary", "id": "u:250b5d8579bf"}], "resetAfterSubmit": false}], "key": "3", "id": "u:13b79a12933b"}, {"type": "collapse", "header": "榜单计算-Step4", "body": [{"id": "u:5c242210439d", "type": "form", "title": "执行榜单计算", "mode": "horizontal", "dsType": "api", "feat": "Insert", "body": [{"type": "input-date", "label": "日期", "name": "date", "id": "u:ca8765664d77", "valueFormat": "YYYYMMDD", "value": "${DATETOSTR(TODAY(), 'YYYYMMDD')}", "required": true, "size": "sm"}], "api": {"url": "${fundPrefix}marketing/etf_competition/v2/compute/rank", "method": "post", "requestAdaptor": "", "adaptor": "const isSucc = payload.status_code === 0;\nreturn {\n  ...api,\n  status: isSucc ? 0 : -1,\n  msg: isSucc ? \"成功\" : payload.status_msg\n}", "messages": {}, "data": {"activityId": "${activityId}", "date": "${date}", "rankKey": "${rankKey}"}}, "actions": [{"type": "button", "label": "日收益率计算", "onEvent": {"click": {"actions": [{"actionType": "setValue", "ignoreError": false, "componentId": "u:5c242210439d", "args": {"value": {"rankKey": "EARNING|DAY"}}}, {"componentId": "u:5c242210439d", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}]}}, "level": "primary", "id": "u:0efa4dafd929"}, {"type": "button", "label": "月收益率计算", "onEvent": {"click": {"actions": [{"actionType": "setValue", "ignoreError": false, "componentId": "u:5c242210439d", "args": {"value": {"rankKey": "EARNING|MONTH"}}}, {"componentId": "u:5c242210439d", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}]}}, "level": "primary", "id": "u:0efa4dafd929"}, {"type": "button", "label": "周收益率计算", "onEvent": {"click": {"actions": [{"actionType": "setValue", "ignoreError": false, "componentId": "u:5c242210439d", "args": {"value": {"rankKey": "EARNING|WEEK"}}}, {"componentId": "u:5c242210439d", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}]}}, "level": "primary", "id": "u:0efa4dafd929"}, {"type": "button", "label": "总收益率计算", "onEvent": {"click": {"actions": [{"actionType": "setValue", "ignoreError": false, "componentId": "u:5c242210439d", "args": {"value": {"rankKey": "EARNING|HISTORY"}}}, {"componentId": "u:5c242210439d", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}]}}, "level": "primary", "id": "u:0efa4dafd929"}, {"type": "button", "label": "正收益天数计算", "onEvent": {"click": {"actions": [{"actionType": "setValue", "ignoreError": false, "componentId": "u:5c242210439d", "args": {"value": {"rankKey": "EARNING|DAY|POSITIVES"}}}, {"componentId": "u:5c242210439d", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}]}}, "level": "primary", "id": "u:0efa4dafd929"}, {"type": "button", "label": "负收益天数计算", "onEvent": {"click": {"actions": [{"actionType": "setValue", "ignoreError": false, "componentId": "u:5c242210439d", "args": {"value": {"rankKey": "EARNING|DAY|NEGATIVES"}}}, {"componentId": "u:5c242210439d", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}]}}, "level": "primary", "id": "u:0efa4dafd929"}, {"type": "button", "label": "波动率计算", "onEvent": {"click": {"actions": [{"actionType": "setValue", "ignoreError": false, "componentId": "u:5c242210439d", "args": {"value": {"rankKey": "EARNING|DAY|DEVIATION"}}}, {"componentId": "u:5c242210439d", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}]}}, "level": "primary", "id": "u:0efa4dafd929"}, {"type": "button", "label": "最大回撤计算", "onEvent": {"click": {"actions": [{"actionType": "setValue", "ignoreError": false, "componentId": "u:5c242210439d", "args": {"value": {"rankKey": "EARNING|DAY|MAX_DRAW_DOWN"}}}, {"componentId": "u:5c242210439d", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}]}}, "level": "primary", "id": "u:0efa4dafd929"}], "resetAfterSubmit": false}, {"id": "u:f09ac1672226", "type": "form", "title": "查询榜单计算状态", "mode": "horizontal", "dsType": "api", "feat": "Insert", "body": [{"type": "input-date", "label": "日期", "name": "date", "id": "u:bf42f8c4f347", "valueFormat": "YYYYMMDD", "value": "${DATETOSTR(TODAY(), 'YYYYMMDD')}", "required": true, "size": "sm"}, {"type": "combo", "label": "", "name": "status", "multiple": false, "addable": false, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b1416491cd58"}, "items": [{"type": "mapping", "id": "u:ad80e93b5c80", "map": {"EARNING|DAY": "日收益", "EARNING|MONTH": "月收益", "EARNING|WEEK": "周收益", "EARNING|HISTORY": "总收益", "EARNING|DAY|POSITIVES": "正收益天数", "EARNING|DAY|NEGATIVES": "负收益天数", "EARNING|DAY|DEVIATION": "波动率", "EARNING|DAY|MAX_DRAW_DOWN": "最大回撤", "*": "未查询"}, "value": "EARNING|DAY", "placeholder": ""}, {"type": "mapping", "id": "u:ecba6e49a1f7", "map": {"0": "<span class='label label-warning'>未开始</span>", "1": "<span class='label label-info'>计算中</span>", "2": "<span class='label label-success'>计算完成</span>", "3": "<span class='label label-danger'>计算失败</span>", "*": "<span class='label label-default'>未查询</span>"}, "value": "${status['EARNING|DAY']}", "placeholder": ""}], "id": "u:7ebf7bfc8a1b", "strictMode": true, "syncFields": [], "static": false, "draggable": false, "flat": false, "size": "sm"}, {"type": "combo", "label": "", "name": "status", "multiple": false, "addable": false, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b1416491cd58"}, "items": [{"type": "mapping", "id": "u:ad80e93b5c80", "map": {"EARNING|DAY": "日收益", "EARNING|MONTH": "月收益", "EARNING|WEEK": "周收益", "EARNING|HISTORY": "总收益", "EARNING|DAY|POSITIVES": "正收益天数", "EARNING|DAY|NEGATIVES": "负收益天数", "EARNING|DAY|DEVIATION": "波动率", "EARNING|DAY|MAX_DRAW_DOWN": "最大回撤", "*": "未查询"}, "value": "EARNING|WEEK", "placeholder": ""}, {"type": "mapping", "id": "u:ecba6e49a1f7", "map": {"0": "<span class='label label-warning'>未开始</span>", "1": "<span class='label label-info'>计算中</span>", "2": "<span class='label label-success'>计算完成</span>", "3": "<span class='label label-danger'>计算失败</span>", "*": "<span class='label label-default'>未查询</span>"}, "value": "${status['EARNING|WEEK']}", "placeholder": ""}], "id": "u:7ebf7bfc8a1b", "strictMode": true, "syncFields": [], "static": false, "draggable": false, "flat": false, "size": "sm"}, {"type": "combo", "label": "", "name": "status", "multiple": false, "addable": false, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b1416491cd58"}, "items": [{"type": "mapping", "id": "u:ad80e93b5c80", "map": {"EARNING|DAY": "日收益", "EARNING|MONTH": "月收益", "EARNING|WEEK": "周收益", "EARNING|HISTORY": "总收益", "EARNING|DAY|POSITIVES": "正收益天数", "EARNING|DAY|NEGATIVES": "负收益天数", "EARNING|DAY|DEVIATION": "波动率", "EARNING|DAY|MAX_DRAW_DOWN": "最大回撤", "*": "未查询"}, "value": "EARNING|MONTH", "placeholder": ""}, {"type": "mapping", "id": "u:ecba6e49a1f7", "map": {"0": "<span class='label label-warning'>未开始</span>", "1": "<span class='label label-info'>计算中</span>", "2": "<span class='label label-success'>计算完成</span>", "3": "<span class='label label-danger'>计算失败</span>", "*": "<span class='label label-default'>未查询</span>"}, "value": "${status['EARNING|MONTH']}", "placeholder": ""}], "id": "u:7ebf7bfc8a1b", "strictMode": true, "syncFields": [], "static": false, "draggable": false, "flat": false, "size": "sm"}, {"type": "combo", "label": "", "name": "status", "multiple": false, "addable": false, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b1416491cd58"}, "items": [{"type": "mapping", "id": "u:ad80e93b5c80", "map": {"EARNING|DAY": "日收益", "EARNING|MONTH": "月收益", "EARNING|WEEK": "周收益", "EARNING|HISTORY": "总收益", "EARNING|DAY|POSITIVES": "正收益天数", "EARNING|DAY|NEGATIVES": "负收益天数", "EARNING|DAY|DEVIATION": "波动率", "EARNING|DAY|MAX_DRAW_DOWN": "最大回撤", "*": "未查询"}, "value": "EARNING|HISTORY", "placeholder": ""}, {"type": "mapping", "id": "u:ecba6e49a1f7", "map": {"0": "<span class='label label-warning'>未开始</span>", "1": "<span class='label label-info'>计算中</span>", "2": "<span class='label label-success'>计算完成</span>", "3": "<span class='label label-danger'>计算失败</span>", "*": "<span class='label label-default'>未查询</span>"}, "value": "${status['EARNING|HISTORY']}", "placeholder": ""}], "id": "u:7ebf7bfc8a1b", "strictMode": true, "syncFields": [], "static": false, "draggable": false, "flat": false, "size": "sm"}, {"type": "combo", "label": "", "name": "status", "multiple": false, "addable": false, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b1416491cd58"}, "items": [{"type": "mapping", "id": "u:ad80e93b5c80", "map": {"EARNING|DAY": "日收益", "EARNING|MONTH": "月收益", "EARNING|WEEK": "周收益", "EARNING|HISTORY": "总收益", "EARNING|DAY|POSITIVES": "正收益天数", "EARNING|DAY|NEGATIVES": "负收益天数", "EARNING|DAY|DEVIATION": "波动率", "EARNING|DAY|MAX_DRAW_DOWN": "最大回撤", "*": "未查询"}, "value": "EARNING|DAY|POSITIVES", "placeholder": ""}, {"type": "mapping", "id": "u:ecba6e49a1f7", "map": {"0": "<span class='label label-warning'>未开始</span>", "1": "<span class='label label-info'>计算中</span>", "2": "<span class='label label-success'>计算完成</span>", "3": "<span class='label label-danger'>计算失败</span>", "*": "<span class='label label-default'>未查询</span>"}, "value": "${status['EARNING|DAY|POSITIVES']}", "placeholder": ""}], "id": "u:7ebf7bfc8a1b", "strictMode": true, "syncFields": [], "static": false, "draggable": false, "flat": false, "size": "sm"}, {"type": "combo", "label": "", "name": "status", "multiple": false, "addable": false, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b1416491cd58"}, "items": [{"type": "mapping", "id": "u:ad80e93b5c80", "map": {"EARNING|DAY": "日收益", "EARNING|MONTH": "月收益", "EARNING|WEEK": "周收益", "EARNING|HISTORY": "总收益", "EARNING|DAY|POSITIVES": "正收益天数", "EARNING|DAY|NEGATIVES": "负收益天数", "EARNING|DAY|DEVIATION": "波动率", "EARNING|DAY|MAX_DRAW_DOWN": "最大回撤", "*": "未查询"}, "value": "EARNING|DAY|NEGATIVES", "placeholder": ""}, {"type": "mapping", "id": "u:ecba6e49a1f7", "map": {"0": "<span class='label label-warning'>未开始</span>", "1": "<span class='label label-info'>计算中</span>", "2": "<span class='label label-success'>计算完成</span>", "3": "<span class='label label-danger'>计算失败</span>", "*": "<span class='label label-default'>未查询</span>"}, "value": "${status['EARNING|DAY|NEGATIVES']}", "placeholder": ""}], "id": "u:7ebf7bfc8a1b", "strictMode": true, "syncFields": [], "static": false, "draggable": false, "flat": false, "size": "sm"}, {"type": "combo", "label": "", "name": "status", "multiple": false, "addable": false, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b1416491cd58"}, "items": [{"type": "mapping", "id": "u:ad80e93b5c80", "map": {"EARNING|DAY": "日收益", "EARNING|MONTH": "月收益", "EARNING|WEEK": "周收益", "EARNING|HISTORY": "总收益", "EARNING|DAY|POSITIVES": "正收益天数", "EARNING|DAY|NEGATIVES": "负收益天数", "EARNING|DAY|DEVIATION": "波动率", "EARNING|DAY|MAX_DRAW_DOWN": "最大回撤", "*": "未查询"}, "value": "EARNING|DAY|DEVIATION", "placeholder": ""}, {"type": "mapping", "id": "u:ecba6e49a1f7", "map": {"0": "<span class='label label-warning'>未开始</span>", "1": "<span class='label label-info'>计算中</span>", "2": "<span class='label label-success'>计算完成</span>", "3": "<span class='label label-danger'>计算失败</span>", "*": "<span class='label label-default'>未查询</span>"}, "value": "${status['EARNING|DAY|DEVIATION']}", "placeholder": ""}], "id": "u:7ebf7bfc8a1b", "strictMode": true, "syncFields": [], "static": false, "draggable": false, "flat": false, "size": "sm"}, {"type": "combo", "label": "", "name": "status", "multiple": false, "addable": false, "removable": false, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b1416491cd58"}, "items": [{"type": "mapping", "id": "u:ad80e93b5c80", "map": {"EARNING|DAY": "日收益", "EARNING|MONTH": "月收益", "EARNING|WEEK": "周收益", "EARNING|HISTORY": "总收益", "EARNING|DAY|POSITIVES": "正收益天数", "EARNING|DAY|NEGATIVES": "负收益天数", "EARNING|DAY|DEVIATION": "波动率", "EARNING|DAY|MAX_DRAW_DOWN": "最大回撤", "*": "未查询"}, "value": "EARNING|DAY|MAX_DRAW_DOWN", "placeholder": ""}, {"type": "mapping", "id": "u:ecba6e49a1f7", "map": {"0": "<span class='label label-warning'>未开始</span>", "1": "<span class='label label-info'>计算中</span>", "2": "<span class='label label-success'>计算完成</span>", "3": "<span class='label label-danger'>计算失败</span>", "*": "<span class='label label-default'>未查询</span>"}, "value": "${status['EARNING|DAY|MAX_DRAW_DOWN']}", "placeholder": ""}], "id": "u:7ebf7bfc8a1b", "strictMode": true, "syncFields": [], "static": false, "draggable": false, "flat": false, "size": "sm"}], "api": {"url": "${fundPrefix}marketing/etf_competition/v2/rank/status", "method": "get", "requestAdaptor": "", "adaptor": "const isSucc = payload.status_code === 0;\nconst status = payload.data;\nreturn {\n  ...api,\n  data: {\n    status\n  },\n  status: isSucc ? 0 : -1,\n  msg: isSucc ? \"成功\" : payload.status_msg\n}", "messages": {}, "data": {"activityId": "${activityId}", "date": "${date}", "taskId": ""}}, "actions": [{"type": "button", "label": "查询", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:f09ac1672226", "outputVar": "submitResult"}]}}, "level": "primary", "id": "u:dafe65204af9"}], "resetAfterSubmit": false}], "key": "4", "id": "u:67da30089e21"}, {"type": "collapse", "header": "榜单审核-Step5", "body": [{"id": "u:49852194948c", "type": "crud", "api": {"url": "${fundPrefix}marketing/etf_competition/v2/rank/top", "method": "post", "requestAdaptor": "", "adaptor": "const items = payload.data.rankInfoList || [];\nconst returnDate = payload.data.date;\n\nreturn {\n  data: {\n    items,\n    returnDate\n  }\n}", "messages": {}, "data": {"activityId": "${id}", "date": "${date}", "key": "${rankKey}", "asc": "${isAsc}"}, "sendOn": "${id}", "dataType": "json", "trackExpression": "${id}"}, "syncLocation": false, "columns": [{"name": "rankValue", "label": "排名(实际返回日期${returnDate})", "type": "text", "id": "u:5823ae49c284", "placeholder": "-"}, {"name": "userId", "label": "用户ID", "type": "text", "id": "u:5af86f9f255f"}, {"type": "text", "label": "加密用户ID", "name": "userIdCipher", "id": "u:fcb1d4bc72c9"}, {"type": "text", "label": "用户名", "name": "feature.nick", "id": "u:342a1064a0da"}, {"type": "text", "label": "收益率", "name": "value", "id": "u:0328e50109d1"}], "bulkActions": [], "itemActions": [], "props": {}, "perPageAvailable": [10], "messages": {}, "primaryField": "userId", "filter": {"title": "查询条件", "body": [{"type": "select", "label": "榜单选择", "name": "<PERSON><PERSON><PERSON>", "options": [{"label": "日收益率", "value": "EARNING|DAY"}, {"label": "月收益率", "value": "EARNING|MONTH"}, {"label": "周收益率", "value": "EARNING|WEEK"}, {"label": "总收益率", "value": "EARNING|HISTORY"}, {"label": "正收益天数", "value": "EARNING|DAY|POSITIVES"}, {"label": "负收益天数", "value": "EARNING|DAY|NEGATIVES"}, {"label": "波动率", "value": "EARNING|DAY|DEVIATION"}, {"label": "最大回撤", "value": "EARNING|DAY|MAX_DRAW_DOWN"}], "props": {}, "id": "u:1a6ab7c32531", "multiple": false, "value": "EARNING|DAY", "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "u:49852194948c", "groupType": "component", "actionType": "setValue", "args": {"value": {"rankKey": "${rankKey}"}}}]}}, "required": true}, {"type": "input-date", "label": "日期", "name": "date", "props": {}, "id": "u:ecb70d6a69db", "value": "${DATETOSTR(TODAY(), 'YYYYMMDD')}", "valueFormat": "YYYYMMDD", "required": true}, {"type": "select", "label": "榜单顺序", "name": "isAsc", "options": [{"label": "正序", "value": true}, {"label": "倒序", "value": false}], "value": true, "required": true, "id": "u:8715e8e5c6d0"}], "id": "u:d62f2ba089f1", "actions": [{"type": "submit", "label": "搜索", "primary": true, "id": "u:9f0c11e03a8d"}, {"type": "button", "id": "u:10013c0b3161", "label": "榜单审核通过", "onEvent": {"click": {"actions": [{"ignoreError": false, "outputVar": "responseResult", "actionType": "ajax", "options": {}, "args": {"api": {"url": "/common_config/hash_data_save", "method": "post", "requestAdaptor": "const { returnDate, activityId } = api.body\n\nreturn {\n  ...api,\n  data: {\n    key: `etf_competition_activity_config_${activityId}_rankVerify`,\n    propName: returnDate,\n    value: \"1\"\n  }\n}", "adaptor": "const isSuccess = payload.code === '0000'\nreturn {\n  code: isSuccess ? 0 : 1,\n  msg: isSuccess ? '成功' : '失败'\n}", "messages": {}, "dataType": "form-data", "data": {"returnDate": "${returnDate}", "activityId": "${activityId}"}}}}]}}, "props": {}, "disabledOn": "${!returnDate}", "confirmText": "确认要审核通过${returnDate}日期的排行榜吗"}]}}], "key": "5", "id": "u:8f6e013df564"}, {"type": "collapse", "key": "6", "header": "工具-查询用户绑定信息", "body": [{"type": "crud", "syncLocation": false, "api": {"method": "post", "url": "${fundPrefix}marketing/etf_competition/v2/user/bind_info/no_auth", "messages": {}, "requestAdaptor": "", "adaptor": "\nconst res = payload.data;\n\napi.data = {\n  items: [res]\n}\n\napi.msg = payload.status_msg;\n\nreturn api;", "sendOn": "${userId}", "data": {"activityId": "${activityId}", "userId": "${userId}"}}, "columns": [{"name": "brokerId", "label": "券商id", "type": "text", "id": "u:35290a90db0b"}, {"name": "brokerName", "label": "券商名称", "type": "text", "id": "u:a8b252e183df"}, {"type": "text", "label": "资金账户Id", "name": "accountId", "id": "u:09127c2a84bb", "buttons": []}, {"type": "text", "label": "加密资金账户Id", "name": "accountShow", "id": "u:4c9ba027662e"}, {"type": "text", "label": "绑定时间", "name": "timestamp", "id": "u:d9eea71096c6"}, {"type": "text", "label": "userId", "name": "userId", "id": "u:5c561b4424b2"}, {"type": "text", "label": "加密userId", "name": "userIdCipher", "id": "u:bbc4de737db1"}, {"type": "text", "label": "能否换绑", "name": "canReBind", "id": "u:d975df545da2"}], "bulkActions": [], "itemActions": [], "props": {}, "id": "u:52fc93a2def5", "perPageAvailable": [10], "messages": {}, "filter": {"title": "查询条件", "body": [{"type": "input-text", "label": "userId", "name": "userId", "id": "u:************", "required": true, "clearable": true}], "id": "u:ca4b05a27432", "actions": [{"type": "submit", "label": "搜索", "primary": true, "id": "u:bd466729ccca"}]}, "features": ["filter"], "filterColumnCount": 3}], "id": "u:ca9be48eae85"}, {"type": "collapse", "key": "7", "header": "文件上传-安全部门审核结果", "body": [{"type": "form", "api": "${khPrefix}assembly/center/activity/reward/v1/issue/import", "data": {"reImport": "${reImport || false}"}, "body": [{"type": "input-file", "name": "file", "label": "File", "accept": "*", "asBlob": true}]}]}], "activeKey": [], "props": {}}], "title": "ETF大赛数据平台-当前活动ID：${activityId}", "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    rankAward: config,\r\n  },\r\n};", "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "rankAward"}}, "pullRefresh": {"disabled": true}}