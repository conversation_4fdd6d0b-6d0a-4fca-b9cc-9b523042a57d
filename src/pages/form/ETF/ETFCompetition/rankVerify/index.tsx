import React, { useEffect } from 'react';
import 'moment/locale/zh-cn';
import amisJSON from './amis.json';
import amisEnv from 'functions/amisEnv';
import {env} from 'config';
import { formatUrlParams} from '../../../abtest/util';
import { defaultActivityId } from '../index';

export default function() {
    let amisScoped:any;
    const init = () => {
        let amis = amisRequire('amis/embed');
        amisScoped = amis.embed('#etfRankVerify', {
          ...amisJSON,
          data:{
            fundPrefix: env === 'prod' ? 'https://fund.10jqka.com.cn/' : 'https://testfund.10jqka.com.cn/',
            khPrefix: env === 'prod' ? 'https://mams.10jqka.com.cn/' : 'https://khtest.10jqka.com.cn/tenon_genius/',
            activityId: formatUrlParams()?.activityId ? formatUrlParams().activityId: defaultActivityId
          }}, {}, amisEnv());
    }
    useEffect(() => {
      init();
      return () => {
        amisScoped.unmount();
      }
    }, [])
    
    return (
      <div id='etfRankVerify'></div>
    )
}

