import React, { useState, useEffect } from 'react';
import { Ta<PERSON>, But<PERSON>, message, Popconfirm } from 'antd';
const { TabPane } = Tabs

import api from 'api';
const { getETFFundList, getETFIndexList, getETFConfig, postETFConfig } = api

import ShowBlock from './../component/showBlock'
import HotItems from './hotItems'
import ShowTopic from './../component/showTopic'
import ETFTopic from './../component/ETFTopic'

export default function () {
    const [funds, setFunds] = useState([])
    const [blocks, setBlocks] = useState([])
    const [disabled, setDisabled] = useState(true)
    const [config, setConfig] = useState({})

    const [hotBlock, setHotBlock] = useState([{}, {}, {}, {}, {}, {}]) //热门板块 展示
    const [hotIndex, setHotIndex] = useState([]) //热门板块 热门指数
    const [hotBlockList, setHotBlockList] = useState([]) //热门板块 板块热点
    const [highRate, setHighRate] = useState([]) //热门板块 高溢价率

    const [themeHome, setThemeHome] = useState([{}, {}, {}, {}, {}, {}]) //主题投资 展示主题
    const [themeDetail, setThemeDetail] = useState([]) //主题投资 主题ETF列表

    useEffect(() => {
        getETFFundList().then((res: any) => {
            if (res.code === '0000' || res.data) {
                setFunds(res.data)
            } else {
                message.error(res.message);
            }
        }).catch((e: any) => {
            message.error('系统异常');
        })
        getETFIndexList().then((res: any) => {
            if (res.code === '0000') {
                setBlocks(res.data)
            } else {
                message.error(res.message);
            }
        }).catch((e: any) => {
            message.error('系统异常');
        })

        getETFConfig().then((res: any) => {
            if (res && res.code === '0000') {
                let _data = JSON.parse(res.data)
                console.log(_data)
                setData(_data)
                setConfig(_data)
            } else {
                message.error(res.message)
            }
        })
    }, [])

    function setData(_data: any) {
        _data = JSON.parse(JSON.stringify(_data))
        setHotBlock(_data.hotPlate)
        setHotIndex(_data.hotPlateDetail.hotIndex)
        setHotBlockList(_data.hotPlateDetail.hotPlate)
        setHighRate(_data.hotPlateDetail.highRate)
        setThemeHome(_data.themeInvestment)
        setThemeDetail(_data.themeInvestmentDetail)
    }

    function open() {
        setDisabled(false)
    }

    function addHotIndex(): void {
        let _hotIndex = JSON.parse(JSON.stringify(hotIndex))
        _hotIndex.push({})
        setHotIndex(_hotIndex)
    }

    function deleteHotIndex(index: number): void {
        let _hotIndex = JSON.parse(JSON.stringify(hotIndex))
        _hotIndex.splice(index, 1)
        setHotIndex(_hotIndex)
    }

    function addHotBlockList(): void {
        let _hotBlockList = JSON.parse(JSON.stringify(hotBlockList))
        _hotBlockList.push({})
        setHotBlockList(_hotBlockList)
    }

    function deleteHotBlockList(index: number): void {
        let _hotBlockList = JSON.parse(JSON.stringify(hotBlockList))
        _hotBlockList.splice(index, 1)
        setHotBlockList(_hotBlockList)
    }

    function addHighRate(): void {
        let _highRate = JSON.parse(JSON.stringify(highRate))
        _highRate.push({})
        setHighRate(_highRate)
    }

    function deleteHighRate(index: number): void {
        let _highRate = JSON.parse(JSON.stringify(highRate))
        _highRate.splice(index, 1)
        setHighRate(_highRate)
    }

    function addThemeDetail(): void {
        let _themeDetail = JSON.parse(JSON.stringify(themeDetail))
        _themeDetail.push({})
        setThemeDetail(_themeDetail)
    }

    function deleteThemeDetail(index: number): void {
        let _themeDetail = JSON.parse(JSON.stringify(themeDetail))
        _themeDetail.splice(index, 1)
        setThemeDetail(_themeDetail)
    }

    /**
     * 检查是否有不合规的
     */
    function checkFunds(array: Array<any>): boolean {
        return array.some(item => JSON.stringify(item) === '{}')
    }

    /**
     * 检查是否重复
     * @param array 数组
     */
    function checkRepetition(array: Array<any>) {
        let _array: Array<string> = [],
            _tag: boolean = false
        array.forEach(item => {
            item.tradecode && _array.push(item.tradecode)
        })
        console.log([...new Set(_array)], _array)
        if ([...new Set(_array)].length !== _array.length) _tag = true
        return _tag
    }

    /**
     * 检查是否合规
     * @param array 对象数组
     * @param isAllowEmpty 是否允许有空对象
     */
    function checkBlocks(array: Array<any>, isAllowEmpty: boolean): boolean {
        return array.some(item => (isAllowEmpty ? false : JSON.stringify(item) === '{}') || Object.keys(item).length === 1 || Object.keys(item).length === 2)
    }

    /**
     * 检查是否有重复 和没有填写说明
     * @param array 
     * @param module 模块名称
     */
    function checkBlocksRepetition(array: Array<any>, module: string) {
        let _fundArray: Array<string> = [],
            _indexArray: Array<string> = [],
            text: string = ''
        array.forEach(item => {
            item.ETF && item.ETF.tradecode && _fundArray.push(item.ETF.tradecode)
            item.index && item.index['thscode_hq'] && _indexArray.push(item.index['thscode_hq'])
            if (item.description === '') text = '主题投资 ' + module + ' 描述没有填写'
        })
        if ([...new Set(_fundArray)].length !== _fundArray.length) text = '主题投资 ' + module + ' 基金代码有重复'
        if ([...new Set(_indexArray)].length !== _indexArray.length) text = '主题投资 ' + module + ' 板块有重复'
        return text
    }

    function upload() {
        let _data = {
            hotPlate: hotBlock,
            hotPlateDetail: {
                hotIndex,
                hotPlate: hotBlockList,
                highRate
            },
            themeInvestment: themeHome,
            themeInvestmentDetail: themeDetail
        }
        setConfig(JSON.parse(JSON.stringify(_data)))
        console.log(_data)
        let _failTag: boolean = false
        const hotNames = ['热门板块 展示板块', '热门板块 热门指数列表', '热门板块 板块热点列表', '热门板块 高溢价率排除列表']
        if ([hotIndex, hotBlockList, highRate].some(item => checkFunds(item))) {
            message.error('热门板块有内容未填写')
            _failTag = true
            return
        }
        [hotBlock, hotIndex, hotBlockList, highRate].map((item, index: number) => {
            if (checkRepetition(item)) {
                message.error(`${hotNames[index]}有重复`)
                _failTag = true
                return
            }
        })
        if (_failTag) return
        // 由于流量分配ETF不需要设置成必选项
        const checkThemeHome = themeHome.some((home) => { if (Object.keys(home).length === 0) return false; return !home.index || !home.description })
        if (checkThemeHome) {
            message.error('主题投资展示主题有内容未填写 ')
            return
        }
        const checkThemeDetail = themeDetail.some((detail) => !detail.index || !detail.description)
        if (checkThemeDetail) {
            message.error('主题投资主题ETF列表有内容未填写')
            return
        }
        if (checkBlocksRepetition(themeHome, '展示主题')) {
            message.error(checkBlocksRepetition(themeHome, '展示主题'))
            return
        }
        if (checkBlocksRepetition(themeDetail, 'ETF列表')) {
            message.error(checkBlocksRepetition(themeDetail, 'ETF列表'))
            return
        }
        postETFConfig({
            value: JSON.stringify(_data)
        }).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.message);
            } else {
                message.success('发布成功！')
            }
        })
    }

    return (
        <div>
            <p style={{ width: 900, marginBottom: 30, textAlign: 'center', fontSize: 30 }}>行情--ETF配置平台</p>
            <div className="u-c-middle" style={{ width: 900 }}>
                <Button type="primary" onClick={open}>编辑</Button>
                <Popconfirm
                    title="请问是否取消"
                    okText="确定"
                    cancelText="取消"
                    onConfirm={() => { setData(config) }}
                    placement="bottom"
                >
                    <Button type="danger" style={{ marginLeft: 30, marginRight: 30 }}>取消</Button>
                </Popconfirm>
                <Popconfirm
                    title="请问是否提交"
                    okText="确定"
                    cancelText="取消"
                    onConfirm={upload}
                    placement="bottom"
                >
                    <Button type="danger">提交</Button>
                </Popconfirm>

            </div>

            <div id="ETF-container" >
                <Tabs defaultActiveKey="1">
                    <TabPane tab="热门板块" key="1">
                        <h1 style={{ fontSize: 30 }}>展示板块</h1>
                        <div>
                            <div style={{ width: '1200px' }}>
                                {
                                    hotBlock.map((item, index) => {
                                        return (
                                            <ShowBlock
                                                key={index}
                                                index={index}
                                                obj={funds}
                                                hotBlockItem={item}
                                                hotBlock={hotBlock}
                                                setHotBlock={setHotBlock}
                                                disabled={disabled}
                                            />
                                        )
                                    })
                                }
                            </div>
                            <Tabs defaultActiveKey="1">
                                <TabPane tab="热门指数列表" key="1">
                                    <HotItems
                                        items={hotIndex}
                                        setItems={setHotIndex}
                                        fundList={funds}
                                        addFunc={addHotIndex}
                                        deleteFunc={deleteHotIndex}
                                        disabled={disabled}
                                    />
                                </TabPane>
                                <TabPane tab="板块热点列表" key="2">
                                    <HotItems
                                        items={hotBlockList}
                                        setItems={setHotBlockList}
                                        fundList={funds}
                                        addFunc={addHotBlockList}
                                        deleteFunc={deleteHotBlockList}
                                        disabled={disabled}
                                    />
                                </TabPane>
                                <TabPane tab="高溢价率排除列表" key="3">
                                    <HotItems
                                        items={highRate}
                                        setItems={setHighRate}
                                        fundList={funds}
                                        addFunc={addHighRate}
                                        deleteFunc={deleteHighRate}
                                        disabled={disabled}
                                    />
                                </TabPane>
                            </Tabs>
                        </div>
                    </TabPane>
                    <TabPane tab="主题投资" key="2">
                        <h1 style={{ fontSize: 30 }}>展示主题</h1>
                        <div style={{ width: 1200 }}>
                            {
                                themeHome.map((item, index) => {
                                    return (
                                        <ShowTopic
                                            funds={funds}
                                            blocks={blocks}
                                            key={index}
                                            themeHome={themeHome}
                                            setThemeHome={setThemeHome}
                                            item={item}
                                            index={index}
                                            disabled={disabled}
                                        />
                                    )
                                })
                            }
                        </div>
                        <h1 style={{ fontSize: 30 }}>主题ETF列表</h1>
                        <p className="u-l-middle">
                            <span className="u-block_il u-w100">序号</span>
                            <span className="u-block_il" style={{ width: 270 }}>主题名和主题代码</span>
                            <span className="u-block_il" style={{ width: 270 }}>主题介绍</span>
                            <span className="u-block_il" style={{ width: 270 }}>基金名和基金代码</span>
                            <span className="u-block_il" style={{ width: 50 }} >操作</span>
                        </p>
                        <div>
                            {
                                themeDetail.map((item, index) => {
                                    return (
                                        <ETFTopic
                                            funds={funds}
                                            blocks={blocks}
                                            key={index}
                                            themeDetail={themeDetail}
                                            setThemeDetail={setThemeDetail}
                                            item={item}
                                            index={index}
                                            deleteThemeDetail={deleteThemeDetail}
                                            disabled={disabled}
                                        />
                                    )
                                })
                            }
                        </div>
                        <Button type="primary" onClick={addThemeDetail} disabled={disabled}>添加</Button>
                    </TabPane>
                </Tabs>
            </div>

        </div>
    )
}