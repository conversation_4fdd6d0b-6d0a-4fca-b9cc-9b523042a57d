.etf-answer {
  :global {
    .ant-radio-button-wrapper {
      margin: 0 4px;
    }
  }
}

.upload-logo {
  :global {
    .avatar-uploader > .ant-upload {
      height: 40px;
      width: 120px;
    }
  }
}
.m-fund-item {
  height: 90px;
  line-height: 90px;
}
.m-logo {
  user-select: none;
  -webkit-user-drag: none;
  height: 36px;
}

.m-funds {
  width: 100%;
  .m-fund-titles {
    display: flex;
    .m-fcode-titles {
      display: flex;
      .m-fund-title {
        margin-right: 10px;
        position: relative;
        > input {
          width: 120px;
        }
        > i {
          right: 4px;
          top: 50%;
          position: absolute;
          cursor: pointer;
          transform: translateY(-50%)
        }
      }
    }
  }
} 
.m-organs {}
.m-list-item {
  display: flex;
  align-items: center;
}
.m-list {
  // padding-left: 20px;
  [class*="DraggableTags-tag"] {
    width: 100%;
  }
}
.m-module {
  .m-title {
    
  }
  > button {
    margin: 20px 0;
  }
}


:global {
  .checkbox-zone {
    display: flex;
    align-items: center;
    justify-items: flex-start;
    .mb2 {
      margin-bottom: 0;
      width: 40px;
      flex-grow: 0 !important;
    }
  }
  .m-value-item {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-height: 1.2em;
    max-height: 2.4em;
    // display: flex;
    // align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    &.m-type-title {
      > input {
        width: 320px;
      }
    }
    .m-required-icon {
      color: red;
    }
  }
  .m-input {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-variant: tabular-nums;
    list-style: none;
    font-feature-settings: 'tnum';
    position: relative;
    display: inline-block;
    width: 140px;
    height: 32px;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    line-height: 1.5;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
    overflow: visible;
    &.m-required {
      border-color: #e21c1c;
    }
    &.m-required:hover {
      border-color: #e21c1c;
    }
    &.m-required:focus {
      border-color: #e21c1c;
      box-shadow: 0 0 0 2px rgba(226, 28, 28, 0.2);
    }
    &:hover {
      border-color: #40a9ff;
      border-right-width: 1px !important;
    }
    &:focus {
      border-color: #40a9ff;
      border-right-width: 1px !important;
      outline: 0;
      box-shadow: 0 0 0 2px rgba(24,144,255,.2);
    }
  }
  .m-select {
    width: 180px;
    &.m-required > div {
      border-color: #f16a6a;
    }
    &.m-required:hover > div {
      border-color: #f16a6a;
    }
    &.m-required:focus > div {
      border-color: #f16a6a;
      box-shadow: 0 0 0 2px rgba(226, 28, 28, 0.2);
    }
  }
}
