export function getGridSchema (isEdit) {
	console.log(isEdit, 'isEdit');
	return {
		"type": "object",
		"required": [
			// "banner",
			// "bannerUrl",
		],
		"properties": {
			// "banner": {
			// 	"title": "banner图片配置",
			// 	"type": "string",
			// 	"ui:widget": "uploadImg",
			// },
			// "bannerUrl": {
			// 	"title": "banner链接",
			// 	"type": "string",
			// },
			// "items": {
			// 	"type": "object",
			// 	"title": "卡片配置",
			// },
			"leftCard": {
				"type": "object",
				"title": "左侧卡片：",
				"required": [
					"title",
					"url"
				],
				"properties": {
					"title": {
						"title": "卡片大标题：",
						"type": "string",
						"description": "",
						"ui:options": {},
					},
					"subTitle": {
						"title": "卡片小标题：",
						"type": "string",
						// "pattern": /^\w+$/,
						"description": "",
						"ui:options": {},
					},
					"url": {
						"title": "卡片链接：",
						"type": "string",
						// "pattern": /^\w+$/,
						"description": "",
						"ui:options": {},
					},
        }
			},
			"rightCard": {
				"type": "object",
				"title": "右侧卡片：",
				"required": [
					"title",
					"url"
				],
				"properties": {
					"title": {
						"title": "卡片大标题：",
						"type": "string",
						"description": "",
						"ui:options": {},
					},
					"subTitle": {
						"title": "卡片小标题：",
						"type": "string",
						// "pattern": /^\w+$/,
						"description": "",
						"ui:options": {},
						"ui:disabled": isEdit === '3' ? true: false,
					},
					"url": {
						"title": "卡片链接：",
						"type": "string",
						// "pattern": /^\w+$/,
						"description": "",
						"ui:options": {},
					},
				}
			},
			"questionArea": {
				"type": "object",
				"title": "去提问配置：",
				"required": [
					"question",
				],
				"properties": {
					"question": {
						"title": "去提问链接：",
						"type": "string",
						// "pattern": /^\w+$/,
						"description": "",
						"ui:options": {},
						"ui:disabled": isEdit === '3' ? true: false,
					},
				}
			},

		}
	}
}

export function getCardSchema (isEdit) {
	console.log(isEdit, 'isEdit');
	return {
		"type": "object",
		"required": [
			"url",
			"title"
		],
		"properties": {
			"id": {
				"title": "文章id",
				"type": "string",
				"pattern": /^\w+$/,
				"description": "需要用英文输入，唯一标识符，不能重复",
				"ui:options": {},
				"ui:disabled": true,
				"default": `etf${(new Date()).valueOf()}`,
			},
			"title": {
				"title": "问题",
				"type": "string",
				// "pattern": /^\w+$/,
				"description": "",
				"ui:options": {},
			},
			"type": {
				"title": "类型",
				"type": "string",
				"enum": [
					"url",
					"text"
				],
				"enumNames": [
					"链接",
					"文本"
				],
				"ui:widget": "radio",
				"default": "url"
			},
			"url": {
				"title": "链接地址",
				"type": "string",
				'ui:hidden': (formData: any) => formData.type === 'text',
				// "displayType": "row",
				"ui:width": "100%",
				"labelWidth": 50,
			},

		}
	}
}

export function getPageSchema (isEdit) {
	console.log(isEdit, 'isEdit');
	return {
		"type": "object",
		"required": [],
		"properties": {
			"card": {
				"title": "tab区域",
				"type": "string",
				"ui:widget": "dragComponent",
				"ui:width": "100%",
				// "ui:displayType": "row"
			},
		},
		"displayType": "column"
	}
}

export const TITLES = {
	article: {
		title: '宫格id',
		name: '宫格名称',
		nameKey: 'gridName',
		add: '增加文章',
		drawerTitle: '文章',
	},
	banner: {
		title: '卡片id',
		name: '卡片名称',
		nameKey: 'cardName',
		add: '卡片',
		drawerTitle: '卡片',
	},
	tab: {
		title: '方案id',
		name: '方案名称',
		nameKey: 'pageName',
		add: '增加tab',
		drawerTitle: 'tab'
	}
}

export const answerKey = {
	home: 'etfAnswer'
}