import React, {useEffect, useState} from 'react'
import FormRender from 'form-render/lib/antd';
import BraftEditor from 'braft-editor'
// 引入编辑器样式
import 'braft-editor/dist/index.css'
import {Button, Popconfirm, message, Modal, Row, Col, Input} from 'antd';
import {getGridSchema, getCardSchema, getPageSchema } from './config';
import api from 'api';
import styles from './index.less';
import { DraggableArea } from '@/components/DragTags';
import classnames from 'classnames';

const {
    postAnswer
} = api;

const MESSAGE = {
    '0': '添加完成',
    '1': '',
    '2': '修改完成',
    '3': '审批完成',
}

export default function({
    type,
    modify,
    tabId,
    isShowAdd,
    activity,
    setActivity,
    article,
    setArticle,
    setIsShowAdd = () => {},
    handleFetchETFActivity = () => {},
    saveArticle = () => {},
}: {
    type: string
    modify: string
    tabId: string
    isShowAdd: boolean
    activity?: any
    setActivity: any
    setIsShowAdd: Function
    handleFetchETFActivity: Function
    saveArticle: Function
    article?: any
    setArticle: Function
}) {

    const [valid, setValid] = useState([]);
    const [funds, setFunds] = useState([]);
    const [cards, setCards] = useState([]);
    const [editorState, setEditState] = useState(BraftEditor.createEditorState(''));

    /**
     * 提交
     */
    function postConfig() {
        if (valid.length > 0) return message.error('必填项未填');

        if (type === 'article') {
            const content = article.type === 'text' ? editorState.toHTML() : article.url;
            if (!content || (article.type === 'text' && content === '<p></p>')) {
                return message.error('必填项为空');
            }
            saveArticle({
                ...article,
                content,
                url: article.type === 'text' ? '' : article.url,
                text: article.type === 'text' ? editorState.toHTML() : '',
            });
            setIsShowAdd(false);
            return;
        }

        let _form: any = {
            ...activity,
            tabs: cards
        }
        saveConfig(_form);
    }

    function saveConfig(_form) {
        postAnswer({
            value: JSON.stringify(_form)
        }).then((res: any) => {
            _.hideFundLoading()
            if (res.code === '0000') {
                setIsShowAdd(false);
                message.success('修改成功');
                handleFetchETFActivity();
            } else {
                message.error('保存失败，请重新再试！');
            }
        }).catch(() => {
            _.hideFundLoading()
        })
    }

    useEffect(() => {
        const cards = activity.tabs || [];
        setCards(cards);
        if (type === 'article' && article.type === 'text') {
            setEditState(BraftEditor.createEditorState(article.text));
        }
        // initSelect();
    }, [])

    // 基金
    const handleFundChange = (index: number, fund: FundItem) => {
        console.log('基金列表变化', index, fund);
        let _funds = JSON.parse(JSON.stringify(cards));
        _funds[index] = fund;
        setCards(_funds);
    }
    const handleFundAdd = (nameArea) => {
        let _funds: FundItem[] = JSON.parse(JSON.stringify(cards));
        _funds.push({
            id: `etf${(new Date()).valueOf()}`,
        })
        setCards(_funds);
    }
    const handleFundDel = (index: number) => {
        const username = JSON.parse(localStorage.userInfos).username;
        if (username !== 'zhanghao') {
            return message.warn('请联系张浩能删除');
        }
        let _funds: FundItem[] = JSON.parse(JSON.stringify(cards));
        _funds.splice(index, 1);
        // _funds.forEach((item, index) => {
        //     item.id = index.toString();
        // })
        // setFunds(_funds);
        setCards(_funds);
    }

    const dragComponent = ({ value, onChange, name}) => {
        return (<section className={classnames(styles['m-funds'], styles['m-module'])}>
        {/* <p className={classnames(styles['m-title'])}>热门基金</p> */}
        <div className={classnames(styles['m-list'])}>
          <DraggableArea
            isList
            tags={ cards}
            render={({tag, index}: {tag: FundItem, index: number}) => (
                <div className={classnames(styles['tag'], styles['m-fund-item'])}>
                    <Row gutter={[32, 16]} className={classnames(styles['m-list-item'], styles['m-row'])}>
                    <Col span={4}>
                        <span>{tag.id}</span>
                    </Col>      
                    <Col span={8}>
                        <div style={{display: 'flex', alignItems: 'center'}}>
                        {/* <span style={{flexShrink: 0}}>tab名称：</span> */}
                        <Input placeholder='请输入tab名称' maxLength={4} defaultValue={tag.name} onBlur={(e: any) => handleFundChange(index, {
                            name: e.target.value,
                            id: tag.id,
                        })} onPressEnter={(e: any) => handleFundChange(index, {
                            name: e.target.value,
                            id: tag.id,
                        })} />
                        </div>
                    </Col>
                    <Col span={2}>
                        <img className={classnames(styles['m-logo'])} src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAMAAACahl6sAAAAgVBMVEUAAAAUEBwUEBwUEBwMCxYTEBsTDxsTDxsTDxwTDxsTDxsTDxsTDxsTDxsTDhoRCxsTDxwTDxsTDBkTDxwTDxsTDxsSDxoRDRsQDxoVABUUDxsTDxsTDxsRDhoNDRsTDxsTDxoTEBkTDxoAABgSDxsSDhkSDhsSCRoAAAAUDxoUEBximHrhAAAAKnRSTlMAQIDAF/Vm1/nFvaighWstpYwn7su6YksxDOqwX1cS4YhRTgqWRjcdCHVGYskOAAAB5klEQVR42u3dWW7bMBSF4Wtr8CRLthzPTuK0SZve/S+wDYqgaNGH4PJBx8z/7eBANC2KR5QBAAAAAAAAAAAgV8f79WJeDGi+WN9/tVTdyiWsOksiEuNNY3HHwoXM4+Nr7lK2FrR2MVcL+eZyDhZx53JmmVyQ2CVZuqBxIMjWBUUmrokLmgSCuKIqEGTjgjafeWgtXNAqEGTngi6BIKULerA8xlZjEWXlYjYPFvLkYvb25vZvt8YWVk9dxrS2BAeZRcmsszSXmQsodpauHA2uNAAAAAAAAAAA/qfc1+NB1fvS0rUS2wqz2tLsTi7idLFfsthEHFvY1aUsLeiLi3mykO9yhYGqvPU6+bvGPnOppnVB9e0X43/b5tJpPOXSMp0GgshNvtG67KMLirRMzy7onMv02waCdC6otwCJFdXfCos4uJzeQn64mDaT196utExpmb4Ta5nm8YAOAAAAAAAAAJClYz8aVH80S9cvBc6nmix7S9PJ1AbOnVkmW2+7TNqytExpmf6LliktU1qmHzBgy1Tov/CPKpe6bDYt08c8jsR2vwsEeXZBz4EgLy5oZHkc+DvJ4+bX/cVCGhfTWJDUxy7cC4t6lTryd/Fqca3M4qpqLUkp8kNpSj42BAAAAAAAAAAAAGk/ASbTtk4dLR//AAAAAElFTkSuQmCC" />
                    </Col>
                    <Col span={2}>
                        <Popconfirm title="确认删除吗？" onConfirm={() => handleFundDel(index)} okText="确认" cancelText="取消">
                            <Button type="danger">删除</Button>
                        </Popconfirm>
                    </Col>
                    </Row>
                </div>
            )}
            onChange={(data:any) => {
                setCards(data)
            }}
          >
          </DraggableArea>
        </div>
        {<Button type="primary" onClick={() => handleFundAdd(name)} disabled={cards.length >= 4}>新增</Button>}
      </section>)
    }

    const handleRichAreaChange = (value) => {
        // setSaveEditState(value)
        setEditState(value)
    }

    const onChangeForm = function (activityN: any) : any{
        if (type === 'banner' && JSON.stringify(activityN) !== JSON.stringify(activity)) {
            setActivity({
                ...activity,
                ...activityN,
            });
        }
        if (type === 'article' && JSON.stringify(activityN) !== JSON.stringify(article)) {
            setArticle(activityN);
        }
    }


    const schemaFunc = {
        article: getCardSchema,
        banner: getGridSchema,
        tab: getPageSchema,
    }

    let SCHEMA = schemaFunc[type](false);
    return (
        <div>
            <FormRender
                propsSchema={SCHEMA}
                onValidate={setValid}
                formData={type === 'article' ? article : activity}
                onChange={onChangeForm}
                showDescIcon={true}
                widgets={{ dragComponent: dragComponent }}
            />
            {type === 'article' && article.type === 'text' && <div>
                <BraftEditor
                    value={editorState}
                    onBlur={handleRichAreaChange}
                    onSave={handleRichAreaChange}
                    // extendControls={extendControls}
                />
            </div>}
            <div style={{marginTop: 60}}>
                <Button type="primary" onClick={() => {setIsShowAdd(false)}} style={{marginRight: 20}}>取消</Button>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要保存么'}
                    onConfirm={postConfig}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="danger" 
                    >
                        保存
                    </Button>
                </Popconfirm>
            </div>

        </div>
    )
}