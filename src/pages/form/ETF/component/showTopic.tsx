import React from 'react';
import { But<PERSON>,  Popconfirm, Input } from 'antd';
import FundSelect from './fundSelect'

interface iProps {
    funds: any
    blocks: any
    themeHome: any
    setThemeHome: Function    
    item: any
    index: number
    disabled: boolean
}

export default function ({
    funds,
    blocks,
    themeHome,
    setThemeHome,  
    item,
    index,
    disabled
}: iProps) {

    function handleThemeHomeFund(value: string) {
        let _themeHome = [...themeHome]
        _themeHome[index].ETF = funds.filter((item: any) => item.tradecode === value)[0]
        console.log(_themeHome)
        setThemeHome(_themeHome)
    }

    function handleThemeHomeBlock(value: string) {
        let _themeHome = [...themeHome]
        _themeHome[index].index = blocks.filter((item: any) => item['thscode_hq'] === value)[0]
        console.log(_themeHome)
        setThemeHome(_themeHome)
    }

    function handleTextarea(e: any) {
        let _themeHome = [...themeHome]
        _themeHome[index].description = e.target.value
        setThemeHome(_themeHome)
    }

    function clear() {
        let _themeHome = [...themeHome]
        _themeHome[index] = {}
        setThemeHome(_themeHome)
    }

    return (
        <div className="u-block_il u-w300" style={{marginRight: 20, marginBottom: 30}}>
            <FundSelect 
            isFund={false}
            placeholder='主题代码 主题名'
            obj = {blocks}
            width={300}
            handleSelect={handleThemeHomeBlock}
            item={item.index}
            disabled={disabled}
            />
            <Input.TextArea style={{width: 300, height: 80, marginTop: 20, marginBottom: 20}} value={item.description} onChange={handleTextarea} disabled={disabled}></Input.TextArea>
            <FundSelect 
            placeholder='ETF基金 ETF名称'
            obj = {funds}
            width={300}
            handleSelect={handleThemeHomeFund}
            item={item.ETF}
            disabled={disabled}
            />
            <Popconfirm
                title="请问是否清空"
                okText="确定"
                cancelText="取消"
                onConfirm={clear}
                placement="bottom"
            >
                <Button type="primary" className="g-mt20" disabled={disabled}>清空</Button>
            </Popconfirm>
        </div>
    )
}