import React from 'react';
import { But<PERSON>, Popconfirm, Input} from 'antd';
import api from 'api';
import FundSelect from './fundSelect'

interface iProps {
    funds: any
    blocks: any
    themeDetail: any
    setThemeDetail: Function    
    item: any
    index: number
    deleteThemeDetail: Function
    disabled: boolean
}


export default function ({
    funds,
    blocks,
    themeDetail,
    setThemeDetail,  
    item,
    index,
    deleteThemeDetail,
    disabled
}: iProps) {

    function handleThemeDetailFund(value: string) {
        let _themeDetail = [...themeDetail]
        _themeDetail[index].ETF = funds.filter((item: any) => item.tradecode === value)[0]
        console.log(_themeDetail)
        setThemeDetail(_themeDetail)
    }

    function handleThemeDetailBlock(value: string) {
        let _themeDetail = [...themeDetail]
        _themeDetail[index].index = blocks.filter((item: any) => item['thscode_hq'] === value)[0]
        console.log(_themeDetail)
        setThemeDetail(_themeDetail)
    }

    function handleTextarea(e: any) {
        let _themeDetail = [...themeDetail]
        _themeDetail[index].description = e.target.value
        setThemeDetail(_themeDetail)
    }

    return (
        <div className="u-l-middle" style={{marginRight: 20, marginBottom: 30}}>
            <span className="u-block_il u-w100">{index + 1}</span>
            <FundSelect 
            isFund={false}
            placeholder='主题代码 主题名'
            obj = {blocks}
            width={250}
            handleSelect={handleThemeDetailBlock}
            item={item.index}
            disabled={disabled}
            />
            <Input.TextArea style={{width: 250, height: 30, marginLeft: 20, marginRight: 20}} value={item.description} onChange={handleTextarea} disabled={disabled}></Input.TextArea>
            <FundSelect 
            placeholder='ETF基金 ETF名称'
            obj = {funds}
            width={250}
            handleSelect={handleThemeDetailFund}
            item={item.ETF}
            disabled={disabled}
            />
            <Popconfirm
                title="请问是否删除"
                okText="确定"
                cancelText="取消"
                onConfirm={() => {deleteThemeDetail(index)}}
                placement="bottom"
            >
                <Button type="primary" style={{marginLeft: 20}} disabled={disabled}>删除</Button>
            </Popconfirm>
            
        </div>
    )
}