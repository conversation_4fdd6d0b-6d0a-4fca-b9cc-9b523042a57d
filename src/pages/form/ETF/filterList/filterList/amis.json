{"type": "page", "id": "u:9179dc7a5d0b", "title": "ETF综合列表页配置", "body": [{"type": "form", "title": "筛选配置", "body": [{"id": "u:23549852cded", "type": "combo", "draggable": true, "label": false, "name": "filterList", "multiple": true, "multiLine": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:f48adfdd05ca"}, "items": [{"type": "select", "name": "type", "id": "u:2638cd5b0a22", "label": "类型", "options": [{"label": "模块", "value": "module"}], "multiple": false, "required": true, "mode": "inline", "size": "sm", "value": "module"}, {"type": "input-text", "name": "name", "id": "u:b7f484c71954", "label": "展示文案", "required": true, "mode": "inline", "size": "sm"}, {"type": "input-text", "label": "备注描述", "name": "indicDesc", "id": "u:4ce3a129a197", "mode": "inline", "size": "sm"}, {"type": "fieldSet", "title": "折叠/展开子节点", "collapsable": true, "collapsed": true, "visibleOn": "this.hasOwnProperty('type') && this.type && this.type !== 'data'", "body": [{"id": "u:23549852cdef", "type": "combo", "draggable": true, "label": false, "name": "childs", "multiple": true, "multiLine": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:f48adfdd05cb"}, "items": [{"type": "select", "name": "type", "id": "u:2638cd5b0a23", "label": "类型", "options": [{"label": "目录", "value": "category"}, {"label": "数据节点", "value": "data"}], "multiple": false, "required": true, "mode": "inline", "size": "sm"}, {"type": "input-text", "name": "name", "id": "u:b7f484c71955", "label": "展示文案", "required": true, "mode": "inline", "size": "sm", "hiddenOn": "this.hasOwnProperty('type') && this.type && this.type === 'data'"}, {"type": "input-text", "label": "备注描述", "name": "indicDesc", "id": "u:4ce3a129a198", "mode": "inline", "size": "sm"}, {"type": "select", "label": "数据项", "name": "id", "id": "u:d0c0bd340861", "multiple": false, "mode": "inline", "hidden": false, "source": "${indicList}", "visibleOn": "this.hasOwnProperty('type') && this.type && this.type === 'data'", "labelField": "indicName", "valueField": "id", "size": "sm", "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "", "args": {}, "actionType": "custom", "script": "context.props.data.name = event.data.selectedItems.indicName;"}]}}}, {"type": "fieldSet", "title": "折叠/展开子节点", "collapsable": true, "collapsed": true, "visibleOn": "this.hasOwnProperty('type') && this.type && this.type !== 'data'", "body": [{"id": "u:23549852cdeg", "type": "combo", "draggable": true, "label": false, "name": "childs", "multiple": true, "multiLine": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:f48adfdd05cc"}, "items": [{"type": "select", "name": "type", "id": "u:2638cd5b0a24", "label": "类型", "options": [{"label": "数据节点", "value": "data"}], "multiple": false, "required": true, "mode": "inline", "size": "sm", "value": "data"}, {"type": "input-text", "name": "name", "id": "u:b7f484c71956", "label": "展示文案", "required": true, "mode": "inline", "size": "sm", "hiddenOn": "this.hasOwnProperty('type') && this.type && this.type === 'data'"}, {"type": "input-text", "label": "备注描述", "name": "indicDesc", "id": "u:4ce3a129a199", "mode": "inline", "size": "sm"}, {"type": "select", "label": "数据项", "name": "id", "id": "u:d0c0bd340862", "multiple": false, "mode": "inline", "hidden": false, "source": "${indicList}", "visibleOn": "this.hasOwnProperty('type') && this.type && this.type === 'data'", "labelField": "indicName", "valueField": "id", "size": "sm", "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "", "args": {}, "actionType": "custom", "script": "context.props.data.name = event.data.selectedItems.indicName;"}]}}}], "syncFields": [], "mode": "normal", "strictMode": true}], "id": "u:f52024f77495"}], "syncFields": [], "mode": "normal", "strictMode": true}], "id": "u:f52024f77496"}], "syncFields": [], "mode": "normal", "strictMode": true}, {"type": "form", "title": "", "body": [{"label": "", "type": "textarea", "name": "config", "id": "u:892fa08400ce", "minRows": 3, "maxRows": 20}], "id": "u:557953fb3741", "actions": [{"type": "button", "label": "获取配置", "onEvent": {"click": {"actions": [{"componentId": "u:892fa08400ce", "args": {"value": "${filterList}"}, "actionType": "setValue"}]}}, "id": "u:3f6d93564d96", "level": "primary"}, {"type": "button", "label": "导入配置", "onEvent": {"click": {"actions": [{"componentId": "u:8f831348565f", "args": {"fromPage": true, "value": {"filterList": "${DECODEJSON(config)}"}}, "actionType": "setValue"}]}}, "id": "u:0601c0ba6d8f", "level": "primary"}]}], "mode": "normal", "id": "u:8f831348565f", "submitText": "保存", "api": {"url": "/common_config/kv_data_save", "method": "post", "messages": {}, "data": {"key": "normal_config_etf_hqtab_filter_list", "value": "${ENCODEJSON(filterList)}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/kv_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "normal_config_etf_hqtab_filter_list"}, "adaptor": "const filterList = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    filterList: filterList,\r\n  },\r\n};"}}], "regions": ["body", "header"]}