import moment from "moment";


interface noticeProps {
    show?: string,
    startTime?: string,
    endTime?: string,
    content?: string,
    url?: string,
}
interface articleProps {
    show?: string,
    startTime?: string,
    endTime?: string,
    content?: string,
    contentIntroduce?: string,
    url?: string,
}

interface contentProps {
    notice?: noticeProps,
    article?: articleProps,
}
interface listProps{
    id: number,
    name?: string, //名称
    submarket_hq?: string,
    type?: string, //类型
    tags?: Array<noticeProps> //标签
}

interface dataProps {
    index?: number, //唯一标识
    name?: string, //板块名称
    id?: string, //板块id
    content?: contentProps, //内容部分
    list?: Array<listProps> //列表部分
    stocklist?: Array<string>,
    marketlist?: Array<string>,
    marketlistStr?: string,
    nameList?: Array<string>,
    tagList?: Array<Array<string>>,
}

export {
    contentProps,
    listProps,
    dataProps, 
    noticeProps
}