import React, { useState, useEffect } from 'react';
import {Row, Col, Button, Select, Popconfirm, message, Input } from 'antd';
import api from 'api'
import {listProps} from './interface'
import FORM_CONFIG from './form2.json';
import FormRender from "form-render/lib/antd";
import styles from './index.less'

const { fetchETFName} = api;
interface iProps {
    data: listProps,
    funds: Array<any>,
    handleSelect: (type: string, data: any, index: number) => boolean,
    edit: Boolean,
    position:number

}
export default function ({ funds, handleSelect, data, edit, position }: iProps) {

    // const [data, setData] = useState<any>({})
    const [list, setList] = useState<Array<any>>([])
    // const [edit, setEdit] = useState<Boolean>(false)


    useEffect(() => {
        if (!funds) return
        setList(funds)
        // setEdit(true)
    }, [funds])
    //选择框单独处理
    function onChange(value: string, text: string): void {
        let _text = text.split(' ')[2] || ''
        let flag = handleSelect('type', {value, _text}, data.id)
        if(!flag) return
        fetchETFName({},value).then((res:any) => {
            if(res.status_code === 0 && res.data) {
                handleSelect('name', res.data.fundName, data.id)
            } else {
                message.error(res.status_msg)
            }
        })
    }

    function onSearch(val: string): void {
        setList(funds.
            filter((item: any, index: number) => item.tradecode.indexOf(val) > -1 || item.secname_pub205.indexOf(val) > -1))
    }

    return (
        <section className={styles['list-item']}>
            <Row>
            <Col span={1}>{position + 1}</Col>

                <Col span={11}>
                    <Select
                        showSearch
                        style={{ width: '450px' }}
                        // placeholder={placeholder}
                        optionFilterProp="children"
                        value={data.type || ''}
                        onChange={(e: any, d:any) => onChange(e, d.props.children)}
                        onSearch={onSearch}
                        disabled={!edit}
                    >
                        {
                            list.map((item: any, index: number) => {
                                return (
                                    <Select.Option key={index} value={item.tradecode} >{item.tradecode + ' ' + item.secname_pub205 + ' ' +  item.submarket_hq}</Select.Option>
                                )
                            })
                        }
                    </Select>
                </Col>
                <Col span={4}>
                <Input value={data.name} onChange={(e: any) => handleSelect('name', e.target.value, data.id)} disabled={!edit}/>
                </Col>
                <Col span={8}>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => handleSelect('delete', null, data.id)}
                        // onCancel={cancel}
                        okText="删除"
                        cancelText="取消"
                    >
                        <Button type="danger" disabled={!edit}>删除</Button>
                    </Popconfirm>
                </Col>
            </Row>

            <section className={'g-mt10'}>
                <FormRender
                    propsSchema={FORM_CONFIG}
                    displayType='row'
                    formData={data.tags || []}
                    onValidate={() => console.log()}
                    onChange={(e: any) => handleSelect('tags', e, data.id)}
                    readOnly={!edit}
                />
            </section>

        </section>
    )
}