import React, { useState, useEffect } from 'react';
import {  <PERSON><PERSON>, DatePicker, message } from 'antd';
import api from 'api'
import { listProps } from './interface'
import { DraggableArea } from '@/components/DragTags'
import ListItem from './listItem'
interface iProps {
    originData: Array<listProps>,
    funds: Array<any>,
    handelSave: (type: string, value: any, flag: string) => void
}
export default function ({ originData, handelSave, funds }: iProps) {

    const [data, setData] = useState<Array<listProps>>([])
    const [edit, setEdit] = useState<Boolean>(false)


    useEffect(() => {
        setEdit(false)

        if (!originData || funds?.length === 0) return
        setData(originData)
    }, [originData, funds])

    function handleFloor(item: Array<listProps>) {
        setData(item)
    }
    function addItem() {
        let _data = [...data]
        let _item = {
            id: new Date().getTime(),
            name: '',
            submarket_hq: '',
            type: ''
        }
        _data.push(_item)
        setData(_data)
    }
    function handleSelect(type: string, value: any, id: number) {
        console.log(type, value, data)
        if(type === 'type') {
            for(let i=0;i<data.length;i++) {
                if(data[i].type === value.value) {
                    message.error('操作失败,该ETF已存在')
                    return false
                }
            }
        }
        console.log('continue')
        let _data = [...data]
        let target = _data.find((val: listProps) => val.id === id)
        if (target) {
            switch (type) {
                case 'type':
                    target.type = value.value
                    target.submarket_hq = value._text
                    break
                case 'name':
                    target.name = value
                    break
                case 'tags':
                    target.tags = value
                    break
                case 'delete':
                    let _index = _data.findIndex((val: listProps) => val.id === id)
                    _data.splice(_index, 1)
                    break
            }

            setData(_data)
        }
        return true

    }

    function save() {

        for(let i=0;i<data.length;i++) {
            if(!data[i].type || !data[i].name) {
                message.error(`不允许保存空数据，第${i+1}条未选择`)
                 return
            }
        }
        handelSave('list', data, 'drawer')
    }

    return (
        <section className={'m-etf-plate'}>
            {
                edit ?
                    <Button onClick={save} >保存</Button> :
                    <Button onClick={() => setEdit(true)} >编辑</Button>
            }
            <Button className={'g-ml20'} onClick={addItem} >增加</Button>

            <DraggableArea
                isList
                tags={data}
                render={({ tag, index }: any) => (
                    <ListItem
                        funds={funds}
                        handleSelect={handleSelect}
                        data={tag}
                        edit={edit}
                        position={index}
                    />
                )}
                onChange={(item: any) => {
                    handleFloor(item)
                }}
            />
        </section>
    )
}