import React, { useState, useEffect } from 'react';
import { Input, But<PERSON>, Popconfirm, message, Table, Modal, Row, Col, Drawer, Radio } from 'antd';
import api from 'api'
import Drawer1 from './drawer1'
import Drawer2 from './drawer2'
import { contentProps, listProps, dataProps, noticeProps } from './interface'
const { fetchETFPlate, postETFPlate, getETFFundList } = api;



export default function () {

    const [auditData, setAuditData] = useState<Array<dataProps>>([])
    const [unauditData, setUnauditData] = useState<Array<dataProps>>([])
    const [status, setStatus] = useState<string>('audit') //audit:已审核  submit：待审核
    const [user, setUser] = useState<string>('')
    const [selectData, setSelectData] = useState<dataProps>({})
    const [modalVisible, setModalVisible] = useState<boolean>(false)
    const [drawer1Visible, setDrawer1Visible] = useState<boolean>(false)
    const [drawer2Visible, setDrawer2Visible] = useState<boolean>(false)
    const [eidt, setEidt] = useState<boolean>(false)
    const [funds, setFunds] = useState([])
    useEffect(() => {
        // let _data = [
        //     {
        //         index: 0,
        //         name: 'test'
        //     }
        // ]
        // setAuditData(_data)
        fetchETFPlate().then((res: any) => {
            if (res.code === '0000' && res.data) {
                let _data = JSON.parse(res.data)
                // console.log(_data)
                setUnauditData(_data.unauditData)
                setAuditData(_data.auditData)
                setStatus(_data.status || 'audit')
                setUser(_data.lastEditor)
            }
        })
        getETFFundList().then((res: any) => {
            if (res.code === '0000' || res.data) {
                setFunds(res.data)
            } else {
                message.error(res.message);
            }
        }).catch((e: any) => {
            message.error('系统异常');
        })
    }, [])
    let columns: any = [
        { title:'序号' , render:(text:any, record:any, index:number) => `${index + 1}`},
        { title: '名称', dataIndex: 'name', key: 'name' },
        { title: 'id', dataIndex: 'id', key: 'id' },
        {
            title: '链接', dataIndex: 'url', key: 'url', render: (text: any, record: any) => location.host.includes('febs') ?
                `https://testfund.10jqka.com.cn/scym_scsy/public/wjz/ETFPlate/dist/index.html?id=${record.id}#/` :
                `https://fund.10jqka.com.cn/scym_scsy/public/hq/ETFPlate/dist/index.html?id=${record.id}#/`
        },


        {
            title: '操作', dataIndex: 'options', key: 'options', render: (text: any, record: any) => { return renderBtns(text, record) }
        }
    ]
    /**
     * @description 列表增加项，及初始化该项数据
     */
    function addItem() {
        let _item = {
            index: new Date().getTime(),
            name: '',
            id: '',
            content: {
                notice: {
                    show: 'false',
                    startTime: '',
                    endTime: '',
                    content: '',
                    url: '',
                },
                article: {
                    show: 'false',
                    startTime: '',
                    endTime: '',
                    content: '',
                    contentIntroduce: '',
                    url: '',
                }

            },
            list: []
        }
        let _unauditData = [...unauditData]
        _unauditData.push(_item)
        console.log('data', _unauditData)
        setUnauditData(_unauditData)
    }
    function deleteItem(record: any) {
        let _unauditData = [...unauditData]
        let _index = _unauditData.findIndex((val: any) => val.index === record.index)
        console.log(_index)
        if (_index > -1) {
            _unauditData.splice(_index, 1)
            setUnauditData(_unauditData)

        }
    }

    /**
     * 
     * @param text 暂无用
     * @param record table行数据
     * @returns 按钮组
     * @description 生成table的按钮
     */
    function renderBtns(text: any, record: any) {
        return <section>
            <Button onClick={() => { setSelectData(record); setModalVisible(true) }}>修改名称</Button>
            <Button onClick={() => { setSelectData(record); setDrawer1Visible(true) }} className={'g-ml20'}>内容配置</Button>
            <Button onClick={() => { setSelectData(record); setDrawer2Visible(true) }} className={'g-ml20'}>行情配置</Button>
            <Popconfirm
                title="确定删除?"
                onConfirm={() => { deleteItem(record) }}
                // onCancel={cancel}
                okText="删除"
                cancelText="取消"
            >
                <Button type={'danger'} className={'g-ml20'}>删除</Button>
            </Popconfirm>

        </section>
    }

    /**
     * 
     * @param _selectData 选中的table行数据
     * @description 为了减少更新数据的影响部分，对于正在修改的行数据复制副本，修改副本后统一更新至源数据
     */
    function handleSelect(_selectData: dataProps) {
        let _index = unauditData.findIndex(val => val.index === _selectData.index)
        let _unauditData = [...unauditData]
        _unauditData[_index] = _selectData || selectData
        let _filter = _unauditData.filter(val => val.id === _selectData.id)
        if(_filter.length > 1) {
            message.error('id已存在')
            return
        }
        setUnauditData(_unauditData)
        setModalVisible(false)
        setDrawer1Visible(false)
        setDrawer2Visible(false)
        setEidt(false)
    }
    /**
     * 
     * @param type 修改的属性名称
     * @param value 修改值
     * @param flag  来自于modal还是drawer
     * @description 修改选定的table行数据的属性值，如果是来自drawer的会调用修改源数据方法
     */
    function changeSelectData(type: string, value: any, flag: string = 'modal') {
        let _selectData = { ...selectData }
        switch (type) {
            case 'name':
                _selectData.name = value;
                break
            case 'id':
                _selectData.id = value;
                break
            case 'content':
                _selectData.content = value;
                break
            case 'list':
                _selectData.list = value;
                let _stocklist: Array<string> = [], _marketlist: Array<string> = [], _nameList: Array<string> = [], _tagList: Array<Array<string>> = []
                for (let i = 0; i < value.length; i++) {
                    _stocklist.push(value[i].type)
                    _marketlist.push(value[i].submarket_hq)
                    _nameList.push(value[i].name)
                    _tagList.push(value[i].tags)
                }
                //生成主站协议所需要的数据格式  36(000001,000002);18(123456,);其他字段保留留作备用
                let _data:any = {}
                let marketListType:Array<string> = []
                for (let i = 0; i < _stocklist.length; i++) {
                    if (!marketListType.includes(_marketlist[i])) {
                        _data[_marketlist[i]] = [_stocklist[i]]
                        marketListType.push(_marketlist[i])
                    } else {
                        _data[_marketlist[i]].push(_stocklist[i])
                    }
                }
                console.log(_data)
                let keyList = Object.keys(_data)
                let str = ''
                for (let i = 0; i < keyList.length; i++) {
                    let marketStr = `${keyList[i]}(code);`
                    let codeStr = ''
                    for (let j = 0; j < _data[keyList[i]].length; j++) {
                        codeStr = codeStr + _data[keyList[i]][j] + ','
                    }
                    let _marketStr = marketStr.replace('code', codeStr)
                    str = str + _marketStr
                }
                console.log('str',str)
                _selectData.stocklist = _stocklist
                _selectData.marketlist = _marketlist
                _selectData.marketlistStr = str
                _selectData.nameList = _nameList
                _selectData.tagList = _tagList
                break
        }
        console.log('dddata', _selectData)
        setSelectData(_selectData)
        if (flag === 'drawer') {
            handleSelect(_selectData)
        }
    }

    function changeAllNotice(noticeData: noticeProps) {
        console.log('noticedata', noticeData)
        try {
            let _unauditData = []
            for (let i = 0; i < unauditData.length; i++) {
                _unauditData[i] = unauditData[i]
                let _content = _unauditData[i].content || {}
                _content.notice = noticeData
                _unauditData[i].content = _content
            }
            console.log('_unauditData', _unauditData)
            setUnauditData(_unauditData)
            message.info('同步成功')
        } catch (e) {
            message.error(e)
        }

    }

    function save(type: string) {
        if(type === 'audit' && localStorage.name === user) {
            message.error('提交人与审核人不可为同一个')
            return
        }
        let _status = ''
        let _auditData = []
        if (type === 'submit') {
            _status = 'submit' //提交修改
            _auditData = auditData
        } else {
            _status = 'audit'  //审核任务
            _auditData = unauditData
        }
        let sendData = {
            status: _status,
            lastEditor: localStorage.name,
            unauditData,
            auditData: _auditData
        }
        postETFPlate({
            value: JSON.stringify(sendData)
        }).then((res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success(type === 'submit' ? '提交成功' : '审核成功');
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }
    return (
        <section>
            <Button className={'g-mb20'} onClick={addItem}>新增</Button>
            <Button className={'g-mb20 g-ml20'} onClick={() => { save('submit') }}>保存</Button>
            <Button className={'g-mb20 g-ml20'} onClick={() => { save('audit') }} disabled={status === 'audit'}>审核</Button>
            <Table
                columns={columns}
                dataSource={unauditData}
            />
            <Modal
                title="关联同顺号"
                visible={modalVisible}
                onOk={() => handleSelect(selectData)}
                onCancel={() => setModalVisible(false)}
                okText="确认"
                cancelText="取消"
            >
                <Row className={'g-mb20'}>
                    <Col span={6}>
                        <p>名称</p>
                    </Col>
                    <Col span={18}>
                        <Input value={selectData.name} onChange={(e: any) => changeSelectData('name', e.target.value, 'modal')} />
                    </Col>
                </Row>
                <Row>
                    <Col span={6}>
                        <p>id</p>
                    </Col>
                    <Col span={18}>
                        <Input value={selectData.id} onChange={(e: any) => changeSelectData('id', e.target.value, 'modal')} />
                    </Col>
                </Row>
            </Modal>
            <Drawer
                width="800"
                title="内容配置"
                placement="right"
                closable={true}
                onClose={() => setDrawer1Visible(false)}
                visible={drawer1Visible}>
                <Drawer1
                    originData={selectData.content || {}}
                    handelSave={changeSelectData}
                    changeAllNotice={changeAllNotice}
                />
            </Drawer>
            <Drawer
                width="1200"
                title="内容配置"
                placement="right"
                closable={true}
                onClose={() => setDrawer2Visible(false)}
                visible={drawer2Visible}>
                <Drawer2
                    originData={selectData.list || []}
                    handelSave={changeSelectData}
                    funds={funds || []}
                />
            </Drawer>
        </section>
    )
}