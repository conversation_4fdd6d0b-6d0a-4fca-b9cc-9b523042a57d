export default function (isEdit) {
	return {
		"type": "object",
		"required": [
			"activeId",
			"activeName",
			"status",
			"title",
			"picture",
			"picture1",
			"product",
			"seasonContent",
			"beginTime",
			"endTime",
			"participateNum",
			"bannerContent",
			"bannerLabel",
			"bannerUrl",
			"recommend",
			"path",
			"activityRules",
			"vuser",
			"vheadUrl",
		],
		"properties": {
			"activeId": {
				"title": "活动id",
				"type": "string",
				"pattern": /^\w+$/,
				"description": "需要用英文输入，唯一标识符，不能重复",
				"ui:options": {},
				"ui:disabled": isEdit ? true: false,
			},
			"activeName": {
				"title": "活动名称",
				"type": "string",
				"ui:options": {},
			},
			"title": {
				"title": "首页奖励描述",
				"type": "string",
				"ui:options": {},
			},
			"picture": {
				"title": "奖励图片1",
				"type": "string",
				"ui:widget": "uploadImg",
				"ui:width": "46%"
			},
			"picture1": {
				"title": "奖励图片2",
				"type": "string",
				"ui:widget": "uploadImg",
				"ui:width": "46%"
			},
			"status": {
				"title": "活动状态（紧急状态下线使用）",
				"type": "string",
				"enum": [
					"1",
					"0"
				],
				"enumNames": [
					"开始",
					"结束"
				],
				"ui:widget": "radio"
			},
			"product": {
				"title": "ETF产品",
				"type": "array",
				"items": {
					"type": "object",
					"properties": {
						"plateName": {
							"title": "板块名称",
							"type": "string",
							"ui:options": {}
						},
						"describe": {
							"title": "解读",
							"type": "string",
							"ui:options": {}
						},
						"etfName": {
							"title": "产品名称",
							"type": "string",
							"ui:options": {}
						},
						"etfCode": {
							"title": "etf产品code",
							"type": "string",
							"ui:options": {}
						},
						"etfMarket":{
							"title": "etf市场code",
							"type": "string",
							"ui:options": {}
						}
					}
				},
				"ui:options": {}
			},
			"items": {
				"type": "object",
				"title": "赛季时间",
			},
			"seasonContent": {
				"title": "赛季logo",
				"type": "string",
				"ui:widget": "uploadImg",
			},
			"beginTime": {
				"ui:labelWidth": 150,
				"title": "活动开始时间",
				"type": "string",
				"format": "date",
				"ui:width": "46%"
			},
			"endTime": {
				"ui:labelWidth": 150,
				"title": "活动结束时间",
				"type": "string",
				"format": "date",
				"ui:width": "46%"
			},
			'participateNum': {
				"title": "参加人数",
				"type": "number",
				"format": "dateTime",
				"ui:width": "46%"
			},
			"bannerItems": {
				"type": "object",
				"title": "slogan banner",
			},
			"bannerContent": {
				"title": "banner文案",
				"type": "string",
				"ui:options": {},
				"maxLength": 40,
				"ui:width": "46%",
			},
			"bannerLabel": {
				"ui:labelWidth": 150,
				"title": "banner副文案",
				"type": "string",
				"ui:options": {},
				"maxLength": 40,
				"ui:width": "46%",
			},
			"bannerUrl": {
				"ui:labelWidth": 150,
				"title": "banner跳转链接",
				"type": "string",
				"ui:options": {},
				"ui:width": "46%",
			},
			"recommend": {
				"title": "热门推荐",
				"type": "array",
				"items": {
					"type": "object",
					"properties": {
						"etfName": {
							"title": "产品名称",
							"type": "string",
							"ui:options": {}
						},
						"etfCode": {
							"title": "etf产品code",
							"type": "string",
							"ui:options": {}
						},
						"etfMarket":{
							"title": "etf市场code",
							"type": "string",
							"ui:options": {}
						}
					}
				},
				"ui:options": {}
			},
			"path": {
				"ui:labelWidth": 150,
				"title": "查看活动入口",
				"type": "string",
				"ui:options": {}
			},
			"activityRules": {
				"title": "活动规则",
				"type": "array",
				"items": {
					"type": "object",
					"properties": {
						"text": {
							"title": "",
							"type": "string",
							"ui:options": {}
						}
					}
				},
				"ui:options": {}
			},
			"Vitems": {
				"title": "大V",
				"type": "object",
			},
			"vuser": {
				"title": "大V名称",
				"type": "string",
				"ui:options": {},
				"ui:width": "46%",
			},
			"vheadUrl": {
				"ui:labelWidth": 150,
				"title": "头像跳转链接",
				"type": "string",
				"ui:options": {},
				"ui:width": "46%",
			},
		}
	}
}