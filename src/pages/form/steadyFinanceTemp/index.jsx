/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import styles from './index.less';
import api from 'api';
import { Button, Input, message, Select } from 'antd';

const { saveSteadyFinanceInfoTemp, getSteadyFinanceInfoTemp } = api;
const { Option  } = Select;
export default function () {
    const [formData,setFormData] = useState({
        bannerList: [
            {
                name: '',
                img: '',
                link: ''
            }
        ],
        modolList: [
            {
                title: '',
                subTitle: '',
                productList: [
                    {
                        name:'',
                        code:'',
                        profit: '',
                        profitDesc: '',
                        term: '',
                        termDesc: '',
                        link: '',
                    }
                ]
            },
            {
                title: '',
                subTitle: '',
                productList: [
                    {
                        name:'',
                        code:'',
                        profit: '',
                        profitDesc: '',
                        term: '',
                        termDesc: '',
                        link: '',
                    }
                ]
            },
            {
                title: '',
                subTitle: '',
                productList: [
                    {
                        name:'',
                        code:'',
                        profit: '',
                        profitDesc: '',
                        term: '',
                        termDesc: '',
                        link: '',
                    }
                ]
            },
        ]
    });
    // const [isUploading,setIsUploading] = useState(false); // 图片上传中
    useEffect(()=>{
        initFormData();
    }, []);

    // 初始化表单
    const initFormData = () => {
        getSteadyFinanceInfoTemp().then((res)=>{
            if (res.code === '0000') {
                const _formData = res.data ? JSON.parse(res.data) : formData;
                setFormData(_formData);
            }
        }).catch()
    }
    // 添加或删除banner
    const addOrDeleteBanner = (type,index) => {
        const _formData = deepClone(formData);
        if (type === 'add') {
            _formData.bannerList.push({
                name: '',
                img: '',
                link: ''
            });
        } else if (type === 'delete') {
            _formData.bannerList.splice(index,1);
        }
        setFormData(_formData);
    }
    // 添加或删除产品
    const addOrDeleteProduct = (type,modolIndex,productIndex) => {
        const _formData = deepClone(formData);
        if (type === 'add') {
            _formData.modolList[modolIndex].productList.push({
                name:'',
                code:'',
                profit: '',
                profitDesc: '',
                term: '',
                termDesc: '',
                link: '',
            });
        } else if (type === 'delete') {
            _formData.modolList[modolIndex].productList.splice(productIndex,1);
        }
        setFormData(_formData);
    }
    // 输入banner信息
    const changeBannerInfo = (bannerIndex,attr,value) => {
        const _formData = deepClone(formData);
        _formData.bannerList[bannerIndex][attr] = value;
        setFormData(_formData);
    }
    // 输入模块标题或副标题
    const changeModolTitle = (modolIndex,attr,value) => {
        const _formData = deepClone(formData);
        _formData.modolList[modolIndex][attr] = value;
        setFormData(_formData);
    }
    // 输入模块产品信息
    const changeModolProduct = (modolIndex,productIndex,attr,value) => {
        const _formData = deepClone(formData);
        _formData.modolList[modolIndex].productList[productIndex][attr] = value;
        setFormData(_formData);
    }
    // 上传图片
    // const uploadImg = (options,bannerIndex)=> {
    //     if (options.file.size > 5120000) {
    //         message.error('文件大小不得超过5M');
    //         return;
    //     }
    //     setIsUploading(true);
    //     message.info('图片上传中');
    //     let params = new FormData();
    //     params.append('file', options.file);
    //     commonUpload(params).then((res)=>{
    //         if (res.status_code === 0) {
    //             let _formData = deepClone(formData);
    //             _formData.bannerList[bannerIndex].img = res.data;
    //             setFormData(_formData);
    //             message.success('上传成功');
    //         } else {
    //             message.error(res.status_msg);
    //         }
    //         setIsUploading(false);
    //     }).catch(()=>{
    //         message.error('上传失败');
    //         setIsUploading(false);
    //     });
    // }
    // 表单检查
    const checkFormData = () => {
        let isPass = true;
        // 所有banner的每一项必填
        formData.bannerList && formData.bannerList.forEach((item,index)=>{
            if (isPass === false) return; 
            if (!item.name || !item.img || !item.link) {
                message.error('请填写必填项');
                isPass = false;
            }
        });
        // 所有模块的主标题必填
        formData.modolList && formData.modolList.forEach((item,index)=>{
            if (isPass === false) return; 
            if (!item.title) {
                message.error('请填写必填项');
                isPass = false;
            }
            // 所有模块的所有产品的每一项必填
            item.productList && item.productList.forEach((item2,index2)=>{
                if (isPass === false) return; 
                if (!item2.name || !item2.code || !item2.profit || !item2.profitDesc || !item2.term || !item2.termDesc) {
                    message.error('请填写必填项');
                    isPass = false;
                }
            });
        });
        return isPass;
    }
    // 保存
    const save = () => {
        if (!checkFormData()) return;
        saveSteadyFinanceInfoTemp({
            value: JSON.stringify(formData),
        }).then((res)=>{
            if (res.code === '0000') {
                message.success('保存成功');
            }
        }).catch();
    }
    // 深拷贝对象/数组
     const deepClone = (obj) => {
        return JSON.parse(JSON.stringify(obj));
    };

    return (
        <div className={styles['steady-finance-temp']}>
            <Button type="primary" onClick={()=>{save()}}>保存</Button>
            <div className={styles['banner-list']}>
                {
                    formData && formData.bannerList.map((item,index)=>{
                        return (
                            <ul className={styles['banner-item']} key={index}>
                                <li>
                                    <div className={styles['label']}><span style={{color:'#f00'}}>*</span>banner名称：</div>
                                    <Input style={{width: 400}} placeholder="请输入banner名称" value={item.name} onChange={(e)=>{changeBannerInfo(index,'name',e.target.value)}}/>
                                </li>
                                <li>
                                    <div className={styles['label']}><span style={{color:'#f00'}}>*</span>banner图片(305px*162px)：</div>
                                    <Input style={{width: 400}} placeholder="请输入图片链接" value={item.img} onChange={(e)=>{changeBannerInfo(index,'img',e.target.value)}}/>
                                    {/* <div className={styles['preview']}>
                                        <img style={{display: item.img ? '' : 'none'}} src={item.img} alt=""/>
                                    </div>
                                    <Upload
                                        customRequest={(options)=>{uploadImg(options,index)}}
                                        showUploadList={false}
                                    >
                                        <Button style={{verticalAlign:'bottom'}} disabled={isUploading}>上传</Button>
                                    </Upload> */}
                                </li>
                                <li>
                                    <div className={styles['label']}><span style={{color:'#f00'}}>*</span>跳转链接：</div>
                                    <Input style={{width: 600}} placeholder="请输入跳转链接" value={item.link} onChange={(e)=>{changeBannerInfo(index,'link',e.target.value)}}/>
                                </li>
                                <div className={styles['delete-btn']} onClick={()=>{addOrDeleteBanner('delete',index)}}>×</div>
                            </ul>
                        )
                    })
                }
            </div>
            <Button type="info" onClick={()=>{addOrDeleteBanner('add')}}>添加banner</Button>
            <div className={styles['modol-list']}>
                {
                     formData && formData.modolList.map((item,index)=>{
                        return (
                            <ul className={styles['modol-item']}  key={index}>
                                <li style={{fontSize: 20}}>#{index+1}</li>
                                <li>
                                    <div className={styles['label']}><span style={{color:'#f00'}}>*</span>栏目标题：</div>
                                    <Input style={{width: 400}} placeholder="请输入栏目标题" value={item.title} onChange={(e)=>{changeModolTitle(index,'title',e.target.value)}}/>
                                </li>
                                <li>
                                    <div className={styles['label']}>栏目副标题：</div>
                                    <Input style={{width: 400}} placeholder="请输入栏目副标题" value={item.subTitle} onChange={(e)=>{changeModolTitle(index,'subTitle',e.target.value)}}/>
                                </li>
                                <li className={styles['product-list']}>
                                    {
                                        item && item.productList.map((item2,index2) => {
                                            return (
                                                <ul className={styles['product-item']}>
                                                    <li>
                                                        <div className={styles['label']}><span style={{color:'#f00'}}>*</span>产品名称：</div>
                                                        <Input style={{width: 400}} placeholder="请输入产品名称：" value={item2.name} onChange={(e)=>{changeModolProduct(index,index2,'name',e.target.value)}}/>
                                                    </li>
                                                    <li>
                                                        <div className={styles['label']}><span style={{color:'#f00'}}>*</span>产品代码：</div>
                                                        <Input style={{width: 400}} placeholder="请输入产品代码：" value={item2.code} onChange={(e)=>{changeModolProduct(index,index2,'code',e.target.value)}}/>
                                                    </li>
                                                    <li>
                                                        <div className={styles['label']}><span style={{color:'#f00'}}>*</span>产品收益值：</div>
                                                        {/* <Input style={{width: 400}} placeholder="请输入产品收益值：" value={item2.profit} onChange={(e)=>{changeModolProduct(index,index2,'profit',e.target.value)}}/> */}
                                                        <Select style={{width:'400px'}} value={item2.profit} onChange={(e) => {changeModolProduct(index,index2,'profit',e)}}>
                                                            <Option value={'month'}>近1个月</Option>
                                                            <Option value={'tmonth'}>近3个月</Option>
                                                            <Option value={'hyear'}>近半年</Option>
                                                            <Option value={'year'}>近1年</Option>
                                                            <Option value={'tyear'}>近3年</Option>
                                                            <Option value={'now'}>成立以来</Option>
                                                        </Select>
                                                    </li>
                                                    <li>
                                                        <div className={styles['label']}><span style={{color:'#f00'}}>*</span>产品收益描述：</div>
                                                        <Input style={{width: 400}} placeholder="请输入产品收益描述：" value={item2.profitDesc} onChange={(e)=>{changeModolProduct(index,index2,'profitDesc',e.target.value)}}/>
                                                    </li>
                                                    <li>
                                                        <div className={styles['label']}><span style={{color:'#f00'}}>*</span>产品期限：</div>
                                                        <Input style={{width: 400}} placeholder="请输入产品期限：" value={item2.term} onChange={(e)=>{changeModolProduct(index,index2,'term',e.target.value)}}/>
                                                    </li>
                                                    <li>
                                                        <div className={styles['label']}><span style={{color:'#f00'}}>*</span>产品期限描述：</div>
                                                        <Input style={{width: 400}} placeholder="请输入产品期限描述：" value={item2.termDesc} onChange={(e)=>{changeModolProduct(index,index2,'termDesc',e.target.value)}}/>
                                                    </li>
                                                    <li>
                                                        <div className={styles['label']}>产品跳转链接：</div>
                                                        <Input style={{width: 400}} placeholder="请输入产品跳转链接：" value={item2.link} onChange={(e)=>{changeModolProduct(index,index2,'link',e.target.value)}}/>
                                                    </li>
                                                    <div className={styles['delete-btn']} onClick={()=>{addOrDeleteProduct('delete',index,index2)}}>×</div>
                                                </ul>
                                            )
                                        })
                                    }
                                </li>
                                <Button type="info" onClick={()=>{addOrDeleteProduct('add',index)}}>添加产品</Button>
                            </ul>
                        )
                     })
                }
            </div>
        </div>
    )
}
