.steady-finance-temp {
    .banner-list {
        .banner-item {
            position: relative;
            width: 100%;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            margin-top: 20px;
            padding: 20px;
            li {
                margin-bottom: 10px;
            }
            .label {
                display: inline-block;
            }
            .preview {
                display: inline-flex;
                width: 152px;
                height: 81px;
                border: 1px dashed #d9d9d9;
                border-radius: 8px;
                margin-right: 20px;
                img {
                    display: inline-block;
                    width: 100%;
                    height: 100%;
                }
            }
            .delete-btn {
                position: absolute;
                top: 0px;
                right: 20px;
                font-size: 40px;
                font-weight: 600;
                line-height: 1;
                cursor: pointer;
            }
        }
    }
    .modol-list {
        .modol-item {
            width: 100%;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            margin-top: 20px;
            padding: 20px;
            li {
                margin-bottom: 10px;
            }
            .label {
                display: inline-block;
            }
            .product-list {
                .product-item {
                    position: relative;
                    width: 100%;
                    border: 1px solid #d9d9d9;
                    border-radius: 8px;
                    margin-top: 20px;
                    padding: 20px;
                    .delete-btn {
                        position: absolute;
                        top: 0px;
                        right: 20px;
                        font-size: 40px;
                        font-weight: 600;
                        line-height: 1;
                        cursor: pointer;
                    }
                }
            }
        }
    }
}