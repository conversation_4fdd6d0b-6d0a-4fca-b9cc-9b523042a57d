import React from 'react';
import api from 'api';
import { autobind } from 'core-decorators';
import { Button, Input, Form, message, Popconfirm, Table } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import styles from './index.less';

const { downOlasFile, getAllGroupId } = api;

const columns = [{
    title: 'groupIDs',
    dataIndex: 'groupIDs',
    key: 'groupIDs',
}, {
    title: 'description',
    dataIndex: 'description',
    key: 'description',
}, {
    title: 'creatTime',
    dataIndex: 'creatTime',
    key: 'creatTime',
}]

interface OlasMessageProps extends FormComponentProps {
    [propsName: string]: any
}
@autobind
class OlasMessage extends React.Component<OlasMessageProps, any> {
    constructor(props: OlasMessageProps) {
        super(props);
        this.state = {
            params: {
                actID: '',
                groupIDs: '',
                description: ''
            },
            dataSource: []
        }
    }

    // 下载 Olas 运营文件
    async fetchOlasFile() {
        let { params } = this.state;
        this.setState({
            dataSource: []
        })
        try {
            const { code, data, message: msg } = await downOlasFile(params);
            if (code === '0000' && data) {
                message.info('正在下载Olas运营信息，清稍后确认');
                this.setState({
                    params: {
                        actID: '',
                        groupIDs: '',
                        description: ''
                    }
                })
                this.props.form.resetFields();
            } else {
                message.error(msg || '下载Olas运营信息失败');
            }
        } catch(e) {
            message.error(e.message);
        }
    }

    async getAllGroupId() {
        try {
            const { code, data, message: msg } = await getAllGroupId();
            if (code === '0000') {
                if (data) {
                    let arr: any[] = [];
                    Object.keys(data).forEach((key, index) => {
                        data[key]['key'] = index;
                        arr.push(data[key])
                    })
                    this.setState({
                        dataSource: [...arr]
                    })
                }
                message.success(msg || '获取OLAS分群群组信息成功');
            } else {
                message.error(msg || '获取OLAS分群群组信息失败');
            }
        } catch(e) {
            message.error(e.message);
        }
    }
    handleSubmit() {
        const _this = this;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                let params = {...values};
                _this.setState({params}, () => {
                    _this.fetchOlasFile();
                })
            }
        });
    }

    render() {
        const { getFieldDecorator } = this.props.form;
        const formItemLayout = {
            labelCol: { span: 8 },
            wrapperCol: { span: 16 },
        };
        const { params, dataSource } = this.state;

        return (
            <div className={styles['down-olas']}>
                <section>
                    <Form layout="inline" {...formItemLayout}>
                        <Form.Item label="actID">
                            {getFieldDecorator("actID", {
                                initialValue: params.actID,
                                rules: [{ required: true, message: "请输入" }],
                            })(<Input />)}
                        </Form.Item>
                        <Form.Item label="groupIDs">
                            {getFieldDecorator("groupIDs", {
                                initialValue: params.groupIDs,
                                rules: [{ required: true, message: "请输入" }],
                            })(<Input />)}
                        </Form.Item>
                        <Form.Item label="description">
                            {getFieldDecorator("description", {
                                initialValue: params.description,
                                rules: [{ required: true, message: "请输入" }],
                            })(<Input />)}
                        </Form.Item>
                        
                        <Form.Item style={{textAlign: 'center'}} wrapperCol={{span: 24}}>
                            <Popconfirm title={'是否请求Olas运营文件？'} onConfirm={() => this.handleSubmit()}>
                                <Button type="primary">确定</Button>
                            </Popconfirm>
                            <Button style={{marginLeft: 20}} onClick={this.getAllGroupId}>查询</Button>
                        </Form.Item>
                    </Form>
                </section>
                <div style={{marginTop: 10}}>ps:点完确定按钮后，请稍后点击查询按钮，若表格有数据，则下载Olas运营信息成功。</div>
                <section style={{marginTop: 40}}>
                    <Table dataSource={dataSource} columns={columns} />
                </section>
            </div>
        )
    }
}

export default Form.create({ name: 'olasMessage' })(OlasMessage)