import React, { useEffect, useState } from 'react';
// 接口
import api from 'api';
// 组件
import { Table, Button, message, Popconfirm } from 'antd';
// 路由
import { history } from 'umi';
// 时间
import moment from 'moment';
// TS
import { Confs, ActivityList } from '../types';
// store
import store from 'store';
import { formatCurrencyTrade } from '@/pages/report/JiShunTong/public/util';

const { postAllActivityList, postActivityStatus, getDataExcel, getStatusDataExcel } = api;
export default function() {
  const columns = [
    {
      title: '活动ID',
      dataIndex: 'indexStr',
      render: (text: string) => (text ? text : '— —'),
    },
    {
      title: '活动名称',
      dataIndex: 'name',
      render: (text: string) => (text ? text : '— —'),
    },
    {
      title: '活动状态',
      dataIndex: 'status',
      render: (text: number) => {
        let arr = ['待审核', '未开始', '进行中', '已结束', '已中止'];
        if (text === 2) {
          return <span style={{ color: 'red' }}>进行中</span>;
        }
        return arr[text];
      },
    },
    {
      title: '活动时间',
      dataIndex: 'activeTime',
      render: (text: string, record: Confs<string>) => {
        if (!record.startTime && !record.endTime) {
          return '— —';
        }
        return `${record.startTime} ~ ${record.endTime}`;
      },
    },
    {
      title: '最后编辑人',
      dataIndex: 'lastEditor',
      render: (text: string) => (text ? text : '— —'),
    },
    {
      title: '最后编辑时间',
      dataIndex: 'lastEditorTime',
      render: (text: string) => (text ? text : '— —'),
    },
    {
      title: '操作',
      dataIndex: 'options',
      // 审核|编辑|作废|查看|导出发放名单|进行中|已结束|已作废
      render: (text: unknown, record: Confs<string>) => {
        function tempRender(
          inner: string,
          fn: (record: Confs<string>) => void,
          marginRight: number = 0,
          type:
            | 'default'
            | 'link'
            | 'ghost'
            | 'primary'
            | 'dashed'
            | 'danger'
            | undefined = 'primary',
        ) {
          if (type === 'danger') {
            return (
              <Popconfirm
                title="你确定中止活动么？"
                okText="确定"
                cancelText="取消"
                onConfirm={() => fn(record)}
              >
                <Button type={type} style={{ marginRight: `${marginRight}px` }}>
                  {inner}
                </Button>
              </Popconfirm>
            );
          }
          return (
            <Button
              type={type}
              style={{ marginRight: `${marginRight}px` }}
              onClick={() => fn(record)}
            >
              {inner}
            </Button>
          );
        }
        switch (record.status) {
          case 0:
            return (
              <section>
                {tempRender('审核', handleVerify, 20, 'primary')}
                {tempRender('编辑', handleEdit)}
              </section>
            );
          case 1:
            return (
              <section>
                {tempRender('查看', handleCheck, 20, 'primary')}
                {tempRender('中止', handleStop, 0, 'danger')}
              </section>
            );
          case 2:
            return (
              <section>
                {tempRender('查看', handleCheck, 20, 'primary')}
                {tempRender('中止', handleStop, 20, 'danger')}
              </section>
            );
          case 3:
            return <section>{tempRender('查看', handleCheck)}</section>;
          case 4:
            return <section>{tempRender('查看', handleCheck)}</section>;
          default:
            return '--';
        }
      },
    },
  ];
  // 表格数据
  const [dataSource, setDataSource] = useState<Confs<string>[]>();
  // 表格加载动画
  const [loadingFlag, setLoadingFlag] = useState<boolean>();
  // 表格总数据
  const [total, setTotal] = useState<number>();
  // 当前分页
  const [current, setCurrent] = useState<number>(1);
  useEffect(() => {
    searchAllActivityList(1);
  }, []);
  // 查询活动列表
  const searchAllActivityList = (pageNum: number) => {
    setLoadingFlag(true);
    postAllActivityList({
      type: 'query',
      pageNum,
    })
      .then((res: ActivityList) => {
        if (res.code === '0000') {
          setDataSource(res.data.confs);
          setTotal(res.data.total);
          setLoadingFlag(false);
          setCurrent(pageNum);
        } else {
          message.error(res.message);
        }
      })
      .catch((err: unknown) => {
        message.error('网络请求错误，请稍后重试');
        console.log('查询活动列表错误：', err);
      });
  };
  // 改变活动状态
  const _postActivityStatus = (body: { activityIndex: string; status: 1 | 2 | 3 | 4 }) => {
    function success() {
      message.success(`状态改变成功`);
      searchAllActivityList(1);
    }
    postActivityStatus(body)
      .then((res: { code: string; message: string }) => {
        res.code === '0000' ? success() : message.error(res.message);
      })
      .catch((err: unknown) => {
        message.error('网络请求错误，请稍后重试');
        console.log('改变活动状态错误：', err);
      });
  };
  // 查看
  const handleCheck = (record: Confs<string>) => {
    history.push({
      pathname: 'detail',
      query: {
        type: 'check',
        id: record.indexStr,
      },
    });
  };
  // 数据查看
  // const handleDataCheck = (record: Confs<string>) => {
  //   getDataExcel({}, `activityIndex=${record.indexStr}`, '', { responseType: 'blob' })
  //     .then((res: any) => {
  //       if (!res.success) message.error(res.message);
  //     })
  //     .catch((err: unknown) => {
  //       message.error('网络请求错误，请稍后重试');
  //       console.log('数据查看DataExcel接口错误：', err);
  //     });
  //   getStatusDataExcel({}, `activityIndex=${record.indexStr}`, '', { responseType: 'blob' })
  //     .then((res: any) => {
  //       if (!res.success) message.error(res.message);
  //     })
  //     .catch((err: unknown) => {
  //       message.error('网络请求错误，请稍后重试');
  //       console.log('数据查看StatusDataExcel接口错误：', err);
  //     });
  // };
  // 中止
  const handleStop = (record: Confs<string>) => {
    _postActivityStatus({
      activityIndex: record.indexStr,
      status: 4,
    });
  };
  // 审核 未开始：1    进行中：2    已结束：3
  const handleVerify = (record: Confs<string>) => {
    const currentUser = store.get('name');
    const lastUser = record.lastEditor;
    if (currentUser === lastUser) {
      return message.error('最后编辑人不可以和审核人一致');
    }
    let status: 1 | 2 | 3 = 1;
    // 点击审核时间大于活动开始时间小于活动结束时间 进行中
    // 点击审核时间小于活动开始时间 进行中
    // 点击审核时间大于活动结束时间 已结束
    const nowTime = moment().format('YYYY-MM-DD HH:mm:ss');
    if (nowTime < record.startTime) {
      status = 1;
    }
    if (nowTime > record.endTime) {
      status = 3;
    }
    if (record.startTime < nowTime && nowTime < record.endTime) {
      status = 2;
    }
    _postActivityStatus({
      activityIndex: record.indexStr,
      status,
    });
  };
  // 编辑
  const handleEdit = (record: Confs<string>) => {
    history.push({
      pathname: 'detail',
      query: {
        id: record.indexStr,
      },
    });
  };
  // 页码变化
  const handlePaginationChange = (page: number) => {
    searchAllActivityList(page);
  };
  return (
    <article>
      <section style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="primary"
          onClick={() => {
            history.push('detail');
          }}
          style={{ marginBottom: '20px' }}
        >
          新增活动
        </Button>
        <span>
          负责人：zhengzekai
          <br />
          手炒链接：https://fund.10jqka.com.cn/scym_scsy/public/m/truthTyj/dist/index$ff8000.html?activityId=
          <br />
          基金链接：https://fund.10jqka.com.cn/ifundapp_app/public/m/truthTyj/dist/index$ff8000.html?activityId=
        </span>
      </section>
      <Table
        columns={columns}
        dataSource={dataSource}
        pagination={{
          current,
          pageSize: 20,
          total: total,
          onChange: handlePaginationChange,
        }}
        scroll={{ x: 'max-content' }}
        loading={loadingFlag}
      ></Table>
    </article>
  );
}
