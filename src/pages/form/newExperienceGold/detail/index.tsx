import React, { useState, useEffect, useCallback } from 'react';
// form-render
import FORM_JSON from './form.json';
import FormRender from 'form-render/lib/antd';
// 接口
import api from 'api';
// 组件
import { Button, message, Icon } from 'antd';
import Drawing from '../components/drawing';
import RedEnvelope from '../components/redEnvelope';
// 样式
import styles from './index.less';
// 时间
import 'moment/locale/zh-cn';
import moment from 'moment';
// webStorage
import store from 'store';
// TypeScript
import { EnveLope, ActivityDetail, tradeDetail, params, CusRouter } from '../types';
// 路由
import { history } from 'umi';
import UserIdList from '@/pages/form/components/userIdList';

const { postActivityDetails, updateUserType } = api;

export default function({ location }: { location: CusRouter }) {
  const [formData, setFormData] = useState<ActivityDetail<string>>();
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);

  const [isNew, setIsNew] = useState(false);
  const [isEdit, setEdit] = useState(true);
  //指定用户
  const [filterId, setFilterId] = useState('')
  const [isUserEdit, setIsUserEdit] = useState(false)
  useEffect(() => {
    // 是否是编辑页
    if (location['query'].id) {
      let body = {
        type: 'query',
        activityIndex: location['query'].id,
      };
      postActivityDetails(body)
        .then((res: { code: string; message: string; data: ActivityDetail<string> }) => {
          if (res.code === '0000') {
            const {filterId, ...other} = res.data;
            setFormData(other);
            setFilterId(filterId ?? '')
          } else {
            message.error(res.message)
          }
        })
        .catch((err: unknown) => {
          console.log('获取活动详情接口错误:', err);
          message.error('网络请求错误，请稍后重试');
        });
    }
    // 修改dom新增按钮文描
    let btn = document.getElementsByClassName('ant-btn ant-btn-sm');
    btn[3].innerHTML = '新增资产类型';
  }, []);
  // 保存
  const onSave = async () => {
    const _formData = formData;
    // 校验
    setShowValidate(true);
    if (formValid.length > 0) {
      return false;
    }
    // 体验天数限制
    if (_formData?.experienceDays) {
      const dateCount = Number(_formData?.experienceDays);
      if (dateCount < 3 || dateCount > 14) {
        return message.error('体验天数范围为3~14天');
      }
    }
    // 体验金额限制
    if (_formData?.activityDetails) {
      const maxAmount = 99999;
      const currentAmount = Number(_formData?.experienceAmount) * _formData?.activityDetails.length;
      if (currentAmount > maxAmount) {
        return message.error(`体验总金额超出最大值${maxAmount}`);
      }
    }
    if (isUserEdit) {
      message.error('请先保存用户类型')
      return
    }


    // 对于设置了"ui:hidden"属性的表单校验只有样式生效，逻辑不生效，所以需要补充逻辑
    if (_formData?.formOfAward === 'redEnvelope') {
      let ifPass = true;
      // 奖励发放形式为满减红包时红包去跳转链接为必填项
      if (!_formData.redEnvelopesJumpUrl) {
        return false;
      }
      // 任意项不可为空
      if (_formData?.redEnvelopeList.length === 0) {
        return false;
      }
      _formData?.redEnvelopeList.map((item: EnveLope) => {
        if (!item.activityId || !item.awardUpperLimit) {
          ifPass = false;
        }
      });
      if (!ifPass) {
        return false;
      }
    }
    if (_formData?.formOfAward === 'share') {
      if (!_formData?.shareUpperLimit || !_formData?.shareLowerLimit) {
        return false;
      }
    }
    // 资产类型卡片索引赋值
    _formData?.activityDetails.map((tradeCard, index) => {
      tradeCard.typeIndex = index;
      tradeCard.fundList.map(item => {
        item.assetType = 'a';
      });
    });
    // 最后编辑人、最后编辑时间
    let lastEditor = store.get('name');
    let updateTime = moment().format('YYYY-MM-DD HH:mm:ss');
    // 默认为新增页
    let params: params = {
      type: 'update',
      value: ''
    };
    let obj: any = {
      ..._formData,
      updateTime,
      lastEditor,
    }
    // 如果是编辑页
    if (location['query'].id) {
      obj.indexStr = location['query'].id;
    }
    
    if (_formData?.abButton) {
      if (filterId) {
        obj.filterId = filterId;
      } else {
        let _userData = {
          blackUserId: "",
          id: "",
          kycLogic: "and",
          kycs: [],
          olasId: "",
          olasType: "1",
          platform: ["and", "ios", "andsdk", "iossdk", "iossdkvip", "andsdkvip"],
          targetType: "kyc",
          updateTime,
          userType: "1",
          utype: ["u0", "u1", "u2", "u3", "u4", "u5", "u6", "u7", "u8", "u9", "u10", "F"],
          whiteUserId: ""
        }
        try {
          const { code, message, data } = await updateUserType(_userData);
          if (code !== '0000') {
            return message.error(message || '系统错误');
          }
          setFilterId(data);
          obj.filterId = data;
        }catch(e) {
          message.error(e.message || '系统错误');
          return;
        }
      }
    } else {
      obj.filterId = '';
    }
    params.value = JSON.stringify(obj);
    _postActivityDetails(params);
  };
  // 取消
  const onCancel = useCallback(() => {
    history.push('list');
  }, []);
  // 处理表单变化
  const handleChange = (data: ActivityDetail<string>) => {
    if (data.endTime) {
      // 精确到分
      if (moment().format('YYYY-MM-DD HH:mm') > data.endTime) {
        return message.error('结束时间不可以小于当前时间');
      }
    }
    if (data.startTime && data.endTime) {
      if (data.startTime > data.endTime) {
        return message.error('结束时间不可以小于开始时间');
      }
    }
    if (data.activityDetails.length === 0) {
      return message.error('最少保留一个资产类型');
    }
    if (data.activityDetails.length > 3) {
      return message.error('最多有3个资产类型');
    }
    let ifPass = true;
    data.activityDetails.map((typeItem: tradeDetail) => {
      if (typeItem.fundList.length === 0) {
        ifPass = false;
      }
    });
    ifPass ? setFormData({ ...formData, ...data }) : message.error('最少保留一个基金组合');
  };
  // 体验金活动详情
  const _postActivityDetails = (body: params) => {
    function _success() {
      message.success('保存成功');
      setTimeout(() => {
        history.push('list');
      }, 300);
    }
    postActivityDetails(body)
      .then((res: { code: string; message: string }) => {
        res.code === '0000' ? _success() : message.error(res.message);
      })
      .catch((err: unknown) => {
        console.log('更新/新增活动详情接口错误：', err);
        message.error('网络请求错误，请稍后重试');
      });
  };
  return (
    <article className={styles['m-new-experience-gold']}>
      <Button onClick={onCancel} className={styles['m-backward-btn']}>
        <Icon type="left" />
        取消
      </Button>
      <FormRender
        propsSchema={FORM_JSON}
        formData={formData ? formData : {
          abButton: false
        }}
        onChange={(data: any) => handleChange(data)}
        onValidate={setFormValid}
        showValidate={showValidate}
        displayType="row"
        widgets={{
          drawing: Drawing,
          redEnveLope: RedEnvelope,
        }}
      />
      { formData && formData.abButton && (
        <UserIdList
          filterId={filterId}
          setFilterId={setFilterId}
          isAdd={false}
          isEdit={isEdit}
          setUserTypeData={() => { }}
          isCanEdit={isUserEdit}
          setIsCanEdit={setIsUserEdit}
      />
      ) }
      
      {location['query'].type !== 'check' && (
        <section>
          <Button onClick={onSave}>
            <Icon type="save" />
            保存
          </Button>
        </section>
      )}
    </article>
  );
}
