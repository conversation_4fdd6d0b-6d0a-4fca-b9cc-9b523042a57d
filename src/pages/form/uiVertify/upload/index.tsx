import React, { useState, useEffect } from 'react';
import { Button, Table, message } from 'antd';
import api from 'api';
import styles from './index.less';
import classNames from 'classnames';
import { useHistory } from 'react-router-dom';
import { compareDates } from './util';

const { getUiList, postMyProperty, deleteUi } = api;
function Demo() {
  const [init, setInit] = useState(false);
  const [formConfig, setFormConfig] = useState({});
  const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);
  let history = useHistory();
  const [data, setUplaodData] = useState([]);
  const columns = [
    {
      title: '上传时间',
      dataIndex: 'projectNo',
      key: 'projectNo',
    },
    {
      title: '文件名称',
      dataIndex: 'fileName',
      key: 'fileName',
    },
    {
      title: '上传者',
      dataIndex: 'uploadPerson',
      key: 'uploadPerson',
    },
    {
      title: '法务审核',
      key: 'legalReview',
      dataIndex: 'legalReview',
      render: (key: any, record: any) => {
        return record.legalReview === '1' ? (
          <div>{'李珍珍审核通过'}</div>
        ) : record.legalReview === '2' ? (
          <div>{'李珍珍审核不通过'}</div>
        ) : (
          '待审核'
        );
      },
    },
    {
      title: '业务负责审核',
      dataIndex: 'businessReview',
      key: 'businessReview',
      render: (key: any, record: any) => {
        return record.businessReview === '1' ? (
          <div>{'赵利明审核通过'}</div>
        ) : record.businessReview === '2' ? (
          <div>{'赵利明审核不通过'}</div>
        ) : (
          '待审核'
        );
      },
    },
    {
      title: '操作',
      key: 'operating',
      render: (key: any) => {
        return (
          <div>
            <a onClick={() => handleEdit(key)}>编辑&nbsp;&nbsp;&nbsp;</a>
            <a onClick={() => handleDelete(key)}>删除</a>
          </div>
        );
      },
    },
  ];
  function handleEdit(key: any, type = 'legal') {
    console.log(key);
    history.push(`/form/uiVertify/edit?projectNo=${key.projectNo}`);
  }
  function handleDelete(key: any) {
    console.log(key);
    deleteUi({
      reqDto: JSON.stringify({ projectNo: key.projectNo }),
    }).then((res: any) => {
      console.log(res);
      message.success('删除成功！');
      setTimeout(() => {
        location.reload();
      }, 500);
    });
  }
  useEffect(() => {
    getUiList({
      reqDto: JSON.stringify({}),
    }).then((res: any) => {
      try {
        let _list = res.data;
        _list = _list.sort(compareDates);
        setUplaodData(_list);
      } catch (e) {
        message.error(e.message);
      }
    });
  }, [init]);
  const onSubmit = () => {
    // valid 是校验判断的数组，valid 长度为 0 代表校验全部通过
    if (valid.length > 0) {
      alert(`校验未通过字段：${valid.toString()}`);
    } else {
      let _postData = {
        ...formData,
      };
      postMyProperty({
        value: JSON.stringify(_postData),
      }).then((res: any) => {
        try {
          if (res.code !== '0000') {
            message.error(res.message);
          } else {
            message.success('发布成功！');
            setTimeout(() => {
              location.reload();
            }, 1000);
          }
        } catch (e) {
          message.error(e.message);
        }
      });
    }
  };
  function onUpload() {
    history.push(`/form/uiVertify/add`);
  }

  function jumpToVideo() {
    history.push('/form/videoVertify/upload');
  }
  return (
    <div style={{ padding: 60 }}>
      <div className={classNames(styles['hide-button'])} onClick={jumpToVideo}>
        上传页面
      </div>
      <div className={classNames(styles['top'])}>
        <Button type="primary" onClick={onUpload}>
          上传
        </Button>
      </div>
      <Table columns={columns} dataSource={data} bordered pagination={false} />
    </div>
  );
}
export default Demo;
