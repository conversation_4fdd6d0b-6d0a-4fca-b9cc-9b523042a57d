import React, { useLayoutEffect, useState } from 'react';
import { Table } from 'antd';
import api from 'api';
import { toast } from 'utils/message';
import { useHistory } from 'react-router-dom';
import styles from './index.less';
import classNames from 'classnames';
import { compareDates } from '../upload/util';
const { getUiList } = api;

export default function() {
  let history = useHistory();
  const [data, setUplaodData] = useState([]);
  const columns = [
    {
      title: '上传时间',
      dataIndex: 'projectNo',
      key: 'projectNo',
    },
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
    },
    {
      title: '上传者',
      dataIndex: 'uploadPerson',
      key: 'uploadPerson',
    },
    {
      title: '法务审核',
      key: 'legalReview',
      dataIndex: 'legalReview',
      render: (key, record) => {
        return record.legalReview === '1' ? (
          <div>{'李珍珍审核通过'}</div>
        ) : record.legalReview === '2' ? (
          <div>{'李珍珍审核不通过'}</div>
        ) : (
          <a onClick={() => handleReview(record, 'legal')}>审核</a>
        );
      },
    },
    {
      title: '业务负责审核',
      dataIndex: 'businessReview',
      key: 'businessReview',
      render: (key, record) => {
        return record.businessReview === '1' ? (
          <div>{'赵利明审核通过'}</div>
        ) : record.businessReview === '2' ? (
          <div>{'赵利明审核不通过'}</div>
        ) : (
          <a onClick={() => handleReview(record, 'bus')}>审核</a>
        );
      },
    },
    {
      title: '状态',
      key: 'status',
      render: (key, record) => {
        return record.businessReview === '1' && record.legalReview === '1' ? '审核通过' : '待审核';
      },
    },
  ];
  function handleReview(key, type) {
    console.log(key);
    history.push(`/form/uiVertify/toreview?projectNo=${key.projectNo}&type=${type}`);
  }
  function jumpToVideo() {
    history.push('/form/videoVertify/videoreview');
  }
  useLayoutEffect(() => {
    getUiList({
      reqDto: JSON.stringify({}),
    })
      .then(res => {
        let _list = res.data;
        _list = _list.sort(compareDates);
        setUplaodData(_list);
      })
      .catch(e => {
        toast.error(e && e.message);
      });
  }, []);
  return (
    <div>
      <div className={classNames(styles['hide-button'])} onClick={jumpToVideo}>
        审核页面
      </div>
      <Table columns={columns} dataSource={data} bordered pagination={false} />
    </div>
  );
}
