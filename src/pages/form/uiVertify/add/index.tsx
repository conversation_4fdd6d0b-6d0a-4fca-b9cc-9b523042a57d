import React, { useEffect, useState } from 'react';
import {
  Row,
  Input,
  Button,
  Icon,
  Select,
  message,
  Collapse,
  Popconfirm,
  DatePicker,
  Tooltip,
} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import moment from 'moment';
import FORM_CONFIG from './form.json';
import FormRender from 'form-render/lib/antd';
import { axios } from '@/functions/request';
import { useHistory } from 'react-router-dom';
import UploadFileWrapper from '../components/UploadFileWrapper';

const { postUiPush } = api;
export default function() {
  const [init, setInit] = useState(true);
  const [formData, setFormState] = useState({ tabService: [] });
  const [formConfig, setFormConfig] = useState(FORM_CONFIG.propsSchema);
  const [uiSchema, setUiSchema] = useState({});
  const [valid, setValid] = useState([]);
  let history = useHistory();
  useEffect(() => {
    // fetchAllBigVEdit().then((res) => {
    //   try {
    //     res = JSON.parse(res.data);
    //     console.log(res);
    //     if (res) {
    //       setFormState(res)
    //     }
    //   } catch (e) {
    //     console.warn(e)
    //   }
    //   setInit(true)
    // })
  }, [init]);
  function onSubmit() {
    console.log('===', formData.tabService);
    // axios.post('/yytjapi/uicheck/uipush', formData.tabService)
    // .then(res => {
    //   console.log(res.data)
    // })
    let param = formData.tabService;
    postUiPush({
      reqDto: JSON.stringify(param),
    }).then((res: any) => {
      console.log(res);
      if (res.code === 0) {
        message.success('上传成功！');
        history.push(`/form/uiVertify/upload`);
        setTimeout(() => {
          location.reload();
        }, 100);
      }
    });
  }

  if (!init) return '加载中';
  return (
    <div className={'kycForm'}>
      {/* <div>ui审核后台</div> */}
      <FormRender
        propsSchema={formConfig}
        uiSchema={uiSchema}
        formData={formData}
        onChange={setFormState}
        onValidate={setValid}
        widgets={{ UploadFileWrapper }}
      />
      <div style={{ marginLeft: 60 }}>
        <Button type="primary" onClick={onSubmit}>
          上传
        </Button>
      </div>
    </div>
  );
}
