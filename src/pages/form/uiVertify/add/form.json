{"propsSchema": {"type": "object", "properties": {"tabService": {"title": "上传页面", "type": "object", "properties": {"projectNo": {"title": "上传时间", "type": "string", "ui:options": {}}, "fileName": {"title": "文件名", "type": "string", "ui:options": {}}, "attachment": {"title": "文件地址", "type": "string", "ui:options": {}, "ui:widget": "UploadFileWrapper"}, "notes": {"title": "备注", "type": "string", "ui:options": {}}, "uploadPerson": {"title": "上传者", "type": "string", "ui:options": {}}}}}}}