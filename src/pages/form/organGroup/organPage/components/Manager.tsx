import React, { useState, useEffect } from 'react';
import styles from '../index.less';
import classnames from 'classnames';
import { Checkbox, Button, Popconfirm, Row, Col, Select, message } from 'antd';
import { ManagerItem } from '../type';
import { checkoutInput, setSearch } from '../../fn';
const { Option } = Select;

interface iProps {
  index: number;
  dataItem: ManagerItem;
  onChange: (index: number, dataItem: ManagerItem) => void;
  onDel: (index: number) => void;
  titleLength: number;
  ids: string[]
}

const rewards = [
  { label: '金牛奖', value: '金牛奖' },
  { label: '金基奖', value: '金基奖' },
]

export default function Manager ({
  index = 0,
  dataItem = { id: '' },
  onChange = () => {},
  onDel = () => {},
  titleLength = 0,
  ids = [],
}: iProps) {
  const { managerId, title, award } = dataItem;
  const [options, setOptions] = useState<any[]>([]);
  useEffect(() => {
    if (ids?.length > 0) {
      const _options = ids.map((id) => {
        return {
          value: id,
          disabled: false,
        }
      })
      setOptions(_options.slice(0, 10));
    }
  }, [ids])

  const renderOptions = (options: any[]) => {
    return options.slice(0, 10).map((item, index) => (
      <Option key={item.value} value={item.value}>{item.value}</Option>
    ))
  }

  const handleSearch = (val: any) => {
    console.log('onSearch', val);
    const _options = setSearch(val, ids);
    setOptions(_options);
  }
  const handleChange = (type: string, value: string|string[]) => {
    console.log('经理人单项变化', type, value, dataItem);
    switch (type) {
      case 'code': dataItem.managerId = value as string; break;
      case 'title': dataItem.title = checkoutInput(value as string, titleLength); break;
      case 'reward': dataItem.award = value as string[]; break;
    }
    onChange(index, dataItem);
  }
  const handleDel = () => {
    onDel(index);
  }

  return (
    <div className={styles['tag']}>
      <Row gutter={[32, 16]} className={classnames(styles['m-manager-item'], styles['m-row'])}>
        <Col span={1}>
          <span>{index + 1}</span>
        </Col>
        <Col span={5}>
          <div className="m-value-item">
            <i className="m-required-icon">*</i><span>基金经理id：</span>
            {/* <input
              type="text"
              className={classnames('m-input', managerId ? '' : 'm-required')}
              value={managerId}
              onChange={() => {}}
              onInput={(e: any) => handleChange('code', e.target.value)}
            /> */}
            <Select
              value={managerId}
              className={classnames('m-select', managerId ? '' : 'm-required')}
              showSearch
              placeholder="请输入基金经理id"
              onChange={(value: any) => handleChange('code', value)}
              onSearch={handleSearch}
              optionFilterProp="children"
            >
              {renderOptions(options)}
            </Select>
          </div>
        </Col>
        <Col span={7}>
          <div className="m-value-item m-type-title">
            <i className="m-required-icon">*</i><span>经理标题：</span>
            <input
              type="text"
              className={classnames('m-input', (title && title?.length <= titleLength) ? '' : 'm-required')}
              value={title}
              onChange={() => {}}
              onInput={(e: any) => handleChange('title', e.target.value)}
            />
          </div>
        </Col>
        <Col span={4}>
          <div className="m-value-item">
            <span>奖项：</span>
            <Checkbox.Group 
              options={rewards}
              value={award}
              onChange={(value: string[]) => handleChange('reward', value)}
            />
          </div>
        </Col>
        <Col span={2}>
          <Popconfirm title="确认删除吗？" onConfirm={handleDel} okText="确认" cancelText="取消">
            <Button type="danger">删除</Button>
          </Popconfirm>
        </Col>
      </Row>
    </div>
  )
}