import React, { useState, useEffect } from 'react';
import { Table, Divider, Button, Popconfirm, message } from 'antd';
import { useHistory } from 'react-router-dom';
import api from 'api';
import { IAbItemInfo, PostAbTest } from './types';
import { PublishState, RequestType, channelOptions, marketOptions } from './constants';
import { deleteTableItem, publishTableItemDirectly } from './util/fetch';

const { postAbTest } = api as { postAbTest: PostAbTest<RequestType.QueryAll> };

export default function() {
  const columns = [
    {
      title: 'Id',
      dataIndex: 'id',
    },
    {
      title: '名称',
      dataIndex: 'abTestName',
    },
    {
      title: '任务号',
      dataIndex: 'taskNumber',
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      render: (text: PublishState) => {
        return text === PublishState.Published ? '已发布' : '已保存待发布';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (_text: undefined, record: IAbItemInfo) => {
        return (
          <span>
            <Button
              disabled={record.status === PublishState.Published}
              onClick={() => {
                publishTableItemDirectly(record.id);
              }}
            >
              发布
            </Button>
            <Divider type="vertical" />
            <Button
              onClick={() => {
                history.push(`abtest/detail?id=${record.id}`);
              }}
            >
              编辑
            </Button>
            <Divider type="vertical" />
            <Popconfirm
              title="是否确认删除？"
              okText="确认"
              cancelText="取消"
              onConfirm={() => {
                deleteTableItem(record);
              }}
            >
              <Button type="danger">删除</Button>
            </Popconfirm>
          </span>
        );
      },
    },
  ];
  const history = useHistory();
  // 表格数据
  const [data, setData] = useState<IAbItemInfo[]>([]);

  useEffect(() => {
    postAbTest({
      type: RequestType.QueryAll,
    })
      .then(res => {
        if (res.status_code === 0) {
          res.data.forEach(item => {
            // 表格每一行数据都需要一个key，防止报错
            item.key = item.id;
            // 修改功能模块中应用市场选项（如果为安卓），把all改为实际选项
            item.functionList.forEach(functionItem => {
              if (
                functionItem.systemSelection === 'android' &&
                functionItem.applicationMarket.includes('all')
              ) {
                functionItem.applicationMarket = [...marketOptions, ...channelOptions];
              }
            });
          });
          res.data.forEach(item => (item.key = item.id));
          setData(res.data);
        } else {
          message.error('查询表格数据失败');
        }
      })
      .catch(() => {
        message.error('查询表格数据失败');
      });
  }, []);

  return (
    <div>
      <Button
        style={{ marginLeft: '89%' }}
        onClick={() => {
          history.push(`abtest/detail`);
        }}
      >
        添加
      </Button>
      <Table dataSource={data} pagination={false} columns={columns}></Table>
    </div>
  );
}
