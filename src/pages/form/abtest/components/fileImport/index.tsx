import React from 'react';
import { Button, Upload, Input } from 'antd';
const { TextArea } = Input;

export default function({ value, onChange, name }: any) {
  const props = {
    // 隐藏传输列表
    fileList: [],
    // 传输文件之前的钩子函数
    beforeUpload(file: any) {
      const reader = new FileReader();
      reader.readAsText(file);
      reader.onload = () => {
        let result;
        if (reader.result) {
          // 返回以换行符为终止符的所有字符数组
          result = reader.result.match(/.+/g);
          // 去除除了逗号以外的无用符号
          result = result.join();
          result = result.replace(/[^(0-9,)]+/g, '');
          // 替换逗号为换行符
          result = result.replace(/,/g, '\r');
        }
        onChange(name, result);
      };
      return true;
    },
  };
  return (
    <div>
      <Button onClick={() => onChange(name, '')}>清空</Button>
      <Button
        onClick={() =>
          window.open(
            'https://testfund.10jqka.com.cn/s3/fund-resource/%E5%AF%BC%E5%85%A5%E8%8C%83%E4%BE%8B.txt',
          )
        }
      >
        查看范例
      </Button>
      <Upload {...props}>
        <Button>导入TXT</Button>
      </Upload>
      <TextArea
        rows={8}
        value={value}
        onChange={e => {
          onChange(name, e.target.value);
        }}
      />
    </div>
  );
}
