import React, { useEffect, useState } from 'react';
import { Checkbox } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { platformOptions, marketOptions, channelOptions } from '../../constants';
import styles from '@/pages/frontend/frontend.less';

export default function({
  value,
  onChange,
  name,
}: {
  value: string[];
  onChange: Function;
  name: string;
}) {
  // 已选中列表
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  // 全选状态
  const [selectAll, setSelectAll] = useState(false);
  // 应用市场全选状态
  const [selectMarket, setSelectMarket] = useState(false);
  // 自身渠道全选状态
  const [selectChannel, setSelectChannel] = useState(false);
  // 应用市场类型是否部分选择
  const isMarketSelectedPartly =
    selectedOptions.some(selectedOption => marketOptions.includes(selectedOption)) &&
    marketOptions.length > selectedOptions.filter(item => marketOptions.includes(item)).length;
  // 自身渠道类型是否部分选择
  const isChannelSelectdPartly =
    selectedOptions.some(selectedOption => channelOptions.includes(selectedOption)) &&
    channelOptions.length > selectedOptions.filter(item => channelOptions.includes(item)).length;

  // 编辑时初始化数据
  useEffect(() => {
    setSelectedOptions(value);
    setSelectAll(value.length === platformOptions.length);
    setSelectMarket(
      value.filter(item => marketOptions.includes(item)).length === marketOptions.length,
    );
    setSelectChannel(
      value.filter(item => channelOptions.includes(item)).length === channelOptions.length,
    );
  }, []);

  const handleSelectAll = (e: CheckboxChangeEvent) => {
    const { checked } = e.target;
    const newOptions = checked ? platformOptions.map(option => option.value) : [];
    onChange(name, newOptions);
    setSelectedOptions(newOptions);
    setSelectAll(checked);
    setSelectMarket(checked);
    setSelectChannel(checked);
  };
  const handleSelectMarket = (e: CheckboxChangeEvent) => {
    const { checked } = e.target;
    const vegetableOptions = platformOptions
      .filter(option => option.category === 'market')
      .map(option => option.value);
    const updatedOptions = checked
      ? [...selectedOptions, ...vegetableOptions]
      : selectedOptions.filter(option => !vegetableOptions.includes(option));
    onChange(name, updatedOptions);
    setSelectedOptions(updatedOptions);
    setSelectMarket(checked);
    setSelectAll(updatedOptions.length === platformOptions.length);
  };
  const handleSelectChannel = (e: CheckboxChangeEvent) => {
    const { checked } = e.target;
    const fruitOptions = platformOptions
      .filter(option => option.category === 'channel')
      .map(option => option.value);
    const updatedOptions = checked
      ? [...selectedOptions, ...fruitOptions]
      : selectedOptions.filter(option => !fruitOptions.includes(option));
    onChange(name, updatedOptions);
    setSelectedOptions(updatedOptions);
    setSelectChannel(checked);
    setSelectAll(updatedOptions.length === platformOptions.length);
  };
  const handleSelectItem = (checkedValue: string[]) => {
    onChange(name, checkedValue);
    setSelectedOptions(checkedValue);
    // 还需要去更新三个全选框的状态
    setSelectAll(checkedValue.length === platformOptions.length);
    setSelectMarket(
      checkedValue.filter(item => marketOptions.includes(item)).length === marketOptions.length,
    );
    setSelectChannel(
      checkedValue.filter(item => channelOptions.includes(item)).length === channelOptions.length,
    );
  };

  return (
    <section className={styles['m-config']}>
      <p className={styles['m-card-label']}>
        <span style={{ color: 'red', fontSize: '18px', marginRight: '5px' }}>*</span>应用市场:
      </p>
      <Checkbox
        indeterminate={selectedOptions.length > 0 && !selectAll}
        onChange={handleSelectAll}
        checked={selectAll}
      >
        全选
      </Checkbox>
      <Checkbox
        indeterminate={isMarketSelectedPartly}
        checked={selectMarket}
        onChange={handleSelectMarket}
      >
        快速选择应用市场
      </Checkbox>
      <Checkbox
        indeterminate={isChannelSelectdPartly}
        checked={selectChannel}
        onChange={handleSelectChannel}
      >
        快速选择自身渠道
      </Checkbox>
      <br />
      <Checkbox.Group
        options={platformOptions}
        onChange={handleSelectItem}
        value={selectedOptions}
        style={{ marginLeft: '110px', marginBottom: '20px' }}
      />
      <br />
    </section>
  );
}
