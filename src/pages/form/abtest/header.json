{"type": "object", "properties": {"id": {"title": "id", "type": "string", "ui:widget": "html", "ui:width": "10%", "ui:labelWidth": 10}, "abTestName": {"title": "AB-test 名称", "type": "string", "ui:labelWidth": 110, "ui:width": "15%"}, "taskNumber": {"title": "任务号", "type": "string", "ui:labelWidth": 70, "ui:width": "15%", "pattern": "^[\\x20-\\x7E]+$", "message": {"pattern": "请使用英文字符输入，不要含空格和换行"}}, "startDate": {"title": "开始时间", "type": "string", "format": "date", "ui:labelWidth": 80, "ui:width": "17%", "displayType": "row"}, "startDateTime": {"type": "string", "format": "time", "ui:labelWidth": 80, "ui:width": "13%"}, "endDate": {"title": "结束时间", "type": "string", "format": "date", "ui:labelWidth": 80, "ui:width": "17%"}, "endDateTime": {"type": "string", "format": "time", "ui:labelWidth": 80, "ui:width": "13%"}, "note": {"type": "string", "ui:widget": "note", "default": "起止时间为可选项，全留空则代表默认一直生效，一边留空则是>=（或<=）"}}, "required": ["abTestName", "taskNumber"]}