import { message } from 'antd';
import api from 'api';
import { RequestType } from '../constants';
import { IAbItemInfo, PostAbTest } from '../types';
import { handleJsonDtoReq } from '.';

const { postAbTest } = api as {
  postAbTest: PostAbTest<
    RequestType.Delete | RequestType.Change | RequestType.Add | RequestType.Update
  >;
};

// 删除某一项ab配置
export const deleteTableItem = (record: IAbItemInfo) => {
  postAbTest({
    type: RequestType.Delete,
    id: record.id,
  })
    .then(res => {
      if (res.status_code === 0) {
        message.success('删除成功');
        location.reload();
      } else {
        message.error('删除失败');
      }
    })
    .catch(() => {
      message.error('删除失败');
    });
};

/**
 * 发布
 * @param id
 * @param refreshType 发布成功后页面的刷新方式，分为立即刷新和返回上一级页面
 */
export const publishTableItemDirectly = (
  id: string,
  refreshType: 'refresh' | 'goback' = 'refresh',
) => {
  postAbTest({
    type: RequestType.Change,
    id,
  })
    .then(res => {
      if (res.status_code === 0) {
        message.success('发布成功');
        if (refreshType === 'refresh') {
          location.reload();
        } else {
          history.go(-1);
        }
      } else {
        message.error('发布失败');
      }
    })
    .catch(() => {
      message.error('发布失败');
    });
};

// 间接发布（ab详情里点击发布）：新增并发布or更新并发布
export const publishTableItemIndirectly = (
  id: string,
  type: RequestType.Add | RequestType.Update,
  jsonDto: string,
) => {
  const jsonDtoHandled = handleJsonDtoReq(jsonDto);
  postAbTest({
    type,
    jsonDto: jsonDtoHandled,
  })
    .then(res => {
      if (res.status_code === 0) {
        // 如果是新增，需要先保存，根据接口返回的id进行发布
        if (type === RequestType.Add) {
          publishTableItemDirectly(res.data.id, 'goback');
        } else {
          publishTableItemDirectly(id, 'goback');
        }
      } else {
        message.error('保存失败');
      }
    })
    .catch(() => {
      message.error('发布失败');
    });
};

// 添加配置，即保存
export const addTableItem = (jsonDto: string) => {
  const jsonDtoHandled = handleJsonDtoReq(jsonDto);
  postAbTest({
    type: RequestType.Add,
    jsonDto: jsonDtoHandled,
  })
    .then(res => {
      if (res.status_code === 0) {
        message.success('保存成功');
        history.go(-1);
      } else {
        message.error('服务器错误');
      }
    })
    .catch((error: any) => {
      console.warn(error);
    });
};

// 修改某一项配置
export const updateTableItem = (jsonDto: string) => {
  const jsonDtoHandled = handleJsonDtoReq(jsonDto);
  postAbTest({
    type: RequestType.Update,
    jsonDto: jsonDtoHandled,
  })
    .then(res => {
      if (res.status_code === 0) {
        message.success('修改成功');
        history.go(-1);
      } else {
        message.error('修改失败');
      }
    })
    .catch(() => {
      message.error('修改失败');
    });
};
