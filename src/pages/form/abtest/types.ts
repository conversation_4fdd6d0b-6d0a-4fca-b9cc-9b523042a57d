import { PublishState, RequestType } from './constants';

export type RequestData<T extends RequestType> = T extends RequestType.QueryAll
  ? {
      type: T;
    }
  : T extends (RequestType.Add | RequestType.Update)
  ? {
      type: T;
      jsonDto: string;
    }
  : T extends (RequestType.Delete | RequestType.Query | RequestType.Change)
  ? {
      type: T;
      id: string;
    }
  : never;

export type ResponseData<T extends RequestType> = T extends (Exclude<
  RequestType,
  RequestType.QueryAll
>)
  ? {
      status_code: number;
      data: IAbItemInfo;
    }
  : T extends RequestType.QueryAll
  ? {
      status_code: number;
      data: IAbItemInfo[];
    }
  : never;

export interface IAbItemInfo {
  key?: string;
  id: string;
  abTestName: string;
  taskNumber: string;
  startDate: string;
  startDateTime?: string;
  endDate: string;
  endDateTime?: string;
  // 发布状态
  status: PublishState;
  functionList: IFunctionItem[];
  createTime: null;
  updateTime: null;
}

export type PostAbTest<T extends RequestType> = (
  requestBody: RequestData<T>,
) => Promise<ResponseData<T>>;

export interface IFunctionItem {
  androidAppLeft: string;
  androidAppRight: string;
  androidSystemLeft: string;
  androidSystemRight: string;
  applicationMarket: string[];
  applicationType: number[];
  contentRules: string;
  userIdList: string;
  distributionMode: string;
  functionId: string;
  functionName: string;
  iosAppLeft: string;
  iosAppRight: string;
  iosSystemLeft: string;
  iosSystemRight: string;
  notes1: string;
  notes2: string;
  notes4: string;
  notes5: string;
  notes6: string;
  notes7: string;
  notes8: string;
  notes9: string;
  selectApplication: any;
  mode: string;
  systemSelection: 'android' | 'ios';
  userIdType: string;
}
