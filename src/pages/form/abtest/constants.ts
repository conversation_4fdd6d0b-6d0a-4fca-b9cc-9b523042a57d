export enum RequestType {
  Add = 'add',
  Delete = 'delete',
  Update = 'update',
  // 发布，将待发布状态修改为已发布
  Change = 'change',
  // 查询单个ab配置
  Query = 'query',
  // 查询所有ab配置
  QueryAll = 'queryAll',
}

export enum PublishState {
  // 已保存待发布
  Saved = '0',
  // 已发布
  Published = '1',
}

// 安卓应用市场配置，分为应用市场和自身渠道
export const platformOptions: { label: string; value: string; category: 'market' | 'channel' }[] = [
  { label: '三星', value: 'samsungmarket', category: 'market' },
  { label: '魅族', value: 'meizu', category: 'market' },
  { label: '淘宝', value: 'taobao', category: 'market' },
  { label: '百度', value: 'baidu', category: 'market' },
  { label: '360', value: '360zhushou', category: 'market' },
  { label: '安智', value: 'anzhi', category: 'market' },
  { label: '小米', value: 'xiaomi', category: 'market' },
  { label: '腾讯', value: 'tencent', category: 'market' },
  { label: '木蚂蚁', value: 'mumayi', category: 'market' },
  { label: '应用汇', value: 'appchina', category: 'market' },
  { label: '搜狗', value: 'sougou', category: 'market' },
  { label: '智汇云', value: 'hicloud', category: 'market' },
  { label: '联想乐商店', value: 'lenovole', category: 'market' },
  { label: 'OPPO商店', value: 'oppomarket', category: 'market' },
  { label: 'VIVO商店', value: 'vivo', category: 'market' },
  { label: '金立', value: 'gionee', category: 'market' },
  { label: '荣耀', value: 'honor', category: 'market' },
  { label: 'PUSH', value: 'thspush', category: 'channel' },
  { label: '资讯', value: 'thszx', category: 'channel' },
  { label: '应用推荐', value: 'thxtj', category: 'channel' },
  { label: '同花顺渠道', value: 'mhexin', category: 'channel' },
  { label: '消息中心', value: 'zixunzhengwen', category: 'channel' },
  { label: 'WEB', value: 'thsmobile', category: 'channel' },
  { label: '神马搜索', value: 'smss', category: 'channel' },
  { label: '下载中心', value: 'thsdownload', category: 'channel' },
  { label: '行情客户端', value: 'hqkhd', category: 'channel' },
  { label: '自更新', value: 'thsgx', category: 'channel' },
];

export const marketOptions = platformOptions
  .filter(item => item.category === 'market')
  .map(item => item.value);

export const channelOptions = platformOptions
  .filter(item => item.category === 'channel')
  .map(item => item.value);
