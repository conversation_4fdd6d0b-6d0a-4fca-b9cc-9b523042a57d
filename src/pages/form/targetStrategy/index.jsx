/**
 * @see 个基详情页-查看指标战法配置页面
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2023-05-19 13:11:50
 */

import React from 'react';
import { Row, Col, Input, Button, Icon, Card, PageHeader, Select, message, Tooltip } from 'antd';
import { autobind } from 'core-decorators';
import api from 'api';
import { connect } from 'dva';
import {
  TEXTCONFIG,
  DATA_CONFIG,
  SUCCESS_CODE,
  USER_RSI_OPTIONS,
  USER_RSI_OPTIONS_TIP,
} from '../../../utils/const.ts';

const { Option } = Select;
const { fetchscTargetStrategy, postTargetStrategy } = api;

// 深拷贝对象/数组
const deepClone = obj => {
  return JSON.parse(JSON.stringify(obj));
};

// 获取字符串长度
const getCharLength = str => {
  let charLength = 0;
  for (let i = 0; i < str.length; i++) {
    if (str.charCodeAt(i) > 255) {
      charLength += 2;
    } else {
      charLength += 1;
    }
  }
  return charLength;
};

//校验是否位数字
const myIsNaN = value => {
  if (isNaN(Number(value, 10))) {
    return false;
  } else {
    return true;
  }
};

@autobind
class TargetStrate extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      dataArr: DATA_CONFIG,
    };
  }
  componentDidMount() {
    this.getData();
  }

  /**
   * @see 页面初始化的时候获取当前的配置
   * @author: liupengwu
   * @date: 2023-05-22 13:55:39
   */
  getData = () => {
    fetchscTargetStrategy()
      .then(data => {
        if (data.code === SUCCESS_CODE) {
          let setArr = [];
          if (data.data) {
            setArr = JSON.parse(data.data);
          } else {
            setArr = DATA_CONFIG;
          }
          this.setState({
            dataArr: setArr,
          });
        } else {
          message.error(data.message || TEXTCONFIG.TEXTCONFIG);
        }
      })
      .catch(() => {
        message.error(TEXTCONFIG.TEXTCONFIG);
      });
  };

  /**
   * @see 输入文案
   * @author: liupengwu
   * @date: 2023-05-22 13:58:07
   */
  changeText(val, index, clickType) {
    val = val.trim();
    const cloneArr = deepClone(this.state.dataArr);
    if (!cloneArr) return;
    cloneArr[index][clickType] = val;
    if(clickType === 'maxNum'){
      cloneArr[index]['minNum'] = 100 - Number(val);
    }
    this.setState({
      dataArr: cloneArr,
    });
  }

  /**
   * @see url输入
   * @author: liupengwu
   * @date: 2023-05-22 13:58:45
   */
  changeUrl(val, key) {
    val = val.trim();
    const cloneArr = deepClone(this.state.dataArr);
    if (!cloneArr) return;
    for (let i = 0; i < cloneArr.length; i++) {
      if (cloneArr[i].key === key) {
        cloneArr[i].toUrl = val;
      }
    }
    this.setState({
      dataArr: cloneArr,
    });
  }

  add() {
    const { dataArr } = this.state;
    if (dataArr.length >= 9) {
      message.info('目前最多添加8个指标');
      //grid多于10个返回
      return;
    } else {
      dataArr.push({ type: '', text: '', toUrl: '', key: 'qsqr' });
      this.setState({ dataArr });
    }
  }

  selectChange(val, index, changeType) {
    const cloneArr = deepClone(this.state.dataArr);
    if (!cloneArr) return;
    cloneArr[index][changeType] = val;
    this.setState({
      dataArr: cloneArr,
    });
  }

  remove(e) {
    const { dataArr } = this.state;
    const k = e.currentTarget.id;
    dataArr.splice(k, 1);
    this.setState({ dataArr });
  }

  /**
   * @see 提交
   * @author: liupengwu
   * @date: 2023-05-22 13:59:11
   */
  save() {
    const saveData = deepClone(this.state.dataArr);
    if (!saveData) {
      message.error('数据异常');
      return;
    }
    for (let i = 0; i < saveData.length; i++) {
      const item = saveData[i];
      if (!item.type) {
        message.error('指标名称不能位空');
        return;
      }
      if (item.key === 'dthc') {
        if (item.text && getCharLength(item.text) > 8) {
          message.error('动态回撤的文案最多4个汉字或8个字符');
          return;
        }
      } else {
        if (item.text && getCharLength(item.text) > 12) {
          message.error('文案最多6个汉字或12个字符');
          return;
        }
      }
      if (item.key === 'hxrsi') {
        if (Object.keys(item).includes('minNum') && !myIsNaN(item.minNum)) {
          message.error('走势图高位区线必须是数字');
          return;
        }
        if (Object.keys(item).includes('maxNum') && !myIsNaN(item.maxNum)) {
          message.error('走势图低位区线必须是数字');
          return;
        }
        if (Object.keys(item).includes('minNum') && (+item.minNum <= 0 || +item.minNum >= 100)) {
          message.error('走势图低位线必须是0-100之间的数字');
          return;
        }
        if (Object.keys(item).includes('maxNum') && (+item.maxNum <= 50 || +item.maxNum >= 100)) {
          message.error('走势图高位线必须是50-100之间的数字');
          return;
        }
        if ((item.minNum && !item.maxNum) || (!item.minNum && item.maxNum)) {
          message.error('走势图低位区线和高位区线必须同时设置');
          return;
        }
        if (+item.minNum >= +item.maxNum) {
          message.error('走势图低位区线不能 大于等于 高位区线');
          return;
        }
      }
      if (item.text && !item.toUrl) {
        message.error('如果填写了文案，跳转链接不能为空');
        return;
      }
      if (!item.text && item.toUrl) {
        message.error('如果填写了链接，文案不能为空');
        return;
      }
    }
    postTargetStrategy({
      value: JSON.stringify(saveData),
    })
      .then(res => {
        const { code } = res;
        if (code === SUCCESS_CODE) {
          message.success('配置成功');
        } else {
          message.error(res.msg || TEXTCONFIG.ERRORTEXT);
        }
      })
      .catch(e => {
        message.error(e);
      });
  }
  render() {
    let { dataArr } = this.state;
    return (
      <article className="salaryManagerBackgroundColor" style={{ width: '1500px' }}>
        <section>
          <PageHeader
            style={{ width: '300px' }}
            title={TEXTCONFIG.PAGETITLE}
            extra={
              <Button type="primary" onClick={this.add}>
                添加
              </Button>
            }
          ></PageHeader>
          <article>
            <Row style={{ width: '1400px' }}>
              {dataArr.map((item, index) => (
                <Col className="g-mt10" span={24} key={item.key}>
                  <div
                    style={{
                      border: '1px solid #c5c4c4',
                      padding: '10px 10px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                    }}
                  >
                    <div>
                      <span style={{ lineHeight: '32px', marginRight: '20px' }}>{index + 1}.</span>
                      <Input
                        style={{ width: '250px', marginRight: '20px' }}
                        className="g-ml10"
                        addonBefore="指标名："
                        onChange={e => this.changeText(e.target.value, index, 'type')}
                        // onChange={e => this.changeText(e.target.value, item.key)}
                        value={item.type}
                      ></Input>
                      <Input
                        style={{ width: '250px', marginRight: '20px' }}
                        className="g-ml10"
                        addonBefore="跳转文案："
                        onChange={e => this.changeText(e.target.value, index, 'text')}
                        value={item.text}
                      ></Input>
                      <Input
                        style={{ width: '300px' }}
                        className="g-ml10"
                        addonBefore="跳转链接:"
                        onChange={e => this.changeText(e.target.value, index, 'toUrl')}
                        value={item.toUrl}
                      ></Input>
                      <Input
                        style={{ width: '200px' }}
                        className="g-ml10"
                        addonBefore="key:"
                        onChange={e => this.changeText(e.target.value, index, 'key')}
                        value={item.key}
                      ></Input>
                      {item.key === 'hxrsi' ? (
                        <>
                          <br />
                          <Row style={{ width: '1000px' }}>
                            <Col className="g-mt10" span={24}>
                              <span style={{ lineHeight: '32px', marginRight: '30px' }}></span>
                              <Input
                                style={{ width: '250px', marginRight: '20px' }}
                                className="g-ml10"
                                addonBefore="走势图高位区线："
                                onChange={e => this.changeText(e.target.value, index, 'maxNum')}
                                // onChange={e => this.changeText(e.target.value, item.key)}
                                value={item.maxNum}
                              ></Input>
                              <Input
                                style={{ width: '250px', marginRight: '20px' }}
                                className="g-ml10"
                                addonBefore="走势图低位区线："
                                disabled
                                onChange={e => this.changeText(e.target.value, index, 'minNum')}
                                // onChange={e => this.changeText(e.target.value, item.key)}
                                value={item.minNum}
                              ></Input>
                              <span style={{ padding: '0 13px', marginLeft: '10px' }}>
                                无权限用户类型：
                              </span>

                              <Select
                                style={{ width: 220 }}
                                onChange={val => {
                                  this.selectChange(val, index, 'needLockUser');
                                }}
                                value={item.needLockUser}
                              >
                                {USER_RSI_OPTIONS.map(item => (
                                  <Option value={item.value}>{item.title}</Option>
                                ))}
                              </Select>
                              <Tooltip
                                title={
                                  <span>
                                    {USER_RSI_OPTIONS_TIP.map(item => (
                                      <>
                                        {item} <br />
                                      </>
                                    ))}
                                  </span>
                                }
                              >
                                <Icon style={{ marginLeft: '10px' }} type="question-circle" />
                              </Tooltip>
                            </Col>
                          </Row>
                          <Row style={{ width: '1000px' }}>
                            <Col className="g-mt10" span={24}>
                              <span style={{ lineHeight: '32px', marginRight: '30px' }}></span>
                              <Input
                                style={{ width: '530px', marginRight: '20px' }}
                                className="g-ml10"
                                addonBefore="蒙层链接："
                                onChange={e => this.changeText(e.target.value, index, 'lockUrl')}
                                // onChange={e => this.changeText(e.target.value, item.key)}
                                value={item.lockUrl}
                              ></Input>
                            </Col>
                          </Row>
                        </>
                      ) : null}
                    </div>
                    {/* <Icon className="g-ml10" type="minus-circle" onClick={this.remove} id={index} /> */}
                  </div>
                </Col>
              ))}
            </Row>
          </article>
        </section>
        <center>
          <Button className="g-mt10" onClick={() => this.save()} type="primary">
            提交
          </Button>
        </center>
      </article>
    );
  }
}

export default connect(({ app, user, permission }) => ({
  theme: app.theme,
  pageLoading: app.pageLoading,
  limits: permission.limits,
}))(TargetStrate);
