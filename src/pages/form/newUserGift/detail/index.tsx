import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker, Button, message } from 'antd';
import styles from './index.less';
import { history } from 'umi';
import Drawing from './drawing';
import api from 'api';
import moment from 'moment';
import { timeFormat2 } from '@/utils/utils';

const { handleGiftActivity, getGiftActivityList, checkCoupon } = api;
const { TextArea } = Input;
const Detail = (props: any) => {
  const { form } = props;
  const isSee = !!props.location.query.type;
  const id = props.location.query.id;
  // 选基指南
  const [fundGuideList, setFundGuideList] = useState([
    {
      fundGuideImg: 'fundGuideImg',
      fundGuideUrl: 'fundGuideUrl',
      flag: 0,
    },
  ]);
  // 新增
  const handleAddGuide = () => {
    const flag = fundGuideList.slice(-1)[0]['flag'] + 1;
    const obj = {
      fundGuideImg: 'fundGuideImg',
      fundGuideUrl: 'fundGuideUrl',
      flag,
    };
    fundGuideList.push(obj);
    setFundGuideList([...fundGuideList]);
  };
  // 删除
  const handleDelGuide = (item: any) => {
    if (fundGuideList.length < 2) {
      message.error('至少保留一个');
      return;
    }
    const index = fundGuideList.findIndex(n => {
      return n.flag === item.flag;
    });
    fundGuideList.splice(index, 1);
    setFundGuideList([...fundGuideList]);
  };
  const handleCancel = () => {
    history.push('list');
  };
  const handleSave = () => {
    form.validateFields((err: any, values: any) => {
      if (!err) {
        const configQuery: any = {};
        const guideList: any[] = [];
        const tabList: any[] = [];
        let flag = '';
        let key = '';
        let key2 = '';
        let key3 = '';
        let key4 = '';
        let obj = {};
        Object.keys(values).forEach(item => {
          if (item.includes('fundGuideImg')) {
            flag = item.slice(12);
            key = `fundGuideUrl${flag}`;
            obj = {
              fundGuideImg: values[item],
              fundGuideUrl: values[key],
            };
            guideList.push(obj);
          } else if (item.includes('tabName')) {
            flag = item.slice(7);
            key = `tabName${flag}`;
            key2 = `tabInner${flag}`;
            key3 = `tabFundCode${flag}`;
            key4 = `tabFundTags${flag}`;
            obj = {
              tabName: values[key],
              tabInner: values[key2],
              tabFundCode: values[key3],
              tabFundTags: values[key4],
            };
            tabList.push(obj);
          } else if (
            item.includes('fundGuideUrl') ||
            item.includes('tabInner') ||
            item.includes('tabFundCode') ||
            item.includes('tabFundTags') ||
            item.includes('welfareOne') ||
            item.includes('welfaretTwo') ||
            item.includes('welfareThree') ||
            item.includes('welfareFour')
          ) {
          } else if (['startTime', 'endTime'].includes(item)) {
            configQuery[item] = values[item].format('YYYY-MM-DD HH:mm:ss');
          } else {
            configQuery[item] = values[item];
          }
        });
        configQuery.tabList = tabList;
        configQuery.guideList = guideList;
        const lastEditor = JSON.parse(localStorage.getItem('name') as string);
        const reqQuery = {
          activityId: id,
          activityName: configQuery.activityName,
          activityEndTime: configQuery.endTime,
          activityStartTime: configQuery.startTime,
          activityStatus: 'ACTIVITY_WAIT_REVIEW',
          lastEditor,
          giftsConfig: JSON.stringify(configQuery),
          // giftsConfig: configQuery,
        };
        handleGiftActivityInfo(reqQuery, configQuery);
        console.log(reqQuery);
      } else {
        message.error('请把信息填写完整');
      }
    });
  };
  const dealWithCoupon = (result: any, configQuery: any) => {
    const { status_code, status_msg } = result;
    if (status_code === 0) {
      if (result?.data?.length > 0) {
        for (let i = 0, len = result.data.length; i < len; i++) {
          const { couponStartDate, couponEndDate, couponId } = result.data[i];
          if (couponStartDate && couponEndDate) {
            let time = timeFormat2();
            time = time.replace(/[^\d]/g, '');
            if (time < couponStartDate || time > couponEndDate) {
              message.error(`当前优惠券ID-${couponId}未处于生效状态`);
              return;
            }
            if (configQuery.startTime) {
              let startTime = configQuery.startTime.replace(/[^\d]/g, '');
              if (startTime < couponStartDate) {
                message.error('模块的时间范围必须在优惠券的时间范围之内');
                return;
              }
            }
            if (configQuery.endTime) {
              let endTime = configQuery.endTime.replace(/[^\d]/g, '');
              if (endTime >= couponEndDate) {
                message.error('模块的时间范围必须在优惠券的时间范围之内');
                return;
              }
            }
          } else {
            message.error(`接口未返回优惠券ID-${couponId}生效时间`);
            return;
          }
        }
      } else {
        message.error('请检查优惠券ID是否正确');
        return;
      }
    } else {
      message.error(status_msg || '网络请求错误，请稍后再试');
      return;
    }
    return true;
  };
  const handleGiftActivityInfo = async (reqQuery: any, configQuery: any) => {
    try {
      let result = await checkCoupon({}, configQuery.hbId);
      let result2 = await checkCoupon({}, configQuery.pzhbId);
      const boolean1 = dealWithCoupon(result, configQuery);
      const boolean2 = dealWithCoupon(result2, configQuery);
      if (!boolean1 || !boolean2) {
        return;
      }
      const res = await handleGiftActivity(reqQuery);
      if (res.status_code === 0) {
        message.success('操作成功');
        history.push('list');
      }
    } catch (error) {
      message.error('网络请求错误，请稍后重试');
    }
  };
  // 获取单个活动的详情信息
  const searchOneActivityList = () => {
    getGiftActivityList({}, `activityId=${id}`)
      .then((res: any) => {
        if (res.status_code === 0) {
          const giftsConfig = res.data[0]?.giftsConfig && JSON.parse(res.data[0].giftsConfig);
          dealwithDetails(giftsConfig);
        } else {
          message.error(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        message.error('网络请求错误，请稍后重试');
      });
  };
  useEffect(() => {
    if (id) {
      searchOneActivityList();
    }
  }, [props.location.query]);
  const dealwithDetails = (giftsConfig: any) => {
    const formObj: any = {};
    let obj: any = [];
    Object.keys(giftsConfig).forEach(key => {
      if (['startTime', 'endTime'].includes(key)) {
        formObj[key] = moment(giftsConfig[key]);
      } else if (key === 'guideList') {
        const newFundGuideList: any[] = [];
        giftsConfig[key].forEach((n, index) => {
          obj = {
            fundGuideImg: 'fundGuideImg',
            fundGuideUrl: 'fundGuideUrl',
            flag: index,
          };
          newFundGuideList.push(obj);
          formObj[`fundGuideImg${index}`] = n.fundGuideImg;
          formObj[`fundGuideUrl${index}`] = n.fundGuideUrl;
        });
        setFundGuideList([...newFundGuideList]);
      } else if (key === 'tabList') {
        giftsConfig[key].forEach((n, index) => {
          formObj[`tabName${index + 1}`] = n.tabName;
          formObj[`tabInner${index + 1}`] = n.tabInner;
          formObj[`tabFundCode${index + 1}`] = n.tabFundCode;
          formObj[`tabFundTags${index + 1}`] = n.tabFundTags;
        });
      } else {
        formObj[key] = giftsConfig[key];
      }
    });
    form.setFieldsValue(formObj);
  };
  return (
    <div>
      <Form className={styles['m-form-container']}>
        <Form.Item label={'活动名称'}>
          {form.getFieldDecorator('activityName', {
            rules: [{ required: true, message: '请输入活动名称' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'开始时间'}>
          {form.getFieldDecorator('startTime', {
            rules: [
              {
                required: true,
                validator: (rule: any, value: any, callback: any) => {
                  if (value === '' || value === null || value === undefined) {
                    callback('请选择开始时间');
                  } else if (!form.getFieldValue('endTime')) {
                    callback();
                  } else if (form.getFieldValue('endTime').isBefore(value)) {
                    callback('开始时间应该在结束时间之前');
                  }
                  callback();
                },
              },
            ],
          })(
            <DatePicker
              style={{ width: '600px' }}
              showTime={{ format: 'HH:mm:ss' }}
              format="YYYY-MM-DD HH:mm:ss"
            />,
          )}
        </Form.Item>
        <Form.Item label={'结束时间'}>
          {form.getFieldDecorator('endTime', {
            rules: [
              {
                required: true,
                validator: (rule: any, value: any, callback: any) => {
                  if (value === '' || value === null || value === undefined) {
                    callback('请选择结束时间');
                  } else if (!form.getFieldValue('startTime')) {
                    callback();
                  } else if (!form.getFieldValue('startTime').isBefore(value)) {
                    callback('结束时间应该在开始时间之后');
                  }
                  callback();
                },
              },
            ],
          })(
            <DatePicker
              style={{ width: '600px' }}
              showTime={{ format: 'HH:mm:ss' }}
              format="YYYY-MM-DD HH:mm:ss"
            />,
          )}
        </Form.Item>
        <Form.Item label={'活动规则'}>
          {form.getFieldDecorator('activityRule', {
            rules: [{ required: true, message: '请输入活动规则' }],
          })(<TextArea style={{ width: '600px' }} rows={6} />)}
        </Form.Item>
        <Form.Item label={'权益1'}>
          {form.getFieldDecorator('welfareOne', {
            rules: [{ required: false, message: '请选择权益' }],
          })(<span>体验金</span>)}
        </Form.Item>
        <Form.Item label={'体验金id'}>
          {form.getFieldDecorator('tyjId', {
            rules: [{ required: true, message: '请输入体验金id' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'气泡图片'}>
          {form.getFieldDecorator('welfareImgOne', {
            rules: [{ required: true, message: '请上传图片' }],
          })(<Drawing />)}
        </Form.Item>
        <Form.Item label={'权益2'}>
          {form.getFieldDecorator('welfaretTwo', {
            rules: [{ required: false, message: '请选择权益' }],
          })(<span>满减红包</span>)}
        </Form.Item>
        <Form.Item label={'红包id'}>
          {form.getFieldDecorator('hbId', {
            rules: [{ required: true, message: '请输入红包id' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'膨胀红包id'}>
          {form.getFieldDecorator('pzhbId', {
            rules: [{ required: true, message: '请输入膨胀红包id' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'气泡图片'}>
          {form.getFieldDecorator('welfareImgTwo', {
            rules: [{ required: true, message: '请上传图片' }],
          })(<Drawing />)}
        </Form.Item>
        <Form.Item label={'跳转链接'}>
          {form.getFieldDecorator('welfaretUrlTwo', {
            rules: [{ required: true, message: '请输入跳转链接' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'权益3'}>
          {form.getFieldDecorator('welfareThree', {
            rules: [{ required: false, message: '请选择权益' }],
          })(<span>0折卡</span>)}
        </Form.Item>
        {/* <Form.Item label={'红包id'}>
          {form.getFieldDecorator('discountId', {
            rules: [{ required: true, message: '请输入红包id' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item> */}
        <Form.Item label={'气泡图片'}>
          {form.getFieldDecorator('welfareImgThree', {
            rules: [{ required: true, message: '请上传图片' }],
          })(<Drawing />)}
        </Form.Item>
        <Form.Item label={'跳转链接'}>
          {form.getFieldDecorator('welfareUrlThree', {
            rules: [{ required: true, message: '请输入跳转链接' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'权益4'}>
          {form.getFieldDecorator('welfareFour', {
            rules: [{ required: false, message: '请选择权益' }],
          })(<span>其他</span>)}
        </Form.Item>
        <Form.Item label={'金额'}>
          {form.getFieldDecorator('otherMoney', {
            rules: [{ required: true, message: '请输入金额' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'标题'}>
          {form.getFieldDecorator('otherTitle', {
            rules: [{ required: true, message: '请输入标题' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'副标题'}>
          {form.getFieldDecorator('otherTitle2', {
            rules: [{ required: true, message: '请输入副标题' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'气泡图片'}>
          {form.getFieldDecorator('otherImg', {
            rules: [{ required: true, message: '请上传图片' }],
          })(<Drawing />)}
        </Form.Item>
        <Form.Item label={'跳转链接'}>
          {form.getFieldDecorator('otherUrl', {
            rules: [{ required: true, message: '请输入跳转链接' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <h4>严选好基</h4>
        <h6>tab1</h6>
        <Form.Item label={'名称'}>
          {form.getFieldDecorator('tabName1', {
            rules: [{ required: true, message: '请输入名称' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'介绍文案'}>
          {form.getFieldDecorator('tabInner1', {
            rules: [{ required: true, message: '请输入介绍文案' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'基金代码'}>
          {form.getFieldDecorator('tabFundCode1', {
            rules: [{ required: true, message: '请输入基金代码' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <div className={styles['tags-div']}>
          <Form.Item label={'基金标签'}>
            {form.getFieldDecorator('tabFundTags1', {
              rules: [{ required: true, message: '请输入基金代码' }],
            })(<Input style={{ width: '600px' }} />)}
          </Form.Item>
          <div>多个标签用中文","隔开</div>
        </div>
        <h6>tab2</h6>
        <Form.Item label={'名称'}>
          {form.getFieldDecorator('tabName2', {
            rules: [{ required: true, message: '请输入名称' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'介绍文案'}>
          {form.getFieldDecorator('tabInner2', {
            rules: [{ required: true, message: '请输入介绍文案' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'基金代码'}>
          {form.getFieldDecorator('tabFundCode2', {
            rules: [{ required: true, message: '请输入基金代码' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <div className={styles['tags-div']}>
          <Form.Item label={'基金标签'}>
            {form.getFieldDecorator('tabFundTags2', {
              rules: [{ required: true, message: '请输入基金代码' }],
            })(<Input style={{ width: '600px' }} />)}
          </Form.Item>
          <div>多个标签用中文","隔开</div>
        </div>
        <h6>tab3</h6>
        <Form.Item label={'名称'}>
          {form.getFieldDecorator('tabName3', {
            rules: [{ required: true, message: '请输入名称' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'介绍文案'}>
          {form.getFieldDecorator('tabInner3', {
            rules: [{ required: true, message: '请输入介绍文案' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <Form.Item label={'基金代码'}>
          {form.getFieldDecorator('tabFundCode3', {
            rules: [{ required: true, message: '请输入基金代码' }],
          })(<Input style={{ width: '600px' }} />)}
        </Form.Item>
        <div className={styles['tags-div']}>
          <Form.Item label={'基金标签'}>
            {form.getFieldDecorator('tabFundTags3', {
              rules: [{ required: true, message: '请输入基金代码' }],
            })(<Input style={{ width: '600px' }} />)}
          </Form.Item>
          <div>多个标签用中文","隔开</div>
        </div>
        <div className={styles['m-btns']}>
          <h4>选基指南</h4>
          <Button type={'primary'} className={styles['m-add-btn']} onClick={handleAddGuide}>
            新增
          </Button>
        </div>
        {fundGuideList.map(item => (
          <div className={styles['m-fund-guide-item']} key={item.flag}>
            <Form.Item label={'模块'}>
              {form.getFieldDecorator(`${item.fundGuideImg}${item.flag}`, {
                rules: [{ required: true, message: '请上传' }],
              })(<Drawing limit={'请上传宽957高225，大小不超过1MB的图片'} />)}
            </Form.Item>
            <Form.Item label={'跳转链接'}>
              {form.getFieldDecorator(`${item.fundGuideUrl}${item.flag}`, {
                rules: [{ required: true, message: '请输入跳转链接' }],
              })(<Input style={{ width: '600px' }} />)}
            </Form.Item>
            <Button
              type={'danger'}
              className={styles['m-del-btn']}
              onClick={() => handleDelGuide(item)}
            >
              删除
            </Button>
          </div>
        ))}
        <div className={styles['m-bottom-btns']}>
          {!isSee && (
            <Button type={'primary'} className={styles['m-save-btn']} onClick={handleSave}>
              保存
            </Button>
          )}
          <Button className={styles['m-cancel-btn']} onClick={handleCancel}>
            取消
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default Form.create<any>({})(Detail);
