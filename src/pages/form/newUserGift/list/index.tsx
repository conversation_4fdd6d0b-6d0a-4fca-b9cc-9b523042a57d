import React, { useEffect, useState } from 'react';
// 接口
import api from 'api';
// 组件
import { Table, Button, message, Popconfirm } from 'antd';
// 路由
import { history } from 'umi';
// store
import store from 'store';
import { ActivityStatus } from './data';

const { getGiftActivityList, handleGiftActivityStatus } = api;
export default function() {
  const columns = [
    {
      title: '活动ID',
      dataIndex: 'activityId',
      render: (text: string) => (text ? text : '— —'),
    },
    {
      title: '活动名称',
      dataIndex: 'activityName',
      render: (text: string) => (text ? text : '— —'),
    },
    {
      title: '活动状态',
      dataIndex: 'activityStatus',
      render: (text: number) => {
        return ActivityStatus[text] || '— —';
      },
    },
    {
      title: '活动时间',
      dataIndex: 'activityTime',
      render: (text: string, record: any) => {
        if (!record.activityStartTime && !record.activityEndTime) {
          return '— —';
        }
        return `${record.activityStartTime} ~ ${record.activityEndTime}`;
      },
    },
    {
      title: '最后编辑人',
      dataIndex: 'lastEditor',
      render: (text: string) => (text ? text : '— —'),
    },
    {
      title: '最后编辑时间',
      dataIndex: 'lastEditorTime',
      render: (text: string) => (text ? text : '— —'),
    },
    {
      title: '操作',
      dataIndex: 'options',
      // 审核|编辑|作废|查看|进行中|已结束|已作废
      render: (text: unknown, record: any) => {
        function tempRender(
          inner: string,
          fn: (record: any) => void,
          marginRight: number = 0,
          type:
            | 'default'
            | 'link'
            | 'ghost'
            | 'primary'
            | 'dashed'
            | 'danger'
            | undefined = 'primary',
        ) {
          if (type === 'danger') {
            return (
              <Popconfirm
                title="你确定中止活动么？"
                okText="确定"
                cancelText="取消"
                onConfirm={() => fn(record)}
              >
                <Button type={type} style={{ marginRight: `${marginRight}px` }}>
                  {inner}
                </Button>
              </Popconfirm>
            );
          }
          return (
            <Button
              type={type}
              style={{ marginRight: `${marginRight}px` }}
              onClick={() => fn(record)}
            >
              {inner}
            </Button>
          );
        }
        switch (record.activityStatus) {
          case 'ACTIVITY_WAIT_REVIEW':
            return (
              <section>
                {tempRender('审核', handleVerify, 20, 'primary')}
                {tempRender('编辑', handleEdit)}
              </section>
            );
          case 'ACTIVITY_ON_LINE':
            return (
              <section>
                {tempRender('查看', handleCheck, 20, 'primary')}
                {tempRender('中止', handleStop, 0, 'danger')}
              </section>
            );
          case 'ACTIVITY_FINISHED':
            return <section>{tempRender('查看', handleCheck)}</section>;
          default:
            return '--';
        }
      },
    },
  ];
  // 表格数据
  const [dataSource, setDataSource] = useState<any[]>();
  // 表格加载动画
  const [loadingFlag, setLoadingFlag] = useState<boolean>();
  // 表格总数据
  const [total, setTotal] = useState<number>();
  useEffect(() => {
    searchAllActivityList();
  }, []);
  // 查询活动列表
  const searchAllActivityList = () => {
    setLoadingFlag(true);
    getGiftActivityList({}, `activityId=`)
      .then((res: any) => {
        if (res.status_code === 0) {
          setDataSource(res.data);
          setTotal(res.data.length);
          setLoadingFlag(false);
        } else {
          message.error(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        message.error('网络请求错误，请稍后重试');
        console.log('查询活动列表错误：', err);
      });
  };
  // 改变活动状态
  const _postActivityStatus = (body: {
    activityId: string;
    activityStatus: 'ACTIVITY_ON_LINE' | 'ACTIVITY_FINISHED ';
  }) => {
    function success() {
      message.success(`状态改变成功`);
      searchAllActivityList();
    }
    handleGiftActivityStatus(body)
      .then((res: { status_code: number; message: string }) => {
        res.status_code === 0 ? success() : message.error(res.message);
      })
      .catch((err: unknown) => {
        message.error('网络请求错误，请稍后重试');
        console.log('改变活动状态错误：', err);
      });
  };
  // 查看
  const handleCheck = (record: any) => {
    history.push({
      pathname: 'detail',
      query: {
        type: 'check',
        id: record.activityId,
      },
    });
  };
  // 中止
  const handleStop = (record: any) => {
    _postActivityStatus({
      activityId: record.activityId,
      activityStatus: 'ACTIVITY_FINISHED ',
    });
  };
  // 审核
  const handleVerify = (record: any) => {
    const currentUser = store.get('name');
    const lastUser = record.lastEditor;
    if (currentUser === lastUser) {
      return message.error('最后编辑人不可以和审核人一致');
    }
    _postActivityStatus({
      activityId: record.activityId,
      activityStatus: 'ACTIVITY_ON_LINE',
    });
  };
  // 编辑
  const handleEdit = (record: any) => {
    history.push({
      pathname: 'detail',
      query: {
        id: record.activityId,
      },
    });
  };
  return (
    <article>
      <section style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="primary"
          onClick={() => {
            history.push('detail');
          }}
          style={{ marginBottom: '20px' }}
        >
          新增活动
        </Button>
        <span>
          负责人：zhengzekai
          <br />
          手炒环境：client.html?action=ijijin^action=webpage,title=,url=https://fund.10jqka.com.cn/ifundapp_app/public/m/ifundNewUserGift/dist/index.html?activityId=
          <br />
          基金环境：https://fund.10jqka.com.cn/ifundapp_app/public/m/ifundNewUserGift/dist/index.html?activityId=
        </span>
      </section>
      <Table
        columns={columns}
        dataSource={dataSource}
        pagination={{
          // current,
          pageSize: 10,
          total: total,
          // onChange: handlePaginationChange,
        }}
        scroll={{ x: 'max-content' }}
        loading={loadingFlag}
      ></Table>
    </article>
  );
}
