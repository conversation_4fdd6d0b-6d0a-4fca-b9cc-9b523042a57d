/*
*接口配置
* <AUTHOR>
* @time 2019.12
*/

import React, { Fragment } from 'react';
import {<PERSON><PERSON>, Card, Row, message, Popconfirm, Collapse} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import ReactDOM from 'react-dom';
import FormRender from 'form-render/lib/antd';

const { fetchTopFund, postTopFund, postTopFundSave, fetchTopFundSave } = api;
const schema = {
    "propsSchema": {
      "type": "object",
          "properties": {
            "title": {
              "title": "标题",
              "type": "string"
            },
            "description": {
              "title": "介绍说明",
              "type": "string",
              "format": "textarea"
            },
            "ruleList": {
              "title": "基金公司列表",
              "type": "array",
              "ui:options": {
                "foldable": true
              },
              "items": {
                "type": "object",
                "properties": {
                  "componyName": {
                    "title": "基金公司",
                    "type": "string",
                    "ui:width": "350px"
                  },
                  "componyCode": {
                    "title": "基金公司代码",
                    "type": "string",
                    "ui:width": "350px"
                  },
                  "fundName": {
                    "title": "基金名字",
                    "type": "string",
                    "ui:width": "350px"
                  },
                  "fundCode": {
                    "title": "基金代码",
                    "type": "string",
                    "ui:width": "350px"
                  },
                  "imageUrl": {
                    "title": "基金公司图片链接",
                    "description": "图片链接只接受https格式的",
                    "type": "string"
                  },
                  "fundTime": {
                    "title": "利率区间",
                    "type": "string",
                    "ui:width": "350px",
                    "enum": [
                      "year",
                      "now",
                      "week",
                      "month",
                      "tmonth",
                      "hyear",
                      "twoyear",
                      "tyear",
                      "fyear",
                      "nowyear"
                    ],
                    "enumNames": [
                      "近一年涨幅-year",
                      "成立以来涨幅-now",
                      "近一周涨幅-week",
                      "近一月涨幅-month",
                      "近三月涨幅-tmonth",
                      "近半年涨幅-hyear",
                      "近两年涨幅-towyear",
                      "近三年涨幅-tyear",
                      "近五年涨幅-fyear",
                      "今年以来涨幅-nowyear"
                    ]
                  },
                  "multiSelect": {
                    "title": "基金标签",
                    "type": "array",
                    "ui:widget": "select",
                    "ui:options": {
                      "mode": "tags"
                    }
                  }
                },
                "required": [
                  "componyName",
                  "componyCode",
                  "fundName",
                  "fundCode",
                  "fundDes",
                  "fundTime",
                  "imageUrl",
                  "multiSelect"
              ]
              },
            }
          },
          "required": [
              'title',
              'description'
          ]
    },
    "formData": {
        "title": "",
        "description": "",
        "ruleList": [
          {
            "componyName": "",
            "componyCode": "",
            "fundName": "",
            "fundCode": "",
            "imageUrl": "",
            "fundTime": "",
            "multiSelect": []
          }
        ]
    }
};


@autobind
class topFund extends React.Component {
    constructor (props) {
        super(props);
        this.state = {formData: schema.formData || {}};
    }


    onChange = formData => {
        this.setState({ formData }, () => {console.log(this.state)});
    }
    
    dataInit () {
      fetchTopFundSave().then(data => {
        if (data.data) {
          data = JSON.parse(data.data);
          this.setState({formData: data});
        }
      })
    }
    
    saveForm () {
      let _formData = this.state.formData;
      for (let i = 0; i < _formData.ruleList.length; i++) {
          if (_formData.ruleList) {
            _formData.ruleList[i].componyCode = _formData.ruleList[i].componyCode.replace(/ /g, "")
            _formData.ruleList[i].imageUrl = _formData.ruleList[i].imageUrl.replace(/ /g, "")
            _formData.ruleList[i].fundCode = _formData.ruleList[i].fundCode.replace(/ /g, "")
            if (_formData.ruleList[i].multiSelect.length > 2){
              message.error(`${i+1} 基金标签最多2个`);
              return false;
            }
          }
      }
      console.log({value: JSON.stringify(_formData)})
      postTopFundSave ({
        value: JSON.stringify(_formData)
      }).then(data => {
          if (data) {
              message.success('保存成功');
              window.location.reload();
          }else {
              message.error(data.message);
          }
      })
  }

    updateForm () {
      let _formData = this.state.formData;
      if (_formData.title.length > 13) {
        message.error('标题最多13个字');
        return;
      }
      if (_formData.ruleList.length < 3) {
        message.error('基金公司不得小于3个');
        return;
      }
      for (let i = 0; i < _formData.ruleList.length; i++) {
        if (_formData.ruleList) {
          _formData.ruleList[i].componyCode = _formData.ruleList[i].componyCode.replace(/ /g, "");
          _formData.ruleList[i].imageUrl = _formData.ruleList[i].imageUrl.replace(/ /g, "")
          _formData.ruleList[i].fundCode = _formData.ruleList[i].fundCode.replace(/ /g, "")
          if (_formData.ruleList[i].multiSelect.length > 2){
            message.error(`${i+1} 基金标签最多2个`);
            return false;
          }
        }
    }
      postTopFundSave ({
        value: JSON.stringify(_formData)
      }).then(data => {
          if (data) {
              message.success('保存成功');
              postTopFund ({
                value: JSON.stringify(_formData)
              }).then(data => {
                  if (data) {
                      message.success('修改成功');
                      window.location.reload();
                  }else {
                      message.error(data.message);
                  }
              })
          }else {
              message.error(data.message);
          }
      })
    }

    componentDidMount () {
        this.dataInit();
        // this.newBoyDataInit();
    }

    render () {
        return (
            <section className="codeInterface" style={{ width: '800px' }}>

                <FormRender
                    propsSchema={schema.propsSchema}
                    formData={this.state.formData}
                    onChange={this.onChange}
                />

                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交么'}
                    onConfirm={this.updateForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="danger" 
                        // style={{ marginLeft: '300px' }}
                    >
                        提交修改
                    </Button>
                </Popconfirm>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要保存么'}
                    onConfirm={this.saveForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="primary" 
                        style={{ marginLeft: '600px' }}
                    >
                        保存
                    </Button>
                </Popconfirm>

            </section>
        )
    }
}

export default topFund;