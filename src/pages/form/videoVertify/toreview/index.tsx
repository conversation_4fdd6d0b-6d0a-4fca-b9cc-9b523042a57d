import React, { useEffect, useState } from 'react';
import { Input, Button, Modal, message } from 'antd';
import api from 'api';
import FORM_CONFIG from './form.json';
import FormRender from 'form-render/lib/antd';
import styles from './index.less';
import classNames from 'classnames';
import { useHistory } from 'react-router-dom';
import { pageTypeEnum } from '../type';

const { TextArea } = Input;
const { getUiList, postUimodify } = api;
export default function() {
  const [init, setInit] = useState(true);
  const [formData, setFormState] = useState({ tabService: [] });
  const [formConfig, setFormConfig] = useState(FORM_CONFIG.propsSchema);
  const [uiSchema, setUiSchema] = useState({});
  const [valid, setValid] = useState([]);
  const [visible, setVisible] = useState(false);
  const [texvalue, setValue] = useState('');
  const [reType, setType] = useState('');
  let history = useHistory();
  useEffect(() => {
    let gettime: any = formatUrlParams();
    console.log(gettime.projectNo);
    setType(gettime.type);
    getUiList({
      reqDto: JSON.stringify({ projectNo: gettime.projectNo, uiType: pageTypeEnum.video }),
    }).then((res: any) => {
      try {
        console.log(res);
        setFormState({ tabService: res.data[0] });
      } catch (e) {
        message.error(e.message);
      }
    });
  }, [init]);
  function onSubmit() {
    let getData: any = formData.tabService;
    let param = {
      projectNo: getData.projectNo,
      fileName: getData.fileName,
      attachment: getData.attachment,
      legalReview: getData.legalReview,
      businessReview: getData.legalReview,
      uploadPerson: getData.uploadPerson,
      notes: getData.notes,
      prePublishDate: getData.prePublishDate,
      legalReviewTime: getData.legalReviewTime || '',
      businessReviewTime: getData.businessReviewTime || '',
    };
    if (reType === 'legal') {
      param.legalReview = '1';
      param.legalReviewTime = param.legalReviewTime ? param.legalReviewTime : new Date();
    } else {
      param.businessReview = '1';
      param.businessReviewTime = param.businessReviewTime ? param.businessReviewTime : new Date();
    }
    postUimodify({
      reqDto: JSON.stringify({ ...param, uiType: pageTypeEnum.video }),
    }).then((res: any) => {
      console.log(res);
      if (res.code === 0) {
        message.success('审核完成！');
        history.push(`/form/videoVertify/videoreview`);
      }
    });
  }
  // 0未审核 1通过 2不通过
  function handleOk() {
    let getData: any = formData.tabService;
    let param = {
      projectNo: getData.projectNo,
      fileName: getData.fileName,
      attachment: getData.attachment,
      legalReview: getData.legalReview,
      businessReview: getData.businessReview,
      uploadPerson: getData.uploadPerson,
      notes: getData.notes,
      prePublishDate: getData.prePublishDate,
      commment: texvalue,
    };
    if (reType === 'legal') {
      param.legalReview = '2';
    } else {
      param.businessReview = '2';
    }
    console.log(param);
    postUimodify({
      reqDto: JSON.stringify({ ...param, uiType: pageTypeEnum.video }),
    }).then((res: any) => {
      console.log(res);
      if (res.code === 0) {
        message.success('上传成功！');
        history.push(`/form/videoVertify/videoreview`);
      }
    });
  }
  function handleCancel() {
    setVisible(false);
  }
  function refuse() {
    setVisible(true);
  }
  // 获取url中的参数 history模式下
  function formatUrlParams() {
    let url = window.location.href;
    let obj = { projectNo: '' };
    if (url.indexOf('?') !== -1) {
      let startIndex = url.indexOf('?') + 1;
      let str = url.substring(startIndex);
      let strs = str.split('&');
      for (let i = 0; i < strs.length; i++) {
        obj[strs[i].split('=')[0]] = strs[i].split('=')[1];
      }
      return obj;
    }
  }
  function onChange(e: any) {
    e.persist();
    setValue(e.target.value);
  }
  if (!init) return '加载中';
  return (
    <div className={'kycForm'}>
      <FormRender
        propsSchema={formConfig}
        uiSchema={uiSchema}
        formData={formData}
        onChange={setFormState}
        onValidate={setValid}
      />
      <div className={classNames(styles['btn-wrap'])}>
        <Button type="primary" onClick={onSubmit} className={classNames(styles['pass'])}>
          审核通过
        </Button>
        <Button type="danger" onClick={refuse}>
          不予通过
        </Button>
      </div>
      <Modal title="审核不通过" visible={visible} onOk={handleOk} onCancel={handleCancel}>
        <div className={classNames(styles['m-dialog'])}>
          <div>批注</div>
          <TextArea rows={4} onChange={e => onChange(e)} />
        </div>
      </Modal>
    </div>
  );
}
