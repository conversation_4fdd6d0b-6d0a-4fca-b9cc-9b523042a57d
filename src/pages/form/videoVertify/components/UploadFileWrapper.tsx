import React from 'react';
import { Icon } from 'antd';
import UploadFile from '@/pages/form/components/uploadFile/index';

const UploadFileWrapper = props => {
  return (
    <>
      <UploadFile
        text={'上传文件'}
        callback={(name, size, url) => {
          props.onChange(props.name, url);
        }}
      />
      {props.value && (
        <div style={{ marginLeft: '-180px' }}>
          {props.value}
          <Icon
            onClick={() => {
              props.onChange(props.name, '');
            }}
            style={{ marginLeft: '12px' }}
            theme="twoTone"
            twoToneColor="#eb2f96"
            type="delete"
          ></Icon>
        </div>
      )}
    </>
  );
};

export default UploadFileWrapper;
