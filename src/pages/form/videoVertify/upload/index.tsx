import React, { useState, useEffect } from 'react';
import { Button, Table, message } from 'antd';
import api from 'api';
import styles from './index.less';
import classNames from 'classnames';
import { useHistory } from 'react-router-dom';
import { pageTypeEnum } from '../type';
import { compareDates } from '../../uiVertify/upload/util';

const { getUiList, deleteUi } = api;
function Demo() {
  const [init, setInit] = useState(false);
  let history = useHistory();
  const [data, setUploadData] = useState([]);
  const columns = [
    {
      title: '上传时间',
      dataIndex: 'projectNo',
      key: 'projectNo',
    },
    {
      title: '文件名称',
      dataIndex: 'fileName',
      key: 'fileName',
    },
    {
      title: '上传者',
      dataIndex: 'uploadPerson',
      key: 'uploadPerson',
    },
    {
      title: '法务审核',
      key: 'legalReview',
      dataIndex: 'legalReview',
      render: (key: any, record: any) => {
        return record.legalReview === '1' ? (
          <div>{'李珍珍审核通过'}</div>
        ) : record.legalReview === '2' ? (
          <div>{'李珍珍审核不通过'}</div>
        ) : (
          '待审核'
        );
      },
    },
    {
      title: '业务负责审核',
      dataIndex: 'businessReview',
      key: 'businessReview',
      render: (key: any, record: any) => {
        return record.businessReview === '1' ? (
          <div>{'赵利明审核通过'}</div>
        ) : record.businessReview === '2' ? (
          <div>{'赵利明审核不通过'}</div>
        ) : (
          '待审核'
        );
      },
    },
    {
      title: '操作',
      key: 'operating',
      render: (key: any) => {
        return (
          <div>
            <a onClick={() => handleEdit(key)}>编辑&nbsp;&nbsp;&nbsp;</a>
            <a onClick={() => handleDelete(key)}>删除</a>
          </div>
        );
      },
    },
  ];
  function handleEdit(key: any, type = 'legal') {
    console.log(key);
    history.push(`/form/videoVertify/edit?projectNo=${key.projectNo}`);
  }
  function handleDelete(key: any) {
    console.log(key);
    deleteUi({
      reqDto: JSON.stringify({ projectNo: key.projectNo, uiType: pageTypeEnum.video }),
    })
      .then((res: any) => {
        console.log(res);
        message.success('删除成功！');
        setTimeout(() => {
          location.reload();
        }, 500);
      })
      .catch((err: any) => {
        console.log(err);
        message.error(`删除失败, ${err}`);
      });
  }
  useEffect(() => {
    getUiList({
      reqDto: JSON.stringify({ uiType: pageTypeEnum.video }),
    }).then((res: any) => {
      try {
        let _list = res.data;
        _list = _list.sort(compareDates);
        setUploadData(_list);
      } catch (e) {
        message.error(e.message);
      }
    });
  }, [init]);
  function onUpload() {
    history.push(`/form/videoVertify/add`);
  }
  return (
    <div style={{ padding: 60 }}>
      <div className={classNames(styles['top'])}>
        <Button type="primary" onClick={onUpload}>
          上传
        </Button>
      </div>
      <Table columns={columns} dataSource={data} bordered pagination={false} />
    </div>
  );
}
export default Demo;
