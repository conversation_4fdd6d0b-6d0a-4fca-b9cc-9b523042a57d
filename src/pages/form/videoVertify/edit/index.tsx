import React, { useEffect, useState } from 'react';
import { Button, message } from 'antd';
import api from 'api';
import FORM_CONFIG from './form.json';
import FormRender from 'form-render/lib/antd';
import styles from './index.less';
import classNames from 'classnames';
import { useHistory } from 'react-router-dom';
import UploadFileWrapper from '../components/UploadFileWrapper';
import { pageTypeEnum } from '../type';
const { postUimodify, getUiList } = api;
export default function() {
  const [init, setInit] = useState(true);
  const [formData, setFormState] = useState({ tabService: [] });
  const [formConfig, setFormConfig] = useState(FORM_CONFIG.propsSchema);
  const [uiSchema, setUiSchema] = useState({});
  const [valid, setValid] = useState([]);
  let history = useHistory();
  useEffect(() => {
    let gettime: any = formatUrlParams();
    console.log(gettime.projectNo);
    getUiList({
      reqDto: JSON.stringify({ projectNo: gettime.projectNo, uiType: pageTypeEnum.video }),
    }).then((res: any) => {
      // 0未审核 1通过 2不通过
      try {
        console.log(res);
        let respond = res.data[0];
        respond.reviews =
          '法务：' +
          (respond.legalReview === '0'
            ? '未审核'
            : respond.legalReview === '1'
            ? '审核通过'
            : '审核不通过') +
          ', ' +
          '业务负责：' +
          (respond.businessReview === '0'
            ? '未审核'
            : respond.businessReview === '1'
            ? '审核通过'
            : '审核不通过');
        setFormState({ tabService: res.data[0] });
      } catch (e) {
        message.error(e.message);
      }
    });
  }, [init]);
  function onUpload() {
    let getData: any = formData.tabService;
    let param = {
      projectNo: getData.projectNo,
      fileName: getData.fileName,
      attachment: getData.attachment,
      legalReview: '0',
      businessReview: '0',
      uploadPerson: getData.uploadPerson,
      notes: getData.notes,
      prePublishDate: getData.prePublishDate,
    };
    postUimodify({
      reqDto: JSON.stringify({ ...param, uiType: pageTypeEnum.video }),
    }).then((res: any) => {
      console.log(res);
      if (res.code === 0) {
        message.success('上传成功！');
        history.push(`/form/videoVertify/upload`);
        setTimeout(() => {
          location.reload();
        }, 100);
      }
    });
  }

  // 获取url中的参数 history模式下
  function formatUrlParams() {
    let url = window.location.href;
    let obj = { projectNo: '' };
    if (url.indexOf('?') !== -1) {
      let startIndex = url.indexOf('?') + 1;
      let str = url.substring(startIndex);
      let strs = str.split('&');
      for (let i = 0; i < strs.length; i++) {
        obj[strs[i].split('=')[0]] = strs[i].split('=')[1];
      }
      return obj;
    }
  }
  if (!init) return '加载中';
  return (
    <div className={'kycForm'}>
      {/* <div>ui审核后台</div> */}
      <FormRender
        propsSchema={formConfig}
        uiSchema={uiSchema}
        formData={formData}
        onChange={setFormState}
        onValidate={setValid}
        widgets={{ UploadFileWrapper }}
      />
      <div className={classNames(styles['bottom'])}>
        <Button type="primary" onClick={onUpload}>
          重新上传
        </Button>
      </div>
    </div>
  );
}
