import React, { useEffect, useState } from 'react';
import { Button, message } from 'antd';
import api from 'api';
import FORM_CONFIG from './form.json';
import FormRender from 'form-render/lib/antd';
import { useHistory } from 'react-router-dom';
import UploadFileWrapper from '../components/UploadFileWrapper';
import { pageTypeEnum } from '../type';

const { postUiPush } = api;
export default function() {
  const [init, setInit] = useState(true);
  const [formData, setFormState] = useState({ tabService: [] });
  const [formConfig, setFormConfig] = useState(FORM_CONFIG.propsSchema);
  const [uiSchema, setUiSchema] = useState({});
  const [valid, setValid] = useState([]);
  let history = useHistory();
  useEffect(() => {}, [init]);
  function onSubmit() {
    let param = formData.tabService;
    postUiPush({
      reqDto: JSON.stringify({ ...param, uiType: pageTypeEnum.video }),
    }).then((res: any) => {
      console.log(res);
      if (res.code === 0) {
        message.success('上传成功！');
        history.push(`/form/videoVertify/upload`);
        setTimeout(() => {
          location.reload();
        }, 100);
      }
    });
  }

  if (!init) return '加载中';
  return (
    <div className={'kycForm'}>
      {/* <div>ui审核后台</div> */}
      <FormRender
        propsSchema={formConfig}
        uiSchema={uiSchema}
        formData={formData}
        onChange={setFormState}
        onValidate={setValid}
        widgets={{ UploadFileWrapper }}
      />
      <div style={{ marginLeft: 60 }}>
        <Button type="primary" onClick={onSubmit}>
          上传
        </Button>
      </div>
    </div>
  );
}
