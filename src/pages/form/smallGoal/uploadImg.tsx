import React, {useEffect, useState} from 'react';
import { Checkbox, Button, message, Popconfirm } from 'antd';
import ImgUpload from '../../frontend/compoment/uploadImg'
interface dataProps {
    name: string,
    onChange: Function,
    value: string
}

export default function (props:dataProps) {

    return <div>
                <ImgUpload 
                        handleChange={(value: any) => props.onChange('img', value)}
                        imageUrl={props.value}
                        // size={['108px*108px']}
                        limit={0.2}
                        isEdit={true}
                        title=''
                    />

            </div>

}