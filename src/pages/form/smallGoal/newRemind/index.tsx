import React, { useState, useEffect } from 'react'
import FormRender from 'form-render/lib/antd'
import FORM_JSON from './form.json'
import api from 'api'
import { Button, Popconfirm, message } from 'antd'

type Iitem = {
    fundCode: string,
    productName: string,
    nextPeriodDate: string,
    tags?: string,
} | []

export default function SmallGoalRemind() {
    const [formData, setFormData] = useState<{reminds:Iitem}>({reminds:[]});
    const [valid, setValid] = useState([]);
    useEffect(() => {
        getData();
    }, []);

    /**
     * @description: 获取上新数据
     */    
    const getData = () => {
        api.fetchSmallGoalRemind().then((res: any)=>{
            if(res && res.code === '0000' && res.data) {
                try {
                    setFormData({
                        reminds:JSON.parse(res.data) || []
                    })
    
                } catch (error) {
                    message.error(error)
                }
            }
        })
    }

    function _setFormData(_formData: any){
        _formData.reminds && (
            _formData.reminds.forEach( (item:any) => {
                item.fundCode = item.fundCode.trim()
            })
        )
        setFormData(_formData)
    }
    /**
     * @description: 保存上新数据
     */    
    const onSubmit = () => {
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }
        try {
            if(formData){
                api.postSmallGoalRemind({ value: JSON.stringify(formData.reminds) }).then((res: any)=>{
                    if(res.code !== '0000'){
                        message.error("发布失败");
                        console.log(res)
                    } else {
                        message.success('发布成功！')
                    }
                })
            }
        } catch (error) {
            message.error(error)
        }

    }
    return (
        <>
            <FormRender
                name="表单配置"
                {...FORM_JSON}
                formData={formData}
                onChange={_setFormData}
                onValidate={setValid}
            />
            <Popconfirm
                placement="rightBottom"
                title={'你确定要提交么'}
                onConfirm={onSubmit}
                okText="确认"
                cancelText="取消"
            >
                <Button type="danger">提交修改</Button>
            </Popconfirm>
        </>

    )
}