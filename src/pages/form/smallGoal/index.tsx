import React, { useState, useEffect } from 'react'
import FormRender from 'form-render/lib/antd'
import FORM_JSON from './form.json'
import axios from 'axios'
import api from 'api'
import { But<PERSON>, Popconfirm, message } from 'antd'
import './uploadImg'
import uploadImg from './uploadImg'

export default function SmallGoal() {
    const [formData, setFormData] = useState<{header:any,body:any}>({header:[],body:[]});
    // const [init, setInit] = useState(false);
    const [valid, setValid] = useState([]);
    useEffect(() => {
        window.copyMe = (list, index) => {
            const item = list[index];
            list.splice(index, 0, item);
            return list;
          };
        window.setTop = (list, index) => {
            const item = list[index];
            list.splice(index, 1);
            list.unshift(item);
            return list;
          };
        getData();
    }, []);
    
    const getData = () => {
        // if (init) return;
        Promise.all([api.fetchSmallGoalHeader(),api.fetchSmallGoalList()]).then((values)=>{
            if(values[0] && values[1]){
                try {
                    setFormData({
                        header:JSON.parse(values[0].data) ||[],
                        body:JSON.parse(values[1].data) ||[]
                    })
    
                } catch (error) {
                    message.error(error)
                }
            }
        })

        // setInit(true)
    }
    /**
     * 跨域jsonp
     */
    
    function getJsonp() {
        
       let _script = document.createElement("script");
        _script.type = "text/javascript";
        _script.src = `http://${window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'test' : ''}fund.10jqka.com.cn/interface/rabbitmq/newyypushlish?key=group`;
        document.body.appendChild(_script);
        _script.onload = function (){
            document.body.removeChild(_script)
        } 
    }
    function _setFormData(_formData:any){
        console.log(_formData)
        _formData.header && (
            _formData.header.forEach( (item:any) => {
                item.fundCode = item.fundCode.trim()
            })
        )
        _formData.body && (
            _formData.body.forEach( (item:any) => {
                item.fundCode = item.fundCode.trim()
            })
        )
        setFormData(_formData)
    }
    const onSubmit = () => {
        if (valid.length > 0) {
            
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }
        try {
            if(formData){
                console.log(formData)
                let allRight = true
                formData.header.map((val) => {
                    let target = formData.body.find((item)=> item.fundCode === val.fundCode)
                    if(!target) {
                        message.error(`${val.fundCode}的是否启用募集限额选项不一致`)
                        allRight = false
                    } else {
                        if(target.useLimit !== val.useLimit) {
                            message.error(`${val.fundCode}的是否启用募集限额选项不一致`)
                            allRight = false
                        }
                        
                    }})
                formData.body.map((val) => {
                    if(val.useTag === 'true' && !val.img) {
                        message.error(`${val.fundCode}的图片未上传`)
                        allRight = false
                    }
                
                })
                if(!allRight) return
                // return
                Promise.all([ api.postSmallGoalHeader({ value: JSON.stringify(formData.header) }),
                    api.postSmallGoalList({ value: JSON.stringify(formData.body) })
                ]).then((res)=>{
                    if(res[0].code !== '0000' || res[1].code !== '0000' ){
                        message.error("发布失败");
                        console.log(res)
                    }
                    else{
                        getJsonp();
                       
                        message.success('发布成功！')
                    }
                })
            }
            
        } catch (error) {
            message.error(error)
        }

    }
    return (
        <>
            <FormRender
                name="表单配置"
                {...FORM_JSON}
                formData={formData}
                onChange={_setFormData}
                onValidate={setValid}
                widgets={{uploadImg: uploadImg}}
            />
            <Popconfirm
                placement="rightBottom"
                title={'你确定要提交么'}
                onConfirm={onSubmit}
                okText="确认"
                cancelText="取消"
            >
                <Button
                    type="danger"
                >
                    提交修改
                        </Button>
            </Popconfirm>
        </>

    )
}