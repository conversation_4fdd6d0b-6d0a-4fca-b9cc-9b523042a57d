{"schema": {"title": "运营位配置", "type": "object", "properties": {"body": {"title": "在售产品列表", "type": "array", "items": {"type": "object", "properties": {"videoUrl": {"title": "视频地址", "pattern": "^http(|s)://.*", "type": "string", "ui:width": "50%"}, "videoImage": {"title": "视频背景图片", "pattern": "^http(|s)://.*", "type": "string", "ui:width": "50%"}}, "required": ["videoUrl", "videoImage"], "minItems": 2, "maxItems": 4}}, "category": {"title": "运营位", "type": "string", "enum": ["banner", "text"], "enumNames": ["banner", "文字链"]}, "bannerImageUrl": {"title": "banner图片地址", "pattern": "^http(|s)://.*", "type": "string", "ui:width": "50%", "ui:hidden": "{{rootValue.category === 'text'}}"}, "bannerJumpUrl": {"title": "banner跳转地址", "pattern": "^http(|s)://.*", "type": "string", "ui:width": "50%", "ui:hidden": "{{rootValue.category === 'text'}}"}, "textContent": {"title": "文字链文案", "type": "string", "ui:width": "50%", "ui:hidden": "{{rootValue.category === 'banner'}}"}, "textJumpUrl": {"title": "文字链跳转地址", "pattern": "^http(|s)://.*", "type": "string", "ui:width": "50%", "ui:hidden": "{{rootValue.category === 'banner'}}"}}}, "displayType": "row", "showDescIcon": true}