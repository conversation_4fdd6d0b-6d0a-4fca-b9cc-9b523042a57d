import React, { useState, useEffect } from 'react'
import FormRender from 'form-render/lib/antd'
import FORM_JSON from './form.json'
import api from 'api'
import { Button, Popconfirm, message } from 'antd'

const {fetchSmallGoalLink, postSmallGoalLink } = api;

export default function LinkConfig() {
    const [formData, setFormData] = useState<any>({});
    const [valid, setValid] = useState([]);
    const [init, setInit] = useState(false);

    useEffect( () => {
        getItem();
    }, [init])

    const getItem = () => {
        fetchSmallGoalLink().then( (res: any) => {
            let _data = FORM_JSON.formData;
            try {
                res = JSON.parse(res.data);
                console.log(res)
                if(res) {
                    _data = res;
                }
            } catch(e) {
                message.error(e.message)
            }
            setInit(true)
            setFormData(_data)
        }).catch( (e: Error) => {
            message.error(e.message)
        })
    }

    const onSubmit = () => {
        if(valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }
        postSmallGoalLink({
            value: JSON.stringify(formData)
        }).then( (res: any) => {
            console.log(res)
            try {
                if(res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('提交成功！')
                }
            } catch(e) {
                message.error(e.message);
            }
        })
    }

    return (
        <section>
            <FormRender 
                propsSchema={FORM_JSON.schema}
                onValidate={setValid}
                formData={formData}
                onChange={setFormData}
                displayType="row"
            />
            <Popconfirm placement="top" title="确认提交吗？" onConfirm={onSubmit}>
                <Button type="danger">提交</Button>
            </Popconfirm>
        </section>
    )
}