import React, { useState, useEffect } from 'react'
import FormRender from 'form-render/lib/antd'
import FORM_JSON from './form.json'
import api from 'api'
import { Button, Popconfirm, message, Radio } from 'antd'
import SelectModel from '@/pages/form/preciseOperation/strategy/detail/selectModel'

// 需要校验的字段
const required = [
    "userActivityId",
    "userPassCount",
    "userPassValidateTime",
    "profitActivityId",
    "profitPassCount",
    "profitPassValidateTime"
]
export default function SmallGoalPass() {
    const [formData, setFormData] = useState({});
    const [valid, setValid] = useState([]);
    const [checkData, setCheckData] = useState({ utype: [], platform: [] });

    useEffect(() => {
        getData();
    }, []);

    /**
     * @description: 获取红包配置数据
     */
    const getData = () => {
        api.fetchSmallGoalPass().then((res: any) => {
            if (res && res.code === '0000' && res.data) {
                try {
                    const _data = JSON.parse(res.data)
                    setFormData(_data || {})
                    const _checkData = {
                        platform: _data.platform,
                        utype: _data.utype
                    }
                    setCheckData(_checkData)
                } catch (error) {
                    message.error(error)
                }
            }
        })
    }

    function _setFormData(_formData: any) {
        setFormData(_formData)
    }
    /**
     * @description: 保存红包配置数据
     */
    const onSubmit = () => {
        let validFlagMsg: string = '';
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }
        if (formData && (formData as any).passCategory === 'system') {
            required.forEach(formItem => {
                if (formData && !(formData as any)[formItem]) {
                    validFlagMsg = formItem;
                    return
                }
            })
        }
        if (validFlagMsg) {
            message.error(`校验未通过字段：${validFlagMsg.toString()}`);
            return;
        }
        if (checkData?.platform.length === 0 || checkData?.utype.length === 0) {
            message.error('请检查必填项');
            return;
        }
        try {
            if (formData) {
                api.postSmallGoalPass({ value: JSON.stringify({ ...formData, ...checkData }) }).then((res: any) => {
                    if (res.code !== '0000') {
                        message.error("发布失败");
                        console.log(res)
                    } else {
                        message.success('发布成功！')
                    }
                })
            }
        } catch (error) {
            message.error(error)
        }

    }
    const handleSelect = (data: any) => {
        setCheckData(data)
    }
    return (
        <>
            <FormRender
                name="表单配置"
                {...FORM_JSON}
                formData={formData}
                onChange={_setFormData}
                onValidate={setValid}
            />
            <SelectModel
                handleChange={(data: any) => { handleSelect(data) }}
                isHead={false}
                isEdit={true}
                data={checkData}
            />
            <Popconfirm
                placement="rightBottom"
                title={'你确定要提交么'}
                onConfirm={onSubmit}
                okText="确认"
                cancelText="取消"
            >
                <Button type="danger">提交修改</Button>
            </Popconfirm>
        </>

    )
}