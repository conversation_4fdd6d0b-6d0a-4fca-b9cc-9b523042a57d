{"schema": {"title": "红包配置", "type": "object", "properties": {"passCategory": {"title": "红包配置", "type": "string", "enum": ["system", "personal"], "enumNames": ["系统红包", "自配置红包"]}, "isNewUser": {"title": "新用户专享红包", "type": "string", "enum": ["1", "0"], "enumNames": ["开", "关"], "ui:width": "20%", "ui:hidden": "{{rootValue.passCategory === 'personal'}}"}, "userActivityId": {"title": "活动id", "type": "string", "ui:width": "30%", "ui:hidden": "{{rootValue.passCategory === 'personal'}}"}, "userPassCount": {"title": "红包金额", "type": "string", "ui:width": "30%", "ui:hidden": "{{rootValue.passCategory === 'personal'}}"}, "userPassValidateTime": {"title": "领取后有效时间", "type": "string", "ui:width": "20%", "ui:hidden": "{{rootValue.passCategory === 'personal'}}"}, "isStopProfit": {"title": "止盈专享红包", "type": "string", "enum": ["1", "0"], "enumNames": ["开", "关"], "ui:width": "20%", "ui:hidden": "{{rootValue.passCategory === 'personal'}}"}, "profitActivityId": {"title": "活动id", "type": "string", "ui:width": "30%", "ui:hidden": "{{rootValue.passCategory === 'personal'}}"}, "profitPassCount": {"title": "红包金额", "type": "string", "ui:width": "30%", "ui:hidden": "{{rootValue.passCategory === 'personal'}}"}, "profitPassValidateTime": {"title": "领取后有效时间", "type": "string", "ui:width": "20%", "ui:hidden": "{{rootValue.passCategory === 'personal'}}"}, "imageUrl": {"title": "弹窗图片地址", "type": "string", "pattern": "^http(|s)://.*", "ui:width": "50%", "ui:hidden": "{{rootValue.passCategory === 'system'}}"}, "jumpUrl": {"title": "跳转地址", "type": "string", "ui:width": "50%", "ui:hidden": "{{rootValue.passCategory === 'system'}}"}}, "required": ["userActivityId", "userPassCount", "userPassValidateTime", "profitActivityId", "profitPassCount", "profitPassValidateTime"]}, "displayType": "row", "showDescIcon": true}