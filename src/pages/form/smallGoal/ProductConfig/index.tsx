import { Button, message, Popconfirm, Select, Spin, Table } from 'antd';
import React, { useState, useEffect, FC, useMemo } from 'react';
import ProductForm from './components/ProductForm';
import api from 'api';
import { DateKeyMap, genIDfromForm, INIT_FORM, PRODUCT_CONFIGKEY, timeSlice } from './const';
import moment from 'moment';

export interface IProps {}
const ProductConfig: FC<IProps> = () => {
  const [loading, setLoading] = useState(false);
  const [iaOptions, setIaOptions] = useState([]);
  const [iaSelect,setIaSelect]=useState();
  
  const [formShow,setFormShow]=useState(false);
  const [formLoading,setFormLoading]=useState(false);
  const [initForm,setInitForm]=useState();
  const [formType,setFormType]=useState<'add'|'edit'>('add')

  const [productData,setProductData]=useState([])
  const [productConfig,setProductConfig]=useState({})
  const [tableLoading,setTableLoading]=useState(false);

  const filterProductData=productData?.filter(item=>{
    if(!iaSelect){
      return true;
    }
    return item.iaCode===iaSelect;
  })
  const iaNameMap=useMemo(()=>{
    const nameMap={};
    iaOptions.forEach(ia=>{
      nameMap[ia.iaCode]=ia.iaName;
    })
    return nameMap
  },[iaOptions])
  const getIaOpts=()=>{
    return api.fetchAllIA().then(res=>{
      try{
        if (res.code !== '0000') {
          throw new Error(res.message);
        }
        setIaOptions(res.data);
      }catch(e){
        message.error(e.message);
      }
      
    })
  }
  const getList=()=>{
    setTableLoading(true);
    return api.fetchHashAll({
      key: PRODUCT_CONFIGKEY,
    }).then(res=>{
      try{
        if (res.code !== '0000') {
          throw new Error(res.message);
        }
        if(!res.data){
          return
        }
        
        const tableData=[];
        for(const key in res.data){
          const jsonStr=res.data[key];
          const jsonValue=JSON.parse(jsonStr)

          for(let jsonKey in jsonValue){
            if(DateKeyMap[jsonKey]){
              const time=jsonValue[jsonKey]
              
              if(time){
                jsonValue[jsonKey]=timeSlice(time)
              }
            }
          }
          tableData.push(jsonValue)
        }
        setProductConfig(res.data)
        setProductData(tableData)
      }catch(e){
        message.error(e.message);
      }
      setTableLoading(false);
    })
  }
  useEffect(() => {
    setLoading(true);
    Promise.all([
      getIaOpts(),
      getList(),
    ]).finally(()=>{
      setLoading(false);
    });
  }, []);
  const handleAdd=()=>{
    setInitForm(INIT_FORM);
    setFormShow(true);
    setFormType('add')
  }
  const handleEdit=(record)=>{
    setInitForm(record);
    setFormShow(true);
    setFormType('edit')
  }
  const handleDel=(record)=>{
    setTableLoading(true);
      api.postHashDel({
        key: PRODUCT_CONFIGKEY,
        propName:genIDfromForm(record)
      }).then(res=>{
        try{
          if (res.code !== '0000') {
            throw new Error(res.message);
          }
          message.success("删除成功")
          getList();
        }catch(e){
          setTableLoading(false);
          message.error(e.message);
        }
       
      })
  }
  const columns = [
    {
      key: 'code',
      dataIndex: 'code',
      title: '产品ID',
      render(_){
        return _||"--"
      }
    },
    {
      key: 'iaCode',
      dataIndex: 'iaCode',
      title: '投顾机构',
      render: (_) => {
        return iaNameMap[_]||"--"
      },
    },
    {
      key: 'name',
      dataIndex: 'name',
      title: '产品名称',
      render(_){
        return _||"--"
      }
    },
    {
      key: 'subscribeStartDate',
      dataIndex: 'subscribeStartDate',
      title: '预约开始日期',
      sorter:(a,b)=>new Date(a.subscribeStartDate)-new Date(b.subscribeStartDate),
      render(_){
        return _||"--"
      }
    },
    {
      key: 'subscribeEndDate',
      dataIndex: 'subscribeEndDate',
      title: '预约结束日期',
      sorter:(a,b)=>new Date(a.subscribeEndDate)-new Date(b.subscribeEndDate),
      render(_){
        return _||"--"
      }
    },
    {
      key: 'operationStartDate',
      dataIndex: 'operationStartDate',
      title: '运作开始日期',
      sorter:(a,b)=>new Date(a.operationStartDate)-new Date(b.operationStartDate),
      render(_){
        return _||"--"
      }
    },
    {
      key: 'cycleMinDate',
      dataIndex: 'cycleMinDate',
      title: '最小运作日期',
      sorter:(a,b)=>new Date(a.cycleMinDate)-new Date(b.cycleMinDate),
      render(_){
        return _||"--"
      }
    },
    {
      key: 'cycleMaxDate',
      dataIndex: 'cycleMaxDate',
      title: '最大运作日期',
      sorter:(a,b)=>new Date(a.cycleMaxDate)-new Date(b.cycleMaxDate),
      render(_){
        return _||"--"
      }
    },
    {
      key: '$modifyTime',
      dataIndex: '$modifyTime',
      title: '修改时间',
      sorter:(a,b)=>new Date(a.$modifyTime)-new Date(b.$modifyTime),
      defaultSortOrder:"descend",
      render(_){
        return _||"--"
      }
    },
    {
      key: '',
      dataIndex: '',
      title: '状态',
      render: (_,record) => {
        const subscribeStartDate=record.subscribeStartDate
        const subscribeEndDate=record.subscribeEndDate;
        const operationStartDate=record.operationStartDate;
        const cycleMaxDate=record.cycleMaxDate;
        const now=moment().format("YYYY-MM-DD")
       
        //未开始
        if(now<subscribeStartDate){
          return '未开始'
        }
        
        //预约中
        if(now>=subscribeStartDate&&now<=subscribeEndDate){
          return '预约中'
        }
        //运作中
        if(now>=operationStartDate&&now<=cycleMaxDate){
          return '运作中'
        }
        //已到期
        if(now>cycleMaxDate){
          return '已到期'
        }
        return '--'
      },
    },
    {
      key: 'opt',
      dataIndex: '',
      title: '操作',
      render: (_,record) => {
        return (
          <>
            <Button style={{ marginRight: '12px' }} onClick={()=>{
              handleEdit(record);
            }}>编辑</Button>
            <Popconfirm title={"请确认是否删除"} onConfirm={()=>{
              handleDel(record)
            }}>
              <Button type={'danger'}>删除</Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];



  const handleSave=async (item)=>{
  
    console.log('handleSave',item)
    const propName=genIDfromForm(item);
    if(formType==='add'&&productConfig[propName]){
      message.error(`${iaNameMap[item.iaCode]}-${item.code}已存在，请检查配置`)
      return;
    }
    setFormLoading(true);
    const submitValue={};
    for(let key in item){
      if(DateKeyMap[key]&&item[key]){
        submitValue[key] = moment(item[key]).format("YYYYMMDD")
      }else{
        submitValue[key] =item[key]
      }
    }
   api.postHash({
        key:PRODUCT_CONFIGKEY,
        propName,
        value:JSON.stringify(submitValue)
    }).then(res=>{
        try{
            if (res.code !== '0000') {
                throw new Error(res.message);
            }
            setFormShow(false);
            message.success("保存成功")
            getList();
        }catch(e){
            message.error(e.message);
        }
        setFormLoading(false);
    })
  }
  return (
    <Spin spinning={loading}>
      <section>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            position: 'relative',
            marginBottom: '12px',
          }}
        >
          <h3>产品列表</h3>

          <div style={{ marginLeft: '44px' }}>
            <span>投顾机构</span>
            <Select allowClear={true} value={iaSelect} style={{ width: '200px', marginLeft: '12px' }} placeholder={'全部'}  onChange={v=>setIaSelect(v)}>
                {iaOptions?.map(item=>{
                    return <Select.Option key={item.iaCode} value={item.iaCode}>{item.iaName}</Select.Option>
                })}
            </Select>
          </div>

          <Button
            style={{
              position: 'absolute',
              right: '0',
              top: '50%',
              transform: 'translate(-50%, -50%)',
            }}
            type="primary"
            onClick={handleAdd}
          >
            新增
          </Button>
        </div>

        <Table  rowKey={(r)=>genIDfromForm(r)}  columns={columns} dataSource={filterProductData} />

        <ProductForm type={formType} iaOptions={iaOptions} loading={formLoading} show={formShow} onClose={()=>{
            !formLoading&&setFormShow(false)
        }} initForm={initForm} onSave={handleSave}/>
      </section>
    </Spin>
  );
};
export default ProductConfig;
