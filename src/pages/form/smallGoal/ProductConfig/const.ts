export const PRODUCT_CONFIGKEY = 'SMALL_GOAL_PRODUCT_CONFIG_FW-4676';

export const INIT_FORM = {
  iaCode: '',
  code: '',
  name: '',
  tradeStatus: '',
  incomeDescribeOfficial: '',
  incomeExplain: '',
  yearIncomeRate: '',
  suggestHoldingDays: '',
  cycleDayOfficial: '',
  cycleDayExplain: '',
  cycleMinDay: '',
  cycleMaxDay: '',
  riskOfficial: '',
  riskLevel: '',
  iniBuyAmountOfficial: '',
  iniBuyAmountPerson: '',
  subscribeStartDate: '',
  subscribeEndDate: '',
  subscribeDateExplain: '',
  operationStartDate: '',
  operationDateExplain: '',
  cycleMinDate: '',
  cycleMinDateExplain: '',
  cycleMaxDate: '',
  cycleMaxDateExplain: '',
  buyAmountBaseNum: '',
  buyPersonBaseNum: '',
  strategyTarget: '',
  strategyFeeExplain: '',
  investmentStrategy: '',
  problemList: [],
  bottomOfficial: '',
};

export const genIDfromForm = record => {
  return `${record.iaCode}_${record.code}`;
};
export const timeSlice = (time: string) => {
  return `${time.slice(0, 4)}-${time.slice(4, 6)}-${time.slice(6, 8)}`;
};
export const DateKeyMap = {
  subscribeStartDate: true,
  subscribeEndDate: true,
  operationStartDate: true,
  cycleMinDate: true,
  cycleMaxDate: true,
};
