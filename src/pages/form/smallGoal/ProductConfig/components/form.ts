export const PropToName = {
  iaCode: '投顾机构',
  code: '产品ID',
  name: '产品名称',
  tradeStatus: '产品状态',
  incomeDescribeOfficial: '收益描述文案',
  incomeExplain: '收益说明',
  yearIncomeRate: '止盈年化收益率',
  // suggestHoldingDays: '建议持有期',
  cycleDayOfficial: '建议持有期描述文案',
  cycleDayExplain: '建议持有期说明',
  cycleMinDay: '最小建议持有期',
  cycleMaxDay: '最大建议持有期',
  riskOfficial: '风险描述文案',
  riskLevel: '风险等级',
  iniBuyAmountOfficial: '起购金额描述文案',
  iniBuyAmountPerson: '个人最低起购金额',
  subscribeStartDate: '预约开始日期',
  subscribeEndDate: '预约结束日期',
  subscribeDateExplain: '预约日期说明',
  operationStartDate: '运作开始日期',
  operationDateExplain: '运作开始日期说明',
  cycleMinDate: '最小运作日期',
  cycleMinDateExplain: '最小运作日期说明',
  cycleMaxDate: '最大运作日期',
  cycleMaxDateExplain: '最大运作日期说明',
  buyAmountBaseNum: '购买金额基数',
  buyPersonBaseNum: '购买人数基数',
  strategyTarget: '产品目标',
  strategyFeeExplain: '产品费率描述',
  investmentStrategy: '投资策略',
  problemList: '常见问题',
  bottomOfficial: '底部描述文案',
  title: '标题',
  content: '内容',

  dbPersonBaseRandomNum: '达标人数随机数',
};
export const PropType = {
  yearIncomeRate: 'number',
  cycleMinDay: 'number',
  cycleMaxDay: 'number',
  iniBuyAmountPerson: 'number',
  buyAmountBaseNum: 'number',
  buyPersonBaseNum: 'number',
  dbPersonBaseRandomNum: 'number',
};
export const Schema = {
  propsSchema: {
    type: 'object',
    properties: {
      iaCode: {
        title: PropToName['iaCode'],
        type: 'string',
        props: {},
        'ui:width': '61%',
      },
      // isCodeValid:{

      // },
      code: {
        title: PropToName['code'],
        type: 'string',
        props: {},

        'ui:width': '45%',
        'ui:widget': 'inputFundCode',
      },
      name: {
        title: PropToName['name'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      tradeStatus: {
        title: PropToName['tradeStatus'],
        type: 'string',
        props: {},
        'ui:width': '61%',
        enum: ['1', '2', '3'],
        enumNames: ['交易', '暂停转入', '暂停转出'],
      },
      incomeDescribeOfficial: {
        title: PropToName['incomeDescribeOfficial'],
        type: 'string',
        props: {},
        'ui:width': '61%',
      },
      incomeExplain: {
        title: PropToName['incomeExplain'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      yearIncomeRate: {
        title: PropToName['yearIncomeRate'],
        type: 'string',
        props: {},

        'ui:width': '45%',
      },
      // suggestHoldingDays: {
      //   title: PropToName['suggestHoldingDays'],
      //   type: 'number',
      //   props: {},
      //   'ui:width': '45%',
      // },
      cycleDayOfficial: {
        title: PropToName['cycleDayOfficial'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      cycleDayExplain: {
        title: PropToName['cycleDayExplain'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      cycleMinDay: {
        title: PropToName['cycleMinDay'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      cycleMaxDay: {
        title: PropToName['cycleMaxDay'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      riskOfficial: {
        title: PropToName['riskOfficial'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      riskLevel: {
        title: PropToName['riskLevel'],
        type: 'string',
        props: {},
        'ui:width': '45%',
        enum: ['1', '2', '3', '4', '5'],
        enumNames: ['低风险', '中低风险', '中风险', '中高风险', '高风险'],
      },
      iniBuyAmountOfficial: {
        title: PropToName['iniBuyAmountOfficial'],
        type: 'string',
        props: {},
        'ui:width': '45%',
        'ui:placeholder': '起购金额（元）',
      },
      iniBuyAmountPerson: {
        title: PropToName['iniBuyAmountPerson'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      subscribeStartDate: {
        title: PropToName['subscribeStartDate'],
        type: 'string',
        format: 'date',
        props: {},
        'ui:width': '45%',
      },
      subscribeEndDate: {
        title: PropToName['subscribeEndDate'],
        type: 'string',
        format: 'date',
        props: {},
        'ui:width': '45%',
      },
      subscribeDateExplain: {
        title: PropToName['subscribeDateExplain'],
        type: 'string',
        props: {},
        format: 'textarea',
      },
      operationStartDate: {
        title: PropToName['operationStartDate'],
        type: 'string',
        format: 'date',
        'ui:width': '45%',
        props: {},
      },
      operationDateExplain: {
        title: PropToName['operationDateExplain'],
        type: 'string',
        props: {},
        format: 'textarea',
      },
      cycleMinDate: {
        title: PropToName['cycleMinDate'],
        type: 'string',
        format: 'date',
        'ui:width': '45%',
        'ui:disabled': true,
        props: {},
      },
      cycleMinDateExplain: {
        title: PropToName['cycleMinDateExplain'],
        type: 'string',
        props: {},
        format: 'textarea',
      },
      cycleMaxDate: {
        title: PropToName['cycleMaxDate'],
        type: 'string',
        'ui:width': '45%',
        'ui:disabled': true,
        format: 'date',
        props: {},
      },
      cycleMaxDateExplain: {
        title: PropToName['cycleMaxDateExplain'],
        type: 'string',
        props: {},
        format: 'textarea',
      },

      buyAmountBaseNum: {
        title: PropToName['buyAmountBaseNum'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      buyPersonBaseNum: {
        title: PropToName['buyPersonBaseNum'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      strategyTarget: {
        title: PropToName['strategyTarget'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      strategyFeeExplain: {
        title: PropToName['strategyFeeExplain'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      investmentStrategy: {
        title: PropToName['investmentStrategy'],
        type: 'string',
        props: {},
        format: 'textarea',
      },
      problemList: {
        title: PropToName['problemList'],
        type: 'array',
        items: {
          type: 'object',
          properties: {
            title: {
              title: PropToName['title'],
              type: 'string',
              'ui:width': '45%',
              format: 'textarea',
            },
            content: {
              title: PropToName['content'],
              type: 'string',
              'ui:width': '45%',
              format: 'textarea',
            },
          },
          required: ['title', 'content'],
        },
        props: {},
      },

      bottomOfficial: {
        title: PropToName['bottomOfficial'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
      dbPersonBaseRandomNum: {
        title: PropToName['dbPersonBaseRandomNum'],
        type: 'string',
        props: {},
        'ui:width': '45%',
      },
    },
    required: [
      'iaCode',

      // 'name',
      'tradeStatus',
      'incomeDescribeOfficial',
      'incomeExplain',
      // 'yearIncomeRate',

      'cycleDayOfficial',
      'cycleDayExplain',
      // 'cycleMinDay',
      // 'cycleMaxDay',
      'riskOfficial',
      'riskLevel',
      'iniBuyAmountOfficial',
      'iniBuyAmountPerson',
      // 'subscribeStartDate',
      // 'subscribeEndDate',
      'subscribeDateExplain',
      // 'operationStartDate',
      'operationDateExplain',
      // 'cycleMinDate',
      'cycleMinDateExplain',
      // 'cycleMaxDate',
      'cycleMaxDateExplain',
      'buyAmountBaseNum',
      'buyPersonBaseNum',
      'strategyTarget',
      'strategyFeeExplain',
      'investmentStrategy',
      'bottomOfficial',
      'dbPersonBaseRandomNum',
    ],
  },
  column: 1,
  labelWidth: 140,
  displayType: 'row',
};
