import { But<PERSON>, Drawer, message, Spin } from 'antd';
import FormRender from 'form-render/lib/antd';
import React, { useState, useEffect, FC, useRef } from 'react';
import {Schema} from './form.ts';
import axios from 'axios';
import api from 'api'
import useDebounceEffect from '../hooks/useDebounceEffect';
import useDebounce from '../hooks/useDebounce';
import { INIT_FORM } from '../const';
import { PropToName, PropType } from './form';
import {apiPrefix} from 'config'
import store from 'store'
import moment from 'moment';
import InputFundCode from './InputFundCode';
import { InputFundCodeContext } from './context';
const urlPrefix=~location.host.indexOf('localhost') ? '/api' : apiPrefix
export interface IProps {
    loading:boolean;
show:boolean;
type:'add'|'edit';
  initForm:any;
  onClose:()=>void;
  onSave:(item:any)=>void;
  iaOptions: { iaCode: string; iaName: string };
}
const ProductForm: FC<IProps> = ({loading,initForm,show,iaOptions,type,onClose,onSave}) => {
  const [innerLoading,setInnerLoading]=useState(false);
  // const [timeLoading,setTimeLoading]=useState(false); 
  const loadingRef=useRef({timeLoading:false}); 
  const [formData, setFormData] = useState(INIT_FORM);
  const [valid, setValid] = useState();
  const formRef=useRef();
  const iaEnums=iaOptions?.map(item=>item.iaCode)||[];
  const iaEnumNames=iaOptions?.map(item=>item.iaName)||[];
  const [isCodeValid,setIsCodeValid]=useState(true);
  useEffect(()=>{
    if(show){
      setIsCodeValid(true);
      setFormData(initForm);
    }
  },[show,initForm])
  const handleSave=async()=>{
    
    console.log("handleSave",formRef.current.resetData)
    if(loadingRef.current.nameLoading){
      message.warn("产品名称数据正在接口请求获取中！~请稍候保存并检查数据是否正常");
      return;
    }
    if(loadingRef.current.timeLoading){
      message.warn("运作日期计算数据正在接口请求获取中！~请稍候保存并检查数据是否正常");
      return;
    }
    setInnerLoading(true);
    try{
        if(valid&&valid.length){
            console.log(valid);
            throw new Error(`请检查字段：${PropToName[valid[0]]}`)
        }
        if(formData.dbPersonBaseRandomNum<=1||formData.dbPersonBaseRandomNum>=2){
          throw new Error(`${PropToName['dbPersonBaseRandomNum']}应在1到2之间`)
        }
        if(String(formData.dbPersonBaseRandomNum).split(".")[1].length>3){
          throw new Error(`${PropToName['dbPersonBaseRandomNum']}最多三位小数`)

        }
        //预约开始日期小于预约结束日期小于运作开始日期
        if(formData.subscribeStartDate&&formData.subscribeEndDate&&formData.subscribeStartDate>=formData.subscribeEndDate){
          throw new Error(`${PropToName['subscribeStartDate']}不得大于等于${PropToName['subscribeEndDate']}`)
          
        }
        if(formData.subscribeEndDate&&formData.operationStartDate&&formData.subscribeEndDate>=formData.operationStartDate){
          
          throw new Error(`${PropToName['subscribeEndDate']}不得大于等于${PropToName['operationStartDate']}`)
        }
        //最小运作周期小于最大运作周期
        if(formData.cycleMinDay&&formData.cycleMaxDay&&Number(formData.cycleMinDay)>=Number(formData.cycleMaxDay)){
          throw new Error(`${PropToName['cycleMinDay']}不得大于等于${PropToName['cycleMaxDay']}`)
        }
        if(formData.cycleMinDate&&formData.cycleMaxDate&&formData.cycleMinDate>=formData.cycleMaxDate){
          throw new Error(`${PropToName['cycleMinDate']}不得大于等于${PropToName['cycleMaxDate']}`)
        }

        for(let key in formData){
          if(PropType[key]==="number"&&isNaN(Number(formData[key]))){
            throw new Error(`${PropToName[key]}必须是合法数字`)
          }
        }
        //　检查产品是否存在
        const productRes=await  api.fetchSmallGoalProductInfo({
          iaCode: formData.iaCode,
          strategyId: formData.code,
        })  
        if (productRes.code !== '0000') {
            throw new Error(productRes.message)
        }
        onSave({...formData,$modifyTime:moment().format("YYYY-MM-DD HH:mm:ss")});
    }catch(e){
        message.error(e.message)
    }finally{
        setInnerLoading(false);
    }
    
    
  }
  const handleClose=()=>{
    if(loading||innerLoading){
        return;
    }
    onClose();
  }
  
  const replaceProductName =  _formData => {
    if (!_formData.code || !_formData.iaCode) {
      return;
    }
    loadingRef.current.nameLoading=true;
  //   api
  //     .fetchSmallGoalProductInfo({
  //       iaCode: _formData.iaCode,
  //       strategyId: _formData.code,
  //     })
  api.fetchSmallGoalProductInfo({
    iaCode: _formData.iaCode,
    strategyId: _formData.code,
  }).then(res => {
         
        try {
          if (res.code !== '0000') {
            throw new Error(res.message);
          }
          setFormData((v)=>({...v, name: res.data.strategyName}));
          setIsCodeValid(true);
          
        } catch (e) {
          message.error(e.message || '产品ID不存在');
          setIsCodeValid(false);
        }
      }).finally(()=>{
        loadingRef.current.nameLoading=false;
      })
  };
  const replaceMinCycleDate = useDebounce(
    _formData => {
      //获取最小运作周期时间
      if (!_formData.operationStartDate || !_formData.cycleMinDay||isNaN(Number(_formData.cycleMinDay))) {
        setFormData((v)=>({...v,cycleMinDate: ""}));;
        return;
      }
      loadingRef.current.timeLoading=true;
    //   api
    //     .fetchCycleDate({
    //       startDate: _formData.operationStartDate,
    //       cycleDay: _formData.cycleMinDay,
    //     })
    axios.get(`${urlPrefix}/ia/config/v1/get_cycle_date`,{
        params:{ startDate: _formData.operationStartDate,
            cycleDay: _formData.cycleMinDay},
                   headers:{
                    Authentication: store.get('user_token'),
                    user:store.get('user')
                   }
    }).then(axiosRes => {
        const res=axiosRes.data
          try {
            if (res.code !== '0000') {
              throw new Error(res.message);
            }
            setFormData((v)=>({...v,cycleMinDate: res.data.cycleDate}));
           
            
          } catch (e) {
            message.error(e.message);
          }
        }).catch(e=>{
          message.error(e.message)
        }).finally(()=>{
          loadingRef.current.timeLoading=false
        })
    },
    500,
    [formData],
  );
  const replaceMaxCycleDate = useDebounce(
    (_formData) => {
      //获取最大运作周期时间
       
       if (!_formData.operationStartDate || !_formData.cycleMaxDay||isNaN(Number(_formData.cycleMaxDay))) {
        setFormData((v)=>({...v,cycleMaxDate: ""}));
        return;
      }
      
      loadingRef.current.timeLoading=true;
    //   api
    //     .fetchCycleDate({
    //       startDate: _formData.operationStartDate,
    //       cycleDay: _formData.cycleMaxDay,
    //     })
    axios.get(`${urlPrefix}/ia/config/v1/get_cycle_date`,{
        params:{ startDate: _formData.operationStartDate,
            cycleDay: _formData.cycleMaxDay},
                   headers:{
                    Authentication: store.get('user_token'),
                    user:store.get('user')
                   }
        }).then(axiosRes => {
            const res=axiosRes.data
          try {
            if (res.code !== '0000') {
              throw new Error(res.message);
            }
            setFormData((v)=>({...v,cycleMaxDate: res.data.cycleDate}));
            
          } catch (e) {
            console.log(e);
            message.error(e.message);
          }
        }).catch(e=>{
          message.error(e.message)
        }).finally(()=>{
          loadingRef.current.timeLoading=false
        }
        )
    },
    300,
    [formData],
  );
  const formJson=Schema;
  formJson.propsSchema.properties.iaCode.enum=iaEnums;
  formJson.propsSchema.properties.iaCode.enumNames=iaEnumNames;
  if(type==='add'){
    formJson.propsSchema.properties.iaCode['ui:disabled']=false;
    formJson.propsSchema.properties.code['ui:disabled']=false;
  }else{
    formJson.propsSchema.properties.iaCode['ui:disabled']=true;
    formJson.propsSchema.properties.code['ui:disabled']=true;
  }

  // const inputFundCodeWidget=(props)=>{
  //   return 
  // }

  return (
    
    <Drawer  onClose={handleClose} bodyStyle={{position:"relative",paddingBottom:80}} width={1200} visible={show} title={"产品信息配置"}>
    <Spin spinning={innerLoading||loading}>
   <InputFundCodeContext.Provider value={
    {isCodeValid,
    onBlur:(e)=>{
      
      console.log("onBlur",e);
      replaceProductName(formData);
    }}
   }>
   
   <div style={{height:"800px",overflow:"auto"}}>
    <FormRender
    ref={formRef}
    name="配置"
    
    {...formJson}
    formData={formData}
    widgets={
     { inputFundCode:InputFundCode}}
    onChange={async v => {
      

      if (v.iaCode !== formData.iaCode) {
         replaceProductName(v);
      }
      if (
        v.operationStartDate !== formData.operationStartDate ||
        v.cycleMinDay !== formData.cycleMinDay
      ) {
         replaceMinCycleDate(v);
      }
      if(
        v.operationStartDate !== formData.operationStartDate ||
        v.cycleMaxDay !== formData.cycleMaxDay
      ){
         replaceMaxCycleDate(v)
      }
      
      for(let key in formData){
        if(PropType[key]==="number"&&typeof v[key]==="string"){
          v[key]=v[key]?.trim()
        }
      }
      setFormData(v);
    }}
    onValidate={setValid}
  /></div>
   </InputFundCodeContext.Provider >
   
    <div style={{width:"1100px",boxSizing:"border-box",position:"absolute", borderTop: '1px solid #e9e9e9',  padding: '10px 16px',display:"flex",justifyContent:"flex-end"}}>
    <Button style={{marginRight:"12px"}}
    onClick={handleClose}
    >取消</Button><Button type="primary" onClick={handleSave}>保存</Button>
    </div>
    </Spin>
    </Drawer>
    
  );
};
export default ProductForm;
