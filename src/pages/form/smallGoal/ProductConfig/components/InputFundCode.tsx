import React, { useContext } from 'react';
import { Input } from 'antd';
import { InputFundCodeContext } from './context';
export default function InputFundCode(props) {
  const { onChange, name, ...rest } = props;

  const errorStyle = {
    borderColor: 'rgb(255, 77, 79)',
    boxShadow: 'rgb(255 77 79 / 20%) 0px 0px 0px 2px',
  };
  const ctx = useContext(InputFundCodeContext);
  const isCodeValid = ctx.isCodeValid;
  const onBlur = ctx.onBlur;
  const getErrorMessage = () => {
    if (!isCodeValid) {
      return '产品ID不存在';
    }
    if (!rest.value) {
      return '不能为空';
    }
    return '';
  };
  const message = getErrorMessage();

  return (
    <span style={{ position: 'relative' }}>
      <Input
        {...rest}
        onChange={e => {
          onChange(name, e.target.value);
        }}
        style={message ? errorStyle : {}}
        onBlur={onBlur}
      ></Input>
      <span style={{ position: 'absolute', left: 0, top: '110%', color: '#f5222d' }}>
        {message}
      </span>
    </span>
  );
}
