import React, { useState, useEffect } from 'react'
import FormRender from 'form-render/lib/antd'
import FORM_JSON from './form.json'
import api from 'api'
import { Button, Popconfirm, message } from 'antd'

export default function SmallGoalList() {
    const [formData, setFormData] = useState<{header:any,body:any}>({header:[],body:[]});
    const [valid, setValid] = useState([]);

    useEffect(() => {
        window.copyMe = (list, index) => {
            const item = list[index];
            list.splice(index, 0, item);
            return list;
        };
        window.setTop = (list, index) => {
            const item = list[index];
            list.splice(index, 1);
            list.unshift(item);
            return list;
        };
        getData();
    }, []);

    const getData = () => {
        api.fetchSmallGoalNewList().then((res:any)=>{
            if(res && res.code === '0000' && res.data) {
                try {
                    let _data = JSON.parse(res.data)
                    console.log('_data', _data)
                    setFormData({
                        header: _data.header || [],
                        body: _data.body || []
                    })
    
                } catch (error) {
                    message.error(error)
                }
            }
        })
    }

    function _setFormData(_formData:any){
        // trim处理
        ['header', 'body'].forEach((propName: string) => {
            _formData[propName] && (
                _formData[propName].forEach( (item:any) => {
                    item.fundCode = item.fundCode.trim()
                    if (propName === 'header' && item.previousData && item.previousData.length > 0) {
                        item.previousData.forEach((v: any) => {
                            v.fundCode = v.fundCode.trim()
                        })
                    }
                })
            )
        })
        setFormData(_formData)
    }

    const onSubmit = () => {
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }
        try {
            if(formData){
                let allRight = true
                formData.header.map((val:any) => {
                    let target = formData.body.find((item:any)=> item.fundCode === val.fundCode)
                    if (target && target.useLimit !== val.useLimit) {
                        message.error(`${val.fundCode}的是否启用募集限额选项不一致`)
                        allRight = false
                    }
                })
                if(!allRight) return
                console.log('formData', formData)
                api.postSmallGoalNewList({ value: JSON.stringify(formData) }).then((res: any)=>{
                    if(res.code !== '0000'){
                        message.error("发布失败");
                        console.log(res)
                    } else {
                        message.success('发布成功！')
                    }
                })
            }
            
        } catch (error) {
            message.error(error)
        }

    }
    return (
        <>
            <FormRender
                name="表单配置"
                {...FORM_JSON}
                formData={formData}
                onChange={_setFormData}
                onValidate={setValid}
            />
            <Popconfirm
                placement="rightBottom"
                title={'你确定要提交么'}
                onConfirm={onSubmit}
                okText="确认"
                cancelText="取消"
            >
                <Button
                    type="danger"
                >
                    提交修改
                        </Button>
            </Popconfirm>
        </>

    )
}