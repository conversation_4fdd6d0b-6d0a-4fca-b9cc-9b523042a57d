{"propsSchema": {"type": "object", "properties": {"header": {"title": "头部推荐位", "type": "array", "items": {"type": "object", "properties": {"fundCode": {"title": "组合代码", "type": "string", "ui:options": {"allowClear": false}}, "tags": {"title": "产品标签", "type": "string", "description": "英文逗号分隔,最多支持三个标签", "pattern": "^[\\w\\u4e00-\\u9fa5]{1,6}(,[\\w\\u4e00-\\u9fa5]{1,6})?(,[\\w\\u4e00-\\u9fa5]{1,6})?$", "ui:options": {}}, "liveStatus": {"title": "直播状态", "type": "string", "enum": ["1", "0"], "enumNames": ["开", "关"]}, "liveLink": {"title": "直播链接", "type": "string", "pattern": "^((https|http)?://)[^\\s]+", "ui:options": {}}, "popoverText": {"title": "按钮气泡", "type": "string", "ui:options": {}}, "useLimit": {"title": "是否限额", "type": "string", "enum": ["true", "false"], "enumNames": ["是", "否"]}, "schemaTxt": {"title": "文字链文案", "type": "string", "ui:options": {}}, "schemaLink": {"title": "文字链链接", "type": "string", "pattern": "^((https|http)?://)[^\\s]+"}, "previousData": {"title": "往期数据", "type": "array", "items": {"type": "object", "properties": {"fundCode": {"title": "组合代码", "type": "string", "ui:readonly": false, "ui:options": {"allowClear": false}, "minLength": null}, "stopProfit": {"title": "止盈年化", "type": "string", "ui:options": {}}}, "required": ["fundCode"]}, "minItems": 1, "maxItems": 5}}, "required": ["fundCode", "liveStatus"]}, "minItems": 1}, "body": {"title": "在售产品列表", "type": "array", "items": {"type": "object", "properties": {"fundCode": {"title": "组合代码", "type": "string", "ui:readonly": false, "ui:options": {"allowClear": false}, "minLength": null}, "tags": {"title": "产品标签", "type": "string", "description": "英文逗号分隔,最多支持三个标签", "pattern": "^[\\w\\u4e00-\\u9fa5]{1,6}(,[\\w\\u4e00-\\u9fa5]{1,6})?(,[\\w\\u4e00-\\u9fa5]{1,6})?$", "ui:options": {}}, "productLink": {"title": "产品卡跳转地址", "type": "string", "pattern": "^((https|http)?://)[^\\s]+"}, "useLimit": {"title": "是否限额", "type": "string", "enum": ["true", "false"], "enumNames": ["是", "否"]}, "schemaTxt": {"title": "文字链文案", "type": "string", "ui:options": {}}, "schemaLink": {"title": "文字链链接", "type": "string", "pattern": "^((https|http)?://)[^\\s]+"}}, "required": ["fundCode", "tags"]}, "ui:options": {"itemButtons": [{"text": "复制", "icon": "copy", "callback": "copyMe"}, {"text": "置顶", "icon": "copy", "callback": "setTop"}]}}}}, "displayType": "row", "showDescIcon": true, "labelWidth": 86, "column": 2}