import api from 'api';
import React, { useEffect, useState } from 'react';
import Upload from './../components/uploadFile';
import { Table, message, Input, Button } from 'antd';

const { fetchFileList, postFileList } = api;

function formatTime(date: Date) {
  let y: number | string = date.getFullYear();
  let m: number | string = date.getMonth() + 1;
  m = m < 10 ? '0' + m : m;
  let d: number | string = date.getDate();
  d = d < 10 ? '0' + d : d;
  let h: number | string = date.getHours();
  h = h < 10 ? '0' + h : h;
  let minute: number | string = date.getMinutes();
  minute = minute < 10 ? '0' + minute : minute;
  let second: number | string = date.getSeconds();
  second = second < 10 ? '0' + second : second;
  return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second;
}

/**
 * 复制
 * @param data
 */
function clipboard(data: string) {
  try {
    let input = document.createElement('input');
    input.value = data;
    input.setAttribute('readonly', 'readonly');
    let dialog = document.createElement('div');
    dialog.style.position = 'fixed';
    dialog.style.top = '0';
    dialog.style.left = '0';
    dialog.style.bottom = '0';
    dialog.style.right = '0';
    document.body.appendChild(dialog);
    dialog.appendChild(input);
    input.focus();
    input.setSelectionRange(0, input.value.length);
    let copyResult = document.execCommand('copy');
    if (copyResult) {
      message.success('复制成功');
    } else {
      message.error('复制失败');
    }
    document.body.removeChild(dialog);
  } catch (e) {
    console.error('copy to clipboard fail' + e);
  }
}

const pageSize = 20;
const columns = [
  {
    title: '上传时间',
    dataIndex: 'time',
    width: 150,
    render: (date: Date) => formatTime(new Date(date)),
  },
  {
    title: '上传者',
    dataIndex: 'editor',
    width: 100,
  },
  {
    title: '大小',
    dataIndex: 'size',
    width: 100,
    render: (size: number) => `${size / 1000}KB`,
  },
  {
    title: '文件名',
    dataIndex: 'name',
    width: 150,
  },
  {
    title: '链接',
    dataIndex: 'url',
    width: 500,
    render: (url: string) => {
      return (
        <div>
          <Button
            type="primary"
            onClick={() => {
              clipboard(url);
            }}
          >
            复制
          </Button>
          <span className="g-ml20">{url}</span>
        </div>
      );
    },
  },
];

const LIST_ERROR = '获取列表失败';
const UPLOAD_ERROR = '上传失败';

export default function() {
  const [data, setData] = useState([]);
  const [showData, setShowData] = useState([]);
  const [text, setText] = useState('');

  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
    filter();
  }, [data]);

  function initData() {
    fetchFileList()
      .then((res: any) => {
        if (res.code !== '0000') return message.error(LIST_ERROR);
        if (!res.data) return;
        setData(JSON.parse(res.data));
      })
      .catch((error: Error) => {
        console.log(error);
        message.error(LIST_ERROR);
      });
  }

  function uploadCallback(name: string, size: number, url: string) {
    if (!name || !url) return message.error(UPLOAD_ERROR);
    _.fundLoading();
    fetchFileList()
      .then((res: any) => {
        if (res.code !== '0000') return message.error(UPLOAD_ERROR);
        let data = JSON.parse(res.data || '[]');
        data.unshift({
          time: new Date(),
          editor: localStorage.name,
          size,
          name,
          url,
        });
        postFileList({
          value: JSON.stringify(data),
        })
          .then((res: any) => {
            _.hideFundLoading();
            if (res.code !== '0000') return message.error(UPLOAD_ERROR);
            initData();
            return message.success('提交成功！');
          })
          .catch((error: Error) => {
            console.log(error);
            _.hideFundLoading();
            message.error(UPLOAD_ERROR);
          });
      })
      .catch((error: Error) => {
        console.log(error);
        _.hideFundLoading();
        message.error(UPLOAD_ERROR);
      });
  }

  function filter() {
    setShowData(
      data.filter(
        (item: any) =>
          ~item.name.indexOf(text) || ~item.url.indexOf(text) || ~item.editor.indexOf(text),
      ),
    );
  }

  return (
    <div>
      <div className="u-l-middle">
        <div style={{ width: 100, marginRight: 150, overflow: 'hidden' }}>
          <Upload text="上传文件" callback={uploadCallback}></Upload>
        </div>
        <Input
          className="g-ml30"
          style={{ width: 300, marginRight: 30 }}
          value={text}
          onChange={e => {
            setText(e.target.value);
          }}
        />
        <Button type="primary" onClick={filter}>
          搜索
        </Button>
      </div>
      <Table columns={columns} dataSource={showData} pagination={{ pageSize }} bordered></Table>
    </div>
  );
}
