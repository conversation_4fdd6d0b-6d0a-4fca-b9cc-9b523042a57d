import React, { useEffect, useState } from 'react';
import ImgUpload from '@/pages/frontend/compoment/uploadImg';
import { Button } from 'antd';
interface dataProps {
  name: string;
  width?:number;
  onChange: Function;
  value: string;
  schema:any
}

export default function(props: dataProps) {
 
  const {value,width="686px",name,schema} = props;
  return (
    <div style={{display:"flex",alignItems:"center"}}>
      <ImgUpload
        handleChange={(value: any) => props.onChange(name, value)}
        imageUrl={value}
        size={[`${width}* 不限高`]}
        limit={0.4}
        isEdit={true}
        isOnlyPng={false}
        isNoCheckHeight={true}
        isNoCheckWidth={true}
    {
      ...schema
    }
    title=""
      />
       {schema.enableDelete && <Button style={{marginLeft:"16px"}} type="danger" onClick={()=>props.onChange(props.name, "")}>删除图片</Button>}
    </div>
  );
}
