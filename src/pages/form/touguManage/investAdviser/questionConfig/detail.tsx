import React, { useState, useEffect, useCallback, useImperativeHandle, forwardRef } from 'react';
// form-render
import FORM_JSON from './form.json';
import FormRender from 'form-render/lib/antd';
// 接口
import api from 'api';
// 组件
import { Button, message, Icon } from 'antd';

import uploadImg from '@/components/uploadImg';

// 时间
import 'moment/locale/zh-cn';
import moment from 'moment';
// webStorage
import store from 'store';
// TypeScript
// 路由
import router from 'umi/router';
const { postHash, fetchHash } = api;

const key = 'touguManage_test';

export default forwardRef(function({ data, handleCancel, handleOk }: any, ref) {
  const [formData, setFormData] = useState(data);
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);
  useImperativeHandle(ref, () => ({
    onCancelClick: onCancel,
    onSubmitClick: onSave,
  }));

  // 保存
  const onSave = async () => {
    // 校验
    setShowValidate(true);
    if (formValid.length > 0) {
      return false;
    }

    handleOk(formData);
  };
  // 取消
  const onCancel = useCallback(() => {
    handleCancel();
  }, []);
  // 处理表单变化
  const handleChange = (data: any) => {

    if (data.choice?.length) {
      for (let i = 0; i < data.choice?.length; i++) {
        const choice = data.choice[i];
        if (!choice.option) {
          choice.option = String.fromCharCode(65 + i); //自动添加option
        }
      }
    }
    setFormData({
      ...formData,
      ...data,
    });
  };
  return (
    <article>
      <FormRender
        propsSchema={FORM_JSON}
        formData={formData}
        onChange={(data: any) => handleChange(data)}
        onValidate={setFormValid}
        showValidate={showValidate}
        displayType="row"
        showDescIcon
        widgets={{ uploadImg: uploadImg }}
      />
    </article>
  );
});
