import React, { useEffect, useState ,useRef} from 'react';
// 接口
import api from 'api';
// 组件
import { Table, Button, message,Modal,Popconfirm } from 'antd';
// 路由
import router from 'umi/router';
import Detail from './detail'
import Axios from 'axios'
import qs from 'qs'
import { getQuestionConfigApi } from '..';

const key = "touguManage_test"
const saveQuestionConfigApi=`https://trade.5ifund.com:8443/rz/ia-front/dubbo/noauth/strategy/kyc-question/v1/save`

const { getQuestionConfig, saveQuestionConfig } = api;
function renderListString(text:string){
  return text ? text : '--'
}
function renderListEnum(arr:any,text:number){

    
      return arr[text];
    
  
}
export default function({taCode}) {

  const columns = [
        
        {
            title:"题目id",
            dataIndex: "id",
            render: renderListString,
        },
        {
            title:"题目",
            dataIndex: "questionTopic",
            render: renderListString,
        }, {
        title: '操作',
        dataIndex: 'options',
        render: (text: unknown, record: any) => {
            function tempRender(
                inner: string,
                fn: (record: any) => void,
                marginRight: number = 0,
                type:
                  | 'default'
                  | 'link'
                  | 'ghost'
                  | 'primary'
                  | 'dashed'
                  | 'danger'
                  | undefined = 'primary',
            ) {
                if (type === 'danger') {
                    return (
                        <Popconfirm
                            title="你确定删除么？"
                            okText="确定"
                            cancelText="取消"
                            onConfirm={() => fn(record)}
                        >
                            <Button type={type} style={{ marginRight: marginRight+"px" }}>
                                {inner}
                            </Button>
                        </Popconfirm>
                    );
                }
                return (
                    <Button
                        type={type}
                        style={{ marginRight: marginRight+"px" }}
                        onClick={() => fn(record)}
                    >
                        {inner}
                    </Button>
                );
            }
            return (
                <section>
         
                    {tempRender('编辑', handleEdit, 20, 'primary')}
                    {tempRender('删除', handleDelete, 0, 'danger')}
                </section>
            );
        },
    }
]
  
  // 表格数据
  const [dataSource, setDataSource] = useState<any[]>([]);
  // 表格加载动画
  const [loadingFlag, setLoadingFlag] = useState<boolean>();
  const [changed, setHasChanged] = useState<boolean>(false);
 const [showModal,setShowModal] = useState(false);
  const [modalProps,setModalProps] = useState();
  const ref = useRef()
  useEffect(() => {
    setLoadingFlag(true)
    searchAllList();
  }, []);
  // 查询活动列表
  const searchAllList = () => {
  
   
  
    Axios.get(getQuestionConfigApi,{params:{ //api中封装的有问题
      iaCode:taCode
    }})
      .then((responce) => {
        const res =responce.data
        
        setLoadingFlag(false);
        if (res.code === '0000') {
          if(!res.data){
              return
            }
            if(!res.data.kycQuestionList?.list){
              return
            }

          setDataSource(res.data.kycQuestionList?.list);
        } else {
          message.error(res.message)
        }
      })
      .catch((err: unknown) => {
        setLoadingFlag(false);
        message.error('网络请求错误，请稍后重试');
      });
        
  };

    // 删除
    const handleDelete = (record: any) => {
      const {id} = record;
      const _dataSource = [...dataSource];
      const itemIndex = _dataSource.findIndex(item => item.id === id);
      _dataSource.splice(itemIndex,1)
      setDataSource(_dataSource)
      setHasChanged(true)
    };
  // 编辑
  const handleEdit = (record: any) => {
    console.log(record)
    setModalProps({
      data:record,
      id: record.id,
    })
    setShowModal(true)

  };
  const handleCancel = () => {
    setShowModal(false)
    // searchAllList()
  };
  const onOk = ()=>{
     setHasChanged(true)
     ref.current &&  ref.current.onSubmitClick()
  } 
  const handleSave=(formData)=>{
    console.log(formData);
    const {id} = formData;
    if(id){
      const _dataSource = [...dataSource];
      const itemIndex = _dataSource.findIndex(item => item.id === id);
      _dataSource[itemIndex] = formData
      setDataSource(_dataSource)
    }
    else{
 
      const _dataSource = [...dataSource];
      formData.id = _dataSource.length+1+"";
      _dataSource.push(formData);
      setDataSource(_dataSource)
    }
    setHasChanged(true)
    setShowModal(false)
  }
  const onSubmitClick=()=>{
    if(!changed){
      message.error("未发生变化，无需保存");
      return;
    }
    saveQuestionConfig({
      iaCode:taCode,
      kycQuestionList:{list:dataSource}
    }).then(res=>{

      if(res&&res.code==="0000"){
        message.success("保存成功");
        setHasChanged(false)
      }
      else{
        message.error("保存失败")
      }
    })
  }
  return (
    <article>
      <section style={{ width: '100%', display: 'flex', }}>
        <Button
          type="primary"
          onClick={() => {
            setShowModal(true);
            setModalProps({})
          }}
          style={{ marginBottom: '20px' }}
        >
          新增
        </Button>
        <Button
          type="primary"
          onClick={() => {
            onSubmitClick()
          }}
          style={{ marginLeft: '20px' }}
        >
          保存
        </Button>
      </section>
      <Table
        columns={columns}
        dataSource={dataSource}
        scroll={{ x: 'max-content' }}
        loading={loadingFlag}
      ></Table>
      {showModal && <Modal visible  onCancel={handleCancel} onOk={onOk} width="60VW">
          <Detail ref={ref}  {...modalProps}  handleCancel={handleCancel} handleOk={handleSave}/>
      </Modal>}
    </article>
  );
}
