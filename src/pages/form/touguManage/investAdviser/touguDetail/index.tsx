import React, { useState, useEffect, useCallback, useImperativeHandle, forwardRef } from 'react';
// form-render
import FORM_JSON from './form.json';
import FormRender from 'form-render/lib/antd';
// 接口
import api from 'api';
// 组件
import { Button, message, Icon, Collapse, Popconfirm } from 'antd';

import uploadImg from '../../uploadImg'

// 时间
// import 'moment/locale/zh-cn';
// import moment from 'moment';
// webStorage
import store from 'store';
// TypeScript
// 路由
import router from 'umi/router';
const { postHash, fetchHash } = api;

const key = "touguManage_tgList"


export default forwardRef(function (props: any, ref) {
  const { id,data, type, handleCancel,handleSubmit ,defaultName} = props
  const [formData, setFormData] = useState(getInitData());
  const [formValid, setFormValid] = useState<string[]>([]);

  const [showValidate, setShowValidate] = useState(false);
  useImperativeHandle(ref, () => ({
    onCancelClick: onCancel,
    onSubmitClick: onSave
  }))
  function getInitData(){
    if(data){
      return data;
    }
    else{
      return {
        baseInfo:{
          alias:defaultName
        }
      }
    }
  }

  
  // 保存
  const onSave = async () => {
    
    // 校验
    
    if (formValid.length > 0) {
      message.error("输入不完整")
      return false;
    }

    // 最后编辑人、最后编辑时间
    // const lastEditor = store.get('name');
    // const updateTime = moment().format('YYYY-MM-DD HH:mm:ss');
    // 默认为新增页
    const params: any = {
      key,
    };
    const obj: any = {
      ...formData,
      // updateTime,
      // lastEditor,
    }
    // 如果是编辑页
    if (id) {
      params.propName = id;
    }
    else {
      params.propName = Date.now()
    }
    handleSubmit(obj)
    return;
    params.value = JSON.stringify(obj);
    postHash(params).then((res: { code: string; message: string }) => {
      if (res.code === "0000") {
        message.success("保存成功")
      }
      else {
        message.success(res.message)
      }
    });
  };
  // 取消
  const onCancel = useCallback(() => {
    handleCancel()
  }, []);
  // 处理表单变化
  const handleChange = (data: any) => {
    setFormData({
      ...formData,
      ...data
    })
  };
  return (
    <article >

      <FormRender
        propsSchema = {FORM_JSON}
       
        formData={formData}
        onChange={handleChange}
        onValidate={setFormValid}
        showValidate={showValidate}
        displayType="row"
        showDescIcon
        widgets={{ uploadImg: uploadImg, collapse: Collapse }}
      />
      {(
        <section style={{ display: "flex", justifyContent: "center", marginTop: "30px" }}>
          <Button onClick={onCancel} style={{ marginRight: "30px" }}>
            <Icon type="cancel" />
            取消
          </Button>
          {/* <Popconfirm
            title="机构信息保存立即生效，提交吗？"
            onConfirm={onSave}
            okText="确定"
            cancelText="取消"
          > */}
            <Button type="primary" onClick={onSave} >
              <Icon type="save" />
              暂存
          </Button>
          {/* </Popconfirm> */}

        </section>
      )}
    </article>
  );
})
