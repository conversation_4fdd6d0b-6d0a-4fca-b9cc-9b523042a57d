import {
    Form,
    Input,
    But<PERSON>,
    Popconfirm,
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { forwardRef } from 'react';
import UploadImg from './uploadImg';

interface baseDrawerProps extends FormComponentProps {
    onEditClose: () => void;
    currentData: any;
    handleData: (data: any) => void;
}
const UploadComponent:any = forwardRef<any, any>((props, _ref) => {
    const { uploadImage, imgUrl } = props;
    return (
        <UploadImg onChange={uploadImage} imgUrl={imgUrl}></UploadImg>
    )
});
class classDrawer extends React.Component<baseDrawerProps, any> {
    constructor(props: baseDrawerProps) {
        super(props);
        const { imgUrl } = props.currentData;
        this.state = {
            imgUrl: imgUrl
        }
    }

    formItemLayout = {
        labelCol: {
            span: 4
        },
        wrapperCol: {
            span: 19
        },
    };
    uploadImage = (val: string) => {
        const { setFieldsValue } = this.props.form;
        this.setState({
            imgUrl: val
        })
        setFieldsValue({
            imgUrl: val
        })
    };
    handleSubmit = (e: any) => { 
        e.preventDefault();
        const { currentData } = this.props;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                values = { 
                    ...currentData,
                    ...values,
                };
                console.log(values);
                this.props.handleData(values);
                this.props.onEditClose();
            }
        });
    };
    render() {
        const { getFieldDecorator } = this.props.form;
        const { imgUrl } = this.state;
        const { onEditClose } = this.props;
        return (
            <>
                <Form {...this.formItemLayout}>
                    <Form.Item label="TA名称：" wrapperCol={{span: 6}}>
                        <span>{this.props.currentData?.taName}</span>
                    </Form.Item>
                    <Form.Item label="卡片标题：">
                        {getFieldDecorator('cardTitle', {
                            initialValue: this.props.currentData?.cardTitle,
                            rules: [{ required: true, message: '请输入卡片标题' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="卡片副标题：">
                        {getFieldDecorator('cardSubTitle', {
                            initialValue: this.props.currentData?.cardSubTitle,
                            rules: [{ required: true, message: '请输入卡片副标题' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="卡片链接：">
                        {getFieldDecorator('cardUrl', {
                            initialValue: this.props.currentData?.cardUrl,
                            rules: [{ required: true, message: '请输入卡片链接' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="卡片配图：" wrapperCol={{span: 6}}>
                        {getFieldDecorator('imgUrl', {
                            initialValue: this.props.currentData?.imgUrl,
                            rules: [{ required: true, message: '请上传卡片配图' }],
                        })(
                            <UploadComponent uploadImage={this.uploadImage} imgUrl={imgUrl}/>
                        )}
                    </Form.Item>
                    <Form.Item style={{textAlign: 'left'}}>
                        <Button style={{marginRight: 20, marginLeft: 200}} onClick={onEditClose}>
                            取消
                        </Button>
                        <Button type="primary">
                                暂存
                            </Button>
                        <Popconfirm placement="left" title="确定要提交吗?" onConfirm={this.handleSubmit}>
                           
                        </Popconfirm>
                    </Form.Item>
                </Form>
            </>
        )
    }
}
const WrappedBaseDrawer = Form.create<baseDrawerProps>({ name: 'classDrawer' })(classDrawer);
export default WrappedBaseDrawer