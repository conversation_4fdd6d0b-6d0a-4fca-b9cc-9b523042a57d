import React, { useState, useEffect, useCallback } from 'react';
// form-render
import FORM_JSON from './form.json';
import FormRender from 'form-render/lib/antd';
// 接口
import api from 'api';
// 组件
import { Button, message, Icon, Popconfirm } from 'antd';

import uploadImg from '@/components/uploadImg';
import styles from './index.less'
import Axios from 'axios';
// 时间

import moment from 'moment';
// webStorage
import store from 'store';
import { getQuestionConfigApi } from '..';
// TypeScript
// 路由

const { postHash, fetchHash, fetchHashAll, postLazyCatAllConfig } = api;

const key = 'touguManage_strategyConfig';

export default function({ taCode, pageData }: any) {
  console.log('pageData', pageData);

  const [formData, setFormData] = useState();
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);
  const [formJSON, setFormJSON] = useState(getAllStrategy());
  useEffect(() => {
    // 是否是编辑页
    searchAllQuestion();
    getAllData();
  }, []);
  useEffect(() => {
    console.log('formJson', formJSON);
  }, [formJSON]);
  function getAllStrategy() {
    let dataSource = pageData;

    const _formJSON = JSON.parse(JSON.stringify(FORM_JSON));
    const formConfig = _formJSON.properties.strategyList.items.properties;
    // 策略类型和策略名称对应项
    formConfig['strategy'] = {
      title: '策略',

      type: 'array',
      items: {
        type: 'string',
      },
      enum: dataSource.map((item: any) => item.key),
      enumNames: dataSource.map((item: any) => item.cardName),
      'ui:widget': 'multiSelect',
    };
    return _formJSON;
  }
  const getAllData = function() {
    let body = {
      key,
      propName: taCode,
    };
    fetchHash(body)
      .then((res: { code: string; message: string; data: string }) => {
        if (res.code === '0000') {
          if (!res.data) {
            return;
          }

          const formData = JSON.parse(res.data);

          setFormData(formData);
        } else {
          message.error(res.message);
        }
      })
      .catch((err: unknown) => {
        message.error('网络请求错误，请稍后重试');
      });
  };
  const searchAllQuestion = () => {
    Axios.get(getQuestionConfigApi, {
      params: {
        //api中封装的有问题
        iaCode: taCode,
      },
    })
      .then(responce => {
        const res = responce.data;

        if (res.code === '0000') {
          if (!res.data || !res.data?.kycQuestionList?.list) {
            message.error('题目数据为空，请到问卷配置中添加');
            return;
          }
          const list = res.data.kycQuestionList.list;
          const _formJSON = JSON.parse(JSON.stringify(formJSON));
          for (const item of list) {
            const formConfig = _formJSON.properties.strategyList.items.properties;
            const questionConfig = getQuestionConfig(item);
            formConfig[questionConfig.questionId] = questionConfig;
          }
          setFormJSON(_formJSON);
        } else {
          message.error(res.message);
        }
      })
      .catch((err: unknown) => {
        message.error('查询列表错误');
        console.log('查询列表错误', err);
      });
  };

  function getQuestionConfig(item: any) {
    const { questionTopic, choice, id } = item;
    const options = choice?.map(item => item.content);
    const optionValues = choice?.map(item => item.option);

    return {
      title: id,
      type: 'array',
      questionId: id,
      description: questionTopic,
      items: {
        type: 'string',
      },
      
      enumNames: options,
      enum: optionValues,
      widget: 'multiSelect',
    };
  }
  // 保存
  const onSave = async () => {
    const _formData = formData;
    // 校验
    setShowValidate(true);
    if (formValid.length > 0) {
      message.error(formValid[0]);
      return false;
    }

    // 最后编辑人、最后编辑时间
    const lastEditor = store.get('name');
    const updateTime = moment().format('YYYY-MM-DD HH:mm:ss');
    // 默认为新增页
    const params: any = {
      key,
      propName: taCode,
    };
    const obj: any = {
      ..._formData,
      // updateTime,
      // lastEditor,
    };
    // 如果是编辑页
    params.value = JSON.stringify(obj);
    postHash(params).then((res: { code: string; message: string }) => {
      if (res.code === '0000') {
        message.success('保存成功');
      } else {
        message.success(res.message);
      }
    });
  };

  // 处理表单变化
  const handleChange = (data: any) => {
    setFormData({
      ...formData,
      ...data,
    });
  };
  return (
    <article className={styles.container}>
      <section className={styles.body}>
        <FormRender
          propsSchema={formJSON}
          formData={formData}
          onChange={(data: any) => handleChange(data)}
          onValidate={setFormValid}
          showValidate={showValidate}
          displayType="row"
          showDescIcon
          widgets={{ uploadImg: uploadImg }}
        />
      </section>

      {
        <section className={styles.footer} >
          <Popconfirm
            title="机构KYC信息保存立即生效，提交吗？"
            onConfirm={onSave}
            okText="确定"
            cancelText="取消"
          >
            <Button type="primary">
              <Icon type="save" />
              保存
            </Button>
          </Popconfirm>
        </section>
      }
    </article>
  );
}
