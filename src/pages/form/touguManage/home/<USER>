import React, { useState, useEffect, useCallback, useRef } from 'react';
// form-render
import FORM_JSON from './form.json';
import FormRender  from 'form-render/lib/antd';

// 接口
import api from 'api';
// 组件
import { Button, message, Icon } from 'antd';

import uploadImg from '../uploadImg'

// 时间

import moment from 'moment';
// webStorage
import store from 'store';
// TypeScript
// 路由

// const FORM_JSON = 

const { postHash,fetchHash ,postLazyCatAllConfig,postLazyfundcompanyTactics} = api;

const key = "touguManage_home"

const propName = "mainData"

export default function() {
  const [formData, setFormData] = useState();
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);
  const [typeData,setTypeData] = useState([])
  const [strategyTypes,setStrategyTypes] = useState()
  const [formJSON,setFormJSON] = useState(FORM_JSON);

  // useEffect(()=>{
  //   postLazyCatAllConfig({
  //     type: 'query'
  // }).then((res:any) => {
  //   if(!res){
  //     message.error("获取策略数据失败")
  //     return
  //   }
  //   if( res.code ==="0000"){
  //     let dataSource = res.data;
  //     // 策略类型和策略名称对应项
  //     setTypeData(dataSource);
      
  //   }
  //    else{
  //      message.error(res.message)
  //    }
  //   })
  //   init()
  // },[])
  useEffect(()=>{
    postLazyfundcompanyTactics().then((res:any) => {
    if(!res){
      message.error("获取策略数据失败")
      return
    }
    if( res.code ==="0000"){
      let dataSource = res.data;
      // 策略类型和策略名称对应项
      setTypeData(dataSource);
      
    }
     else{
       message.error(res.message)
     }
    })
    init()
  },[])
  useEffect(()=>{
    if(typeData && typeData.length){
      const names = [] //typeData.map(item =>item.typeName);
      const values = []// typeData.map(item => item.typeId);
      const strategyTypes = {};
      for(var i = 0 ;i<typeData.length;i++){
        const name = typeData[i].taName;
        const value = typeData[i].taCode;
        names.push(name);
        values.push(value)
        const {consultTacticsList=[]} = typeData[i] 
        strategyTypes[value] = {
          names:consultTacticsList.map(item => item.investConsultTacticsName ).join(),
          values:consultTacticsList.map(item => item.consultTacticsId ).join(),
        }
      }
      const _formJSON  = {
        ...formJSON
      }
      setStrategyTypes(strategyTypes)
    
      _formJSON.properties.productList.items.properties.taCode.enum=values;
      _formJSON.properties.productList.items.properties.taCode.enumNames=names;
       _formJSON.properties.productList.items.properties.strategy.enum="{{rootValue.productValues?rootValue.productValues.split(','):[]}}";
      _formJSON.properties.productList.items.properties.strategy.enumNames="{{rootValue.productNames?rootValue.productNames.split(','):[]}}";
      setFormJSON(_formJSON);
      
    }
    
  },[typeData]);
  function init(){
    let body = {
      key,
      propName
    };
    fetchHash(body)
      .then((res: { code: string; message: string; data: string }) => {
        if (res.code === '0000') {
          if(!res.data){
              return
            }
          const formData = JSON.parse(res.data)
          setFormData(formData);
        } else {
          message.error(res.message)
        }
      })
      .catch((err: unknown) => {
     
        message.error('网络请求错误，请稍后重试');
      });
  }
  
  // 保存
  const onSave = async () => {
    const _formData = formData;
    // 校验
    setShowValidate(true);
    if (formValid.length > 0) {
      return false;
    }

    // 最后编辑人、最后编辑时间
    const lastEditor = store.get('name');
    const updateTime = moment().format('YYYY-MM-DD HH:mm:ss');
    // 默认为新增页
    const params:any = {
      key,
      propName
    };
    // const productList =JSON.parse(JSON.stringify(_formData.productList)) ;
    //  productList.forEach(item => {
    //    delete item.productValues
    //    delete item.productNames
    // })
    const obj=_formData
    
   


    // 如果是编辑页
    params.value = JSON.stringify(obj);
    postHash(params).then((res: { code: string; message: string })=>{
      if(res.code ==="0000"){
        message.success("保存成功")
      }
      else{
        message.success(res.message)
      }
    });
  };

  // 处理表单变化
  const handleChange = (data: any) => {
    console.log(data)
    const {productList=[]} = data ;
    for(var i = 0;i<productList.length;i++){
      productList[i].productNames = strategyTypes[productList[i].taCode].names;
      productList[i].productValues = strategyTypes[productList[i].taCode].values;
      if( productList[i].productValues.indexOf(productList[i].strategy)<0){
        productList[i].strategy = null
      }
    }
    
    setFormData({
      ...formData,
      ...data
    })
  };

  return (
    <article  >
         
       <section style={{ display: 'flex', marginBottom: '20px', alignItems: 'center' }}>
    
        <Button
          type="link"
          onClick={() => {
            location.hash = '/form/touguManage/investAdviser';
          }}
        >
          投顾产品配置
        </Button>
        <Button
          style={{ marginLeft: '120px' }}
          type="link"
          onClick={() => {
            location.hash = '/form/lazyCat/strategyType';
          }}
        >
          策略类型配置
        </Button>
      </section>
      <h2 style={{lineHeight:"66px"}}>投顾管家首页配置</h2>
      <FormRender
        propsSchema={formJSON}
        formData={formData }
        onChange={(data: any) => handleChange(data)}
        onValidate={setFormValid}
        showValidate={showValidate}
        displayType="row"
        showDescIcon
        
        widgets={{ uploadImg: uploadImg }}
      />
     { (
        <section style={{display:"flex",justifyContent:"center",marginTop:"30px"}}>
          <Button onClick={onSave} type="primary" >
            <Icon type="save" />
            保存
          </Button>
        </section>
      )}
    </article>
  );
}
