import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Popconfirm, Modal, Row, Col, Input, Spin } from 'antd';
import api from 'api';
import InputTitle from '@/pages/form/findChanceV2/components/InputTitle';
import { toast } from '@/utils/message';
const { TextArea } = Input;
const { postHash, fetchHashAll, getFundName, postHashDel } = api;

interface convertConfigRespProps {
  fundCode: string;
  fundName: string;
  holdingConfigMessage: string;
  redeemConfigMessage: string;
  buyConfigMessage: string;
  timestamp?: Number
}

const yieldConversionConfigKey = 'yieldConversionConfig'
function walletCurrencyWeightSetting() {
  const [yieldList, setYieldList] = useState<convertConfigRespProps[]>([])
  const [toAddMap, setAddMapp] = useState<convertConfigRespProps>({ fundCode: '', fundName: "", holdingConfigMessage: '', redeemConfigMessage: '', buyConfigMessage: '' })
  const [modalShow, setModalShow] = useState(false)
  const [pageLoading, setPageLoading] = useState(false); // 页面loading
  const [modifyState, setModifyState] = useState(false);

  useEffect(() => { setPageLoading(true); getYieldList() }, [])
  useEffect(() => {
    if (toAddMap.fundCode.length == 6) {
      getFundName({ tradeCodeList: [toAddMap.fundCode], typeList: ['simpleName', 'fullname'] }).then((res: any) => {
        setAddMapp({ ...toAddMap, fundName: res.data[toAddMap.fundCode].fullname })
      })
    } else {
      setAddMapp({ ...toAddMap, fundName: '' })
    }
  }, [toAddMap.fundCode])


  const initYieldList = () => {
    setAddMapp({ fundCode: '', fundName: "", holdingConfigMessage: '', redeemConfigMessage: '', buyConfigMessage: '', timestamp:0 })
  }
  // get
  const getYieldList = async () => {
    setPageLoading(true)
    try {
      const res = await fetchHashAll({ key: yieldConversionConfigKey })
      if (res.code !== "0000") {
        throw new Error(res.message)
      }
      let arr = []
      for (let key in res.data) {
        arr.push(JSON.parse(res.data[key]))
      }

      arr.sort((a,b)=>b.timestamp-a.timestamp)
      setYieldList(arr)
      setPageLoading(false)
    } catch (e: any) {
      setPageLoading(false)
      toast.error(e.message || "未知异常")
    }
  }

  // delete
  const deleteRecord = (yieldItem: convertConfigRespProps) => {
    postHashDel({ key: yieldConversionConfigKey, propName: yieldItem.fundCode }).then((result: any) => {
      if (result.code === '0000') {
        toast.success('删除成功')
        getYieldList()
      } else {
        toast.error(`删除失败${result.message}`)
      }
    }).catch((err: object) => {
      toast.error(`删除失败，系统异常`)
    });
  }

  // add & post
  const handleAddMap = async () => {
    if (!toAddMap.redeemConfigMessage || !toAddMap.holdingConfigMessage || !toAddMap.buyConfigMessage) {
      toast.error('请校验是否填写文案')
      return
    }
    if (!modifyState) {
      if (!toAddMap.fundCode || !toAddMap.fundName) {
        toast.error('请校验是否输入正确的基金代码')
        return
      }
      const fundArray = yieldList.map(e => { return e.fundCode })
      if (fundArray.indexOf(toAddMap.fundCode) > -1) {
        toast.error('原基金已经出现在转移列表中！')
        return
      }
      let item = Object.assign(toAddMap,{timestamp:(new Date()).getTime()})
      await setAddMapp(item)
    }

    await postHash({ key: yieldConversionConfigKey, propName: toAddMap.fundCode, value: JSON.stringify(toAddMap) }).then((res: any) => {
      if (res.code == '0000') {
        if (!modifyState){
          toast.success('添加成功')
        }else{
          toast.success('修改成功')
        }
        getYieldList()
      } else {
        if (!modifyState){
          toast.error(`添加失败${res.message}`);
        }else{
          toast.error(`修改失败${res.message}`);
        }
        
      }
    }).catch((err: any) => {
      if (!modifyState){
        toast.error(`添加失败，系统异常`);
      }else{
        toast.error(`修改失败，系统异常`);
      }
      return;
    });
    setModalShow(false)
  }

  const addFund = () => {
    initYieldList()
    setModalShow(true)
    setModifyState(false)
  }

  const ModifyText = (yieldItem: convertConfigRespProps) => {
    setModalShow(true)
    setModifyState(true)
    setAddMapp(yieldItem)
  }


  return (
    <div>
      <Spin spinning={pageLoading}>
        <h1 className="g-fs28 f-bold">收益宝迁移引导配置</h1>
        <section>  <Button type="primary" style={{ margin: '20px 0px 20px' }} onClick={() => addFund()}>添加</Button></section>
        <Row gutter={[16, 16]}>
          <Col span={2} ><h3>基金代码</h3></Col>
          <Col span={4} ><h3>基金名称</h3></Col>
          <Col span={4} ><h4>持仓界面提示文案</h4></Col>
          <Col span={4}><h4>赎回界面勾选文案</h4></Col>
          <Col span={4}><h4>申购界面提示文案</h4></Col>
          <Col span={4} />
        </Row>
        {yieldList?.map((yieldItem, index) => {
          return (<Row gutter={[16, 16]} key={yieldItem?.fundCode + '+' + index}>
            <Col span={2} ><span className=''>{yieldItem?.fundCode}</span></Col>
            <Col span={4} ><span>{yieldItem?.fundName}</span></Col>
            <Col span={4} ><span>{yieldItem?.holdingConfigMessage}</span></Col>
            <Col span={4}><span>{yieldItem?.redeemConfigMessage}</span></Col>
            <Col span={4}><span>{yieldItem?.buyConfigMessage}</span></Col>
            <Col span={4}>
              <Button type='primary' onClick={() => { ModifyText(yieldItem) }} style={{ display: 'inline-block', marginRight: '20px' }}>修改</Button>

              <Popconfirm
                title="确定删除?"
                onConfirm={() => { deleteRecord(yieldItem) }}
                okText="是"
                cancelText="否"
                style={{ display: 'inline-block' }}
              >
                <Button type='danger'>删除</Button>
              </Popconfirm>

            </Col>
          </Row>)
        })}

        <Modal title={'添加货币转移设置配置'} visible={modalShow} onOk={handleAddMap} onCancel={() => { setModalShow(false); setModifyState(false) }}>
          <Row gutter={[12, 16]}>
            <Input addonBefore={<InputTitle title={'基金代码'} />}
              disabled={modifyState}
              value={toAddMap.fundCode}
              onChange={(e) => { e.persist(); setAddMapp({ ...toAddMap, fundCode: e.target.value }); }}></Input>
          </Row>
          <Row gutter={[12, 16]} style={{ marginTop: '20px' }}>
            <Input disabled addonBefore={<InputTitle title={'基金名称'} />}
              value={toAddMap.fundName}
              onChange={(e) => { e.persist(); }}></Input>
          </Row>
          <Row gutter={[12, 16]} style={{ marginTop: '20px' }}>
            <InputTitle title={'持仓界面提示文案'} />
            <TextArea
              value={toAddMap.holdingConfigMessage}
              onChange={(e) => { e.persist(); setAddMapp({ ...toAddMap, holdingConfigMessage: e.target.value }) }}></TextArea>
          </Row>
          <Row gutter={[12, 16]} style={{ marginTop: '20px' }}>
            <InputTitle title={'赎回界面勾选文案'} />
            <TextArea
              value={toAddMap.redeemConfigMessage}
              onChange={(e) => { e.persist(); setAddMapp({ ...toAddMap, redeemConfigMessage: e.target.value }) }}></TextArea>
          </Row>
          <Row gutter={[12, 16]} style={{ marginTop: '20px' }}>
            <InputTitle title={'申购界面提示文案'} />
            <TextArea
              value={toAddMap.buyConfigMessage}
              onChange={(e) => { e.persist(); setAddMapp({ ...toAddMap, buyConfigMessage: e.target.value }) }}></TextArea>
          </Row>
          <h4 style={{ marginTop: '20px', }} >文案格式为：
            <br /> XXXXXX
            <span style={{ color: "red" }}>^T^XXXXX$$</span>XXXXXX， ^T^ 和 $$ 中间文案为跳转链接</h4>
        </Modal>
      </Spin>
    </div >

  )
}

export default React.memo(walletCurrencyWeightSetting);