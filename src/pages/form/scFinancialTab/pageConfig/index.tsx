import React, { useEffect, useState } from 'react';
import { Checkbox, Button, message, Row, Col, Popconfirm } from 'antd';
import styles from './index.less';
import api from 'api';
import { DraggableArea } from '@/components/DragTags/index.js';
const {fetchSearchhotword, fetchMixedbanner, fetchscNotice, fetchscGongge, fetchscGonggeAll, fetchscOperationPosition, fetchscUserComment, fetchscCoupon,
    postscFilterId, fetchscOperationblock, fetchscBiggiftbag, fetchscProductcard, fetchscOperationcard, fetchscThreesteps, fetchscMultidimension, fetchscFeaturedList, fetchscFinancialtab, postscFinancialtab} = api;
interface iFloorData {
    type: string,
    lastEditor: string | null,
    lastEditTime: string,
    content: string,
    index: number,
    name: string,
    config?: any,
    iconTypeSet?: string[],
    title?: any,
    id?: number,
}
const typeExchange = {
    'HotSearch': 'searchhotword',
    'OperationPosition': 'mixbanner',
    'Notice': 'notice',
    'Icon': 'gongge',
    'AllIcon': 'gonggeall',
    'Holder': 'operationposition',
    'UserComment': 'usercomment',
    'Packet': 'coupon',
    'Block': 'operationblock',
    'customize-BigGift': 'biggiftbag',
    'OperationCard': 'operationCard',
    'customize-NewAccount': 'threesteps',
    'customize-ProductCard': 'productCard',
    'ChooseFund': 'multidimension',
    'SpecialList': 'featuredList',
}
//index是列表数序
const init1: iFloorData[] = [
    {type: "HotSearch", index: 0, name: '搜索热词', lastEditor: null, lastEditTime: '', content:''},
    {type: "OperationPosition", name: '融合运营位', index: 1, lastEditor: null, lastEditTime: '', content:''},
    {type: "Icon", index: 2, name: '宫格', lastEditor: null, lastEditTime: '', content:''},
    {type: "AllIcon", index: 3, name: '宫格-全部页面', lastEditor: null, lastEditTime: '', content:''},
    {type: "Notice", index: 4, name: '公告', lastEditor: null, lastEditTime: '', content:''},
    {type: "Holder", index: 5, name: '持仓运营位', lastEditor: null, lastEditTime: '', content:''},
    {type: "UserComment", index: 999, name: '用户精评', lastEditor: null, lastEditTime: '', content:''},
]
//index默认列表顺序，会按照接口返回重新排序，id是拖动排序时的索引
const init2: iFloorData[] = [
    {type: "Packet", index: 7, name: '营销利益点-优惠券', lastEditor: null, lastEditTime: '', content:'', id: 7},
    {type: "Block", index: 8, name: '营销利益点-运营豆腐块', lastEditor: null, lastEditTime: '', content:'', id: 8},
    {type: "customize-BigGift", index: 9, name: '营销利益点-开户/首购大礼包', lastEditor: null, lastEditTime: '', content:'', id: 9},
    {type: "OperationCard", index: 10, name: '营销利益点-运营大卡',lastEditor: null, lastEditTime: '', content:'', id: 10},
    {type: "customize-NewAccount", index: 11, name: '营销利益点-新人三步走', lastEditor: null, lastEditTime: '', content:'', id: 11},
    {type: "customize-ProductCard", index: 12, name: '基金推荐-产品大卡', lastEditor: null, lastEditTime: '', content:'', id: 12},
    {type: "ChooseFund", index: 13, name: '基金推荐-多维度选基', lastEditor: null, lastEditTime: '', content:'', id: 13},
    {type: "SpecialList", index: 14, name: '基金推荐-特色榜单', lastEditor: null, lastEditTime: '', content:'', id: 14}
]


export default function () {
    const [initData1, setData1] = useState(init1);
    const [initData2, setData2] = useState(init2);
    const [init, setInit] = useState(false);
    const [filterIdStr, setFilterIdStr] = useState('');

    useEffect(() => {
        fetchData();
    },[])
    function fetchData() {
        let promise0 = fetchscFinancialtab();
        let promise1 = fetchSearchhotword();
        let promise2 = fetchMixedbanner();
        let promise3 = fetchscNotice();
        let promise4 = fetchscGongge();
        let promise5 = fetchscGonggeAll();
        let promise6 = fetchscOperationPosition();
        let promise7 = fetchscUserComment();
        let promise8 = fetchscCoupon();
        let promise9 = fetchscOperationblock();
        let promise10 = fetchscBiggiftbag();
        let promise11 = fetchscProductcard();
        let promise12 = fetchscOperationcard();
        let promise13 = fetchscThreesteps();
        let promise14 = fetchscMultidimension();
        let promise15 = fetchscFeaturedList();
        let promiseArr = [promise0, promise1, promise2, promise3, promise4, promise5, promise6, promise7, promise8, promise9,
            promise10, promise11, promise12, promise13, promise14, promise15];
        Promise.all(promiseArr).then((res: any) => {
            
            let res0 = res[0];
            let _initData1 = [...initData1];
            let _initData2 = [...initData2];
            if (res0?.code === '0000') {
                let data = res0?.data && JSON.parse(res0.data);
                console.log(data);
                if (data) {
                    Object.keys(data)?.forEach((val: any)=> {
                        let _target1 = _initData1.find((item) => item.type === val)
                        if (_target1) {
                            _target1.lastEditor = data[val]?.lastEditor;
                            _target1.lastEditTime = data[val]?.lastEditTime;
                            _target1.content = data[val]?.content;
                        } else {
                            let _target2 = _initData2.find((item) => item.type === val)
                            if (_target2) {
                                _target2.lastEditor = data[val]?.lastEditor;
                                _target2.lastEditTime = data[val]?.lastEditTime;
                                _target2.content = data[val]?.content;
                                _target2.index = data[val]?.index;
                            }
                        }
                    })
                    console.log(_initData2)
                  _initData2.sort((a,b)=> a.index - b.index)
                }
               
            } else {
                message.error(res0?.message || '系统繁忙');
            }
            let str = '';
            for (let i = 1; i <= 15; i++) {
                let result = res[i];
                if (result?.code === '0000') {
                    let data = (result?.data && JSON.parse(result.data)) ?? {};
                    console.log(data);
                    let _target1 = _initData1.find((item) => item.type === data.type);
                    if (_target1) {
                        let content = '', config: any = [];
                        _target1.lastEditor = data.lastEditor;
                        _target1.lastEditTime = data.lastEditTime;
                        if (data.type === 'HotSearch') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { searchWord, jumpAction, version, startTime, endTime, id } = item;
                                content += `${searchWord}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    word: searchWord,
                                    url: jumpAction,
                                    version,
                                    startTime,
                                    endTime,
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'OperationPosition') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { mixName, mixType, navColorLeft, navColorRight, imageUrl, jumpAction, version, startTime, endTime,
                                masterTitle, masterColor, subTitle, subColor, buttonText, buttonColor, buttonTextColor, id } = item;
                                content += `${mixName}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj: any = {
                                    index,
                                    name: mixName,
                                    pageType: mixType,
                                    barColor: navColorRight ? [navColorLeft, navColorRight] : [navColorLeft],
                                    page: imageUrl,
                                    actUrl: jumpAction,
                                    version,
                                    startTime,
                                    endTime,
                                    mainTitle: masterTitle,
                                    mainTitleColor: masterColor,
                                    secTitle: subTitle,
                                    secTitleColor: subColor,
                                    buttonText: buttonText,
                                    buttonColor: buttonColor,
                                    buttonTextColor: buttonTextColor,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'Notice') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { noticeTitle, jumpAction, version, startTime, endTime, id } = item;
                                content += `${noticeTitle}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    title: noticeTitle,
                                    url: jumpAction,
                                    version,
                                    startTime,
                                    endTime,
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'Icon') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { gonggeName, page, darkPage, jumpAction, version, angleMark, startTime, endTime, id } = item;
                                content += `${gonggeName}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    name: gonggeName,
                                    page,
                                    darkPage,
                                    url: jumpAction,
                                    version,
                                    mark: angleMark,
                                    startTime,
                                    endTime,
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'AllIcon') {
                            _target1.iconTypeSet = data.gonggeClassify ?? [];
                            data.confs?.forEach((item: any, index: number) => {
                                const { gonggeType, gonggeName, page, darkPage, jumpAction, version, angleMark, startTime, endTime, id } = item;
                                content += `${gonggeName}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    iconType: gonggeType,
                                    name: gonggeName,
                                    page,
                                    darkPage,
                                    url: jumpAction,
                                    version,
                                    mark: angleMark,
                                    startTime,
                                    endTime,
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'Holder') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { textTitle, jumpAction, version, startTime, endTime, id } = item;
                                content += `${textTitle}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    title: textTitle,
                                    url: jumpAction,
                                    version,
                                    startTime,
                                    endTime,
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        } else if (data.type === 'UserComment') {
                            data.confs?.forEach((item: any, index: number) => {
                                const { userCommentName, commentList, id } = item;
                                content += `${userCommentName}${index === data.confs.length - 1 ? '' : ','}`;
                                let obj = {
                                    name: userCommentName,
                                    commentList: commentList ?? [],
                                    index,
                                    filterId: id
                                }
                                config.push(obj);
                            })
                            _target1.content = content;
                            _target1.config = config;
                        }
                    } else {
                        let _target2 = _initData2.find((item) => item.type === data.type)
                        if (_target2) {
                            let content = '', config: any = [];
                            _target2.lastEditor = data.lastEditor;
                            _target2.lastEditTime = data.lastEditTime;
                            if (data.type === 'Packet') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const { couponName, imageUrl, couponText, couponId, achieveText, seeText, jumpAction, version, startTime, endTime, id } = item;
                                    content += `${couponName}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        name: couponName,
                                        page: imageUrl,
                                        content: couponText,
                                        packetId: couponId,
                                        receiveButton: achieveText,
                                        watchButton: seeText,
                                        watchUrl: jumpAction,
                                        version,
                                        startTime,
                                        endTime,
                                        index,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'Block') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const { name, blocks, startTime, endTime, id } = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        name,
                                        blocks: blocks ?? [],
                                        startTime,
                                        endTime,
                                        index,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'customize-BigGift') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const { name, page, title, noneReceiveButton, noneAccountButton, noneBuyButton, noneBuyUrl, version,
                                        gifts, startTime, endTime, id } = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        name,
                                        page,
                                        title,
                                        noneReceiveButton,
                                        noneAccountButton,
                                        noneBuyButton,
                                        noneBuyUrl,
                                        version,
                                        gifts: gifts ?? [],
                                        startTime,
                                        endTime,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'OperationCard') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const { name, page, darkPage, url, version, startTime, endTime, id } = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        name,
                                        page,
                                        darkPage,
                                        content: item.content,
                                        url,
                                        version,
                                        startTime,
                                        endTime,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'customize-NewAccount') {
                                data.confs?.forEach((item: any, index: number) => {
                                    const { name, page, title, steps, url, finishButton, version, startTime, endTime, id } = item;
                                    content += `${name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        name,
                                        page,
                                        title,
                                        steps: steps ?? [],
                                        finishButton,
                                        url,
                                        version,
                                        startTime,
                                        endTime,
                                        filterId: id
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'customize-ProductCard') {
                                _target2.title = data.title;
                                data.confs?.forEach((item: any, index: number) => {
                                    content += `${item.name}${index === data.confs.length - 1 ? '' : ','}`;
                                    let fundManagerId = '';
                                    if (item.type === '1') {
                                        for(let i = 0, len = item.managerInfo?.length; i < len; i++) {
                                            if (item.managerInfo[i]?.name === item.fundManagerName) {
                                                fundManagerId = item.managerInfo[i]?.id;
                                                break;
                                            }
                                        }
                                    }
                                    
                                    let obj = {
                                        type: item.type,
                                        name: item.name,
                                        fundCode: item.fundCode,
                                        fundName: item.fundName,
                                        fundManagerName: item.fundManagerName,
                                        fundManagerId: fundManagerId,
                                        fundManagerTag: item.fundManagerTag,
                                        reason: item.reason,
                                        show: item.show ?? [],
                                        title: item.title,
                                        secTitle: item.secTitle,
                                        fundManagerPage: item.fundManagerPage,
                                        fundTag: item.fundTag,
                                        profitSection: item.profitSection,
                                        buyStartTime: item.buyStartTime,
                                        buyEndTime: item.buyEndTime,
                                        targetData: item.targetData ?? [],
                                        buyCount: item.buyCount,
                                        recommendType: item.recommendType,
                                        button: item.button,
                                        watchUrl: item.watchUrl,
                                        version: item.version,
                                        startTime: item.startTime,
                                        endTime: item.endTime,
                                        filterId: item.id,
                                        index
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'ChooseFund') {
                                data.confs?.forEach((item: any, index: number) => {
                                    content += `${item.title}${index === data.confs.length - 1 ? '' : ','}`;
                                    let arr: any = [];
                                    let obj = {
                                        tabType: item.tabType,
                                        title: item.title,
                                        secTitle: item.secTitle,
                                        url: item.url,
                                        version: item.version,
                                        recommendType: item.recommendType,
                                        fundConfigList: [],
                                        startTime: item.startTime,
                                        endTime: item.endTime,
                                        filterId: item.id,
                                        index
                                    }
                                    item.fundConfigList?.forEach((list: any) => {
                                        const { profitSection, profitSectionTwo, ...other } = list;
                                        let value = {...other};
                                        if (item.tabType !== 'mainTrack') {
                                            value.range = profitSection;
                                        } else {
                                            value.rangeRateList = [
                                                {range: profitSection},
                                                {range: profitSectionTwo}
                                            ]
                                        }
                                        arr.push(value);
                                    })
                                    obj.fundConfigList = arr;
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            } else if (data.type === 'SpecialList') {
                                data.confs?.forEach((item: any, index: number) => {
                                    content += `${item.title}${index === data.confs.length - 1 ? '' : ','}`;
                                    let obj = {
                                        title: item.title,
                                        secTitle: item.secTitle,
                                        moreTitle: item.moreTitle,
                                        url: item.url,
                                        version: item.version,
                                        lists: item.lists ?? [],
                                        startTime: item.startTime,
                                        endTime: item.endTime,
                                        filterId: item.id,
                                        index
                                    }
                                    config.push(obj);
                                })
                                _target2.content = content;
                                _target2.config = config;
                            }
                        }
                    }
                    if (data?.confs?.length > 0) {
                        data.confs.forEach((item: any) => {
                            if (item?.id) {
                                str += item.id + ','
                            }
                        })
                    }
                } else {
                    message.error(res0?.message || '系统繁忙');
                }
            }
            if (str.length > 0 && str[str.length - 1] === ',') {
                str = str.substr(0, str.length-1);
            }
            setFilterIdStr(str);
            setData1(_initData1)
            setData2(_initData2)
            setInit(true)
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }
    function handleSubmit(flag: boolean){ 
        let _data = [...initData1, ...initData2];
        if (flag) {
            _data.sort((a, b) =>  (a.lastEditTime || b.lastEditTime) ? (+new Date(b.lastEditTime)  - (+new Date(a.lastEditTime))) : 1)
            if(_data[0].lastEditor === localStorage.name){
                message.error('发布者和最后一个编辑楼层的最后编辑人不能为同一人')
                return
            }
        }
        let _sendData: any = {};
        initData1.forEach((item: any) => {
            const { type, ...other } = item;
            _sendData[type] = {
                ...other,
                type: '固定楼层'
            }
        })
        initData2.forEach((item: any, index: number) => {
            const { type, ...other } = item;
            _sendData[type] = {
                ...other,
                type: '可变楼层',
                index: index + 7
            }
        })
        console.log(_sendData);
        postscFinancialtab({
            value: JSON.stringify(_sendData),
        }).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.message);
            } else {
                message.success('保存并发布成功！');
            }
        }).catch((e: Error) => {
            message.error(e?.message);
        })
        postscFilterId({
            value: JSON.stringify({
                filterIds: filterIdStr
            }),
        }).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.message);
            }
        }).catch((e: Error) => {
            message.error(e?.message);
        })
    } 
    function jumpUrl(tag: iFloorData){
        let hash = typeExchange[tag.type];
        location.href = `#/form/scFinancialTab/pageConfig/${hash}`
    }
    function handleFloor(dataFloor: iFloorData[]) {
        console.log(123, dataFloor);
        setData2(dataFloor);
    } 
    if (!init) return '加载中'
    return <div className={styles['m-drag']}>
            <div className={'g-mb20'}>
                <span className={'g-ml20'}>
                    <Popconfirm
                        title="确定保存并发布？"
                        onConfirm={() => handleSubmit(true)}
                        okText="确定"
                        cancelText="取消"
                    >
                        <Button type="primary">保存并发布</Button>
                    </Popconfirm>
                    
                </span>
                {/* <span className={'g-ml20'}>
                    <Popconfirm
                        title="确定保存并发布？"
                        onConfirm={() => handleSubmit(false)}
                        okText="确定"
                        cancelText="取消"
                    >
                        <Button type="primary">保存并发布(测试环境使用，无人员校验)</Button>
                    </Popconfirm>
                </span> */}
            </div>
            <div  className={styles['m-head']}>
                <Row className={styles['m-row']}>
                    <Col span={4}>楼层样式</Col>
                    <Col span={2}>楼层类型</Col>
                    <Col span={10}>楼层内容</Col>
                    <Col span={3}>最后编辑人</Col>
                    <Col span={3}>最后编辑时间</Col>
                    <Col span={2}>操作</Col>
                </Row> 
                
            </div>   
            {initData1.map((tag,index) => {
                return <div className={styles['tag']} key={index}>
                    <Row className={styles['m-row-top']}>
                        <Col span={4}>{tag.name}</Col>
                        <Col span={2}>固定楼层</Col>
                        <Col span={10} className={styles['tag-content']}>{tag.content}</Col>
                        <Col span={3}>{tag.lastEditor}</Col>
                        <Col span={3}>{tag.lastEditTime}</Col>
                        <Col span={2}>
                            <Button onClick={()=> jumpUrl(tag)}>编辑</Button>
                        </Col>
                    </Row>
                </div>
            })}            
            <DraggableArea
                isList
                tags={initData2}
                render={({tag}: {tag: iFloorData}) => (
                    <div className={styles["tag"]}>
                        <Row className={styles['m-row']}>
                            <Col span={4}>{tag.name}</Col>
                            <Col span={2}>可变楼层</Col>
                            <Col span={10} className={styles['tag-content']}>{tag.content}</Col>
                            <Col span={3}>{tag.lastEditor}</Col>
                            <Col span={3}>{tag.lastEditTime}</Col>
                            <Col span={2}>
                                <Button  onClick={()=> jumpUrl(tag)}>编辑</Button>
                            </Col>
                        </Row>
                    </div>
                )}
                onChange={(data: iFloorData[]) => {
                    handleFloor(data)
                }}
            />
            </div>

}