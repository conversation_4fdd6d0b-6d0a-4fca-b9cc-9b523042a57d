{"schema": {"type": "object", "properties": {"tabType": {"title": "tab类型", "type": "string", "enum": ["mainTrack", "hotSale", "goodFund", "planTrade", "selfDefine"], "enumNames": ["主题赛道", "销量排行", "严选好基", "热门定投", "自定义榜单"], "ui:width": "51%", "ui:widget": "select"}, "title": {"title": "tab标题", "type": "string", "ui:width": "60%", "ui:options": {}}, "secTitle": {"title": "tab副标题", "type": "string", "ui:width": "60%", "ui:options": {}}, "url": {"title": "跳转链接", "type": "string", "ui:width": "60%", "ui:options": {}}, "version": {"title": "跳转链接版本控制", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "recommendType": {"title": "是否由系统推荐", "type": "string", "default": "1", "enum": ["1", "2"], "enumNames": ["系统推荐", "人工配置"], "ui:disabled": "{{!['selfDefine', 'goodFund', 'mainTrack'].includes(rootValue.tabType)}}", "ui:width": "41%", "ui:widget": "select"}, "fundConfigList": {"title": "", "type": "array", "ui:hidden": "{{rootValue.tabType === 'hotSale' || rootValue.tabType === 'planTrade' || rootValue.recommendType === '1'}}", "ui:widget": "fundConfigList"}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "60%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "60%"}}, "required": ["tabType", "title", "secTitle", "recommendType"]}, "displayType": "row", "showDescIcon": true, "labelWidth": 140}