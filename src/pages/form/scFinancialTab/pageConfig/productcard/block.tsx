import React from 'react';
import { FormComponentProps } from 'antd/es/form';
import styles from '../index.less';
import { Form, Input, Select, Checkbox, DatePicker, Button, Popconfirm, message, InputNumber } from 'antd';
import UploadImg from '../../components/wrapperUploadImg'; 
import classNames from 'classnames';
import moment from 'moment';
import api from 'api';
import { timeFormat2 } from '@/utils/utils';

const { fetchTargetByCode, fetchFundNameByCode } = api;

const { Option } = Select;

const formItemLayout = {
  labelCol: {
    span: 3,
  },
  wrapperCol: {
    span: 21
  }
};
interface BlockFormProps extends FormComponentProps {
    blockData: any,
    handleUpdate: (data: any) => void,
    handleFocus: (data: any) => void,
    isEdit: boolean
}

interface iState {
  fundManagerPage: string,
  targetData: any[]
}

class Block extends React.Component<BlockFormProps, iState> {
    constructor(props: BlockFormProps) {
      super(props);
      this.state = {
        fundManagerPage: props.blockData?.fundManagerPage ?? '',
        targetData: props.blockData?.targetData ?? []
      }
    }
    handleImage = (val: string) => {
      const { blockData, handleUpdate } = this.props;
      this.setState({
        fundManagerPage: val
      })
      handleUpdate({
        ...blockData,
        fundManagerPage: val
      });
    }
    handleDeleteTarget = (index: number) => {
        const { blockData, handleUpdate } = this.props;
        let data = [...this.state.targetData];
        data.splice(index, 1);
        this.setState({
            targetData: data
        })
        handleUpdate({
            ...blockData,
            targetData: data
        });
    }
    handleInput = (e: any, index: number, type: 'name'|'profit') => {
        const { blockData, handleUpdate } = this.props;
        let data = [...this.state.targetData];
        let obj = data[index] ?? {
            name: '',
            profit: ''
        }
        if (type === 'name') {
            obj[type] = e.target.value;
        } else {
            obj[type] = String(e);
        }
        
        data[index] = obj;
        this.setState({
            targetData: data
        })
        handleUpdate({
            ...blockData,
            targetData: data
        });
    }
    addTarget = () => {
        const { blockData, handleUpdate } = this.props;
        let data = [...this.state.targetData];
        data.push({
            name: '',
            profit: ''
        })
        this.setState({
            targetData: data
        } )
        handleUpdate({
            ...blockData,
            targetData: data
        });
    }
    handleCode = (code: string) => {
        const { handleUpdate, blockData, handleFocus } = this.props;
        const { setFieldsValue } = this.props.form;
        handleFocus(true);
        if (code === "") return;
        code = code.trim();
        if (blockData?.type === '3') {
            fetchTargetByCode({}, code).then((data: any) => {
                if (data.status_code === 0) {
                    data = data.data;
                    if (data) {
                        let { vc_groupname, vc_startdate, vc_enddate } = data;
                        vc_startdate = vc_startdate?.length === 8 ? `${vc_startdate.slice(0, 4)}/${vc_startdate.slice(4, 6)}/${vc_startdate.slice(6, 8)}`: vc_startdate;
                        vc_enddate = vc_enddate?.length === 8 ? `${vc_enddate.slice(0, 4)}/${vc_enddate.slice(4, 6)}/${vc_enddate.slice(6, 8)}`: vc_startdate;
                        
                        vc_startdate = vc_startdate ? timeFormat2(vc_startdate) : vc_startdate;
                        vc_enddate = vc_enddate ? timeFormat2(vc_enddate) : vc_enddate;
                        handleUpdate({
                            ...blockData,
                            fundName: vc_groupname,
                            buyStartTime: vc_startdate,
                            buyEndTime: vc_enddate
                        });
                        setFieldsValue({
                            fundName: vc_groupname,
                            buyStartTime: vc_startdate ? moment(vc_startdate) : null,
                            buyEndTime: vc_enddate ? moment(vc_enddate) : null
                        })
                    } else {
                        handleUpdate({
                            ...blockData,
                            fundCode: ''
                        });
                        setFieldsValue({
                            fundCode: ''
                        })
                        message.error('该基金不为小目标产品，请重新输入')
                    }
                } else {
                    handleUpdate({
                        ...blockData,
                        fundCode: ''
                    });
                    setFieldsValue({
                        fundCode: ''
                    })
                    message.error('请求基金详情接口失败，请稍后重试')
                }
            }).catch((e: Error) => {
                handleUpdate({
                    ...blockData,
                    fundCode: ''
                });
                setFieldsValue({
                    fundCode: ''
                });
                message.error(e?.message || '系统繁忙');
            })
        } else {
            fetchFundNameByCode({
                fundCode: code
            }).then((data: any) => {
                if (data.code === '0000') {
                    data = data.data;
                    if (data) {
                        let { name, ifnew, type, zdsg, managerInfo, levelOfRisk, rgstart, rgend } = data;
                        let arr: any = [];
                        managerInfo?.forEach((item: any) => {
                            let obj = {
                                name: item.name,
                                id: item.id
                            }
                            arr.push(obj);
                        })
                        if ((blockData?.type === '1' && ifnew === 1) || (['2', '5'].includes(blockData?.type) && ifnew === 0)) {
                            let tag = `${type ? type : ''},${levelOfRisk ? levelOfRisk : ''},${zdsg}元起购`;
                            if (blockData.type === '1') {
                                rgstart = rgstart ? rgstart.replace(/-/g, '/') : rgstart;
                                rgend = rgend ? rgend.replace(/-/g, '/') : rgend;
                                rgstart = rgstart ? timeFormat2(rgstart) : rgstart;
                                rgend = rgend ? timeFormat2(rgend) : rgend;
                                handleUpdate({
                                    ...blockData,
                                    fundName: name,
                                    fundManagerName: arr.length > 0 ? arr[0]?.name : '',
                                    fundTag: tag,
                                    buyStartTime: rgstart,
                                    buyEndTime: rgend,
                                    managerInfo: arr
                                });
                                setFieldsValue({
                                    fundName: name,
                                    fundManagerName: arr.length > 0 ? arr[0]?.name : '',
                                    fundTag: tag,
                                    buyStartTime: rgstart ? moment(rgstart) : null,
                                    buyEndTime: rgend ? moment(rgend) : null,
                                })
                            } else if (blockData.type === '2') {
                                handleUpdate({
                                    ...blockData,
                                    fundName: name,
                                    fundTag: tag
                                });
                                setFieldsValue({
                                    fundName: name,
                                    fundTag: tag,
                                })
                            } else if (blockData.type === '5') {
                                handleUpdate({
                                    ...blockData,
                                    fundName: name,
                                });
                                setFieldsValue({
                                    fundName: name,
                                })
                            }
                            
                        } else {
                            handleUpdate({
                                ...blockData,
                                fundCode: ''
                            });
                            setFieldsValue({
                                fundCode: ''
                            })
                            let msg = '';
                            switch(blockData.type){
                                case '1':
                                    msg = '该基金不为新发基金，请重新输入';
                                    break;
                                case '2':
                                case '5':
                                    msg = '该基金不为持营基金，请重新输入';
                                    break;
                            }
                            msg && message.error(msg)
                        }
                    } else {
                        handleUpdate({
                            ...blockData,
                            fundCode: ''
                        });
                        setFieldsValue({
                            fundCode: ''
                        })
                        let msg = '';
                        switch(blockData.type){
                            case '1':
                                msg = '该基金不为新发基金，请重新输入';
                                break;
                            case '2':
                            case '5':
                                msg = '该基金不为持营基金，请重新输入';
                                break;
                        }
                        msg && message.error(msg)
                    }
                } else {
                    handleUpdate({
                        ...blockData,
                        fundCode: ''
                    });
                    setFieldsValue({
                        fundCode: ''
                    })
                    message.error('请求基金详情接口失败，请稍后重试')
                }
            }).catch((e: Error) => {
                handleUpdate({
                    ...blockData,
                    fundCode: ''
                });
                setFieldsValue({
                    fundCode: ''
                });
                message.error(e?.message || '系统繁忙');
            })
        }
    }
    handleCodeFocus = () => {
        this.props.handleFocus(false);
    }
    handleManager = () => {
        const { setFieldsValue } = this.props.form;
        setFieldsValue({
            fundCode: ''
        })
    }
    static getDerivedStateFromProps(nextProps: any, prevState: any) {
      console.log(1111, nextProps.blockData?.targetData, prevState.targetData, JSON.stringify(nextProps.blockData?.targetData) !== JSON.stringify(prevState.targetData))
      
      if ((nextProps.blockData?.fundManagerPage !== prevState.fundManagerPage) || 
        JSON.stringify(nextProps.blockData?.targetData) !== JSON.stringify(prevState.targetData)) {
        return {
          fundManagerPage: nextProps.blockData?.fundManagerPage ?? '',
          targetData: nextProps.blockData?.targetData ?? [],
        }; 
      }
      return null;
  }
    render() {
      const { fundManagerPage, targetData } = this.state;
      const { getFieldDecorator } = this.props.form;
      const { blockData, isEdit } = this.props;
      const { type, name, fundCode, fundName, fundManagerName, fundManagerTag, reason, button, buyCount, buyEndTime, buyStartTime,
        endTime, fundTag, startTime, version, watchUrl, show, title, secTitle, profitSection, managerInfo } = blockData;
        console.log(fundCode, fundName);
      return (
        <div className={classNames(styles['m-card'])}>
          <Form {...formItemLayout}>
            <Form.Item label="产品大卡类型" wrapperCol={{span: 12}}>
                {getFieldDecorator('type', {
                    initialValue: type,
                    rules: [{ required: true, message: '请选择产品大卡类型' }],
                })(
                  <Select style={{width: 200}} disabled={!isEdit} onChange={this.handleManager}>
                    <Option value="1">新发基金</Option>
                    <Option value="2">持营基金</Option>
                    <Option value="3">小目标</Option>
                    <Option value="4">自选股选基</Option>
                    <Option value="5">板块推基</Option>
                </Select>
                )}
            </Form.Item>
            <Form.Item label="卡片名称" wrapperCol={{span: 12}}>
                {getFieldDecorator('name', {
                    initialValue: name,
                    rules: [{ required: true, message: '请填写卡片名称' }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            {
                ['1', '2', '3', '5'].includes(type) && (
                    <>
                    <Form.Item label={type === '3' ? '小目标代码' : '基金代码'} wrapperCol={{span: 12}}>
                        {getFieldDecorator('fundCode', {
                            initialValue: fundCode,
                            rules: [{ required: true, message: `请填写${ type==='3' ? '小目标' : '基金'}代码` }],
                        })(
                            <Input disabled={!isEdit} onFocus={this.handleCodeFocus} onBlur={(e) => this.handleCode(e.target.value)}/>
                        )}
                        { type==='3' && <span className={styles['m-card-required']} s-cr="#f5222d">配置tacode,code，中间用英文逗号隔开</span> }
                    </Form.Item>
                    <Form.Item label={type === '3' ? '小目标名称' : '基金名称'} wrapperCol={{span: 12}}>
                        {getFieldDecorator('fundName', {
                            initialValue: fundName,
                            rules: [{ required: true, message: `请填写${ type==='3' ? '小目标' : '基金'}名称` }],
                        })(
                            <Input disabled={true}/>
                        )}
                    </Form.Item>
                    </>
                )
            }
            { type === '1' && (
                <>
                <Form.Item label="基金经理" wrapperCol={{span: 12}}>
                    {getFieldDecorator('fundManagerName', {
                        initialValue: fundManagerName,
                        rules: [{ required: true, message: '请选择基金经理' }],
                    })(
                    <Select style={{width: 200}} disabled={!isEdit}>
                        { managerInfo?.map((item: any) => {
                            return (
                                <Option value={item.name} key={item.name}>{item.name}</Option>
                            )
                        }) }
                    </Select>
                    )}
                </Form.Item>
                <Form.Item label="基金经理标签" wrapperCol={{span: 12}}>
                    {getFieldDecorator('fundManagerTag', {
                        initialValue: fundManagerTag,
                        rules: [{ required: true, message: '请填写基金经理标签' }],
                    })(
                        <Input disabled={!isEdit}/>
                    )}
                </Form.Item>
                <Form.Item label="推荐理由" wrapperCol={{span: 12}}>
                    {getFieldDecorator('reason', {
                        initialValue: reason,
                        rules: [{ required: true, message: '请填写推荐理由' }],
                    })(
                        <Input disabled={!isEdit}/>
                    )}
                </Form.Item>
                <Form.Item label="展示指标维度" wrapperCol={{span: 12}}>
                    {getFieldDecorator('show', {
                        initialValue: show ?? [],
                        rules: [{ required: true, message: '请选择展示指标维度' }],
                    })(
                    <Checkbox.Group style={{ width: '100%' }} disabled={!isEdit}>
                        <Checkbox value="0">年化回报</Checkbox>
                        <Checkbox value="1">最大盈利</Checkbox>
                        <Checkbox value="2">管理规模</Checkbox>
                        <Checkbox value="3">擅长投资</Checkbox>
                        <Checkbox value="4">任职年限</Checkbox>
                    </Checkbox.Group>
                    )}
                </Form.Item>
                <Form.Item label="基金经理图片" required={true}>
                    <UploadImg onChange={this.handleImage} imgUrl={fundManagerPage} disabled={!isEdit} size={['126*126']}></UploadImg>
                </Form.Item>
                </>
            ) }
            {  
                ['2', '3', '5'].includes(type) && (
                    <Form.Item label="主标题文案" wrapperCol={{span: 12}}>
                        {getFieldDecorator('title', {
                            initialValue: title,
                            rules: [{ required: true, message: '请填写主标题文案' }],
                        })(
                            <Input disabled={!isEdit}/>
                        )}
                    </Form.Item>
                )
            }
            { type === '5' && (
                <>
                    <Form.Item label="副标题文案" wrapperCol={{span: 12}}>
                        {getFieldDecorator('secTitle', {
                            initialValue: secTitle,
                            rules: [{ required: true, message: '请填写副标题文案' }],
                        })(
                            <Input disabled={!isEdit}/>
                        )}
                    </Form.Item>
                    <Form.Item label="主标题标签">
                        {getFieldDecorator('fundTag', {
                            initialValue: fundTag,
                        })(
                            <Input disabled={!isEdit} style={{width: 200, marginRight: 10}}/>
                        )}
                        <span className={styles['m-card-required']} s-cr="#f5222d">只能填写一个标签</span>
                    </Form.Item>
                </>
            ) }
            { ['1', '2'].includes(type) && (
                <Form.Item label="基金标签" wrapperCol={{span: 12}}>
                    {getFieldDecorator('fundTag', {
                        initialValue: fundTag,
                        rules: [{ required: true, message: '请填写基金标签' }],
                    })(
                        <Input disabled={!isEdit}/>
                    )}
                    <span className={styles['m-card-required']} s-cr="#f5222d">如有多个标签，请问英文逗号隔开</span>
                </Form.Item>
            ) }
            { ['2', '5'].includes(type) && (
                <Form.Item label="收益时间区间" wrapperCol={{span: 12}}>
                    {getFieldDecorator('profitSection', {
                        initialValue: profitSection,
                        rules: [{ required: true, message: '请选择收益时间区间' }],
                    })(
                    <Select style={{width: 200}} disabled={!isEdit}>
                        <Option value="month">近1月</Option>
                        <Option value="threeMonth">近3月</Option>
                        <Option value="halfYear">近6月</Option>
                        <Option value="year">近1年</Option>
                        <Option value="threeYear">近3年</Option>
                        <Option value="fiveYear">近5年</Option>
                        <Option value="startBuild">成立来</Option>
                    </Select>
                    )}
                </Form.Item>
            ) }
            { type === '3' && (
                <Form.Item label="小目标历史业绩">
                    <div className={styles['m-classify-content']}>
                        <ul>
                            { targetData?.length > 0 && targetData.map((item: any, index: number) => {
                                return (
                                    <li key={index} className="u-flex">
                                        <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>名称</p>
                                        <Input style={{marginRight: 20, width: 200}} disabled={!isEdit} value={item.name} onChange={(e) => this.handleInput(e, index, 'name')} />
                                        <p className={styles['m-card-label']} style={{width: 140}}><span className={styles['m-required']}>*</span>止盈时年化收益</p>
                                        <InputNumber 
                                            style={{marginRight: 20, width: 200}} 
                                            disabled={!isEdit} 
                                            value={item.profit} 
                                            onChange={(e) => this.handleInput(e, index, 'profit')}
                                            precision={2}
                                        />
                                        <Popconfirm
                                            title="确定删除?"
                                            onConfirm={() => {this.handleDeleteTarget(index)}}
                                            okText="是"
                                            cancelText="否"
                                            disabled={!isEdit}
                                        >
                                            <Button type="danger" ghost disabled={!isEdit}>删除</Button>
                                        </Popconfirm>
                                        
                                    </li>
                                )
                            }) }
                        </ul>
                    </div>
                    <Button style={{marginLeft: 20}} disabled={!isEdit} onClick={this.addTarget}>添加</Button>
                </Form.Item>
             ) }
            { ['1', '3'].includes(type) && (
                <>
                <Form.Item label="认购开始时间" wrapperCol={{span: 12}}>
                    {getFieldDecorator('buyStartTime', {
                        initialValue: buyStartTime ? moment(buyStartTime) : null,
                        rules: [{ required: type === '1', message: '请填写认购开始时间' }],
                    })(
                        <DatePicker disabled={!isEdit} showTime />
                    )}
                </Form.Item>
                <Form.Item label="认购结束时间" wrapperCol={{span: 12}}>
                    {getFieldDecorator('buyEndTime', {
                        initialValue: buyEndTime ? moment(buyEndTime) : null,
                        rules: [{ required: type === '1', message: '请填写认购结束时间' }],
                    })(
                        <DatePicker disabled={!isEdit} showTime />
                    )}
                </Form.Item>
                <Form.Item label={`产品${type === '1' ? '认购' : '购买'}人数`} wrapperCol={{span: 12}}>
                    {getFieldDecorator('buyCount', {
                        initialValue: buyCount,
                        rules: [{ required: true, message: `请填写产品${type === '1' ? '认购' : '购买'}人数` }],
                    })(
                        <Input disabled={!isEdit}/>
                    )}
                </Form.Item>
                </>
            ) }
            
            {
                type === '4' && (
                    <Form.Item label="是否由系统配置" wrapperCol={{span: 12}}>
                        {getFieldDecorator('recommendType', {
                            initialValue: "1",
                            rules: [{ required: true, message: '请选择是否由系统配置' }],
                        })(
                        <Select style={{width: 200}} disabled={true}>
                            <Option value="1">系统推荐</Option>
                        </Select>
                        )}
                    </Form.Item>
                )
            }
            { ['5'].includes(type) && (
                <Form.Item label="推荐理由" wrapperCol={{span: 12}}>
                    {getFieldDecorator('reason', {
                        initialValue: reason,
                        rules: [{ required: true, message: '请填写推荐理由' }],
                    })(
                        <Input disabled={!isEdit}/>
                    )}
                </Form.Item>
            )}
            <Form.Item label="按钮文案" wrapperCol={{span: 12}}>
                {getFieldDecorator('button', {
                    initialValue: button,
                    rules: [{ required: true, message: '请填写按钮文案' }],
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label="跳转链接" wrapperCol={{span: 12}}>
                {getFieldDecorator('watchUrl', {
                    initialValue: watchUrl
                })(
                    <Input disabled={!isEdit}/>
                )}
            </Form.Item>
            <Form.Item label="版本控制" wrapperCol={{span: 12}}>
                {getFieldDecorator('version', {
                    initialValue: version,
                })(
                    <Input disabled={!isEdit}/>
                )}
                <span className={styles['m-card-required']} s-cr="#f5222d">格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist</span>
            </Form.Item>
            <Form.Item label="开始时间" wrapperCol={{span: 12}}>
                {getFieldDecorator('startTime', {
                    initialValue: startTime ? moment(startTime) : null
                })(
                    <DatePicker disabled={!isEdit} showTime/>
                )}
            </Form.Item>
            <Form.Item label="结束时间" wrapperCol={{span: 12}}>
                {getFieldDecorator('endTime', {
                    initialValue: endTime ? moment(endTime) : null
                })(
                    <DatePicker disabled={!isEdit} showTime/>
                )}
            </Form.Item>
          </Form>
        </div>
      )
    }
}

const WrappedBlock = Form.create<BlockFormProps>({ 
  name: 'block',
  onValuesChange: (props, changedValues, allValues) => {
    const { handleUpdate, blockData } = props;
    console.log(props, changedValues, allValues);
    const { startTime, endTime, buyStartTime, buyEndTime, ...other } = allValues
    let updateObj = {
        ...other,
        startTime: startTime ? timeFormat2(startTime) : '',
        endTime: endTime ? timeFormat2(endTime) : '',
        buyStartTime: buyStartTime ? timeFormat2(buyStartTime) : '',
        buyEndTime: buyEndTime ? timeFormat2(buyEndTime) : '',
        fundManagerPage: blockData.fundManagerPage,
        managerInfo: blockData.managerInfo ?? [],
        targetData: blockData.targetData ?? [],
    }
    if (changedValues.type) {
        updateObj.fundCode  = '';
    }
    if (allValues.type === '4') {
        updateObj = {
            button: allValues.button,
            name: allValues.name,
            recommendType: '1',
            startTime: startTime ? timeFormat2(startTime) : '',
            endTime: endTime ? timeFormat2(endTime) : '',
            type: '4',
            version: allValues.version,
            watchUrl: allValues.watchUrl,
        }
    }
    handleUpdate(updateObj);
  }
 })(Block);
export default WrappedBlock;