import React, {useEffect, useState} from 'react';
import WrappedBlock from './block';

interface dataProps {
    onChange: Function,
    value: any,
    readonly: boolean
}
export default function ({onChange, value, readonly }:dataProps ) {
    
    const handleUpdate = (data: any, index: number) => {
        value[index] = data;
        onChange('blocks', value);
    }
    return (
        <div>
            {
                [0, 1].map((item) => {
                    let blockData = value[item] ?? {}
                    return (
                        <div key={item}>
                            <WrappedBlock position={item} handleUpdate={handleUpdate} isEdit={!readonly} blockData={blockData}></WrappedBlock>
                        </div>
                        
                    )
                })
            }
            
        </div>
    )

}