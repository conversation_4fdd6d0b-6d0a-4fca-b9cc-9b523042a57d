{"schema": {"type": "object", "properties": {"mixName": {"title": "融合运营位名称", "type": "string", "description": "", "ui:width": "60%", "ui:options": {}}, "mixType": {"title": "融合运营位类型", "type": "string", "enum": ["1", "2"], "enumNames": ["自定义图片", "模板图片"], "ui:widget": "radio"}, "navColorLeft": {"title": "导航栏色值-左", "type": "string", "ui:width": "51%"}, "navColorRight": {"title": "导航栏色值-右", "type": "string", "ui:width": "70%", "ui:options": {"addonAfter": "若填写，则为渐变效果，请填写与导航栏色值-左不同的色值"}}, "imageUrl": {"title": "{{formData.mixType === '1' ? '融合运营位图片' : '背景图片'}}", "type": "string", "ui:widget": "uploadImg"}, "masterTitle": {"title": "主标题", "type": "string", "description": "", "ui:width": "60%", "ui:hidden": "{{formData.mixType === '1'}}", "ui:options": {"addonAfter": "最多10个字"}, "maxLength": 10}, "masterColor": {"title": "主标题色值", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType === '1'}}"}, "subTitle": {"title": "副标题", "type": "string", "description": "", "ui:width": "60%", "ui:hidden": "{{formData.mixType === '1'}}", "ui:options": {"addonAfter": "最多15个字"}, "maxLength": 15}, "subColor": {"title": "副标题色值", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType === '1'}}"}, "buttonText": {"title": "按钮文描", "type": "string", "description": "", "ui:width": "60%", "ui:hidden": "{{formData.mixType === '1'}}", "ui:options": {"addonAfter": "最多6个字"}, "maxLength": 6}, "buttonColor": {"title": "按钮色值", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType === '1'}}"}, "buttonTextColor": {"title": "按钮文字色值", "type": "string", "ui:width": "51%", "ui:hidden": "{{formData.mixType === '1'}}"}, "jumpAction": {"title": "跳转链接", "type": "string", "default": "", "ui:width": "60%", "ui:labelWidth": 0, "ui:options": {}}, "version": {"title": "版本控制", "type": "string", "ui:width": "80%", "ui:options": {"addonAfter": "*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist"}}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "60%", "ui:className": "u-block"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "60%"}}, "required": ["mixName", "mixType", "navColorLeft", "imageUrl", "masterTitle", "masterColor"]}, "displayType": "row", "showDescIcon": true, "labelWidth": 140}