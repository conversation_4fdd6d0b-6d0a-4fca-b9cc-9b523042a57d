export const handleApi = (hash: string) => {
    let get =  '';
    let post = ''
    switch(hash) {
        case 'searchhotword':
            get = 'fetchSearchhotword';
            post = 'postSearchhotword';
            break;
        case 'mixbanner':
            get = 'fetchMixedbanner';
            post = 'postMixedbanner';
            break;
        case 'notice':
            get = 'fetchscNotice';
            post = 'postscNotice';
            break;
        case 'gongge':
            get = 'fetchscGongge';
            post = 'postscGongge';
            break;
        case 'gonggeall':
            get = 'fetchscGonggeAll';
            post = 'postscGonggeAll';
            break;
        case 'operationposition':
            get = 'fetchscOperationPosition';
            post = 'postscOperationPosition';
            break;
        case 'usercomment':
            get = 'fetchscUserComment';
            post = 'postscUserComment';
            break;
        case 'coupon':
            get = 'fetchscCoupon';
            post = 'postscCoupon';
            break;
        case 'operationblock':
            get = 'fetchscOperationblock';
            post = 'postscOperationblock';
            break;
        case 'biggiftbag':
            get = 'fetchscBiggiftbag';
            post = 'postscBiggiftbag';
            break;
        case 'operationCard':
            get = 'fetchscOperationcard';
            post = 'postscOperationcard';
            break;
        case 'threesteps':
            get = 'fetchscThreesteps';
            post = 'postscThreesteps';
            break;
        case 'productCard':
            get = 'fetchscProductcard';
            post = 'postscProductcard';
            break;
        case 'multidimension':
            get = 'fetchscMultidimension';
            post = 'postscMultidimension';
            break;
        case 'featuredList':
            get = 'fetchscFeaturedList';
            post = 'postscFeaturedList';
            break;

    }
    return {
        get,
        post
    };
}
export const modelName = (hash: string) => {
    const obj = {
        'searchhotword': {
            location: '搜索热词',
            name: 'searchWord',
            type: 'HotSearch'
        },
        'mixbanner': {
            location: '融合运营位',
            name: 'mixName',
            type: 'OperationPosition'
        },
        'notice': {
            location: '公告',
            name: 'noticeTitle',
            type: 'Notice'
        },
        'gongge': {
            location: '宫格',
            name: 'gonggeName',
            type: 'Icon'
        },
        'gonggeall': {
            location: '宫格-全部页面',
            name: 'gonggeName',
            type: 'AllIcon'
        },
        'operationposition': {
            location: '持仓运营位',
            name: 'textTitle',
            type: 'Holder'
        },
        'usercomment': {
            location: '用户精评',
            name: 'userCommentName',
            type: 'UserComment'
        },
        'coupon': {
            location: '营销利益点-优惠券',
            name: 'couponName',
            type: 'Packet'
        },
        'operationblock': {
            location: '营销利益点-运营豆腐块',
            name: 'name',
            type: 'Block'
        },
        'biggiftbag': {
            location: '营销利益点-开户/首购大礼包',
            name: 'name',
            type: 'customize-BigGift'
        },
        'operationCard': {
            location: '营销利益点-运营大卡',
            name: 'name',
            type: 'OperationCard'
        },
        'threesteps': {
            location: '营销利益点-新人三步走',
            name: 'name',
            type: 'customize-NewAccount'
        },
        'productCard': {
            location: '基金推荐-产品大卡',
            name: 'name',
            type: 'customize-ProductCard'
        },
        'multidimension': {
            location: '基金推荐-多维度选基',
            name: 'title',
            type: 'ChooseFund'
        },
        'featuredList': {
            location: '基金推荐-特色榜单',
            name: 'title',
            type: 'SpecialList'
        },
    }
    return obj[hash]
}

export const fetchData = (item: any, hash: string) => {
    let formData: any = {};
    switch(hash) {
        case 'searchhotword':
            formData = {
                searchWord: item.searchWord,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime
            }
            break;
        case 'mixbanner':
            formData = {
                mixName: item.mixName,
                mixType: item.mixType ?? '1',
                navColorLeft: item.navColorLeft,
                navColorRight: item.navColorRight,
                imageUrl: item.imageUrl,
                masterTitle: item.masterTitle,
                masterColor: item.masterColor,
                subTitle: item.subTitle,
                subColor: item.subColor,
                buttonText: item.buttonText,
                buttonColor: item.buttonColor,
                buttonTextColor: item.buttonTextColor,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime
            }
            break;
        case 'notice':
            formData = {
                noticeTitle: item.noticeTitle,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime
            }
            break;
        case 'gongge':
            formData = {
                gonggeName: item.gonggeName,
                page: item.page,
                darkPage: item.darkPage,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
                angleMark: item.angleMark
            }
            break;
        case 'gonggeall':
            formData = {
                gonggeType: item.gonggeType,
                gonggeName: item.gonggeName,
                page: item.page,
                darkPage: item.darkPage, 
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
                angleMark: item.angleMark
            }
            break;
        case 'operationposition':
            formData = {
                textTitle: item.textTitle,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime
            }
            break;
        case 'usercomment':
            formData = {
                userCommentName: item.userCommentName,
            }
            break;
        case 'coupon':
            formData = {
                couponName: item.couponName,
                imageUrl: item.imageUrl,
                couponText: item.couponText,
                couponId: item.couponId,
                achieveText: item.achieveText,
                seeText: item.seeText,
                jumpAction: item.jumpAction,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'operationblock':
            formData = {
                name: item.name,
                blocks: item.blocks ?? [],
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'biggiftbag':
            formData = {
                name: item.name,
                page: item.page,
                title: item.title,
                noneReceiveButton: item.noneReceiveButton,
                noneAccountButton: item.noneAccountButton,
                noneBuyButton: item.noneBuyButton,
                noneBuyUrl: item.noneBuyUrl,
                version: item.version,
                gifts: item.gifts ?? [],
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'operationCard':
            formData = {
                name: item.name,
                page: item.page,
                darkPage: item.darkPage,
                content: item.content,
                url: item.url,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime
            }
            break;
        case 'threesteps':
            formData = {
                name: item.name,
                page: item.page,
                title: item.title,
                steps: item.steps ?? [],
                finishButton: item.finishButton,
                url: item.url,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'productCard':
            formData = {
                type: item.type,
                name: item.name,
                fundCode: item.fundCode,
                fundName: item.fundName,
                fundManagerName: item.fundManagerName,
                managerInfo: item.managerInfo ?? [],
                fundManagerTag: item.fundManagerTag,
                reason: item.reason,
                show: item.show ?? [],
                title: item.title,
                secTitle: item.secTitle,
                fundManagerPage: item.fundManagerPage,
                fundTag: item.fundTag,
                profitSection: item.profitSection,
                buyStartTime: item.buyStartTime,
                buyEndTime: item.buyEndTime,
                targetData: item.targetData ?? [],
                buyCount: item.buyCount,
                recommendType: item.recommendType,
                button: item.button,
                watchUrl: item.watchUrl,
                version: item.version,
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'multidimension':
            formData = {
                tabType: item.tabType,
                title: item.title,
                secTitle: item.secTitle,
                url: item.url,
                version: item.version,
                recommendType: item.recommendType,
                fundConfigList: item.fundConfigList ?? [],
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
        case 'featuredList':
            formData = {
                title: item.title,
                secTitle: item.secTitle,
                moreTitle: item.moreTitle,
                url: item.url,
                version: item.version,
                lists: item.lists ?? [],
                startTime: item.startTime,
                endTime: item.endTime,
            }
            break;
    }          
    return formData;        
}
export const addData = (hash: string) => {
    let formData: any = {};
    switch(hash) {
        case 'searchhotword':
            formData = {
                ssearchWord: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'mixbanner':
            formData = {
                mixName: '',
                mixType:  '1',
                navColorLeft:  '',
                navColorRight: '',
                imageUrl:  '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'notice':
            formData = {
                noticeTitle: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'gongge':
            formData = {
                gonggeName: '',
                page: '',
                darkPage: '',
                angleMark: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'gonggeall':
            formData = {
                gonggeType: '',
                gonggeName: '',
                page: '',
                darkPage: '',
                angleMark: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'operationposition':
            formData = {
                textTitle: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'usercomment':
            formData = {
                userCommentName: '',
            }
            break;
        case 'coupon':
            formData = {
                couponName: '',
                imageUrl: '',
                couponText: '',
                couponId: '',
                achieveText: '',
                seeText: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: '',
            }
            break;
        case 'operationblock':
            formData = {
                name: '',
                blocks: [],
                startTime: '',
                endTime: '',
            }
            break;
        case 'biggiftbag':
            formData = {
                name: '',
                page: '',
                title: '',
                noneReceiveButton: '',
                noneAccountButton: '',
                noneBuyButton: '',
                noneBuyUrl: '',
                version: '',
                gifts: [],
                startTime: '',
                endTime: '',
            }
            break;
        case 'operationCard':
            formData = {
                name: '',
                page: '',
                darkPage: '',
                content: '',
                url: '',
                version: '',
                startTime: '',
                endTime: ''
            }
            break;
        case 'threesteps':
            formData = {
                name: '',
                page: '',
                title: '',
                steps: [],
                finishButton: '',
                url: '',
                version: '',
                startTime: '',
                endTime: '',
            }
            break;
        case 'productCard':
            formData = {
                type: '1',
                name: '',
                fundCode: '',
                fundName: '',
                fundManagerName: '',
                managerInfo: [],
                fundManagerTag: '',
                reason: '',
                show: [],
                fundManagerPage: '',
                fundTag: '',
                buyStartTime: '',
                buyEndTime: '',
                buyCount: '',
                button: '',
                watchUrl: '',
                version: '',
                startTime: '',
                endTime: '',
            }
            break;
        case 'multidimension':
            formData = {
                tabType: 'selfDefine',
                title: '',
                secTitle: '',
                url: '',
                version: '',
                recommendType: '1',
                fundConfigList: [],
                startTime: '',
                endTime: '',
            }
            break;
        case 'featuredList':
            formData = {
                title: '',
                secTitle: '',
                moreTitle: '',
                url: '',
                version: '',
                lists: [],
                startTime: '',
                endTime: '',
            }
            break;
    }          
    return formData;        
}
export const checkUrl = (str: string, sort: string = '') => {
    if (str) {
        if (!/^(http:\/\/|https:\/\/|client.html).+/.test(str)) {
            return {
                isError: true,
                msg: `请填写${sort}正确的跳转链接`
            }
        }
        if (str.length !== str.trim().length) {
            return {
                isError: true,
                msg: `${sort}跳转链接前后不能有空格`
            }
        } else {
            return {
                isError: false,
                msg: ''
            }
        }
    } else {
        return {
            isError: false,
            msg: ''
        }
    }
}