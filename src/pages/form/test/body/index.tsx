import React, { useState, useEffect } from 'react';
import {But<PERSON>, Card, Row, message, Popconfirm, Collapse, Input, Select} from 'antd';
import api from 'api';
import FormRender from 'form-render/lib/antd';

const { fetchJsonList, fetchJson, fetchGeneralPage, postGeneralPage, fetchGeneralPageHash, postGeneralPageHash, fetchAllOptions } = api;

export default function () {
    const [keys, setKeys] = useState([])
    const [names, setNames] = useState([])
    const [option, setOption] = useState('') //选择
    const [propName, setPropName] = useState('') //第二参数

    const [propsSchema, setPropsSchema] = useState({}); //文本框配置
    const [uiSchema, setUiSchema] = useState({});
    const [formData, setData] = useState({}) //配置数据
    const [valid, setValid] = useState([]);

    const [allData, setAllData] = useState([])

    useEffect(() => {
        fetchJsonList().then((res: any) => {
            try {
                res = JSON.parse(res.data);
                setNames(res.names)
                setKeys(res.keys)
            } catch(e) {
                console.log(e)
            }
        })
        let _key: string | null = localStorage.getItem('normal_json_key') ? localStorage.getItem('normal_json_key') : ''
        if (_key) {
            setOption(_key)
            localStorage.removeItem('normal_json_key')
            getConfig(_key)
        }
        try {
            let _option: string = location.hash.split('?')[1].split('option=')[1]
            setOption('normal_config_' + _option)
            getConfig('normal_config_' + _option)
        } catch(e) {
            console.log(e)
        }
    }, [])

    /**
     * 根据JSON格式获取数据格式
     * @param JSON JSON文本格式
     * @param obj 获取到的数据格式
     */
    function getFormData(JSON: any, obj: any) {
        //如果为对象则继续，否则则认为到最内层，退出
        if(typeof JSON === 'object'){
            //获取对应属性
            let _keys = JSON.properties ? Object.keys(JSON.properties) : []
            for (let i = 0; i < _keys.length; i++) {
                let _item: any = JSON.properties[_keys[i]] //当前item 
                //如果属性类型为对象则继续遍历
                if (_item.type === 'object') {
                    obj[_keys[i]] = {}
                    getFormData(_item, obj[_keys[i]])
                } 
                //如果属性为数组
                else if (_item.type === 'array') {
                    obj[_keys[i]] = []
                    //数组分为对象数组和其他类型数组
                    //如果类型为对象
                    if (_item.items.type === 'object' && _item.items.properties && Object.keys(_item.items.properties).length > 0) {
                        obj[_keys[i]].push({})
                        getFormData(_item.items, obj[_keys[i]][0])
                    }
                    // 其他类型数组
                    else {
                        obj[_keys[i]] = []
                    }
                }
                //其他情况直接放置空 
                else {
                    obj[_keys[i]] = ''
                }
            }
        }
        else return
    }

    /**
     * 修改数据格式
     * @param data 原本数据格式
     * @param formData 新生成的默认数据格式
     */
    function addValue(data: any, formData: any) {
        //判断是否为对象或数组
        if (typeof data === 'object') {
            //如果新数据格式中没有对应对象数组返回
            if (!formData) {
                return
            }
            //如果数据格式为数组
            if (data instanceof Array) {
                if(data.length === 0) {
                    formData.shift()
                    return
                }
                data.map((item, index) => {
                    if (typeof item === 'object') {
                        formData[index] = {} //给定一个默认初始值
                        addValue(item, formData[index])
                    } else {
                        formData[index] = item
                    }
                })
            } 
            //如果是对象
            else {
                let _keys = Object.keys(data)
                _keys.forEach((key) => {
                    //如果为数组或者对象
                    if (typeof data[key] === 'object') {
                        addValue(data[key], formData[key])
                    } else {
                        formData[key] = data[key]
                    }
                })
            }
        }
    }

    /**
     * 获取配置的json
     */
    function getConfig(_option ?: string | undefined): void {
        _option = _option ? _option : option
        !/^\w*$/g.test(propName) ?
        message.error('propName中不能含有中文字和特殊符号')
        :
        fetchJson({
            propName: _option ? _option : option
        }).then((res: any) => {
            try {
                res = JSON.parse(res.data);
                let _data = {}
                setPropsSchema(res)
                // getFormData(res, _data)
                // setData(_data)
                getData(_data, _option);
            } catch(e) {
                console.log(e)
            }
        })
    }

    /**
     * 获取配置
     */
    function getData(_data: any, _option: string | undefined): void {
        _option = _option ? _option : option
        propName ?
        fetchGeneralPageHash(null, _option, propName).then((res: any) => {
            try {
                res = JSON.parse(res.data);
                if (res && res.formData) {
                    setData(res.formData)
                    // addValue(res.formData, _data)
                    // console.log(_data)
                    // setData(_data)
                } else {
                    setData({})
                }
            } catch (e) {
                console.warn(e)
            }
        })
        :
        fetchGeneralPage(null, _option).then((res: any) => {
            try {
                res = JSON.parse(res.data);
                if (res && res.formData) {
                    setData(res.formData)
                    // console.log(res.formData, _data)
                    // addValue(res.formData, _data)
                    // console.log(_data)
                    // setData(_data)
                } else {
                    setData({})
                }
            } catch (e) {
                console.warn(e)
            }
        })
    }

    function fetchAllData() {
        if (!option) return
        fetchAllOptions(null, option).then((data: any) => {
            if (data.code === "0000") {
                setAllData(data.data.map((item: any) => item.key))
            }
        }).catch((e: Error) => {
            console.log(e)
            setAllData('获取错误')
        })
    }

    /**
     * 提交配置
     */
    function submit(): void {
        if (valid.length > 0) {
            console.log(formData)
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }

        let _formData: any = formData;
        propName ?
        postGeneralPageHash ({
            value: JSON.stringify({formData: _formData})
        }, option, propName).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('提交成功！');
                }
            } catch (e) {
                message.error(e.message);
            }
        })
        :
        postGeneralPage ({
            value: JSON.stringify({formData: _formData})
        }, option).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('提交成功！');
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    return (
        <article>
            <header>
                {
                    location.hash.split('?').length > 1 ?
                    null
                    :
                    <section className="u-l-middle">
                        <p style={{width: 150}}>请选择模块：</p>
                        <Select 
                        showSearch
                        style={{ width: 400, marginLeft: 50 }} 
                        value={option}
                        onChange={(value:string) => {
                            setOption(value); 
                        }}>
                            {
                                keys.map ((key, index) => {
                                    return (
                                        <Select.Option value={key} key={index}>{`${names[index]}   ${key}`}</Select.Option>
                                    )
                                })
                            }
                        </Select>
                    </section>
                }
                
                <section className="u-l-middle" style={{marginTop: 20, marginBottom: 20}}>
                    <p style={{width: 150, marginRight: 50 }}>请输入propName值(第二参数，例如：日期)：</p>
                    <Input 
                    style={{width: 400, marginRight: 50}} 
                    onChange={(e: any) => {
                        setPropName(e.target.value)
                    }}
                    />
                    <Button type="primary" onClick={() => {getConfig()}}>获取该配置</Button>
                </section>
                
                
            </header>

            <article style={{marginTop: 30}}>
                {
                    Object.keys(propsSchema).length > 0 ?
                    <div style={{
                        boxShadow: '0px 0px 8px grey',
                        width: '1100px',
                        borderRadius: '8px',
                        padding: '50px'
                        }}>
                        <FormRender
                            propsSchema={propsSchema}
                            uiSchema={uiSchema}
                            formData={formData}
                            onChange={setData}
                            displayType="row"
                            showDescIcon={true}
                        />
                        <div className="u-j-middle">
                            <Popconfirm
                                placement="rightBottom"
                                title={'你确定要提交么'}
                                onConfirm={submit}
                                okText="确认"
                                cancelText="取消"
                            >
                                <Button 
                                type="danger" 
                                > 提交 </Button>
                            </Popconfirm>
                            <p>
                                {
                                    propName ?
                                    `http://${location.port ? 'test' : ''}fund.10jqka.com.cn/hqapi/static/hq/commonconfig/hashdatasave/${option}/${propName}`
                                    :
                                    `http://${location.port ? 'test' : ''}fund.10jqka.com.cn/hqapi/static/hq/commonconfig/kvdatasave/${option}`
                                }
                            </p>
                        </div>
                    </div>
                    :
                    ''
                }
            </article>

            <div className="g-mt20">
                <Button type="primary" onClick={fetchAllData}>获取所有props配置内容</Button>
                <div className="u-w600">
                    {
                        allData.length === 0
                        ?
                        null
                        :
                        <div style={{width: 800}}>
                            {
                                allData.map((item: string, index: number) => 
                                    <span className="u-block_il u-w100" key={index}>{item}</span>
                                )
                            }
                        </div>
                    }
                </div>
            </div>

        </article>
    )
}