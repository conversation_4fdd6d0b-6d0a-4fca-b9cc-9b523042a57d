import * as monaco from 'monaco-editor/esm/vs/editor/editor.api.js';
import 'monaco-editor/esm/vs/editor/editor.all.js';//javascript
import 'monaco-editor/esm/vs/editor/contrib/find/findController.js'; //查找软件
require('monaco-editor/esm/vs/editor/browser/editorExtensions.js').EditorExtensionsRegistry.getEditorActions()

import React, { useState, useEffect } from 'react';
import { Button, Card, Row, message, Popconfirm, Collapse, Input, Select } from 'antd';
import api from 'api';
import FormRender from 'form-render/lib/antd';

const { fetchJsonList, postJsonList, fetchJson, postJson } = api;

const _json = {
    "type": "object",
    "properties": {
        "title": {
            "title": "标题",
            "type": "string"
        },
        "items": {
            "title": "决策提醒基金配置",
            "type": "array",
            "minItems": 3,
            "maxItems": 16,
            "ui:options": {
                "foldable": "true"
            },
            "items": {
                "type": "object",
                "properties": {
                    "code": {
                        "title": "基金代码",
                        "type": "string",
                        "ui:width": "50%"
                    },
                    "time": {
                        "title": "收益区间",
                        "type": "string",
                        "default": "year",
                        "ui:width": "50%",
                        "enum": [
                            "year",
                            "now",
                            "week",
                            "month",
                            "tmonth",
                            "hyear",
                            "twoyear",
                            "tyear",
                            "fyear",
                            "nowyear"
                        ],
                        "enumNames": [
                            "近一年涨幅-year",
                            "成立以来涨幅-now",
                            "近一周涨幅-week",
                            "近一月涨幅-month",
                            "近三月涨幅-tmonth",
                            "近半年涨幅-hyear",
                            "近两年涨幅-towyear",
                            "近三年涨幅-tyear",
                            "近五年涨幅-fyear",
                            "今年以来涨幅-nowyear"
                        ]
                    },
                    "buttonText": {
                        "title": "按钮文案",
                        "type": "string",
                        "ui:width": "50%"
                    },
                    "des": {
                        "title": "标签/副标题",
                        "type": "string",
                        "description": "填写一个标签",
                        "ui:width": "50%",
                        "maxLength": 6
                    }
                },
                "required": [
                    "name",
                    "code",
                    "time",
                    "buttonText"
                ]
            }
        }
    }
}

let monacoInstance: any = ''

export default function (props: any) {
    const [step, setStep] = useState('1') //现在到的流程
    const [propsSchema, setPropsSchema] = useState({});
    const [uiSchema, setUiSchema] = useState({});
    const [formData, setData] = useState({})
    const [type, setType] = useState('') //选择的模式 add modify
    const [key, setKey] = useState(''); //新加的key
    const [keys, setKeys] = useState([]); //所有key值
    const [name, setName] = useState(''); //新添加的模板名称
    const [names, setNames] = useState([]); //所有模板名称
    const [option, setOption] = useState(''); //选择的模板

    useEffect(() => {
        fetchJsonList().then((res: any) => {
            try {
                res = JSON.parse(res.data);
                setNames(res.names)
                setKeys(res.keys)
            } catch (e) {
                message.error(e);
            }
        })
    }, [])

    /**
     * 初始化输入框
     */
    function initMonaco(json: any) {
        let _board: any = document.getElementById("monaco")
        _board.innerHTML = '';
        _board.innerHTML = `<div id="monaco"
            className="test"
            style={{ height: '800px', width: '600px' }}
        > </div>`
        let _div: any = document.getElementById("monaco")
        monacoInstance = monaco.editor.create(_div, {
            theme: 'vs-dark',
            value: JSON.stringify(json, null, 2),
            language: 'json',
        })
        console.log(monacoInstance.getValue())
        monacoInstance.trigger('anything', 'editor.action.formatDocument')

        monacoInstance.onKeyUp(
            () => {
                monacoInstance.trigger('anything', 'editor.action.formatDocument')
                let _json = '';
                try {
                    _json = JSON.parse(monacoInstance.getValue())
                } catch (e) {
                    console.log(e)
                }
                setPropsSchema(_json)
            }
        )
    }

    /**
     * 提交
     */
    function upload(): void {
        if (step === '4') {
            if (!type) {
                message.error('没有type');
                return;
            }
            if (!monacoInstance.getValue().trim() || !key || !name) {
                message.error('请输入内容');
                return;
            }
            if (type === 'add') {
                if (!/^\w*$/g.test(key)) {
                    message.error('key中不能含有中文字和特殊符号');
                    return;
                }
                let _key: string = 'normal_config_' + key
                if (keys.indexOf(_key) !== -1) {
                    message.error('key值重复，请重新输入');
                    return;
                }else if (names.indexOf(name) !== -1) {
                    message.error('名字重复，请重新输入');
                    return;
                }
                let _keys: Array<string> = keys;
                _keys.push(_key);
                let _names: Array<string> = names;
                _names.push(name)
                postJsonList({
                    value: JSON.stringify({
                        keys: _keys,
                        names: _names
                    })
                }).then((res: any) => {
                    try {
                        if (res.code !== '0000') {
                            message.error(res.message);
                        } else {
                            message.success('提交成功！')
                            postJson({
                                value: JSON.stringify(propsSchema)
                            }, _key).then((res: any) => {
                                try {
                                    if (res.code !== '0000') {
                                        message.error(res.message);
                                    } else {
                                        message.success('提交成功！')
                                        localStorage.setItem('normal_json_key', _key)
                                        props.history.push(`/form/test/body`)
                                    }
                                } catch (e) {
                                    message.error(e.message);
                                }
                            })
                        }
                    } catch (e) {
                        message.error(e.message);
                    }
                })
            } 
        } else if (step === '3' && type === 'modify') {
            postJson({
                value: JSON.stringify(propsSchema)
            }, option).then((res: any) => {
                try {
                    if (res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success('提交成功！')
                        localStorage.setItem('normal_json_key', option)
                        props.history.push(`/form/test/body`)
                    }
                } catch (e) {
                    message.error(e.message);
                }
            })
        } else if (step === '2' && type === 'modify') {
            getJson()
        } else {
            let _step: number = parseInt(step)
            setStep((_step + 1).toString())
        }
    }

    /**
     * 添加新配置
     */
    function addNewConfig() {
        setStep('3')
        setType('add')
        setPropsSchema(_json)
        initMonaco(_json)
    }

    /**
     * 获取配置
     */
    function getJson(): void {
        if (!option) {
            message.error('请选择配置')
            return
        }
        fetchJson({
            propName: option
        }).then((res: any) => {
            try {
                res = JSON.parse(res.data);
                setStep('3')
                setPropsSchema(res)
                initMonaco(res)
            } catch (e) {
                console.log(e)
            }
        })
    }

    /**
     * 返回上一步
     */
    function goBack(): void {
        if (step === '3' && type === 'add') {
            setStep('1')
        } else {
            let _step: number = parseInt(step)
            setStep((_step - 1).toString())
        }
    }

    return (
        <article>
            {
                step === '1'
                    ?
                    <div>
                        <p>请问需要怎么操作？</p>
                        <section className="u-l-middle" style={{ marginBottom: 20 }}>
                            <Button type="primary" onClick={addNewConfig}>添加一个新的配置</Button>
                            {
                                ~location.href.indexOf('BigMaster') ?
                                <Button className="g-ml100" type="primary" onClick={() => {
                                    setType('modify')
                                    setStep('2')
                                }}>修改以前的配置</Button>
                                :
                                null
                            }
                        </section>
                    </div>
                    : null
            }
            {
                step === '2'
                    ?
                    <div>
                        <p>请选择一个需要修改的配置</p>
                        <section className="g-mt20 u-l-middle" >
                            <Select
                                showSearch
                                style={{ width: 400, marginRight: 100 }}
                                value={option}
                                onChange={(value: string) => {
                                    setOption(value);
                                }}>
                                {
                                    keys.map((key, index) => {
                                        return (
                                            <Select.Option value={key} key={index}>{`${names[index]}  ${key}`}</Select.Option>
                                        )
                                    })
                                }
                            </Select>
                            {/* <Button
                                type="primary"
                                onClick={getJson}
                            >确认选择</Button> */}
                        </section>
                    </div>
                    : null
            }
            {
                <div>
                    <section 
                    className="u-l-middle g-mb20 f-ovhidden" 
                    style={step === '3' ? {} : { height: 0 }}
                    >
                        <div id="board">
                            <div id="monaco"
                                className="test"
                                style={{ height: '800px', width: '600px' }}
                            > </div>
                        </div>
                        <div style={{ width: '600px', height: '800px', overflow: 'scroll', marginLeft: '50px' }}>
                            <FormRender
                                propsSchema={propsSchema}
                                uiSchema={uiSchema}
                                formData={formData}
                                onChange={setData}
                                displayType="row"
                                showDescIcon={true}
                            />
                        </div>
                    </section>
                </div>
            }
            {
                step === '4'
                    ?
                    <section>
                        <header className="u-l-middle">
                            <p style={{width: 170}}>请输入key值(英文)：key值为此页面的唯一标识，能于生成唯一数据接口</p>
                            <Input
                                style={{ marginLeft: '30px', width: '300px', marginBottom: '30px' }}
                                value={key}
                                onChange={(e) => {
                                    setKey(e.target.value)
                                }}
                            />
                        </header>
                        <header className="u-l-middle">
                            <p style={{width: 170}}>请输入名字：</p>
                            <Input
                                style={{ marginLeft: '30px', width: '300px', marginBottom: '30px' }}
                                value={name}
                                onChange={(e) => {
                                    setName(e.target.value)
                                }}
                            />
                        </header>
                    </section>
                    :
                    ''
            }

            <footer className="u-c-middle g-mt20" style={{ width: '1200px' }}>
                {
                    step === '1'
                        ?
                        null
                        :
                        <Button type="primary" onClick={goBack}>上一步</Button>
                }
                {
                    step === '1'
                        ?
                        null
                        :
                        
                        (step === '3' && type === 'modify') || step === '4'
                            ?
                            <Popconfirm
                                placement="rightBottom"
                                title={'你确定要提交么'}
                                onConfirm={upload}
                                okText="确认"
                                cancelText="取消"
                            >
                                <Button type="danger" className="g-ml50">提交</Button>
                            </Popconfirm>
                            :
                            <Button type="primary" className="g-ml50" onClick={upload}>下一步</Button>
                }
            </footer>
        </article>
    )
}