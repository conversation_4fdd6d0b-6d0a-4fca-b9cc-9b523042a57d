import React, { useState, useEffect } from 'react'
import FormR<PERSON> from 'form-render/lib/antd'
import FORM_JSON from './form.json'
import api from 'api'
import styles from './index.less'
import { Button, message, Popconfirm, Input, Collapse, Select } from 'antd';
const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;
import UploadImg from '@/pages/frontend/compoment/uploadImg'

interface Gift {
    name: string,
    desc: string,
    image_big: string,
    image_small: string,
    btn_text: string,
    memo: string,
    url: string,
    env: '0'|'1'|'2'
}

const env = [
    {value: '0', title: '仅手炒'},
    {value: '1', title: '仅爱基金'},
    {value: '2', title: '手炒+基金'}
]

export default function rookieGift() { 
    const {fetchRookieGift, postRookieGift} = api;

    const [formData, setFormData] = useState({})
    const [init, setInit] = useState(false)
    const [valid, setValid] = useState([])

    const [themeImg, setThemeImg] = useState('')
    const [gifts, setGifts] = useState<Gift[]>([])
    
    useEffect( () => {
        fetchForm()
    }, [init])

    const fetchForm = () => {
        fetchRookieGift().then( (res: any) => {
            let _data = FORM_JSON.formData;
            console.log(_data, FORM_JSON)
            try {
                res = JSON.parse(res.data);
                console.log(res)
                if(res) {
                    _data = res;
                }
            } catch(e) {
                message.error(e.message)
            }
            setInit(true)
            setThemeImg(res.themeImage)
            setGifts(res.gifts || [])
            setFormData(_data)
        }).catch( (e: Error) => {
            message.error(e.message)
        })
    }

    const postForm = () => {
        if(valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }
        // 检查themeImg字段
        if(!themeImg) {
            message.error(`请添加'主标题宣传图'`);
            return;
        }
        // 检查gifts字段
        let giftsChecked = true;
        const regHttp = /http(s|):\/\/.+/,
            regChinese = /[\u4e00-\u9fa5]/g;
        gifts.map( (gift: Gift, index: number) => {
            if(!gift.image_big) {
                message.error(`请添加'礼包${index + 1}'的${'礼包大图'}`)
                giftsChecked = false;
                return;
            } else if(!gift.name) {
                message.error(`请填写'礼包${index + 1}'的${'礼包名称'}`)
                giftsChecked = false;
                return;
            }else if(!gift.desc) {
                message.error(`请填写'礼包${index + 1}'的${'礼包内容描述'}`)
                giftsChecked = false;
                return;
            }else if(!gift.btn_text) {
                message.error(`请填写'礼包${index + 1}'的${'按钮文案'}`)
                giftsChecked = false;
                return;
            } else if(!gift.url) {
                message.error(`请填写'礼包${index + 1}'的${'跳转链接'}`)
                giftsChecked = false;
                return;
            } else if(!regHttp.exec(gift.url)) {
                message.error(`请正确填写'礼包${index + 1}'的${'跳转链接'}`)
                giftsChecked = false;
                return;
            } else if(!gift.env) {
                message.error(`请填写'礼包${index + 1}'的${'环境'}`)
                giftsChecked = false;
                return;
            } else if(gift.name || gift.desc) {
                let regArr = gift.name.split(''),
                    arr1 = regArr.filter(str => str.match(regChinese)),
                    arr2 = regArr.filter(str => !str.match(regChinese)),
                    length = 0;
                length = arr1.length * 2 + arr2.length;
                console.log(regArr, arr1, arr2, length)
                if(length > 14) {
                    message.warning(`'礼包${index + 1}'的'礼包名称'长度应小于14个字符（7个汉字）！`);
                    giftsChecked = false;
                    return;
                }

                regArr = gift.desc.split(''),
                arr1 = regArr.filter(str => str.match(regChinese)),
                arr2 = regArr.filter(str => !str.match(regChinese)),
                length = arr1.length * 2 + arr2.length;
                console.log(regArr, arr1, arr2, length)
                if(length > 12) {
                    message.warning(`'礼包${index + 1}'的'礼包内容描述'长度应小于12个字符（6个汉字）！`);
                    giftsChecked = false;
                    return;
                }
            } 
        })

        if(giftsChecked) {
            const _gifts = gifts.slice(),
                _formData = JSON.parse(JSON.stringify(formData));
            const value: any = {
                ..._formData,
                themeImage: themeImg,
                gifts: _gifts,
            }
            console.log(value);

            postRookieGift({
                value: JSON.stringify(value)
            }).then( (res: any) => {
                console.log(res)
                try {
                    if(res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success('提交成功');
                    }
                } catch(e) {
                    message.error(e.message);
                }
            })
        }
    }

    const handleUploadImg = (url: string) => {
        setThemeImg(url)
    }
    const handleGift = (str: string, index: number, type: 'name'|'desc'|'image_big'|'btn_text'|'memo'|'url'|'env') => {
        let _gifts = JSON.parse(JSON.stringify(gifts));
        _gifts[index][type] = str;
        setGifts(_gifts);
        console.log(_gifts)
    }
    const handleAddGift = () => {
        let _gifts = JSON.parse(JSON.stringify(gifts));
        _gifts.push({
            image_big: '',
            image_small: '',
            btn_text: '',
            memo: '',
            url: ''
        })
        setGifts(_gifts);
    }
    const handleDeleteGift = (index: number) => {
        let _gifts = JSON.parse(JSON.stringify(gifts));
        _gifts.splice(index, 1)
        setGifts(_gifts);
    }

    return (
        <article>
            <section className={styles['m-theme']}>
                <p className={styles['m-object-title']}>主题:</p>
                <UploadImg
                    handleChange={(url: string) => {handleUploadImg(url)}}
                    imageUrl={themeImg}
                    size={['628px*247px']}
                    title={'主标题宣传图'}
                    isEdit={true}
                />
            </section>
            <section className={styles['m-gifts']}>
                <p className={styles['m-object-title']}>礼包列表:</p>
                <div className={styles['m-gifts-list']}>
                    <Collapse>
                        {
                            gifts.map( (gift: Gift, index: number) => {
                                return (
                                    <Panel key={index} header={`礼包${index + 1}`}>
                                        <div className={`${styles['m-gift-item']} g-pr`}>
                                            <UploadImg 
                                                handleChange={(url: string) => {console.log('修改后url',url);handleGift(url, index, 'image_big')}}
                                                imageUrl={gift.image_big}
                                                size={['451px*451px']}
                                                title={'礼包大图'}
                                                isEdit={true}
                                            />
                                            {/* <UploadImg 
                                                handleChange={(url: string) => {handleGift(url, index, 'image_small')}}
                                                imageUrl={gift.image_small}
                                                size={['70px*70px']}
                                                title={'礼包小图'}
                                                isEdit={true}
                                            /> */}
                                            <div className={`${styles['m-item-inputs']} u-flex`}>
                                                <div className={`${styles['m-item-input']} ${styles['m-name']} u-flex`}>
                                                    <span><span className={styles['m-required']}>* </span>礼包名称：</span>
                                                    <Input 
                                                        defaultValue={gift.name}
                                                        onChange={(e: any) => {handleGift(e.target.value, index, 'name')}}
                                                    />
                                                </div>
                                                <div className={`${styles['m-item-input']} ${styles['m-desc']} u-flex`}>
                                                    <span><span className={styles['m-required']}>* </span>礼包内容描述：</span>
                                                    <Input 
                                                        defaultValue={gift.desc}
                                                        onChange={(e: any) => {handleGift(e.target.value, index, 'desc')}}
                                                    />
                                                </div>
                                                <div className={`${styles['m-item-input']} ${styles['m-btn_text']} u-flex`}>
                                                    <span><span className={styles['m-required']}>* </span>按钮文案：</span>
                                                    <Input 
                                                        defaultValue={gift.btn_text}
                                                        onChange={(e: any) => {handleGift(e.target.value, index, 'btn_text')}}
                                                    />
                                                </div>
                                                <div className={`${styles['m-item-input']} ${styles['m-url']} u-flex`}>
                                                    <span><span className={styles['m-required']}>* </span>跳转链接：</span>
                                                    <Input 
                                                        defaultValue={gift.url}
                                                        onChange={(e: any) => {handleGift(e.target.value, index, 'url')}}
                                                    />
                                                </div>
                                                <div className={`${styles['m-item-input']} ${styles['m-memo']} u-flex`}>
                                                    <span>备注：</span>
                                                    <TextArea
                                                        rows={4} 
                                                        defaultValue={gift.memo}
                                                        onChange={(e: any) => {handleGift(e.target.value, index, 'memo')}}
                                                    />
                                                </div>
                                                <div className={`${styles['m-item-input']} ${styles['m-env']} u-flex`}>
                                                    <span><span className={styles['m-required']}>* </span>环境：</span>
                                                    <Select
                                                        style={{width: '100%'}}
                                                        defaultValue={gift.env}
                                                        onChange={(value: any) => {handleGift(value, index, 'env')}}>
                                                        {
                                                            env.map( (env: {value: string, title: string}, index: number) => {
                                                                return (
                                                                    <Option key={index} value={env.value}>{env.title}</Option>
                                                                )
                                                            })
                                                        }
                                                    </Select>
                                                </div>
                                            </div>
                                            <p className={styles['m-delete']}>
                                                <Popconfirm placement="top" title="确定要删除吗？" onConfirm={() => {handleDeleteGift(index)}}>
                                                    <Button type="danger" ghost>删除</Button>
                                                </Popconfirm>
                                            </p>
                                        </div>
                                    </Panel>
                                )
                            })
                        }
                    </Collapse>
                    <p className="u-flex"><Button type="primary" className={styles['m-gifts-add']} onClick={handleAddGift}>新增</Button></p>
                </div>
            </section>
            <FormRender
                propsSchema={FORM_JSON.schema}
                onValidate={setValid}
                formData={formData}
                onChange={setFormData}
                displayType="row"
                showDescIcon={true}
            />
            <Popconfirm 
                placement="bottom"
                title={'确认提交吗？'}
                okText={'确认'}
                cancelText={'取消'}
                onConfirm={postForm}>
                <Button type="primary">提交</Button>
            </Popconfirm>
        </article>
    )
}