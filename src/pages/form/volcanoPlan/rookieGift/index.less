.m-theme {
    
}
.m-gifts {
    .m-gifts-list {
        .m-gift-item {
            .m-item-inputs {
                flex-flow: row wrap;
                .m-item-input {
                    flex-flow: row nowrap;
                    align-items: center;
                    margin: 10px 0px 10px 20px;
                    > span {
                        width: 12vw;
                        text-align: end;
                    }
                }
                .m-name {
                    width: 40%;
                }
                .m-desc {
                    width: 40%;
                }
                .m-btn_text {
                    width: 40%;
                }
                .m-url {
                    width: 40%;
                }
                .m-memo {
                    width: 40%;
                }
                .m-env {
                    width: 20%
                }
            }
            .m-delete {
                display: flex;
                > button {
                    margin-left: auto;
                }
            }
        }
        .m-gifts-add {
            margin: 20px 0 0 auto;
        }
    }
}
.m-object-title {
    padding: .25rem 0;
    border-bottom: solid 1px rgba(0,0,0,.2);
    font-size: 16px;
    color: #222;
}

.m-required {
    font-size: 14px;
    color: red;
    line-height: 20px;
    font-family: SimSun, sans-serif;
}