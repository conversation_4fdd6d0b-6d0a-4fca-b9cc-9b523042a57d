import React, {useState, useEffect} from 'react';
import FORM_JSON from './form.json';
import Form<PERSON><PERSON> from "form-render/lib/antd";
import { Button, message } from 'antd';
import api from 'api';
const { fetchVolcanoRobotAccount, postVolcanoRobotAccount } = api;

const schema = FORM_JSON.schema;

export default function () {
    const [formData, setFormData] = useState({})
    useEffect(() => {
        fetchVolcanoRobotAccount().then((res: any) => {
            if(res.code === '0000') {
                let data = JSON.parse(res.data) || {"list": [{"customId": "", "score": ""}]};
                setFormData(data)
            } else {
              message.error(res.status_msg)
            }
        }) 
    }, [])

    const handleChange = (formData: any) => {
        setFormData(formData)
    }

    const handlePost = () => {
        let _data: any = formData;
        for (let item of _data.list) {
            item.time = new Date().getTime();
        }
        console.log(_data);
        postVolcanoRobotAccount({value:JSON.stringify(_data)}).then((res: any)=>{
            try {
                if (res.code === '0000') {
                    message.success("保存成功");
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    message.error(res.message);
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    };

    return (
        <>
            <FormRender
                propsSchema={schema}
                formData={formData}
                displayType='row'
                labelWidth='120px'
                onChange={handleChange}
                onValidate={() => {}}
            ></FormRender>
            <Button type="primary" onClick={handlePost}>发布</Button>
        </>
    )
}