import React, { useEffect, useState } from 'react';
import { Input, Row, Col, message, But<PERSON>, Popconfirm } from 'antd';
import api from 'api';
import ImgUpload from '../../../frontend/compoment/uploadImg'

const { fetchVolcanoZhibo, postVolcanoZhibo, deleteVolcanoZhibo } = api
export default function () {

    const [allData, setAllData] = useState([])

    let style1 = {
        fontSize: '20px',
        marginBottom: '40px',
        fontWeight: 'bold'
    }
    let rowStyle = {
        width: '720px'
    }

    useEffect(() => {
        getData()
    }, [])
    function getData() {
        fetchVolcanoZhibo().then((res: any) => {
            try {
              if (res) {
                let _res = []
                res.data.map((val)=>_res.push(JSON.parse(val.value)))
                setAllData(_res)
              }
      
            } catch (e) {
              console.warn(e)
            }
          })
    }
    function submit(){
        allData.map((val ,index) => {
            let sendData = {
                SID: val.SID,
                url: val.url,
                img: val.img,
                statids: val.statids
                
                
            }
            postVolcanoZhibo({
                value:JSON.stringify(sendData),
              },sendData.SID).then((res)=>{
                try {
                  if (res.code !== '0000') {
                    message.error(res.message);
                  } else if(index === allData.length-1){
                    message.success('提交成功！');
                  }
                } catch (e) {
                  message.error(e.message);
                }
              })
        })
    }
    function handleImg(data:any, index:any){
        let _allData = [].concat(allData)
        _allData[index].img = data
        setAllData(_allData)
    }
    function handleSID(data:any, index:any){
        let _allData = [].concat(allData)
        _allData[index].SID = data
        setAllData(_allData)
    }
    function handleURL(data:any, index:any){
        let _allData = [].concat(allData)
        _allData[index].url = data
        setAllData(_allData)
    }
    function handleClick(data:any, index:any){
        let _allData = [].concat(allData)
        _allData[index].statids.click = data
        setAllData(_allData)
    }
    function handleJump(data:any, index:any){
        let _allData = [].concat(allData)
        _allData[index].statids.jump = data
        setAllData(_allData)
    }
    function handleClose(data:any, index:any){
        let _allData = [].concat(allData)
        _allData[index].statids.close = data
        setAllData(_allData)
    }
    function addItem() {
        let _data = {
            SID: '',
            url: '',
            img: '',
            statids: {
                click: '',
                close: '',
                jump: ''
            }
        }
        let _allData = [].concat(allData)
        _allData.push(_data)
        setAllData(_allData)
    }
    function deleteSID(sid){
        deleteVolcanoZhibo({},sid).then((res)=>{
            try {
              if (res.code !== '0000') {
                message.error(res.message);
              } else {
                message.success('删除成功！');
                getData()
              }
            } catch (e) {
              message.error(e.message);
            }
          })
    }
    return (<div style={rowStyle}>
        <p style={style1}>基金直播红包配置后台：</p>
        {console.log(allData)}
        {
            allData.map((val, index) => {
                return <div style={{width: '800px', border:"1px solid #666", marginTop:'30px', padding:'10px'}} key={val.index}>
                    <ImgUpload 
                        handleChange={(value: any) =>{handleImg(value, index)}}
                        imageUrl={val.img}
                        size={['108px*108px']}
                        limit={0.2}
                        isEdit={true}
                        title='图片选择'
                    />
                    <Row>
                        <Col span={4}>
                            <p>直播间SID</p>
                        </Col>
                        <Col offset={1} span={19}>
                            <Input
                                onChange={(e) => handleSID(e.target.value, index)}
                                value={val.SID}
                            />
                        </Col>
                    </Row>
                    <Row>
                        <Col span={4}>
                            <p>红包跳转链接</p>
                        </Col>
                        <Col offset={1} span={19}>
                            <Input
                                onChange={(e) => handleURL(e.target.value, index)}
                                value={val.url}
                            />
                        </Col>
                    </Row>
                    <Row>
                        <Col span={4}>
                            <p>点击埋点</p>
                        </Col>
                        <Col offset={1} span={19}>
                            <Input
                                onChange={(e) => handleClick(e.target.value, index)}
                                value={val.statids.click}
                            />
                        </Col>
                    </Row>
                    <Row>
                        <Col span={4}>
                            <p>跳转埋点</p>
                        </Col>
                        <Col offset={1} span={19}>
                            <Input
                                onChange={(e) => handleJump(e.target.value, index)}
                                value={val.statids.jump}
                            />
                        </Col>
                    </Row>
                    <Row>
                        <Col span={4}>
                            <p>关闭埋点</p>
                        </Col>
                        <Col offset={1} span={19}>
                            <Input
                                onChange={(e) => handleClose(e.target.value, index)}
                                value={val.statids.close}
                            />
                        </Col>
                    </Row>
                    <Popconfirm
                        title="确定删除?删除后将立即生效。"
                        onConfirm={() => {deleteSID(val.SID)}}
                        okText="是"
                        cancelText="否"
                    >
                         <Button>删除该项</Button>
                    </Popconfirm>
                   
                </div>
            })
        }

        <div className={'g-mt20'}>
            <Button onClick={addItem}>增加</Button>
            <Button onClick={submit} className={'g-ml20'}>保存</Button>
        </div>
    </div>)

}