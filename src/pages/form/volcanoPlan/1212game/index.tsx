import React, { useState, useEffect } from 'react'
import <PERSON><PERSON><PERSON> from 'form-render/lib/antd'
import FORM_JSON from './form.json'
import api from 'api'
import { message, But<PERSON>, Popconfirm } from 'antd';

const {fetch1212game} = api;

interface globalRes {
    startDate: string,
    endDate: string,
    addSumTimes: number,
    sumTimes: number,
    reward: string,
    packetMap: {1: string, 2: string, 3: string, 4: string, 5: string},
    newPacketMap: {1: string, 2: string, 3: string, 4: string, 5: string},
    session?: string,
    solved?: boolean,
    peopleNum?: number,
}

interface sessionRes {
    startTime: string,
    endTime: string,
    reward: string,
    addSumTimes: number,
    realSumTimes: number,
    session: string,
}

interface form_data {
    startDate: string,
    endDate: string,
    addSumTimes: number,
    sumTimes: number,
    reward: string,
    sessions: sessionRes[],
    packetMap: {1: string, 2: string, 3: string, 4: string, 5: string},
    newPacketMap: {1: string, 2: string, 3: string, 4: string, 5: string},
}

export default function game1212() {
    const [formData, setFormData] = useState<form_data>({} as form_data);
    const [init, setInit] = useState(false);
    const [valid, setValid] = useState([]);

    useEffect( () => {
        getItem()
    }, [])

    const getItem = () => {
        let _data = FORM_JSON.formData as form_data || {} as form_data;
        const fetch = [
            fetch1212game({reqDto: JSON.stringify({type: 'getGlobalInfo'})}, 'global_get'),
            fetch1212game({reqDto: JSON.stringify({type: 'getSessionList'})}, 'session_get')
        ]
        Promise.all(fetch).then( (res: any) => {
            console.log(res)
            try {
                let global = res[0].data as globalRes,
                    sessions = res[1].data as sessionRes[];
                if(global && sessions) {
                    let _sessions = sessions;
                    for(let i = 0; i < _sessions.length; i++) {
                        for(let j = 0; j < _sessions.length - 1 - i; j++) {
                            if(Date.parse(_sessions[j].startTime) > Date.parse(_sessions[j + 1].startTime)) {
                                let session = _sessions[j];
                                _sessions[j] = _sessions[j + 1];
                                _sessions[j + 1] = session;
                            }
                        }
                    }
                    _data = {
                        startDate: global.startDate,
                        endDate: global.endDate,
                        addSumTimes: global.addSumTimes,
                        sumTimes: global.sumTimes,
                        reward: global.reward,
                        sessions: _sessions,
                        packetMap: global.packetMap,
                        newPacketMap: global.newPacketMap
                    }
                    console.log('全局', global, '\n场次', _sessions, '\nform', _data)
                }
            } catch(e) {
                console.log('error1')
                message.error(e.message)
            }

            setFormData(_data)
            setInit(true)
        }).catch( (e: Error) => {
            console.log('error2')
            message.error(e.message)
        })
    }

    const onSubmit = () => {
        if(valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }
        const global = {
            reward: formData.reward,
            addSumTimes: formData.addSumTimes,
            startDate: formData.startDate.replace(/T/, ' '),
            endDate: formData.endDate.replace(/T/, ' '),
            packetMap: formData.packetMap,
            newPacketMap: formData.newPacketMap
        },
        sessions: sessionRes[] = formData.sessions;

        fetch1212game({
            reqDto: JSON.stringify({type: 'updateGlobalInfo', ...global})
        }, 'global_post').then( (res: any) => {
            try {
                if(res.status_code !== 0) {
                    message.error('全局信息提交失败：' + res.status_msg);
                } else {
                    message.success('全局信息提交成功');
                }
            } catch(e) {
                message.error('全局信息提交失败：' + e.message);
            }
        })

        const updateList = [
            ...sessions.map( (session: sessionRes, index: number) => {
                return fetch1212game({
                    reqDto: JSON.stringify({type: 'updateSession', reward: session.reward, addSumTimes: session.addSumTimes, session: session.session})
                }, `session_post_${index}`)
            })
        ]
        console.log('update', updateList)
        Promise.all(updateList).then( (res_all: any[]) => {
            let success = true;
            for(let [index, res] of res_all.entries()) {
                try {
                    if(res.status_code !== 0) {
                        message.error(`${res.status_msg} 索引为${index}，场次为${sessions[index].session}`);
                        success = false;
                        break;
                    }
                } catch(e) {
                    message.error(e.message);
                }
            }
            if(success) {
                message.success('各场次提交成功');
                // location.reload();
                getItem();
            }
        })
    }

    return (
        <section>
            <FormRender 
                propsSchema={FORM_JSON.schema}
                onValidate={setValid}
                formData={formData}
                onChange={setFormData}
                displayType="row"
                showDescIcon={true}
            />
            <Popconfirm placement="right" title="确认提交吗？" onConfirm={onSubmit}>
                <Button type="danger">提交</Button>
            </Popconfirm>
        </section>
    )
}