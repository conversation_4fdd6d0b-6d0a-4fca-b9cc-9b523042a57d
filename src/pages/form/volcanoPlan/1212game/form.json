{"schema": {"type": "object", "required": ["addSumTimes"], "properties": {"startDate": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "34%", "ui:labelWidth": 135}, "endDate": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "34%", "ui:labelWidth": 135}, "addSumTimes": {"title": "游戏总登顶人数", "type": "string", "ui:width": "34%", "ui:labelWidth": 135}, "sumTimes": {"title": "场次累计登顶数", "type": "string", "ui:width": "34%", "ui:labelWidth": 135, "ui:readonly": true}, "packetMap": {"title": "老用户关卡红包", "type": "object", "properties": {"1": {"title": "第一关", "type": "string", "ui:width": "30%", "ui:options": {}}, "2": {"title": "第二关", "type": "string", "ui:width": "30%", "ui:options": {}}, "3": {"title": "第三关", "type": "string", "ui:width": "30%", "ui:options": {}}, "4": {"title": "第四关", "type": "string", "ui:width": "30%", "ui:options": {}}, "5": {"title": "第五关", "type": "string", "ui:width": "30%", "ui:options": {}}}, "required": ["1", "2", "3", "4", "5"]}, "newPacketMap": {"title": "新人关卡红包", "type": "object", "properties": {"1": {"title": "第一关", "type": "string", "ui:width": "30%", "ui:options": {}}, "2": {"title": "第二关", "type": "string", "ui:width": "30%", "ui:options": {}}, "3": {"title": "第三关", "type": "string", "ui:width": "30%", "ui:options": {}}, "4": {"title": "第四关", "type": "string", "ui:width": "30%", "ui:options": {}}, "5": {"title": "第五关", "type": "string", "ui:width": "30%", "ui:options": {}}}, "required": ["1", "2", "3", "4", "5"]}, "sessions": {"title": "场次奖励金额和登顶人数", "type": "array", "items": {"type": "object", "properties": {"startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "40%", "ui:labelWidth": 135, "ui:readonly": true}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "40%", "ui:labelWidth": 135, "ui:readonly": true}, "reward": {"title": "场次金额", "type": "string", "ui:width": "25%", "ui:labelWidth": 135, "ui:options": {}}, "addSumTimes": {"title": "加权登顶人数", "type": "string", "ui:width": "25%", "ui:labelWidth": 135, "ui:readonly": false}, "realSumTimes": {"title": "真实登顶人数", "type": "string", "ui:width": "25%", "ui:labelWidth": 135, "ui:readonly": true}, "session": {}}, "required": ["reward", "addSumTimes"]}, "ui:readonly": true, "ui:options": {"pageSize": 5}}}}, "formData": {"startDate": "", "endDate": "", "addSumTimes": "", "sumTimes": "", "packetMap": {"1": "", "2": "", "3": "", "4": "", "5": ""}, "newPacketMap": {"1": "", "2": "", "3": "", "4": "", "5": ""}, "sessions": [{"startTime": "", "endTime": "", "reward": "", "addSumTimes": "", "realSumTimes": "", "session": ""}]}}