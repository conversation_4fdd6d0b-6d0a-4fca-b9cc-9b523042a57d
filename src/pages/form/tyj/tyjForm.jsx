/*
*接口配置
* <AUTHOR>
* @time 2019.12
*/
import React, { Fragment } from 'react';
import {But<PERSON>, Card, Row, message, Popconfirm, Collapse} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import ReactDOM from 'react-dom';
import FormRender from 'form-render/lib/antd';

const { fetchTyjList, fetchActivityTyj, postActivityTyj} = api;

const schema = {
    "propsSchema": {
        "type": "object",
        "ui:width": "300px",
        "properties": {
            "activityName": {
                "title": "活动名称（必填）",
                "type": "string"
            },
            "activityId": {
                "title": "活动Id（必填）",
                "type": "string"
            },
            "ticketName": {
                "title": "券名称（必填）",
                "type": "string"
            },
            "shareType": {
                "title": "收益类型（必填）",
                "type": "string",
                "enum": [
                  "0",
                  "1"
                ],
                "enumNames": [
                  "0-固定收益",
                  "1-浮动收益"
                ]
              },
            "prodType": {
                "title": "产品类型（必填）",
                "type": "string",
                "enum": [
                    "fund",
                    "group",
                    "bank"
                  ],
                "enumNames": [
                    "fund",
                    "group",
                    "bank"
                  ]
            },
            "bankType": {
                "title": "银行类型（当prodType为bank时必填）",
                "type": "string",
                "description": "pab、aib"
            },
            "prodId": {
                "title": "产品代码（必填）",
                "type": "string"
            },
            "prodName": {
                "title": "产品名称（必填）",
                "type": "string"
            },
            "awardShare": {
                "title": "奖励金额（必填）",
                "type": "string"
            },
            "awardRate": {
                "title": "奖励利率 如：4%则为0.04（固定收益必填）",
                "type": "string"
            },
            "awardDate": {
                "title": "奖励天数（必填）",
                "type": "string"
            },
            "maxActivityAward": {
                "title": "活动最大奖励金额（必填）",
                "type": "string"
            },
            "startDate": {
                "title": "活动开始日期（必填）",
                "type": "string",
                "format": "dateTime",
                "description": "(yyyy-MM-dd HH:mm:SS) 2020-01-20 00:00:00 活动开始时间"
            },
            "endDate": {
                "title": "活动结束日期（必填）",
                "type": "string",
                "format": "dateTime",
                "description": "(yyyy-MM-dd HH:mm:SS) 2020-01-23 22:59:59 活动结束时间"
            },
            "activityState": {
                "title": "活动状态 （必填）",
                "type": "string",
                "description": "0-正常 1-结束 3-提前结束（活动总资金达到上限）      注意activityState在0、3的状态下是会正常发放已经使用的用户收益的；当为1时，加息终止，此状态是为特殊情况预留。"
            }
        },
        "required": [
            "activityName",
            "activityId",
            "ticketName",
            "shareType",
            "prodType",
            "prodId",
            "awardShare",
            "prodName",
            "title",
            "awardDate",
            "maxActivityAward",
            "startDate",
            "endDate",
            "activityState"
        ]
    },
    "formData": {
        "activityName": "",
        "activityId ": "",
        "ticketName": "",
        "shareType": "",
        "prodType": "",
        "bankType": "",
        "prodId": "",
        "prodName": "",
        "awardShare": "",
        "awardRate": "",
        "awardDate": "",
        "maxActivityAward": "",
        "startDate": "",
        "endDate": "",
        "activityState": ""
    }
}

@autobind
class TyjForm extends React.Component {
    constructor (props) {
        super(props);
        this.state = { formData: props.data }
    }

    onChange = formData => {
        this.setState({ formData }, () => {console.log(this.state)});
    };

    updateActivityForm () {
        let _formData = this.state.formData;
        console.log(_formData)
        postActivityTyj ({
            type: 'update',
            value: JSON.stringify(_formData)
        }).then(data => {
            if (data.code === '0000') {
                message.success('修改成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
    }

    addNewActivityForm () {
        let _formData = this.state.formData;
        console.log('addnew',_formData);
        let obj = {
                type: 'insert',
                value: JSON.stringify(_formData)
        }
        postActivityTyj (obj).then(data => {
            if (data.code === '0000') {
                message.success('添加成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
    }

    deleteActivityForm () {
        let _formData = this.state.formData;
        console.log('addnew',_formData);
        let obj = {
                type: 'delete',
                value: JSON.stringify(_formData)
        }
        postActivityTyj (obj).then(data => {
            if (data.code === '0000') {
                message.success('删除成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
    }

    render () {
        const { formData } = this.state;
        const { propsSchema } = schema;
        return (
          <section>
                <Collapse
                style={{ marginBottom: 30}}>
                    <Collapse.Panel 
                        header={this.state.formData.activityId + this.state.formData.activityName} 
                        key="1">
                        <FormRender
                            propsSchema={propsSchema}
                            formData={formData}
                            onChange={this.onChange}
                            column={2}
                            showDescIcon={true}
                        />
                        <div style={{paddingBottom: 30}}>

                            {
                                !this.props.check ? 
                                <div>
                                    <Popconfirm
                                        placement="leftBottom"
                                        title={'你确定要修改么'}
                                        onConfirm={this.updateActivityForm}
                                        okText="确认"
                                        cancelText="取消"
                                    >
                                        <Button
                                            type="primary" 
                                            style={{ float: 'right' }}
                                        >
                                            提交修改
                                        </Button>
                                    </Popconfirm>
                                    
                                    <Popconfirm
                                        placement="rightBottom"
                                        title={'你确定要删除该活动么'}
                                        onConfirm={this.deleteActivityForm}
                                        okText="确认"
                                        cancelText="取消"
                                    >
                                        <Button
                                            type="danger" 
                                            style={{ float: 'left' }}
                                        >
                                            删除活动
                                        </Button>
                                    </Popconfirm>
                                </div>
                                : 
                                <Popconfirm
                                    placement="rightBottom"
                                    title={'你确定要新增加该活动么'}
                                    onConfirm={this.addNewActivityForm}
                                    okText="确认"
                                    cancelText="取消"
                                >
                                    <Button
                                        type="primary" 
                                        style={{ float: 'right' }}
                                    >
                                        增加活动
                                    </Button>
                                </Popconfirm>
                            }                            
                        </div>
                    </Collapse.Panel>
                </Collapse>
          </section>
        );
    }
}

export default TyjForm;