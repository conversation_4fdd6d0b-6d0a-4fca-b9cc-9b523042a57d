/*
*接口配置
* <AUTHOR>
* @time 2019.12
*/
import React, { Fragment } from 'react';
import {<PERSON><PERSON>, Card, Row, message, Popconfirm, Collapse} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import ReactDOM from 'react-dom';
import <PERSON><PERSON><PERSON> from 'form-render/lib/antd';
import TyjF<PERSON> from "./tyjForm";

const { fetchTyjList } = api;

@autobind
class tyj extends React.Component {
    constructor (props) {
        super(props);
        this.state = { 
            activityForms: [],  
            isNew: [],       //是否为新加入的配置   
        }
    }

    dataInit () {
        fetchTyjList({
            type: 'query'
        }).then(data => {
            if(data.code === '0000') {
                let tyjData = [];
                let _isNew = [];
                for(let key  in data.data){
                    tyjData.push(data.data[key]);
                    _isNew.push(false);
                }
                console.log(tyjData)
                this.setState({activityForms: tyjData, isNew: _isNew});
            }
        })
    }

    addTyj () {
        let _activityForms = this.state.activityForms;
        _activityForms.push({});
        let _isNew = this.state.isNew;
        _isNew.push(true);
        this.setState({ activityForms: _activityForms, isNew: _isNew });
    }

    componentDidMount () {
        this.dataInit();
    }

    render () {
        return (
          <div className='codeInterface'>
            {this.state.activityForms.map((activity, index) => (
                <section
                key={index}
                style={{width: '800px'}}>
                    <TyjForm 
                    data={activity}
                    check={this.state.isNew[index]}
                    ></TyjForm>
                </section>
            ))}
            <Button
                type="primary" 
                style={{ float: 'left' }}
                onClick={this.addTyj}
                className="g-mt50 g-mb50"
            >
                增加
            </Button>
          </div>
        );
    }
}

export default tyj;