import React from 'react';
import { Tabs } from 'antd';
import VideoConfig from './video';
import HotFocus from './hotFocus';
import WealthColumn from './column';
import TopConfig from './topConfig';

const { TabPane } = Tabs;
/**
 * 理财速递
 */
const WealthExpress = () => {
  return (
    <>
      <Tabs defaultActiveKey="hotFucus">
        <TabPane tab="热点关注" key="hotFucus">
          <HotFocus />
        </TabPane>
        <TabPane tab="理财视频" key="wealthVideo">
          <VideoConfig />
        </TabPane>
        <TabPane tab="理财专栏" key="wealthColumn">
          <WealthColumn />
        </TabPane>
        <TabPane tab="顶部栏配置" key="topConfig">
          <TopConfig />
        </TabPane>
      </Tabs>
    </>
  );
};

export default WealthExpress;
