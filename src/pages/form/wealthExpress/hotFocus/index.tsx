import React, { useState, useEffect } from 'react';
import classnames from 'classnames';
import FormRender from 'form-render/lib/antd';
import { DraggableArea } from '../../../../components/DragTags';
import { Button, Drawer, Row, Col, message, Table, Popconfirm } from 'antd';
import { addHotFocusFormConfig } from './form';
import api from 'api';
import styles from './index.less';
/**
 * 热点关注
 */
const HotFocus = () => {
  const [focusList, setFocusList] = useState([]);
  const [formData, setFormData] = useState({});
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [valid, setValid] = useState([]);
  const [visible, setVisible] = useState(false);
  const [edit, setEdit] = useState(false);

  useEffect(() => {
    api.fetchExpressHotFocus().then(res => {
      if (res.code === '0000') {
        if (res.data) {
          setFocusList(JSON.parse(res.data).hotFocusList || []);
        }
      } else {
        message.error(res.message);
      }
    });
  }, []);

  const onClose = () => {
    setVisible(false);
    resetForm();
    setEdit(false);
  };
  const handleClickAdd = () => {
    setVisible(true);
  };

  const handleClickConfirm = () => {
    if (valid.length) {
      return;
    }
    let hotFocusList = focusList.slice();
    if (editIndex !== null) {
      hotFocusList[editIndex] = formData;
    } else {
      hotFocusList = hotFocusList.concat([formData]);
    }
    api
      .postExpressHotFocus({
        value: JSON.stringify({
          hotFocusList,
        }),
      })
      .then(res => {
        if (res.code === '0000') {
          message.success('保存成功');
          setFocusList(hotFocusList);
          setVisible(false);
          resetForm();
        } else {
          message.error(res.message);
        }
      });
  };

  const resetForm = () => {
    setFormData({});
    setEditIndex(null);
  }

  const toTop = index => {
    const newList = focusList.slice();
    const spliceItem = focusList[index];
    newList.splice(index, 1);
    newList.unshift(spliceItem);
    console.log(newList, focusList);
    setFocusList(newList);
    api
      .postExpressHotFocus({
        value: JSON.stringify({
          hotFocusList: newList,
        }),
      })
      .then(res => {
        if (res.code === '0000') {
          message.success('保存成功');
        } else {
          message.error(res.message);
        }
      });
  };

  const handleClickDelete = (index: number) => {
    const newList = focusList.slice();
    newList.splice(index, 1);
    setFocusList(newList);
    api
      .postExpressHotFocus({
        value: JSON.stringify({
          hotFocusList: newList,
        }),
      })
      .then(res => {
        if (res.code === '0000') {
          message.success('删除成功');
          setVisible(false);
        } else {
          message.error(res.message);
        }
      });
  };

  const handleClickEdit = (index: number) => {
    setEdit(true);
    setFormData(JSON.parse(JSON.stringify(focusList[index])));
    setEditIndex(index);
    setVisible(true);
  };

  const tableColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render(row, record, index) {
        return index + 1;
      },
    },
    {
      title: '标签',
      key: 'label',
      dataIndex: 'label',
    },
    {
      title: '标题',
      key: 'title',
      dataIndex: 'title',
    },
    {
      title: '跳转链接',
      key: 'link',
      dataIndex: 'link',
    },
    {
      title: '展示时间',
      key: 'link',
      dataIndex: 'link',
      render(row, record) {
        return record.dateRange?.[0] + '-' + record.dateRange?.[1];
      },
    },
    {
      title: '操作',
      key: 'caozuo',
      dataIndex: 'caozuo',
      render: (row, record, index) => {
        return (
          <div>
            <Popconfirm
              placement="rightBottom"
              title={`你确定要置顶吗？`}
              onConfirm={() => toTop(index)}
              okText="确认"
              cancelText="取消"
            >
              <span style={{ cursor: 'pointer', marginRight: 10 }}>置顶</span>
            </Popconfirm>
            <span
              style={{ cursor: 'pointer', marginRight: 10 }}
              onClick={() => handleClickEdit(index)}
            >
              编辑
            </span>
            <span onClick={() => handleClickDelete(index)}>删除</span>
          </div>
        );
      },
    },
  ];

  return (
    <div>
      <Drawer
        title={edit ? '编辑热点关注' : '新增热点关注'}
        width={600}
        visible={visible}
        onClose={onClose}
        destroyOnClose={true}
      >
        <FormRender
          formData={formData}
          onChange={setFormData}
          onValidate={setValid}
          displayType="row"
          showDescIcon={true}
          propsSchema={addHotFocusFormConfig}
        />
        <Row>
          <Col span={4} offset={8}>
            <Button onClick={onClose}>取消</Button>
          </Col>
          <Col span={4} offset={2}>
            <Button type="primary" onClick={handleClickConfirm}>
              确定
            </Button>
          </Col>
        </Row>
      </Drawer>

      <Button type="primary" onClick={handleClickAdd}>
        新增
      </Button>
      <Table columns={tableColumns} dataSource={focusList} pagination={false} />
    </div>
  );
};

export default HotFocus;
