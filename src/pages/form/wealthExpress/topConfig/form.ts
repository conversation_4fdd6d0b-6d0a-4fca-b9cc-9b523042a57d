const calendarItem = {
  type: 'object',
  properties: {
    labelName: {
      title: '日历标签',
      type: 'string',
      'ui:options': {
        placeholder: '例：宜加仓',
      },
    },
    labelLink: {
      title: '跳转地址',
      type: 'string',
      pattern: '^(http(s)?://|client)[^\n ，]*$',
    },
  },
  required: ['labelName', 'labelLink'],
};
export const calendarConfig = {
  type: 'object',
  properties: {
    workDayLabels: {
      title: '交易日日历',
      type: 'array',
      items: calendarItem,
      'ui:options': {
        foldable: true,
      },
    },
    restDayLabels: {
      title: '非交易日日历',
      type: 'array',
      items: calendarItem,
      'ui:options': {
        foldable: true,
      },
    },
  },
};
