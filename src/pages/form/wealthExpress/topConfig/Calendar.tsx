import React, { useState, useEffect, useRef } from 'react';
import FormRender from 'form-render/lib/antd';
import { Button, message } from 'antd';
import { calendarConfig } from './form';
import api from 'api';
import moment from 'moment';

function isObjectValueEqual(a, b) {
  var aProps = Object.getOwnPropertyNames(a);
  var bProps = Object.getOwnPropertyNames(b);

  if (aProps.length !== bProps.length) {
    return false;
  }

  for (var i = 0; i < aProps.length; i++) {
    var propName = aProps[i];
    var propA = a[propName];
    var propB = b[propName];
    if (propA && propB && typeof propA === 'object' && typeof propB === 'object') {
      return isObjectValueEqual(propA, propB);
    }
    if (propA !== propB) {
      return false;
    }
  }
  return true;
}

const initData = { workDayLabels: [{}], restDayLabels: [{}] };
const Calendar = () => {
  const [formData, setFormData] = useState(initData);
  const [valid, setValid] = useState([]);
  // 上一次的数据
  const lastDataRef = useRef({
    ...initData,
    workDayLabelLatestModified: '',
    restDayLabelLatestModified: '',
  });

  useEffect(() => {
    api.fetchExpressCalendar().then(res => {
      if (res.code === '0000') {
        if (res.data) {
          const data = JSON.parse(res.data);
          lastDataRef.current = data;
          setFormData(data);
        }
      } else {
        message.error(res.message);
      }
    });
  }, []);

  const handleClickSubmit = () => {
    if (valid.length) {
      return;
    }
    if (!formData.workDayLabels.length || !formData.restDayLabels.length) {
      return message.error('日历配置列表不可能为空');
    }
    const nowStr = moment(new Date().getTime()).format('YYYYMMDD');
    // 工作日标签最新变更日期，yyyyMMdd格式
    let workDayLabelLatestModified = nowStr;
    let restDayLabelLatestModified = nowStr;
    const newFormData = {
      workDayLabels: formData.workDayLabels.map((item, index) => ({ index, ...item })),
      restDayLabels: formData.restDayLabels.map((item, index) => ({ index, ...item })),
    };
    if (isObjectValueEqual(lastDataRef.current.workDayLabels, newFormData.workDayLabels)) {
      console.log('交易日日历未作修改');
      workDayLabelLatestModified = lastDataRef.current.workDayLabelLatestModified;
    }
    if (isObjectValueEqual(lastDataRef.current.restDayLabels, newFormData.restDayLabels)) {
      console.log('非交易日日历未作修改');
      workDayLabelLatestModified = lastDataRef.current.workDayLabelLatestModified;
    }
    const queryData = {
      workDayLabelLatestModified,
      restDayLabelLatestModified,
      ...newFormData,
    };
    api
      .postExpressCalendar({
        value: JSON.stringify(queryData),
      })
      .then(res => {
        if (res.code === '0000') {
          lastDataRef.current = queryData;
          message.success('保存成功');
        } else {
          message.error(res.message);
        }
      });
  };
  return (
    <div>
      <FormRender
        formData={formData}
        onChange={setFormData}
        onValidate={setValid}
        displayType="row"
        showDescIcon={true}
        propsSchema={calendarConfig}
        column={2}
      />
      <Button type="primary" onClick={handleClickSubmit}>
        提交
      </Button>
    </div>
  );
};

export default Calendar;
