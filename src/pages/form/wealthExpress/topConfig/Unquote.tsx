import React, { useEffect, useState } from 'react';
import { Button, Form, Input, Upload, Icon, message, Table, Modal } from 'antd';
import * as XLSX from 'xlsx';
import api from 'api';
import FormRender from 'form-render/lib/antd';
const { confirm } = Modal;

const Unquote = () => {
  const [fileList, setFileList] = useState([]);
  const [listData, setListData] = useState([]);
  const [listDataSave, setListDataSave] = useState([]);
  const [add, setAdd] = useState(false);
  const [formData, setFormData] = useState({
    quotation: '第一大投资原则是独立思考和内心的平静。',
    transactionDate: '20220929',
  });
  const [isModalOpen, setIsModalOpen] = useState(false);

  const validateList = data => {
    const index = data.findIndex(item => {
      return !item.transactionDate || !item.quotation;
    });
    if (index > -1) {
      message.error(`第${index + 1}行存在空的问题，请重新输入`);
      return false;
    }
    return true;
  };

  const onEditClick = (row, record) => {
    setFormData(record);
    setIsModalOpen(true);
  };

  const handleOk = () => {
    confirm({
      title: '提示',
      content: '确定是否修改',
      okText: '确定',
      cancelText: '取消',
      centered: true,
      onOk() {
        setIsModalOpen(false);
        console.log(formData, 'formData');
        api
          .postQuotationsSave([formData])
          .then(res => {
            console.log(res, 'eidt me');
            getQuotations();
            message.success('修改成功');
          })
          .catch(() => {
            message.error('修改失败');
          });
      },
    });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const fileReader = new FileReader();
      fileReader.onload = (event: any) => {
        try {
          const { result } = event.target;
          // 以二进制流方式读取得到整份excel表格对象
          const workbook = XLSX.read(result, { type: 'binary' });
          let data: any = []; // 存储获取到的数据
          // 遍历每张工作表进行读取（这里默认只读取第一张表）
          console.log(workbook.Sheets);
          for (const sheet in workbook.Sheets) {
            // 利用 sheet_to_json 方法将 excel 转成 json 数据
            data = XLSX.utils.sheet_to_json(workbook.Sheets[sheet], {
              header: ['transactionDate', 'quotation'], // 如果header被指定，第一行就会被当做数据行；如果header未指定，第一行是header并且不作为数据。
            });
            break;
          }
          console.log(data);
          // 是否为数字
          const ifNumber = (value: any) => {
            const ifString = typeof value === 'string';
            let ifAllNumber = true;
            if (ifString) {
              ifAllNumber = /^\d+$/.test(value);
            }
            return ifAllNumber;
          };
          if (
            file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.type === 'application/vnd.ms-excel'
          ) {
            // if () {

            // }
            const validRes = validateList(data);
            if (!validRes) {
              return;
            }

            setListData(data);
            // 第一行为一些数据时数据会失效，所以第一行不能为数据
            if (data[0].__rowNum__ === 0 && !data[0].transactionDate) {
              return message.error('请上传正确格式的excel');
            } else {
              setFileList([
                {
                  uid: '1',
                  status: 'done',
                  name: file.name,
                  size: file.size,
                  type: file.type,
                },
              ]);
              setListData(data);
              setAdd(true);
              // props.onChange(file);
            }
          } else {
            return message.error('请上传正确格式的excel');
          }
        } catch (e) {
          return message.error('请上传正确文件类型');
        }
      };
      fileReader.readAsBinaryString(file);
      return false;
    },
    onRemove: () => false,
  };

  const tableColumns = [
    {
      title: '日期',
      dataIndex: 'transactionDate',
      key: 'transactionDate',
    },
    {
      title: '内容',
      key: 'quotation',
      dataIndex: 'quotation',
    },
    {
      title: '操作',
      key: 'caozuo',
      render: (row, record, index) => {
        {
          return add ? (
            <div></div>
          ) : (
            <div style={{ color: '#1890ff' }} onClick={() => onEditClick(row, record)}>
              编辑
            </div>
          );
        }
      },
    },
  ];
  const handleClickSubmit = () => {
    if (listData.length < 1) {
      message.warn('请上传文件');
      return;
    }
    const validRes = validateList(listData);
    if (!validRes) {
      return;
    }
    api
      .postQuotationsSave(listData)
      .then(res => {
        console.log(res, 'res me');
        getQuotations();
        setAdd(false);
        setFileList([]);
        message.success('保存成功');
      })
      .catch(() => {
        message.error('保存失败');
      });
  };

  const getQuotations = () => {
    api.getQuotations().then(res => {
      console.log(res, 'res query me');
      const list = res.data;
      setListDataSave(list);
    });
  };

  useEffect(() => {
    getQuotations();
  }, []);

  const focusFormConfig = {
    type: 'object',
    required: ['transactionDate'],
    properties: {
      transactionDate: {
        title: '日期',
        type: 'string',
        'ui:options': {
          placeholder: '输入日期',
        },
        'ui:width': '100%',
      },
      quotation: {
        title: '语录',
        type: 'string',
        'ui:options': {
          placeholder: '输入语录',
        },
        'ui:width': '100%',
      },
    },
  };

  return (
    <div style={{ marginTop: '60px' }}>
      <h2 style={{ marginBottom: '20px' }}>日历语录</h2>
      <div style={{ marginTop: '20px', marginBottom: '10px', display: 'flex' }}>
        <Upload {...uploadProps} fileList={fileList}>
          <Button style={{ marginRight: '40px' }}>
            <Icon type="upload" /> 选择语录文件
          </Button>
        </Upload>
        <Button type="primary" onClick={handleClickSubmit}>
          上传并保存
        </Button>
      </div>
      {add ? (
        <Table columns={tableColumns} dataSource={listData} />
      ) : (
        <Table columns={tableColumns} dataSource={listDataSave} />
      )}
      <Modal
        title="修改语录"
        visible={isModalOpen}
        onOk={handleOk}
        cancelText="取消"
        okText="确认"
        onCancel={handleCancel}
        destroyOnClose={true}
      >
        {/* <div>
          <span>日期</span>
          <Input value={formData.transactionDate} />
        </div>
        <div>
          <span>语录</span>
          <Input value={formData.quotation} />
        </div> */}
        <FormRender
          formData={formData}
          onChange={setFormData}
          displayType="row"
          showDescIcon={true}
          propsSchema={focusFormConfig}
          column={2}
        />
      </Modal>
    </div>
  );
};

export default Unquote;
