export const focusFormConfig = {
  type: 'object',
  properties: {
    focusColumns: {
      title: '理财专栏',
      type: 'array',
      items: {
        type: 'object',
        properties: {
          cid: {
            title: '栏目ID',
            type: 'string',
            'ui:options': {
              placeholder: '输入栏目ID',
            },
          },
          title: {
            title: '标题',
            type: 'string',
            'ui:options': {
              placeholder: '输入标题',
            },
          },
          labels: {
            title: '标签',
            type: 'string',
            'ui:options': {
              placeholder: '多个标签用英文逗号隔开',
            },
          },
          link: {
            title: '跳转链接',
            type: 'string',
            'ui:options': {
              placeholder: '输入跳转链接',
            },
          },
          pictureUrl: {
            title: '封面图片',
            type: 'string',
            'ui:widget': 'pictureUpload',
          },
          shieldWord: {
            title: '文章标题屏蔽词',
            type: 'string',
            'ui:options': {
              placeholder: '输入文章标题屏蔽词',
            },
          },
        },
        required: ['cid', 'labels', 'link', 'title', 'pictureUrl', 'shieldWord'],
      },
      'ui:options': {
        foldable: true,
      },
    },
  },
};
