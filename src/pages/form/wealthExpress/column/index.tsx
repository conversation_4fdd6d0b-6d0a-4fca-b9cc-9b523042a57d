import React, { useState, useEffect, useRef } from 'react';
import FormRender from 'form-render/lib/antd';
import { Button, message } from 'antd';
import { focusFormConfig } from './form';
import api from 'api';
import Upload from '../../components/uploadFile';

const LogoUpload = ({ value, onChange, name }) => {
  const uploadCallback = (fileName: string, size: number, url: string) => {
    onChange(name, url);
  };
  return (
    <>
      {value && <img src={value} style={{ width: '140px' }} />}
      <span>
        <Upload text="选择文件" callback={uploadCallback} />
      </span>
    </>
  );
};

const Column = () => {
  const [formData, setFormData] = useState({ focusColumns: [{}] });
  const [valid, setValid] = useState([]);

  useEffect(() => {
    api.fetchExpressColumn().then(res => {
      if (res.code === '0000') {
        if (res.data) {
          setFormData(JSON.parse(res.data));
        }
      } else {
        message.error(res.message);
      }
    });
  }, []);

  const handleClickSubmit = () => {
    if (valid.length) {
      return;
    }
    api
      .postExpressColumn({
        value: JSON.stringify(formData),
      })
      .then(res => {
        if (res.code === '0000') {
          message.success('保存成功');
        } else {
          message.error(res.message);
        }
      });
  };
  return (
    <div>
      <div>栏目ID：7428亿见，7452赛道掘金，7744每周一基，用于获取文章更新时间</div>
      <FormRender
        formData={formData}
        onChange={setFormData}
        onValidate={setValid}
        displayType="row"
        showDescIcon={true}
        propsSchema={focusFormConfig}
        column={2}
        widgets={{
          pictureUpload: LogoUpload,
        }}
      />
      <Button type="primary" onClick={handleClickSubmit}>
        保存数据
      </Button>
    </div>
  );
};

export default Column;
