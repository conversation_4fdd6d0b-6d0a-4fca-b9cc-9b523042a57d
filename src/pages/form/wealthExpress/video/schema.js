export default function(isEdit) {
  return {
    type: 'object',
    required: [
      'title',
      'sid',
      'fid',
      'avatar',
      'company',
      'channelName',
      'recommendLabels',
      'avatar',
      'recommendStartTime',
      'recommendEndTime',
    ],
    properties: {
      title: {
        title: '直播名称',
        type: 'string',
        'ui:options': {},
      },
      sid: {
        title: '直播间id',
        type: 'string',
        'ui:options': {},
        'ui:disabled': true,
      },
      fid: {
        title: '直播间账号',
        type: 'string',
        'ui:options': {},
        'ui:disabled': true,
      },
      avatar: {
        title: '主播头像',
        type: 'string',
        'ui:widget': 'uploadImg',
        'ui:width': '46%',
      },
      company: {
        title: '基金公司',
        type: 'string',
      },
      channelName: {
        title: '直播姓名',
        type: 'string',
      },
      recommendLabels: {
        type: 'string',
        title: '直播标签（多个标签，英文，分开）',
      },
      recommendStartTime: {
        title: '开始时间',
        type: 'string',
        format: 'dateTime',
      },
      recommendEndTime: {
        title: '结束时间',
        type: 'string',
        format: 'dateTime',
      },
      advanceRecommend: {
        'ui:labelWidth': 150,
        type: 'boolean',
        title: '预告期间是否推荐',
        'ui:widget': 'switch',
      },
    },
  };
}
