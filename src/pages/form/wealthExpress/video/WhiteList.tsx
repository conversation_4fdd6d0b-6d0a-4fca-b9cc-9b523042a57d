import React, { useEffect, useState } from 'react';
import { Button, Input, Row, Col, message } from 'antd';
import api from 'api';

const WhiteList = () => {
  const [whiteList, setWhiteList] = useState('');
  useEffect(() => {
    api.fetchExpressWhiteList().then(res => {
      if (res.code === '0000') {
        const data = JSON.parse(res.data || '{}');
        setWhiteList(data.whiteList || '');
      } else {
        message.error(res.message);
      }
    });
  }, []);

  const handleClickSubmit = () => {
    if (!whiteList.length) {
      message.error('请输入uid');
      return;
    }
    api
      .postExpressWhiteList({
        value: JSON.stringify({ whiteList }),
      })
      .then(res => {
        if (res.code === '0000') {
          message.success('保存成功');
        } else {
          message.error(res.message);
        }
      });
  };
  return (
    <div>
      <Row>
        <Col span={10}>
          <Input.TextArea
            placeholder="请输入uid，每个uid之间用英文逗号隔开"
            autoSize={{ minRows: 3 }}
            value={whiteList}
            onChange={e => setWhiteList(e.target.value)}
          />
        </Col>
      </Row>
      <Row>
        <Col>
          <div style={{ marginTop: '12px' }}>
            <Button type="primary" onClick={handleClickSubmit}>
              白名单保存
            </Button>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default WhiteList;
