import { Table, Drawer, Popconfirm, Button, message } from 'antd';
import React, { useEffect, useState } from 'react';
import api from 'api';
import FormRender from 'form-render/lib/antd';
import UploadImg from '../../components/uploadFile';
import getSchema from './schema';
const SCHEMA1 = getSchema(true);

/**
 *
 * @returns 直播配置，前端分页，后端一次性返回
 */
const Live = () => {
  const [listData, setListData] = useState([]);
  const [isShowAdd, setIsShowAdd] = useState(false); //是否显示modal
  const [formData, setFormData] = useState({}); //是否显示modal
  const [valid, setValid] = useState([]);
  const getLivesConfig = () => {
    api.getLivesConfig().then(res => {
      console.log(res, 'res pei');
      const list = res.data?.wealthV3LiveList || [];
      setListData(list);
    });
  };

  const onRecommandClick = (row, record) => {
    setIsShowAdd(true);
    setFormData({
      ...record,
      advanceRecommend: record.advanceRecommend === '0' ? false : true,
    });
  };
  const onRemoveRecommandClick = (row, record) => {
    const params = {
      ...record,
      recommend: record.recommend === '0' ? '1' : '0',
    };
    postRecommand(params);
  };
  const onFixClick = (row, record) => {
    const params = {
      ...record,
      pin: record.pin === '1' ? '0' : '1',
    };
    api.postLivesConfig([params]).then(() => {
      message.success('保存成功');
      getLivesConfig();
    });
  };
  const onRemoveClick = (row, record) => {
    const params = {
      ...record,
      undercarriage: record.undercarriage === '1' ? '0' : '1',
    };
    console.log(row, record, params, 'onRemoveClick');
    api.postLivesConfig([params]).then(() => {
      message.success('保存成功');
      getLivesConfig();
    });
  };

  const LogoUpload = ({ value, onChange, name }) => {
    console.log(value, name);
    const uploadCallback = (fileName: string, size: number, url: string) => {
      onChange(name, url);
    };
    return (
      <div style={{ width: '100vw' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {value && <img src={value} style={{ width: '140px', height: '140px' }} />}
          <UploadImg text="选择文件" callback={uploadCallback} />
        </div>
        <div style={{ display: 'flex', width: '100%' }}>图片要求：1. 格式为.png 2.底色透明</div>
      </div>
    );
  };

  const tableColumns = [
    {
      title: '类型',
      dataIndex: 'status',
      key: 'status',
      width: 60,
      render: (row, record, index) => (
        <span>{record.status === '0' ? '预告' : record.status === '1' ? '直播 ' : '回看'}</span>
      ),
    },
    {
      title: '直播sid',
      key: 'sid',
      width: 80,
      dataIndex: 'sid',
    },
    {
      title: '直播间fid',
      key: 'fid',
      width: 80,
      dataIndex: 'fid',
    },
    {
      title: '开播时间',
      key: 'date',
      width: 120,
      dataIndex: 'date',
      render: (row, record, index) => (
        <span>
          {record.startTime &&
            record.startTime.replace(
              /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/,
              '$1-$2-$3 $4:$5:$6',
            )}
        </span>
      ),
    },
    {
      title: '主播昵称',
      key: 'channelName',
      width: 120,
      dataIndex: 'channelName',
      ellipsis: true,
    },
    {
      title: '直播标题',
      key: 'title',
      width: 280,
      dataIndex: 'title',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'caozuo',
      width: 160,
      render: (row, record, index) => (
        <div style={{ display: 'flex', cursor: 'pointer' }}>
          {record.recommend !== '1' ? (
            <div
              style={{ color: '#1890ff', marginRight: '12px' }}
              onClick={() => onRecommandClick(row, record)}
            >
              推荐
            </div>
          ) : (
            <Popconfirm
              placement="rightBottom"
              title={`你确定要${record.pin === '1' ? '取消置顶' : '置顶'}吗？`}
              onConfirm={() => onRemoveRecommandClick(row, record)}
              okText="确认"
              cancelText="取消"
            >
              <div style={{ color: '#1890ff', marginRight: '12px' }}>取消推荐</div>
            </Popconfirm>
          )}
          <Popconfirm
            placement="rightBottom"
            title={`你确定要${record.pin === '1' ? '取消置顶' : '置顶'}吗？`}
            onConfirm={() => onFixClick(row, record)}
            okText="确认"
            cancelText="取消"
          >
            <div style={{ color: '#1890ff', marginRight: '12px' }}>
              {record.pin === '1' ? '取消置顶' : '置顶'}
            </div>
          </Popconfirm>
          <Popconfirm
            placement="rightBottom"
            title={`你确定要${record.undercarriage === '1' ? '下架' : '上架'}吗？`}
            onConfirm={() => onRemoveClick(row, record)}
            okText="确认"
            cancelText="取消"
          >
            <div style={{ color: '#1890ff' }}>{record.undercarriage === '1' ? '下架' : '上架'}</div>
          </Popconfirm>
        </div>
      ),
    },
  ];

  function onChangeForm(activity: any): any {
    console.log(activity, 'activity');
    setFormData(activity);
  }

  function postRecommand(params) {
    api
      .postRecommandLive([params])
      .then(() => {
        getLivesConfig();
        setIsShowAdd(false);
        message.success('推荐设置成功');
      })
      .catch(() => {
        message.error('推荐设置失败');
      });
  }

  function onRecommandSubmit() {
    if (valid.length > 0) {
      return message.warn('请输入必填项');
    }
    const params = {
      ...formData,
      recommendStartTime: `${formData.recommendStartTime
        .replace(/-/g, '')
        .replace(/:/g, '')
        .replace(' ', '')}`,
      recommendEndTime: `${formData.recommendEndTime
        .replace(/-/g, '')
        .replace(/:/g, '')
        .replace(' ', '')}`,
      advanceRecommend: formData.advanceRecommend ? '1' : '0',
      recommend: '1',
    };

    postRecommand(params);
  }

  useEffect(() => {
    getLivesConfig();
  }, []);

  return (
    <div>
      <Table columns={tableColumns} dataSource={listData} />
      <Drawer
        title="设置推荐"
        width={900}
        onClose={() => {
          setIsShowAdd(false);
        }}
        visible={isShowAdd}
        bodyStyle={{ paddingBottom: 80 }}
      >
        {isShowAdd && (
          <div>
            <FormRender
              propsSchema={SCHEMA1}
              onValidate={setValid}
              formData={formData}
              onChange={formData => onChangeForm(formData)}
              displayType="row"
              showDescIcon={true}
              widgets={{ uploadImg: LogoUpload }}
            />
            <div className="u-l-middle" style={{ margin: 20 }}>
              <Popconfirm
                placement="rightBottom"
                title={'你确定要提交么'}
                onConfirm={onRecommandSubmit}
                okText="确认"
                cancelText="取消"
              >
                <Button type="danger">提交</Button>
              </Popconfirm>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default Live;
