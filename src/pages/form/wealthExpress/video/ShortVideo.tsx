import { Table, Popconfirm, InputNumber, Modal, message } from 'antd';
import React, { useState, useEffect } from 'react';
import api from 'api';
import moment from 'moment';

/**
 *
 * @returns 短视频配置，前端分页，后端一次性返回
 */
const ShortVideo = () => {
  const [listData, setListData] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [fixTime, setFixTime] = useState(2);
  const [params, setParams] = useState({});

  const onFixClick = (row, record) => {
    const params = {
      ...record,
      pin: record.pin === '1' ? '0' : '1',
    };
    if (record.pin === '1') {
      api
        .postPinLive([
          {
            ...params,
          },
        ])
        .then(() => {
          message.success('保存成功');
          getLivesConfig();
        });
    } else {
      setIsModalOpen(true);
      setParams(params);
    }
  };
  const onRemoveClick = (row, record) => {
    const params = {
      ...record,
      undercarriage: record.undercarriage === '1' ? '0' : '1',
    };
    api.postPinLive([params]).then(() => {
      message.success('保存成功');
      getLivesConfig();
    });
  };

  const tableColumns = [
    {
      title: '视频ID',
      dataIndex: 'vid',
      key: 'vid',
    },
    {
      title: '作者UID',
      key: 'uid',
      dataIndex: 'uid',
    },
    {
      title: '作家昵称',
      key: 'username',
      dataIndex: 'username',
    },
    {
      title: '发布时间',
      key: 'date',
      dataIndex: 'date',
      render: (row, record, index) => (
        <span>
          {record.createTime &&
            record.createTime.replace(
              /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/,
              '$1-$2-$3 $4:$5:$6',
            )}
        </span>
      ),
    },
    {
      title: '置顶结束时间',
      key: 'pinEndTime',
      dataIndex: 'pinEndTime',
      render: (row, record, index) => (
        <span>
          {record.pinEndTime &&
            record.pinEndTime.replace(
              /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/,
              '$1-$2-$3 $4:$5:$6',
            )}
        </span>
      ),
    },
    {
      title: '视频标题',
      key: 'title',
      dataIndex: 'title',
    },
    {
      title: '操作',
      key: 'caozuo',
      render: (row, record, index) => (
        <div style={{ display: 'flex', cursor: 'pointer' }}>
          <Popconfirm
            placement="rightBottom"
            title={`你确定要${record.pin === '1' ? '取消置顶' : '置顶'}吗？`}
            onConfirm={() => onFixClick(row, record)}
            okText="确认"
            cancelText="取消"
          >
            <div style={{ color: '#1890ff', marginRight: '12px' }}>
              {record.pin === '1' ? '取消置顶' : '置顶'}
            </div>
          </Popconfirm>
          <Popconfirm
            placement="rightBottom"
            title={`你确定要${record.undercarriage === '1' ? '下架' : '上架'}吗？`}
            onConfirm={() => onRemoveClick(row, record)}
            okText="确认"
            cancelText="取消"
          >
            <div style={{ color: '#1890ff' }}>{record.undercarriage === '1' ? '下架' : '上架'}</div>
          </Popconfirm>
        </div>
      ),
    },
  ];

  const getLivesConfig = () => {
    api.getPinLive().then(res => {
      const list = res.data?.wealthV3LiveList || [];
      setListData(list);
    });
  };

  const handleOk = () => {
    setIsModalOpen(false);
    api
      .postPinLive([
        {
          ...params,
          pinStartTime: moment().format('yyyyMMDDHHmmss'),
          pinEndTime: moment()
            .add(fixTime, 'h')
            .format('yyyyMMDDHHmmss'),
        },
      ])
      .then(() => {
        message.success('保存成功');
        getLivesConfig();
      });
  };

  useEffect(() => {
    getLivesConfig();
  }, []);

  return (
    <div>
      <Modal
        title="置顶时长配置"
        visible={isModalOpen}
        onOk={handleOk}
        cancelText="取消"
        okText="确认"
        onCancel={() => setIsModalOpen(false)}
        destroyOnClose={true}
      >
        <div>
          <span>设置置顶时长（小时）</span>
          <InputNumber
            min={1}
            precision={0}
            value={fixTime}
            onChange={value => setFixTime(value)}
            style={{ width: 100, marginBottom: 10, marginLeft: 5 }}
          />
        </div>
      </Modal>
      <Table columns={tableColumns} dataSource={listData} />
    </div>
  );
};

export default ShortVideo;
