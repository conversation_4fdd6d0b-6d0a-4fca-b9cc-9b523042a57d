/*
* 严选好基四期
* <AUTHOR>
* @time 2020.08
*/

import React from 'react';
import { autobind } from 'core-decorators';
import api from 'api';
import { Button, message, Table, Icon } from 'antd';

const { fetchyxorder, postyxorder } = api;


@autobind
class yxorder extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            data: [
                {name: "大盘指数",key:'1',sort:""},
                {name: "行业主题",key:'2',sort:""},
                {name: "牛人牛基",key:'3',sort:""},
                {name: "理财进阶",key:'4',sort:""},
                {name: "闲钱理财",key:'5',sort:""}]
        }
        this.columns = [
            {
                title: '排序',
                dataIndex: 'sort',
                key: 'sort',
                render: (_, record) => (
                    <div>
                        <Icon type="up" onClick={() => this.handleMoveUp(record)} />
                        <Icon type="down" onClick={() => this.handleMoveDown(record)} />
                    </div>
                )
            },
            {
                title: '标签名称',
                dataIndex: 'name',
                key: 'name',
                width: 120
            }
        ]
    }

    componentDidMount() {
        fetchyxorder().then((res) => {    
            if(res.code === '0000') {
                let data = JSON.parse(res.data);
                console.log(data);
                if (!data) {
                    data = [
                        {name: "大盘指数",key:'1',sort:""},
                        {name: "行业主题",key:'2',sort:""},
                        {name: "牛人牛基",key:'3',sort:""},
                        {name: "理财进阶",key:'4',sort:""},
                        {name: "闲钱理财",key:'5',sort:""}];
                }
                this.setState({
                    data: data
                });
            } else {
              message.error(res.status_msg)
            }
        }).catch(e => console.log(e));
    }

    // 处理上移操作
    handleMoveUp = (record) => {
        let _data = this.state.data;
        let index = _data.indexOf(record);
        if (index > 0) {
            this.swapArray(_data, index, index - 1);
            this.setState({data: _data});
        } else {
            message.info("已经在第一位")
        }
    }

    // 处理下移操作
    handleMoveDown = (record) => {
        let _data = this.state.data;
        let index = _data.indexOf(record);
        if (index + 1 < _data.length) {
            this.swapArray(_data, index, index + 1);
            this.setState({data: _data});
        } else {
            message.info("已经在最后一位")
        }
    }

    swapArray = (arr, index1, index2) => {
        arr[index1] = arr.splice(index2, 1, arr[index1])[0];
        return arr;
    }

    handleSave = () => {
        let _data = this.state.data;
        postyxorder({value:JSON.stringify(_data)}).then((res)=>{
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success("保存成功");
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    };
    
    render () {
        return (
            <div>
                <Table
                    dataSource={this.state.data}
                    columns={this.columns}
                    pagination={ false }
                    style={{width: 240}}
                />
                <Button onClick={this.handleSave}>发布</Button>
            </div>
        )
    }

}

export default yxorder;
