/*
* 严选好基四期
* <AUTHOR>
* @time 2021.11
*/

import React from 'react';
import { autobind } from 'core-decorators';
import FormRender from 'form-render/lib/antd';
import FORM_JSON from './form.json';
import api from 'api';
import { Button, message } from 'antd';

const { fetchhwtz, posthwtz } = api;

const schema = FORM_JSON.propsSchema;
const formData = FORM_JSON.formData;

@autobind
class hwtz extends React.Component {
    constructor(props) {
       super(props);
       this.state = {
           formData: formData
       } 
    }

    componentDidMount() {
        fetchhwtz().then((res) => {    
            if(res.code === '0000') {
                let data = JSON.parse(res.data);
                console.log(data);
                this.setState({
                    formData: data
                });
            } else {
              message.error(res.status_msg)
            }
        }).catch(e => console.log(e));
    }

    handleChange = formData => {
        this.setState({formData});
    };

    handleSave = () => {
        let _data = this.state.formData;
        posthwtz({value:JSON.stringify(_data)}).then((res)=>{
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success("保存成功");
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    };
    
    render () {
        return (
            <div>
                <FormRender
                    propsSchema={schema}
                    formData={this.state.formData || {}}
                    onChange={this.handleChange}
                    showDescIcon={true}
                    displayType="row"
                />
                <Button onClick={this.handleSave}>发布</Button>
            </div>
        )
    }

}

export default hwtz;
