import React, { useState, useEffect } from 'react';
import Form<PERSON><PERSON> from 'form-render/lib/antd';
import api from 'api';
import { Button, Popconfirm, message, Tabs, Input} from 'antd';
import FORM_JSON from './form.json';

const { TabPane } = Tabs;
const { fetchAssetAllocation, postAssetAllocation } = api;
const fundTypes = [{
    name: '偏股类',
    type: 'stock'
}, {
    name: '偏债类',
    type: 'debt'
}, {
    name: '现金类',
    type: 'gold'
}, {
    name: '其他类',
    type: 'other'
}]
export default function () {
    const [init, setInit] = useState(false);
    const [formData, setFormData] = useState<any>({});
    const [valid, setValid] = useState([]);
    const [threshold, setThreshold] = useState('');

    useEffect(() => {
        let dataJSON = {
            stock: {
                assetList: []
            },
            debt: {
                assetList: []
            },
            gold: {
                assetList: []
            },
            other: {
                assetList: []
            },
        }
        fetchAssetAllocation().then((res: any) => {
            let { code, data } = res;
            if ( code === '0000' &&　data ) {
                data = JSON.parse(data);
                setThreshold(data.threshold);
                dataJSON = data.formData;
            }
            setFormData(dataJSON);
            setInit(true);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, []);
    const updateForm = () => {
        // valid 是校验判断的数组，valid 长度为 0 代表校验全部通过
        if (valid.length > 0) {
            alert(`校验未通过字段：${valid.toString()}`);
        } else {
            let obj = {
                threshold,
                formData
            }
            postAssetAllocation({
                value: JSON.stringify(obj),
            }).then((res: any) => {
                if (res.code !== '0000') {
                    message.error(res.msg);
                } else {
                    message.success('提交成功！');
                }
            }).catch((e: Error) => {
                message.error(e.message);
            });
        } 
    };
    const changeFormData = (value: any, type: string) => {
        let data = JSON.parse(JSON.stringify(formData));
        data[type] = value;
        setFormData(data);
    } 
    const saveInput = (event: React.ChangeEvent<HTMLInputElement>) => {
        let value = (event.target as HTMLInputElement).value;
        setThreshold(value);
    }
    if (!init) return '加载中';
    return (
        <div style={{padding: 60}}>
            <section className={'g-mb20'}>
                <h1 className={'g-fs24'}>配置方案与用户持仓的差距阈值</h1>
                <h3>当用户在某个资产大类上的持仓比例和建议比例之间的差值（绝对值）大于该阈值时，会推荐对应类别的基金产品。</h3>
                <div>
                    阈值<Input 
                            type="number" 
                            style={{width: '70px', margin: '0 10px'}} 
                            value={threshold} 
                            onChange={saveInput} 
                        />个百分点
                </div>
            </section>
            <section>
                <h1 className={'g-fs24'}>产品推荐名单</h1>
                <Tabs defaultActiveKey="1" type="card">
                    { fundTypes.map((item) => (
                        <TabPane tab={item.name} key={item.type}>
                            <FormRender 
                                propsSchema={FORM_JSON.propsSchema}
                                formData={formData[item.type]}
                                onChange={(value) => changeFormData(value, item.type)}
                                onValidate={setValid}
                                displayType="column"
                                showDescIcon={true}
                            />
                        </TabPane>
                    )) }
                </Tabs>
            </section>
            <Popconfirm
                placement="rightBottom"
                title={'你确认要更新么'}
                onConfirm={updateForm}
                okText="确认"
                cancelText="取消"
            >
                <Button
                    type="primary" 
                >
                    提交
                </Button>
            </Popconfirm>
        </div>
    )
}