{"propsSchema": {"type": "object", "properties": {"assetList": {"title": "", "type": "array", "items": {"type": "object", "properties": {"fundCode": {"title": "基金代码", "type": "string", "ui:width": "15%"}, "productText": {"title": "产品推荐话术", "type": "string", "ui:width": "35%"}, "marketLabel": {"title": "营销标签", "type": "string", "ui:width": "15%"}, "robotText": {"title": "机器人话术", "type": "string", "format": "textarea", "ui:width": "35%"}}, "required": ["fundCode", "productText", "marketLabel", "robotText"]}}}}, "formData": {"assetList": []}}