import React, { useState, useEffect } from 'react';
import FormRender from 'form-render/lib/antd';
import api from 'api';
import { Button, Popconfirm, message } from 'antd';
import FORM_JSON from './form.json';
import uploadImg from './uploadImg';

const { fetchRevenueTarget, postRevenueTarget } = api;

export default function () {
    const [init, setInit] = useState(false);
    const [formData, setFormData] = useState<any>({});
    const [valid, setValid] = useState([]);
    
    useEffect(() => {
        let dataJSON = FORM_JSON;
        fetchRevenueTarget().then((res: any) => {
            let { code, data } = res;
            if ( code === '0000' &&　data ) {
                data = JSON.parse(data);
                dataJSON.formData = {
                    positionEntry: data.positionEntry || [],
                    revenueObj: data.revenueObj || {}
                }
            }
            setFormData(dataJSON.formData);
            setInit(true);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, []);

    const updateForm = () => {
        // valid 是校验判断的数组，valid 长度为 0 代表校验全部通过
        if (valid.length > 0) {
            alert(`校验未通过字段：${valid.toString()}`);
        } else {
            console.log(formData);
            let arr = [], len = formData.positionEntry?.length;
            for (let i = 0; i < len; i++) {
                let obj:any = formData.positionEntry[i];
                if (obj.rangeStart >= obj.rangeEnd) {
                    message.error('目标范围开始值应小于目标范围结束值');
                    return;
                }
                arr.push([obj.rangeStar, obj.rangeEnd]);
            }
            if (len > 1) {
                for (let i = 0; i < len - 1; i++) {
                    for (let j = i + 1; j < len; j++) {
                        let start1 = formData.positionEntry[i].rangeStart;
                        let start2 = formData.positionEntry[j].rangeStart;
                        let end1 = formData.positionEntry[i].rangeEnd;
                        let end2 = formData.positionEntry[j].rangeEnd;
                        if (start1 < start2) {
                            if (end1 > start2) {
                                message.error('目标配置范围不能重叠');
                                return;
                            }
                        } else if (start1 === start2) {
                            message.error('目标配置范围不能重叠');
                            return;
                        } else {
                            if (start1 < end2) {
                                message.error('目标配置范围不能重叠');
                                return;
                            }
                        }
                    }
                }
            }
            
            postRevenueTarget({
                value: JSON.stringify(formData),
            }).then((res: any) => {
                if (res.code !== '0000') {
                    message.error(res.msg);
                } else {
                    message.success('提交成功！');
                }
            }).catch((e: Error) => {
                message.error(e.message);
            });
        } 
    };
    if (!init) return '加载中';
    
    return (
        <div style={{padding: 60}}>
            <h1 className={'g-fs24'}>配置说明：目标范围限制左闭右开，同时每个目标配置范围不能重叠。</h1>
            <FormRender 
                propsSchema={FORM_JSON.propsSchema}
                formData={formData}
                onChange={setFormData}
                onValidate={setValid}
                displayType="row"
                showDescIcon={true}
                widgets={{uploadImg: uploadImg}}
            />
            <footer>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确认要提交么'}
                    onConfirm={updateForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="primary" 
                    >
                        提交
                    </Button>
                </Popconfirm>
                
            </footer>
        </div>
    )
}