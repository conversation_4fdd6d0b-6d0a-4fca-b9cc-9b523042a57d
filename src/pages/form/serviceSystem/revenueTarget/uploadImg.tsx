import React from 'react';
import ImgUpload from '../../../frontend/compoment/uploadImg/index.jsx';

interface dataProps {
    name: string,
    onChange: Function,
    value: string
}

export default function (props:dataProps) {

    return <div>
                <ImgUpload 
                        handleChange={(value: any) => props.onChange('img', value)}
                        imageUrl={props.value}
                        // size={['108px*108px']}
                        limit={0.2}
                        isEdit={true}
                        title=''
                    />

            </div>

}