import React, { useState, useEffect } from 'react';
import FORM_JSON from './form.json';
import FormRender from 'form-render/lib/antd';
import { Button, message } from 'antd';
import api from 'api';
import Index from '../cooperateFund';
const { fetchZxFundRecomConfig, postZxFundRecomConfig } = api;
const SUCCESS_CODE = '0000';
const schema = FORM_JSON.schema;

export default function() {
  const [formData, setFormData] = useState({ list: [], product: {} });
  const [valid, setValid] = useState([]);
  useEffect(() => {
    fetchZxFundRecomConfig().then((res: any) => {
      if (res.code === SUCCESS_CODE) {
        let data = JSON.parse(res.data);
        setFormData(data);
      } else {
        message.error(res.status_msg);
      }
    });
  }, []);

  const handleChange = (formData: any) => {
    setFormData(formData);
  };

  const handlePost = () => {
    if (valid.length > 0) {
      message.error(`校验未通过字段：${valid.toString()}`);
      return;
    }
    postZxFundRecomConfig({ value: JSON.stringify(formData) }).then((res: any) => {
      try {
        if (res.code === SUCCESS_CODE) {
          message.success('保存成功！');
        } else {
          message.error(res.message);
        }
      } catch (e) {
        message.error(e);
      }
    });
  };

  return (
    <>
      <FormRender
        propsSchema={schema}
        formData={formData}
        displayType="row"
        labelWidth="120px"
        onChange={handleChange}
        onValidate={setValid}
        showDescIcon
      ></FormRender>
      <Button type="primary" onClick={handlePost}>
        发布
      </Button>
    </>
  );
}
