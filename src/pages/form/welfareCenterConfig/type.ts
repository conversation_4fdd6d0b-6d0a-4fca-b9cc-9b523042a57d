import { fundTarget } from './type';
import { fundRecommendHelper } from './../findChanceV2/components/FundRecommend/helper';
import { EnumType } from './../holdShareList/companyAndMarketing/types';
export type ConfigData = {
  id: string; //当前配置项的id
  pageTitle: pageTitle;
  moreRank: moreRank[];
  couponGrant?: couponGrant;
  fundTarget: fundTarget[];
  whetherGrant: boolean;
  changeProfit: changeProfit;
};
export type pageTitle = {
  strategyName: string;
};
export type changeProfit = {
  changeProfitLink: string;
};
export type moreRank = {
  mianTitle: string;
  introduceText: string;
  backImg: string;
  jumpLink: string;
};
// export type ConfigData = {
//   pageTitle: pageTitle;
// };
export type couponGrant = {
  couponId?: string;
  knowledge?: string;
  // equityLink?: string;
  couponValidity?: string;
  whetherValidity?: boolean;
  whetherOpenKyc?: boolean;
  startTime?: string;
  endTime?: string;
  kycConfig?: {
    kycFilterId?: string;
    couponId?: string;
    knowledge?: string;
    // equityLink?: string;
    couponValidity?: string;
    whetherValidity?: boolean;
    startTime?: string;
    endTime?: string;
  };
};
export enum LightSpotType {
  POSITION_PEOPLE = '1',
  LIGHT_TAG = '2',
}
export type lightSpotPos = {
  peopleCoefficient: string;
};
export type lightSpotCustom = {
  customTag: string;
};
export enum displayStyleType {
  PROFIT = '1',
  CUSTOM_TEXT = '2',
}
export type displayProfit = {
  profitArea: string;
  spot: string;
  fundRecommendText: string;
};
export type displayCustom = {
  mainTitle: string;
  subTitle: string;
};
export type fundTarget = {
  fundId: string;
  fundName: string;
  fundTag1: string;
  fundTag2?: string;
  lightSpotTagType: LightSpotType;
  lightSpot: lightSpotPos | lightSpotCustom;
  displayStyleType: displayStyleType;
  displayStyle: displayCustom | displayProfit;
  fundJumpLink: string;
  fundButtonText: string;
  fundRecommendImg?: string;
};
