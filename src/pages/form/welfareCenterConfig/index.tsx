import React, { useState, useEffect, FC, } from 'react';
import classnames from 'classnames';

import { But<PERSON>, Collapse, message, Spin } from 'antd';
import ConfigItem from './components/ConfigItem';
import { ConfigData } from './type';
import { configItemhelper } from './components/configItemhelper';
const { Panel } = Collapse;
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import DragAndSortWrapper from './components/DragAndSortWrapper';
import api from "api";
// import { changeZiXunToHotSpot, changeZiXunToInfo, HOTSPOT_KEY, KEY, setRandomId } from './utils';
// import HotSpotPool from './hotSpotPool';
import { TopicFundContext } from './context'
import { HOTSPOT_KEY, KEY, setRandomId } from './utils';

const { postHash, fetchHashAll, postHashDel, getTopicFunds } = api


export interface IProps { }
const welfareCenterConfig: FC<IProps> = () => {
  // const [baseConfigDataList,setBaseConfigDataList]=useState<ConfigData[]>([]);
  const [configDataList, setConfigDataList] = useState<any[]>([]);

  const [isSavedKey, setIsSavedKey] = useState<string[]>([]);
  const [isDelKey, setIsDelKey] = useState<string[]>([]);
  const [poolDataAll, setPoolDataAll] = useState({});
  const [topicFundList, setTopicFundList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [init, setInit] = useState(false);
  // 初始化列表
  const getConfigDataList = async () => {
    try {
      setInit(false);
      const res = await fetchHashAll({ key: KEY })
      if (res.code !== "0000") {
        throw new Error(res.message)
      }
      const { data: configList } = res;
      const list = [];
      for (const key in configList) {
        const normalData = JSON.parse(configList[key]);
        // 尽量防止ID重复生成
        setRandomId(normalData.id)
        list.push(normalData)
      }
      setConfigDataList(list);
      setInit(true);
    } catch (e) {
      message.error(e.message || "未知异常")
    }
  }

  const addSavedKey = (keys: string[]) => {
    const savedKeySet = new Set(isSavedKey);
    for (const i in keys) {
      const keyStr = keys[i];
      console.log(keyStr)
      savedKeySet.add(keyStr)
    }

    setIsSavedKey([...savedKeySet])
  }
  const removeSavedKey = (keys: string[]) => {
    const savedKeySet = new Set(isSavedKey);
    for (const i in keys) {
      const keyStr = keys[i];
      savedKeySet.delete(keyStr)
    }
    setIsSavedKey([...savedKeySet])
  }
  const addDelKey = (keys: string[]) => {
    const delKeySet = new Set(isDelKey);
    for (const i in keys) {
      const keyStr = keys[i];
      delKeySet.add(keyStr)
    }
    setIsDelKey([...delKeySet])
    removeSavedKey(keys)
  }

  const handleChange = (index, v) => {
    const newData = [...configDataList]
    newData[index] = v;
    newData[index].index = index;
    setConfigDataList(newData);
    addSavedKey([v.id])
  }
  const handleDel = (index) => {
    const newData = [...configDataList];
    const targetItem = newData[index]
    newData.splice(index, 1);
    newData.forEach((item, index) => {
      item.index = index
    })
    addDelKey([targetItem.id])
    setConfigDataList(newData);
  }
  const onPublish = async () => {
    console.log(isSavedKey, isDelKey)
    //处理保存
    setLoading(true);
    try {
      for (const i in isSavedKey) {
        const propName = isSavedKey[i]
        const value = configDataList.find((item) => item.id === propName)
        configItemhelper.checkData(value)
        if (!value) {
          continue;
        }
        const res = await postHash({ key: KEY, propName, value: JSON.stringify(value) })
        if (res.code === "0000") {
          message.success(`第${value.index + 1}项发布成功，id=${value.id}`)
        } else {
          throw new Error(res.message)
        }
      }
    } catch (e) {
      message.error(e.message || "未知错误")
    } finally {
      setLoading(false);
    }
    setLoading(true);
    //处理删除
    try {
      for (const i in isDelKey) {
        const propName = isDelKey[i]
        // const value=configDataList.find((item)=>item.id===propName)
        if (!propName) {
          return;
        }
        if (poolDataAll[propName]) {
          const res = await postHashDel({ key: HOTSPOT_KEY, propName })
          if (res.code !== "0000") {
            throw new Error(`id:${propName},热点池数据销毁失败`)
          }
        }
        const res = await postHashDel({ key: KEY, propName })
        if (res.code === "0000") {
          message.success(`删除成功，id=${propName}`)
        } else {
          throw new Error(res.message)
        }
      }
    } catch (e) {
      message.error(e.message || "未知错误")
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    getConfigDataList();
  }, [])
  return (
    <div>
      <Spin spinning={loading}>
        <TopicFundContext.Provider value={topicFundList}>
          <Button onClick={onPublish} disabled={!init} type={'primary'} style={{ marginBottom: '12px' }}>
            发布
          </Button>

          <Collapse>
            {configDataList?.map((item, index) => {
              return <Panel
                key={item.id}
                header={<div style={{ display: 'flex', justifyContent: "space-between" }}>
                  <div className='u-l-middle'>
                    <span>策略名称：{item.pageTitle.strategyName}</span>
                    <span style={{ marginLeft: '20px' }}>(正式环境地址：https://fund.10jqka.com.cn/fefund/welfareCenter/ifundapp_app/public/index.html?id={item.id})</span>
                  </div>
                </div>}
              >
                <ConfigItem baseData={item} onSave={(v) => handleChange(index, v)} onDel={() => handleDel(index)}></ConfigItem>
              </Panel>
            })}
          </Collapse>


          <Button
            disabled={!init}
            type={'primary'}
            style={{ marginTop: '12px' }}
            onClick={() => {
              setConfigDataList([...configDataList, configItemhelper.addInit(configDataList.length)]);
            }}
          >
            新增
          </Button>
        </TopicFundContext.Provider>
        {/* <HotSpotPool/> */}
      </Spin>
    </div>
  );
};
export default welfareCenterConfig;
