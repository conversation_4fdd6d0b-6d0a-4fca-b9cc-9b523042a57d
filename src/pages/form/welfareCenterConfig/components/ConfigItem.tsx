import React, { useState, useEffect, FC } from 'react';
import moment from 'moment';
import { Button, Input, message, Popconfirm, DatePicker, Tabs, BackTop, Radio } from 'antd';

import { ConfigData } from '../type';
import InputTitle from './InputTitle';
import SplitArea from './SplitArea';
import { configItemhelper, mapPropToName } from './configItemhelper';
import api from 'api';
import { HOTSPOT_KEY, mergePoolToBase } from '../utils';
import WelfareGrant from './WelfareGrant';
import WelfareUse from './WelfareUse';
import PageTitle from './PageTitle';
import MoreRank from './moreRank';
import FundTarget from './FundTargets';
import CouponGrant from './CouponGrant';
import RadioGroup from 'antd/lib/radio/group';
import ChangeProfit from './ChangeProfit';
const { postHashDel } = api;
export interface IProps {
  baseData: ConfigData;
  basePool?: {
    hotspots: any[];
    relateInfos: any[];
  };
  onSave: () => void;
  onDel: () => void;
}
const { TabPane } = Tabs;
const ConfigItem: FC<IProps> = ({ onSave, baseData, onDel }) => {
  const [isEdit, setIsEdit] = useState(false);
  const [data, setData] = useState<ConfigData>(baseData);
  const [tabKey, setTabKey] = useState('1');
  const [whetherGrant, setWhetherGrant] = useState(false);
  const handleChange = (name, value) => {
    setData({
      ...data,
      [name]: value,
    });
  };
  return (
    <div>
      <div
        style={{
          backgroundColor: '#95badd',
          height: '44px',
          display: 'flex',
          justifyContent: 'flex-end',
          alignItems: 'center',
          marginBottom: '12px',
        }}
      >
        {!isEdit && (
          <Button
            onClick={() => {
              setIsEdit(true);
            }}
          >
            编辑
          </Button>
        )}
        {isEdit && (
          <Button
            type={'primary'}
            onClick={() => {
              try {
                // data.hotspots=data.hotspots?.filter((item,index)=>index<3)||[]
                // data.infomation=data.infomation?.filter((item,index)=>index<5)||[]
                configItemhelper.checkData(data);
                onSave(data);
                setIsEdit(false);
              } catch (e) {
                message.error(`请确认字段-${e.message}`);
              }
            }}
          >
            保存
          </Button>
        )}
        <Popconfirm
          title={'请确认是否删除'}
          okText={'确认'}
          cancelText={'取消'}
          onConfirm={() => onDel(data.index)}
        >
          <Button style={{ marginLeft: '12px' }} type={'primary'} danger="true">
            删除
          </Button>
        </Popconfirm>
      </div>
      <PageTitle
        disabled={!isEdit}
        data={data.pageTitle}
        onChange={v => handleChange('pageTitle', v)}
      />
      <SplitArea />
      <div style={{ marginBottom: 10, width: 400 }}>
        <InputTitle title={'是否发放优惠券'} />
        <Radio.Group
          disabled={!isEdit}
          style={{ display: 'inline' }}
          onChange={e => {
            handleChange('whetherGrant', e.target.value);
          }}
          value={data.whetherGrant}
        >
          <Radio value={true}>开启</Radio>
          <Radio value={false}>关闭</Radio>
        </Radio.Group>
      </div>

      {data.whetherGrant && (
        <>
          <CouponGrant
            disabled={!isEdit}
            data={data.couponGrant}
            onChange={v => handleChange('couponGrant', v)}
          />
          <SplitArea />
        </>
      )}
      <SplitArea />
      <ChangeProfit
        data={data.changeProfit}
        disabled={!isEdit}
        onChange={v => handleChange('changeProfit', v)}
      />
      <FundTarget
        disabled={!isEdit}
        data={data.fundTarget}
        onChange={v => handleChange('fundTarget', v)}
      />
      <SplitArea />

      <MoreRank
        disabled={!isEdit}
        data={data.moreRank}
        onChange={v => handleChange('moreRank', v)}
      />
      <BackTop />
    </div>
  );
};
export default ConfigItem;
