import React, { useState, useEffect, FC } from 'react';

export default function InputTitle({
  title,
  isRequired = true,
}: {
  title: string;
  isRequired?: boolean;
}) {
  return (
    <>
      {isRequired && (
        <span
          style={{
            color: '#ff330a',
            fontSize: 14,
            fontFamily: 'SimSun, sans-serif',
            margin: '1px 4px 0 0',
          }}
        >
          *
        </span>
      )}
      {title}：
    </>
  );
}
