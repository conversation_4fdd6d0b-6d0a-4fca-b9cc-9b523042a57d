import React, { FC, useState } from 'react';
import { Button, Collapse, message, Spin, Card } from 'antd';
import { GrantConfigData } from '../type';
import PageTitle from './PageTitle';
const { Panel } = Collapse;
export interface IProps {
  item:GrantConfigData
  onSave: (item:GrantConfigData) => void;
}
const WelfareGrant: FC<IProps> = ({item,onSave}) => {
  const [isEdit, setIsEdit] = useState(false);
  const [data, setData] = useState<GrantConfigData>({})
  const handleChange = (name, value) => {
    setData({
      ...data,
      [name]: value,
    });
  };
  return (
    <Card
      title={`策略名称：${item?.pageTitle?.strategyName || ''}`}
    >        {!isEdit && (
      <Button
        onClick={() => {
          setIsEdit(true);
        }}
      >
        编辑
      </Button>
    )}
      {isEdit && (
        <Button
          type={'primary'}
          onClick={() => {
            try {
              onSave(data);
              setIsEdit(false);
            } catch (e) {
              message.error(`请确认字段-${e.message}`);
            }
          }}
        >
          保存
        </Button>
      )}
      {/* <PageTitle /> */}
    </Card>
  );
};
export default WelfareGrant;
