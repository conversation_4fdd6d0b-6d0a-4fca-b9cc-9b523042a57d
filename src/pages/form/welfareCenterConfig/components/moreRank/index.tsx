import { But<PERSON>, Card, Input, Popconfirm } from 'antd';
import React, { FC } from 'react';
import { DndProvider } from 'react-dnd';
import { ConfigData, GrantConfigData } from '../../type';
import InputTitle from '../InputTitle';
import UploadImg from '../UploadImg';
import { mapPropToName, moreRankHelper } from './helper';
import { HTML5Backend } from 'react-dnd-html5-backend';
import styles from './index.less';
import DragAndSortWrapper from '../DragAndSortWrapper';
export interface IProps {
  data: ConfigData['moreRank'];
  onChange: (data: ConfigData['moreRank']) => void;
  disabled: boolean;
}
const MoreRank: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleChange = (index, name, value) => {
    const newData = [...data];
    newData[index][name] = value;
    onChange(newData);
  };
  const handleAdd = () => {
    onChange([...data, moreRankHelper.addArrayData(),]);
  };
  const handleDel = index => {
    const newData = [...data];
    console.log('index',index)
    newData.splice(index, 1);
    onChange(newData);
  };
  const changePosition = (dragIndex, hoverIndex) => {
    let dragItem=data[dragIndex];
    let hoverItem=data[hoverIndex];
    if(!dragItem||!hoverItem){
      return;
    }
    //hoverIndex后面的都往后移动一位
  
    let newData=[...data];
    //先去掉在数组中被拖动的数据
    newData.splice(dragIndex,1);
    console.log('newData',newData);
    //获取放置位置以及后面的数据
    const postfixArr=newData.slice(hoverIndex)||[];
    const prefixArr=newData.slice(0,hoverIndex+1)||[];
    prefixArr[hoverIndex]=dragItem;
    const changeIndexKeys=[];
    console.log(prefixArr,postfixArr)
    newData=[...prefixArr,...postfixArr].filter(item=>Boolean(item)).map((item,index)=>{
    
      if(item.index!==index){
        item.index=index;
        changeIndexKeys.push(item.id)
      }
      return item;
    })
    onChange(newData);
  };
  return (
    <section>
      <div style={{}}>
        <h3>更多榜单</h3>
        <DndProvider backend={HTML5Backend}>
          {data?.map((item, index) => {
            return (<Card style={{ width: '400px', display: "inline-block", marginLeft: '20px' }}  key={index}>
              <DragAndSortWrapper
                changePosition={changePosition}
                id={index}
                key={index}
                index={index}
              >
                <Button disabled={disabled} style={{ marginBottom: '12px' }} type="dashed">
                  拖拽移动
                </Button>
              </DragAndSortWrapper>
              <Input
                value={item.mianTitle}
                onChange={e => handleChange(index, 'mianTitle', e.target.value)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
                addonBefore={<InputTitle title={mapPropToName['mianTitle']} />}
              />
              <Input
                value={item.introduceText}
                onChange={e => handleChange(index, 'introduceText', e.target.value)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
                addonBefore={<InputTitle title={mapPropToName['introduceText']} />}
              />
              <Input
                value={item.jumpLink}
                onChange={e => handleChange(index, 'jumpLink', e.target.value)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
                addonBefore={<InputTitle title={mapPropToName['jumpLink']} />}
              />
              <UploadImg
                style={{ width: '400px' }}
                disabled={disabled}
                value={item.backImg}
                onChange={v => handleChange(index, 'backImg', v)}
                title={mapPropToName['backImg']}
              ></UploadImg>
              <Popconfirm disabled={disabled} title={'请确认是否删除'} okText={'确认'} cancelText={'取消'}
                onConfirm={() => handleDel(index)}
              >
                <Button disabled={disabled} style={{ marginTop: '4px' }} size="small">
                  删除
                </Button>
              </Popconfirm>
            </Card>)
          })}
        </DndProvider>
      </div>
      <div>
        <Button disabled={disabled} onClick={handleAdd} style={{ marginTop: '4px' }} size="small">
          新增榜单
        </Button>
      </div>
    </section>
  );
};
export default MoreRank;
