import { moreRank, pageTitle } from './../../type';
import { checkRequiredAndThrow } from '../../utils';
export const mapPropToName = {
  mianTitle: '主标题',
  introduceText: '介绍文字',
  backImg: '背景图片',
  jumpLink: '跳转链接',
};
export const moreRankHelper = {
  addInit: function(): moreRank[] {
    return [];
  },
  addArrayData: function(): moreRank {
    return {
      mianTitle: '',
      introduceText: '',
      backImg: '',
      jumpLink: '',
    };
  },
  checkData: function(data) {
    try {
      // if (data.open) {
      //   checkRequiredAndThrow(data.img, mapPropToName['img']);
      //   checkRequiredAndThrow(data.name, mapPropToName['name']);
      //   checkRequiredAndThrow(data.link, mapPropToName['link']);
      // }
      // if (data.startTime && data.endTime && data.startTime > data.endTime) {
      //   throw new Error('开始时间应小于结束时间');
      // }
    } catch (e) {
      throw new Error(`首屏直播卡-${e.message}`);
    }
    return true;
  },
};
