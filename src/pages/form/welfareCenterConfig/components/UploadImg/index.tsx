import React from 'react';
// @ts-ignore
import ImgUpload from '../UploadImg/uploadImg/index.jsx';
interface dataProps {
  onChange: Function;
  value: string;
  size?: string[];
  title: string;
  disabled: boolean;
  isRequired?: boolean;
  style?: any;
}

export default function UploadImg({
  onChange,
  value,
  size,
  title,
  disabled,
  isRequired = true,
  style,
}: dataProps) {
  return (
    <div style={style}>
      <ImgUpload
        handleChange={(value: any) => onChange(value)}
        imageUrl={value}
        isEdit={!disabled}
        title={title}
        size={size}
        isRequired={isRequired}
      />
    </div>
  );
}
