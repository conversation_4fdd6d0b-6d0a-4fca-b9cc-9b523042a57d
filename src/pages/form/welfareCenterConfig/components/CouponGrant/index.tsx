import { But<PERSON>, Card, DatePicker, Input, Radio } from 'antd';
import moment from 'moment';
import React, { FC, useState } from 'react';
import { ConfigData, couponGrant, GrantConfigData } from '../../type';
import InputTitle from '../InputTitle';
import UserIdList from './components/userIdList';
import styles from './index.less';
export interface IProps {
  data: ConfigData['couponGrant'];
  onChange: (data: ConfigData['couponGrant']) => void;
  disabled: boolean;
}
const CouponGrant: FC<IProps> = ({ data, onChange, disabled }) => {
  const [whetherValidity, setWhetherValidity] = useState(false);
  const [whetherKycValidity, setWhetherKycValidity] = useState(false);
  const [whetherOpenKyc, setWhetherOpenKyc] = useState(data?.whetherOpenKyc);
  //指定用户
  const [filterId, setFilterId] = useState(data?.kycConfig?.kycFilterId || '')
  const [isUserEdit, setIsUserEdit] = useState(false)
  const handleChange = (name, value) => {
    onChange({
      ...data,
      [name]: value,
    });
  };
  const handleKycChange = (name, value) => {
    const newData = { ...data };
    newData.kycConfig = {
      ...newData.kycConfig,
      [name]: value,
    };
    onChange(newData);
  };
  return (
    <section>
      <div>
        <h3>优惠券发放(不发放则不填)</h3>
        <Input
          value={data?.couponId}
          onChange={e => handleChange('couponId', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title={'优惠券ID'} />}
          addonAfter={<InputTitle title={'多个ID则按英文逗号间隔'} isRequired={false} />}
        />
        <div style={{ marginBottom: 10, width: 400 }}>
          <InputTitle title={'开启优惠券有效期'} />
          <Radio.Group
            disabled={disabled}
            style={{ display: 'inline' }}
            onChange={e => {
              setWhetherValidity(e.target.value);
              handleChange('whetherValidity', e.target.value);
            }}
            value={data?.whetherValidity}
          >
            <Radio value={true}>开启</Radio>
            <Radio value={false}>关闭</Radio>
          </Radio.Group>
        </div>

        {data?.whetherValidity && <Input
          value={data?.couponValidity}
          onChange={e => handleChange('couponValidity', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title={'优惠券有效期'} />}
          addonAfter={<div><InputTitle title={'h'} isRequired={false} /><Button>保存</Button></div>}
        />}
        <Input
          value={data?.knowledge}
          onChange={e => handleChange('knowledge', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title={'理财小知识文描'} />}
        />
        <div style={{ marginBottom: 10, width: 400 }}>
          <InputTitle title={'开启条件匹配'} />
          <Radio.Group
            disabled={disabled}
            style={{ display: 'inline' }}
            onChange={e => {
              setWhetherOpenKyc(e.target.value);
              handleChange('whetherOpenKyc', e.target.value);
            }}
            value={data?.whetherOpenKyc}
          >
            <Radio value={true}>开启</Radio>
            <Radio value={false}>关闭</Radio>
          </Radio.Group>
        </div>
        {data?.whetherOpenKyc && <Card>
          <UserIdList
            filterId={filterId}
            setFilterId={(id) => {
              setFilterId(id)
              handleKycChange('kycFilterId', id)
            }}
            isAdd={false}
            isEdit={data?.whetherOpenKyc&&!disabled || false}
            setUserTypeData={() => { }}
            isCanEdit={isUserEdit}
            setIsCanEdit={setIsUserEdit}
          />
          <Input
            value={data?.kycConfig?.couponId}
            onChange={e => handleKycChange('couponId', e.target.value)}
            disabled={disabled}
            style={{ marginBottom: 10 }}
            addonBefore={<InputTitle title={'条件匹配优惠券ID'} />}
            addonAfter={<InputTitle title={'多个ID则按英文逗号间隔'} isRequired={false} />}
          />
          <div style={{ marginBottom: 10, width: 400 }}>
            <InputTitle title={'开启条件匹配优惠券有效期'} />
            <Radio.Group
              disabled={disabled}
              style={{ display: 'inline' }}
              onChange={e => {
                // setWhetherKycValidity(e.target.value);
                handleKycChange('whetherValidity', e.target.value);
              }}
              value={data?.kycConfig?.whetherValidity}
            >
              <Radio value={true}>开启</Radio>
              <Radio value={false}>关闭</Radio>
            </Radio.Group>
          </div>

          {data?.kycConfig?.whetherValidity && <Input
            value={data?.kycConfig?.couponValidity}
            onChange={e => handleKycChange('couponValidity', e.target.value)}
            disabled={disabled}
            style={{ marginBottom: 10 }}
            addonBefore={<InputTitle title={'条件匹配优惠券有效期'} />}
            addonAfter={<div><InputTitle title={'h'} isRequired={false} /><Button>保存</Button></div>}
          />}
          <Input
            value={data?.kycConfig?.knowledge}
            onChange={e => handleKycChange('knowledge', e.target.value)}
            disabled={disabled}
            style={{ marginBottom: 10 }}
            addonBefore={<InputTitle title={'条件匹配理财小知识文描'} />}
          />
          <div style={{ marginBottom: 10, width: 300 }}>
            <InputTitle title={'优惠券发放开始时间'} />
            <DatePicker
              value={data?.kycConfig?.startTime ? moment(data?.kycConfig?.startTime) : ''}
              onChange={(date, dateString) => handleKycChange('startTime', dateString)}
              showTime
              disabled={disabled}
              placeholder={'请选择日期'}
            />
          </div>
          <div style={{ marginBottom: 10, width: 300 }}>
            <InputTitle title={'优惠券发放结束时间'} />
            <DatePicker
              value={data?.kycConfig?.endTime ? moment(data?.kycConfig?.endTime) : ''}
              onChange={(date, dateString) => handleKycChange('endTime', dateString)}
              showTime
              disabled={disabled}
              placeholder={'请选择日期'}
            />
          </div>
        </Card>}

        {/* <Input
          value={data?.equityLink}
          onChange={e => handleChange('equityLink', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title={'可兑换其他权益跳转链接'}  />}
        /> */}
        <div style={{ marginBottom: 10, width: 300 }}>
          <InputTitle title={'优惠券发放开始时间'} />
          <DatePicker
            value={data?.startTime ? moment(data?.startTime) : ''}
            onChange={(date, dateString) => handleChange('startTime', dateString)}
            showTime
            disabled={disabled}
            placeholder={'请选择日期'}
          />
        </div>
        <div style={{ marginBottom: 10, width: 300 }}>
          <InputTitle title={'优惠券发放结束时间'} />
          <DatePicker
            value={data?.endTime ? moment(data?.endTime) : ''}
            onChange={(date, dateString) => handleChange('endTime', dateString)}
            showTime
            disabled={disabled}
            placeholder={'请选择日期'}
          />
        </div>
      </div>
    </section>
  );
};
export default CouponGrant;
