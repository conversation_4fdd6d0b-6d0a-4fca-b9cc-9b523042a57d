import { checkLengthAndThrow, checkRequiredAndThrow } from './../../utils';
import { couponGrant, pageTitle } from '../../type';

export const couponGrantHelper = {
  addInit: function(): couponGrant {
    return {
      couponId: '',
      knowledge: '',
      couponValidity: '',
      whetherValidity: false,
      whetherOpenKyc: false,
      startTime: '',
      endTime: '',
      kycConfig: {
        kycFilterId: '',
        couponId: '',
        knowledge: '',
        couponValidity: '',
        whetherValidity: false,
        startTime: '',
        endTime: '',
      },
    };
  },

  checkData: function(data) {
    try {
      if (data.whetherValidity) {
        checkRequiredAndThrow(data.couponValidity, '优惠券有效期');
      }
      checkRequiredAndThrow(data.couponId, '优惠券ID');
      checkRequiredAndThrow(data.knowledge, '理财小知识文描');
      checkRequiredAndThrow(data.startTime, '优惠券发放开始时间');
      checkRequiredAndThrow(data.endTime, '优惠券发放结束时间');
      checkLengthAndThrow(data.knowledge, `理财小知识文描`, 15);
      if (data.whetherOpenKyc) {
        checkRequiredAndThrow(data.kycConfig.kycFilterId, '请检查KYC配置是否保存');
        checkRequiredAndThrow(data.kycConfig.couponId, '条件匹配优惠券ID');
        checkRequiredAndThrow(data.kycConfig.knowledge, '条件匹配优惠券ID');
        checkLengthAndThrow(data.kycConfig.knowledge, `条件匹配理财小知识文描`, 15);
        if (data.kycConfig.whetherValidity) {
          checkRequiredAndThrow(data.kycConfig.couponValidity, '条件匹配优惠券有效期');
        }
        checkRequiredAndThrow(data.kycConfig.startTime, '条件匹配优惠券发放开始时间');
        checkRequiredAndThrow(data.kycConfig.endTime, '条件匹配优惠券发放结束时间');
      }
      // if (data.open) {
      //   checkRequiredAndThrow(data.img, mapPropToName['img']);
      //   checkRequiredAndThrow(data.name, mapPropToName['name']);
      //   checkRequiredAndThrow(data.link, mapPropToName['link']);
      // }
      if (data.couponId.indexOf('，') >= 0) {
        throw new Error('请检查中文逗号');
      }
      // if()
    } catch (e) {
      throw new Error(`优惠券发放-${e.message}`);
    }
    return true;
  },
};
