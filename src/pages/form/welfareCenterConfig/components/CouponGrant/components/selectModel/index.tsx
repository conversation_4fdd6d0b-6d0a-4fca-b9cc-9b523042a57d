import React, { useEffect, useState } from 'react';
import { Checkbox, Button } from 'antd';
import styles from '@/pages/frontend/frontend.less';
import ConfigSelect from '../selectModel/index';
interface selectProps {
  isHead?: boolean; //顶部筛选与内部配置项公用一个组件
  isEdit?: boolean; //内部配置项是否可用
  data?: any;
  handleChange: Function;
}
export default function(props: selectProps) {
  const { handleChange } = props;
  const [platform, setPlatform] = useState<string[]>([]);
  const [user, setUser] = useState<string[]>([]);
  const [checkAllPlatform, setCheckAllPlatform] = useState(false);
  const [checkAllUser, setCheckAllUser] = useState(false);
  const [indeterminateUser, setiUser] = useState(true);
  const [indeterminatePlat, setiPlat] = useState(true);
  const platformOptions = [
    { label: 'App-安卓', value: 'and' },
    { label: 'App-iOS', value: 'ios' },
    { label: 'SDK-安卓', value: 'andsdk' },
    { label: 'SDK-iOS', value: 'iossdk' },
    { label: 'ios至尊', value: 'iossdkvip' },
    { label: '安卓至尊', value: 'andsdkvip' },
  ];
  const userOptions = [
    { label: '默认', value: 'u0' },
    { label: '新手用户', value: 'u1' },
    { label: '次新用户', value: 'u2' },
    { label: '休眠新用户', value: 'u3' },
    { label: '流失新用户', value: 'u4' },
    { label: '成长用户', value: 'u5' },
    { label: '忠实用户', value: 'u6' },
    { label: '高净值用户', value: 'u7' },
    { label: '休眠用户', value: 'u8' },
    { label: '流失用户', value: 'u9' },
    { label: '开户未交易用户', value: 'u10' },
    { label: 'F类审核用户', value: 'F' },
  ];
  useEffect(() => {
    let _getAll1: string[] = userOptions.map(item => item.value);
    let _getAll2: string[] = platformOptions.map((item, index) => item.value);
    setPlatform(_getAll2);
    setUser(_getAll1);
    let data = {
      platform: _getAll2,
      utype: _getAll1,
    };
    handleChange(data);
  }, []);
  useEffect(() => {
    if (props.data) {
      if (props.data.utype) {
        setUser(props.data.utype);
      }
      setPlatform(props.data.platform);
    }
  }, [props.data]);
  const onChangePlatform = (platform: string[]) => {
    console.log(platform);
    setiPlat(!!platform.length && platform.length < platformOptions.length);
    setPlatform(platform);
    setCheckAllPlatform(platform.length === platformOptions.length);
    if (!props.isHead) {
      let data = {
        platform: platform,
        utype: user,
      };
      handleChange(data);
    }
  };
  const onChangeUser = (user: string[]) => {
    setiUser(!!user.length && user.length < userOptions.length);
    setUser(user);
    setCheckAllUser(user.length === userOptions.length);
    if (!props.isHead) {
      let data = {
        platform: platform,
        utype: user,
      };
      handleChange(data);
    }
  };
  const onCheckAllChangePlatform = (e: any) => {
    setiPlat(false);
    setCheckAllPlatform(e.target.checked);
    let _getAll: string[] = platformOptions.map((item, index) => item.value);
    setPlatform(e.target.checked ? _getAll : []);
    if (!props.isHead) {
      let data = {
        platform: e.target.checked ? _getAll : [],
        utype: user,
      };
      handleChange(data);
    }
  };
  const onCheckAllChangeUser = (e: any) => {
    setiUser(false);
    setCheckAllUser(e.target.checked);
    let _getAll: string[] = userOptions.map(item => item.value);
    setUser(e.target.checked ? _getAll : []);
    if (!props.isHead) {
      let data = {
        platform: platform,
        utype: e.target.checked ? _getAll : [],
      };
      handleChange(data);
    }
  };
  const handleSelect = () => {
    // console.log(platform,user)
    let data = {
      platform: platform,
      utype: user,
    };
    handleChange(data);
  };
  return (
    <section className={styles['m-config']}>
      <p className={styles['m-card-label']}>
        {!props.isHead ? <span className={styles['m-required']}>*</span> : null}适用平台:
      </p>
      <Checkbox
        indeterminate={indeterminatePlat}
        onChange={onCheckAllChangePlatform}
        checked={checkAllPlatform}
        // style={{marginLeft:'20px'}}
        disabled={!props.isHead && !props.isEdit}
      >
        全选
      </Checkbox>
      <br />
      <Checkbox.Group
        options={platformOptions}
        onChange={onChangePlatform}
        value={platform}
        style={{ marginLeft: '110px', marginBottom: '20px' }}
        disabled={!props.isHead && !props.isEdit}
      />
      <br />
      <p className={styles['m-card-label']}>
        {!props.isHead ? <span className={styles['m-required']}>*</span> : null}用户类型:
      </p>
      <Checkbox
        indeterminate={indeterminateUser}
        onChange={onCheckAllChangeUser}
        checked={checkAllUser}
        // style={{marginLeft:'20px'}}
        disabled={!props.isHead && !props.isEdit}
      >
        全选
      </Checkbox>
      <br />
      <Checkbox.Group
        options={userOptions}
        onChange={onChangeUser}
        value={user}
        style={{ marginLeft: '110px' }}
        disabled={!props.isHead && !props.isEdit}
      />
      <br />
      {props.isHead ? (
        <Button onClick={handleSelect} type="primary" style={{ marginTop: '20px' }}>
          查询
        </Button>
      ) : null}
    </section>
  );
}
