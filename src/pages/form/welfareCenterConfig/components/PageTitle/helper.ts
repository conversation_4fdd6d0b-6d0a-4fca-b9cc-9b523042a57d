import { pageTitle } from './../../type';
import { checkRequiredAndThrow } from '../../utils';

export const pageTitleHelper = {
  addInit: function(): pageTitle {
    return {
      strategyName: '',
    };
  },

  checkData: function(data) {
    try {
      // if (data.open) {
      //   checkRequiredAndThrow(data.img, mapPropToName['img']);
      //   checkRequiredAndThrow(data.name, mapPropToName['name']);
      //   checkRequiredAndThrow(data.link, mapPropToName['link']);
      // }
      // if (data.startTime && data.endTime && data.startTime > data.endTime) {
      //   throw new Error('开始时间应小于结束时间');
      // }
    } catch (e) {
      throw new Error(`首屏直播卡-${e.message}`);
    }
    return true;
  },
};
