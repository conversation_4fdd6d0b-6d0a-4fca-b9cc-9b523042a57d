import { Input } from 'antd';
import React, { FC } from 'react';
import { ConfigData, GrantConfigData } from '../../type';
import InputTitle from '../InputTitle';
import styles from './index.less';
export interface IProps {
  data: ConfigData['pageTitle'];
  onChange: (data: ConfigData['pageTitle']) => void;
  disabled: boolean;
}
const PageTitle: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleChange = (name, value) => {
    onChange({
      ...data,
      [name]: value,
    });
  };
  return (
    <section>
      <div>
        <h3>页面标题</h3>
        <Input
          value={data.strategyName}
          onChange={e => handleChange('strategyName', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title={'策略名称'} />}
        />
      </div>
    </section>
  );
};
export default PageTitle;
