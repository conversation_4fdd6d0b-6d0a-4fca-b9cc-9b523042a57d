import { Button, Card, Input, Popconfirm, Radio, Select } from 'antd';
import React, { FC, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { ConfigData, displayCustom, displayProfit, displayStyleType, fundTarget, GrantConfigData, lightSpotCustom, lightSpotPos, LightSpotType } from '../../type';
import { getTimeRangeMap } from '../../utils';
import DragAndSortWrapper from '../DragAndSortWrapper';
import InputTitle from '../InputTitle';
import UploadImg from '../UploadImg';
import { fundTargetHelper } from './helper';
import styles from './index.less';
export interface IProps {
  data: ConfigData['fundTarget'];
  onChange: (data: ConfigData['fundTarget']) => void;
  disabled: boolean;
}
const FundTarget: FC<IProps> = ({ data, onChange, disabled }) => {
  const [spotTagArr, setSpotTagArr] = useState<string[]>([])
  const [displayStyleArr, setDisplayStyleArr] = useState<string[]>([])
  const handleChange = (index, name, value) => {
    const newData = [...data];
    newData[index][name] = value;
    onChange(newData);
  };
  const handleAdd = () => {
    onChange([...data, fundTargetHelper.addArrayData(),]);
  };
  const handleDel = (index: number) => {
    const newData = [...data];
    newData.splice(index, 1);
    onChange(newData);
  };
  const renderSpotTag = (index: number, item: fundTarget) => {
    if (item) {
      if (item.lightSpotTagType === LightSpotType.POSITION_PEOPLE) {
        return (<Input
          value={(item.lightSpot as lightSpotPos).peopleCoefficient}
          onChange={e => handleChange(index, 'lightSpot', { peopleCoefficient: e.target.value })}
          disabled={disabled}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<InputTitle title={'持仓人数系数建议填99'} isRequired={false} />}
        />)
      } else {
        return (<Input
          value={(item.lightSpot as lightSpotCustom).customTag}
          onChange={e => handleChange(index, 'lightSpot', { customTag: e.target.value })}
          disabled={disabled}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<InputTitle title={'自定义标签'} isRequired={false} />}
        />)
      }

    } else {
      return ''
    }
  }
  const renderStyle = (index: number, item: fundTarget) => {
    if (item) {
      if (item.displayStyleType === displayStyleType.PROFIT) {
        return (<div style={{ marginBottom: 10, width: 350 }}>
          <div>
            <InputTitle title={'收益时间区间'} />
            <Select
              value={(item.displayStyle as displayProfit).profitArea}
              onChange={v => {
                handleChange(index, 'displayStyle', {
                  ...item.displayStyle,
                  profitArea: v
                })
              }}
              disabled={disabled}
              style={{ width: 100 }}
            >
              {(item.displayStyle as displayProfit)?.spot&&getTimeRangeMap((item.displayStyle as displayProfit)?.spot).map(optionItem=>{
                return <Option value={optionItem.value}>{optionItem.describe}</Option>
              })}
            </Select>
          </div>
          <InputTitle title={'亮点'} />
          <Select
            value={(item.displayStyle as displayProfit).spot}
            onChange={v => handleChange(index, 'displayStyle', {
              ...item.displayStyle,
              spot: v,
              profitArea:''
            })}
            disabled={disabled}
            style={{ marginBottom: 10, width: 100 }}
          >
            <Option value="rank">排名</Option>
            <Option value="earnings">超额收益</Option>
            <Option value="rollback">最大回撤</Option>
            <Option value="fluctuation">波动率</Option>
            <Option value="sharp">夏普比率</Option>
          </Select>
          <Input
            value={item.displayStyle.fundRecommendText}
            onChange={e => handleChange(index, 'displayStyle',
              {
                ...item.displayStyle,
                fundRecommendText: e.target.value
              })}
            disabled={disabled}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<InputTitle title={'基金推荐文案'} />}
          />
        </div>)
      } else {
        return (<div style={{ marginBottom: 10, width: 350 }}>
          <Input
            value={(item.displayStyle as displayCustom).mainTitle}
            onChange={e => handleChange(index, 'displayStyle', {
              ...item.displayStyle,
              mainTitle: e.target.value
            })}
            disabled={disabled}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={
              <InputTitle title={'主标题'} />
            }
          />
          <Input
            value={(item.displayStyle as displayCustom).subTitle}
            onChange={e => handleChange(index, 'displayStyle', {
              ...item.displayStyle,
              subTitle: e.target.value
            })}
            disabled={disabled}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={
              <InputTitle title={'副标题'} />
            }
          />
        </div>)
      }

    } else {
      return ''
    }
  }
  const changePosition = (dragIndex, hoverIndex) => {
    let dragItem = data[dragIndex];
    let hoverItem = data[hoverIndex];
    if (!dragItem || !hoverItem) {
      return;
    }
    //hoverIndex后面的都往后移动一位

    let newData = [...data];
    //先去掉在数组中被拖动的数据
    newData.splice(dragIndex, 1);

    //获取放置位置以及后面的数据
    const postfixArr = newData.slice(hoverIndex) || [];
    const prefixArr = newData.slice(0, hoverIndex + 1) || [];
    prefixArr[hoverIndex] = dragItem;
    const changeIndexKeys = [];
    
    newData = [...prefixArr, ...postfixArr]
      .filter(item => Boolean(item))
      .map((item, index) => {
        if (item.index !== index) {
          item.index = index;
          changeIndexKeys.push(item.id);
        }
        return item;
      });

    onChange(newData);
  };
  return (
    <section>
      <div>
        <h3>基金标的</h3>
        <DndProvider backend={HTML5Backend}>
          {data?.map((item, index) => {
            return (<Card style={{ width: '400px', display: "inline-block", marginLeft: '20px' }} key={index}>
              <DragAndSortWrapper
                changePosition={changePosition}
                id={index}
                key={index}
                index={index}
              >
                <Button disabled={disabled} style={{ marginBottom: '12px' }} type="dashed">
                  拖拽移动
                </Button>
              </DragAndSortWrapper>
              <Input
                value={item.fundId}
                onChange={e => handleChange(index, 'fundId', e.target.value)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
                addonBefore={<InputTitle title={'基金ID'} />}
              />
              <Input
                value={item.fundName}
                onChange={e => handleChange(index, 'fundName', e.target.value)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
                addonBefore={<InputTitle title={'基金名称'} />}
              />
              <Input
                value={item.fundTag1}
                onChange={e => handleChange(index, 'fundTag1', e.target.value)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
                addonBefore={<InputTitle title={'基金标签1'} />}
              />
              <Input
                value={item.fundTag2}
                onChange={e => handleChange(index, 'fundTag2', e.target.value)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
                addonBefore={<InputTitle title={'基金标签2'} isRequired={false} />}
              />
              <div style={{ marginBottom: 10, width: 400 }}>
                <InputTitle title={'亮点基金标签'} />
                <Radio.Group
                  style={{ display: 'inline' }}
                  disabled={disabled}
                  onChange={e => {
                    handleChange(index, 'lightSpotTagType', e.target.value)
                  }}
                  value={item.lightSpotTagType}
                >
                  <Radio value={LightSpotType.POSITION_PEOPLE}>持仓人数</Radio>
                  <Radio value={LightSpotType.LIGHT_TAG}>自定义标签</Radio>
                </Radio.Group>
                {renderSpotTag(index, item)}
              </div>
              <div style={{ marginBottom: 10, width: 400 }}>
                <InputTitle title={'展示样式'} />
                <Radio.Group
                  disabled={disabled}
                  style={{ display: 'inline' }}
                  onChange={e => {
                    setDisplayStyleArr((prev) => {
                      const styleArr = [...prev]
                      styleArr[index] = e.target.value
                      return styleArr
                    })
                    handleChange(index, 'displayStyleType', e.target.value)
                  }}
                  value={item.displayStyleType}
                >
                  <Radio value={displayStyleType.PROFIT}>展示收益</Radio>
                  <Radio value={displayStyleType.CUSTOM_TEXT}>自定义文字</Radio>
                </Radio.Group>
                {renderStyle(index, item)}
              </div>
              <Input
                value={item.fundJumpLink}
                onChange={e => handleChange(index, 'fundJumpLink', e.target.value)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
                addonBefore={<InputTitle title={'基金跳转链接'} />}
              />

              <Input
                value={item.fundButtonText}
                onChange={e => handleChange(index, 'fundButtonText', e.target.value)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
                addonBefore={<InputTitle title={'购买按钮文案'} />}
              />
              <UploadImg
                style={{ width: '400px' }}
                disabled={disabled}
                value={item.fundRecommendImg}
                onChange={v => handleChange(index, 'fundRecommendImg', v)}
                title={'基金推荐图片'}
                isRequired={false}
              ></UploadImg>
              <Popconfirm disabled={disabled} title={'请确认是否删除'} okText={'确认'} cancelText={'取消'}
                onConfirm={() => handleDel(index)}
              >
                <Button disabled={disabled} style={{ marginTop: '4px' }} size="small">
                  删除
                </Button>
              </Popconfirm>
            </Card>)
          })}
        </DndProvider>
      </div>
      <div>
        <Button disabled={disabled} onClick={handleAdd} style={{ marginTop: '4px' }} size="small">
          新增基金标的
        </Button>
      </div>
    </section>
  );
};
export default FundTarget;
