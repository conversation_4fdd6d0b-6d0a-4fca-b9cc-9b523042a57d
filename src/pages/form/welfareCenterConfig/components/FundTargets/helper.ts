import {
  displayCustom,
  displayProfit,
  lightSpotCustom,
  lightSpotPos,
  LightSpotType,
} from './../../type';
import { couponGrant, displayStyleType, fundTarget, LightSpotType, pageTitle } from '../../type';
import { checkLengthAndThrow, checkRequiredAndThrow } from '../../utils';

export const fundTargetHelper = {
  addInit: function(): fundTarget[] {
    return [];
  },
  addArrayData: function(): fundTarget {
    return {
      fundId: '',
      fundName: '',
      fundTag1: '',
      fundTag2: '',
      lightSpotTagType: LightSpotType.POSITION_PEOPLE,
      lightSpot: {},
      displayStyleType: displayStyleType.PROFIT,
      displayStyle: {},
      fundJumpLink: '',
      fundRecommendText: '',
      fundButtonText: '',
      fundRecommendImg: '',
    };
  },

  checkData: function(data: fundTarget[]) {
    try {
      data.forEach((element, index) => {
        checkRequiredAndThrow(element.fundId, `基金ID-第${index+1}项`);
        checkRequiredAndThrow(element.fundName, `基金名称-第${index+1}项`);
        checkRequiredAndThrow(element.fundTag1, `基金标签1-第${index+1}项`);
        checkLengthAndThrow(element.fundTag1, `基金标签1-第${index+1}项`, 6);
        checkLengthAndThrow(element.fundTag2, `基金标签2-第${index+1}项`, 6);
        if (element.lightSpotTagType === LightSpotType.POSITION_PEOPLE) {
          checkRequiredAndThrow(
            (element.lightSpot as lightSpotPos).peopleCoefficient,
            `持仓人数系数-第${index+1}项`,
          );
        } else {
          checkRequiredAndThrow(
            (element.lightSpot as lightSpotCustom).customTag,
            `自定义标签-第${index+1}项`,
          );
          checkLengthAndThrow(
            (element.lightSpot as lightSpotCustom).customTag,
            `自定义标签-第${index+1}项`,
            6,
          );
        }
        if (element.displayStyleType === displayStyleType.PROFIT) {
          checkRequiredAndThrow(
            (element.displayStyle as displayProfit).profitArea,
            `收益时间区间-第${index+1}项`,
          );
          checkRequiredAndThrow((element.displayStyle as displayProfit).spot, `亮点-第${index+1}项`);
          checkRequiredAndThrow((element.displayStyle as displayProfit).fundRecommendText, `基金推荐文案-第${index+1}项`);
          checkLengthAndThrow((element.displayStyle as displayProfit).fundRecommendText, `基金推荐文案-第${index+1}项`, 28);
        } else {
          checkRequiredAndThrow(
            (element.displayStyle as displayCustom).mainTitle,
            `主标题-第${index+1}项`,
          );
          checkLengthAndThrow(
            (element.displayStyle as displayCustom).mainTitle,
            `主标题-第${index+1}项`,
            20,
          );
          checkRequiredAndThrow(
            (element.displayStyle as displayCustom).subTitle,
            `副标题-第${index+1}项`,
          );
          checkLengthAndThrow(
            (element.displayStyle as displayCustom).subTitle,
            `副标题-第${index+1}项`,
            25,
          );
        }
        checkRequiredAndThrow(element.fundJumpLink, `基金跳转链接-第${index+1}项`);

        checkRequiredAndThrow(element.fundButtonText, `购买按钮文案-第${index+1}项`);
        checkLengthAndThrow(element.fundButtonText, `购买按钮文案-第${index+1}项`, 7);
      });
      // checkRequiredAndThrow(data.'基金ID')
      // if (data.open) {
      //   checkRequiredAndThrow(data.img, mapPropToName['img']);
      //   checkRequiredAndThrow(data.name, mapPropToName['name']);
      //   checkRequiredAndThrow(data.link, mapPropToName['link']);
      // }
    } catch (e) {
      throw new Error(`基金标的-${e.message}`);
    }
    return true;
  },
};
