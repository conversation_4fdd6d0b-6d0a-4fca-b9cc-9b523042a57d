import { ConfigData, GrantConfigData } from '../type';
import {
  checkLengthAndThrow,
  checkRequiredAndThrow,
  checkUrlAndThrow,
  getRandomId,
  setRandomId,
  checkMarker,
} from '../utils';
import { changeProfitHelper } from './ChangeProfit/helper';
import { couponGrantHelper } from './CouponGrant/helper';
import { fundTargetHelper } from './FundTargets/helper';
import { moreRankHelper } from './moreRank/helper';
import { pageTitleHelper } from './PageTitle/helper';

export const mapPropToName = {
  pageTitle: '策略名称',
};
const mapPropToHelper = {
  pageTitle: pageTitleHelper,
  moreRank: moreRankHelper,
  couponGrant: couponGrantHelper,
  fundTarget: fundTargetHelper,
  changeProfit: changeProfitHelper,
};
export const generateId = (prefix = '', length = 6) => {
  const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length);
    result += chars[randomIndex];
  }
  const myid = `${prefix}${result}`;
  if (getRandomId(myid)) {
    return generateId(prefix, length);
  }
  setRandomId(myid);
  return myid;
};
export const configItemhelper = {
  addInit: function(index = 0): ConfigData {
    return {
      id: generateId(),
      pageTitle: mapPropToHelper['pageTitle'].addInit(),
      moreRank: mapPropToHelper['moreRank'].addInit(),
      couponGrant: mapPropToHelper['couponGrant'].addInit(),
      fundTarget: mapPropToHelper['fundTarget'].addInit(),
      changeProfit: mapPropToHelper['changeProfit'].addInit(),
      whetherGrant: false,
      // hotspots: mapPropToHelper['hotspots'].addInit(),
      // screenStream: mapPropToHelper['screenStream'].addInit(),
      // sectorAnalyze: mapPropToHelper['sectorAnalyze'].addInit(),
      // managerAnalyze: mapPropToHelper['managerAnalyze'].addInit(),
      // fundRecommend: mapPropToHelper['fundRecommend'].addInit(),
      // streamCard: mapPropToHelper['streamCard'].addInit(),
      // banner: mapPropToHelper['banner'].addInit(),
      // infomation: mapPropToHelper['infomation'].addInit(),
      // chanceLift: mapPropToHelper['chanceLift'].addInit(),
    };
  },
  checkData: function(data: ConfigData) {
    checkRequiredAndThrow(data.pageTitle.strategyName, mapPropToName['pageTitle']);
    for (const key in data) {
      const helper = mapPropToHelper[key];
      console.log('key', key, data.whetherGrant, helper);
      if (helper) {
        if (key === 'couponGrant' && !data.whetherGrant) {
          continue;
        }
        helper.checkData(data[key]);
        // } else if (key !== 'couponGrant' && helper) {
        //   helper.checkData(data[key]);
        // } else {
      }
    }
  },
};
export const grantConfigItemHelper = {
  addInit: function(index = 0): GrantConfigData {
    return {
      pageTitle: mapPropToHelper['pageTitle'].addInit(),
    };
  },
};
