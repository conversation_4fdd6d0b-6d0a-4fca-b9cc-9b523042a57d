import { Input } from 'antd';
import React, { FC } from 'react';
import { ConfigData, GrantConfigData } from '../../type';
import InputTitle from '../InputTitle';
import styles from './index.less';
export interface IProps {
  data: ConfigData['changeProfit'];
  onChange: (data: ConfigData['changeProfit']) => void;
  disabled: boolean;
}
const ChangeProfit: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleChange = (name, value) => {
    onChange({
      ...data,
      [name]: value,
    });
  };
  return (
    <section>
      <div>
        <h3>优惠券兑换其他权益</h3>
        <Input
          value={data?.changeProfitLink}
          onChange={e => handleChange('changeProfitLink', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10 }}
          addonBefore={<InputTitle title={'可兑换其他权益跳转链接'} isRequired={false}/>}
        />
      </div>
    </section>
  );
};
export default ChangeProfit;
