import * as React from 'react';
import classNames from 'classnames';
import { Button, message, Popconfirm } from 'antd';

import api from 'api';
import Form<PERSON>ender from 'form-render/lib/antd';
import FROM_JSON from './form.json';

const { fetchUserImageDialog, postUserImageDialog, fetchRedeemConditionField } = api;

const { useState, useEffect } = React;

interface iProps {
  className?: string
}

function fetchConfig() {
  return new Promise((resolve, reject) => {
    fetchUserImageDialog().then((data: any) => {
      if (data.code === '0000') {
        resolve(data.data && JSON.parse(data.data));
      } else {
        message.info(data.message);
        reject();
      }
    }).catch((e: any) => {
      message.info(e.message);
    });
  });
}

function postConfig(value: any) {
  const dataToSend = { value: JSON.stringify(value) };
  return new Promise((resolve, reject) => {
    postUserImageDialog(dataToSend).then((data: any) => {
      if (data.code === '0000') {
        resolve(data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch((e: any) => {
      message.info(e.message);
    });
  });
}


function fetchTag(tag: string) {
  return new Promise((resolve, reject) => {
    fetchRedeemConditionField({
      labelNum: tag,
    }).then((data: any) => {
      if (data.data) {
        let _data = JSON.parse(data.data).data;
        let _nameIndex = 0,
          _enumIndex = 0;
        for (let i = 0; i < _data.head.length; i++) {
          if (_data.head[i].id === 'label_name') {
            _nameIndex = i;
          }
          if (_data.head[i].id === 'label_enum') {
            _enumIndex = i;
          }
        }
        let _result = '';
        if (_data.body[0]) {
          _result = `名称：${_data.body[0][_nameIndex]}；枚举值：${_data.body[0][_enumIndex]}`;
        } else {
          _result = '名称：；枚举值：';
        }
        resolve(_result);
      }
    });
  });
}

async function filterData(data: any, conditions: any = []) {
  if (!data || !data.dialogList) return;

  function checkIn(field: string) {
    for (let item of conditions) {
      if (item.field === field) {
        return true;
      }
    }
  }

  let _conditions: Array<any> = conditions;
  for (let dialog of data.dialogList) {
    if (dialog.conditions && dialog.conditions.length) {
      for (let conditionItem of dialog.conditions) {
        // 检查标签
        if (conditionItem.field.length >= 7 && !checkIn(conditionItem.field)) {
          let content = await fetchTag(conditionItem.field);
          _conditions.push({
            field: conditionItem.field,
            des: content,
          });
        }
        // 值数组转字符串
        if (conditionItem.params && conditionItem.params instanceof Array) {
          conditionItem.params = conditionItem.params.join(',');
        }
      }
    }
  }


  return _conditions;
}


export default function({ className }: iProps) {
  const [formData, setFormData] = useState({});
  const [valid, setValid] = useState([]);
  const [conditions, setConditions]: any = useState([]);

  function changeData(data: any) {
    filterData(data, conditions).then(_conditions => {
      setConditions(_conditions);
      setFormData(data);
    });

  }


  useEffect(() => {
    fetchConfig().then((data: any) => {
      if (data) {
        filterData(data).then(_conditions => {
          setFormData(data);
          setConditions(_conditions);
        });
      }
    });
  }, []);

  const onSubmit = () => {
    // valid 是校验判断的数组，valid 长度为 0 代表校验全部通过
    if (valid.length > 0) {
      message.info(`校验未通过字段：${valid.toString()}`);
    } else {
      const sendData:any = formData;
      for (let dialog of sendData.dialogList) {
        if (dialog.conditions && dialog.conditions.length) {
          for (let conditionItem of dialog.conditions) {
            if (conditionItem.params) {
                conditionItem.params = conditionItem.params.split(',')
            }
          }
        }
      }
      postConfig(sendData).then(() => {
        message.info('编辑成功');
      });
    }
  };

  return (
    <section className={classNames(className)}>
      <div>
        {conditions.map((tag: any, index: number) => {
          return (
            <div key={index}>
              <span style={{ marginRight: '5px' }}>{tag.code}:{tag.des}</span>
            </div>
          );
        })}
      </div>
      <FormRender
        propsSchema={FROM_JSON}
        formData={formData}
        onChange={changeData}
        onValidate={setValid}
      />
      <Popconfirm
        title="确认呢要提交么？如果是修改会覆盖线上配置！"
        onConfirm={onSubmit}
        okText="确定"
        cancelText="取消"
      >
        < Button type={'primary'}>提交</Button>
      </Popconfirm>
    </section>
  );
}
