{"type": "object", "properties": {"dialogList": {"title": "弹窗列表", "type": "array", "items": {"type": "object", "properties": {"imgUrl": {"title": "图片链接", "type": "string", "description": "请上传465px*571px的图片", "ui:width": "50%"}, "url": {"title": "链接", "type": "string", "ui:width": "50%"}, "versionControl": {"title": "版本控制", "type": "string", "description": "格式(有新功能上线的版本和跳转组成)：version=5.63.01###action=gsList", "ui:width": "60%"}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "20%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "20%"}, "conditions": {"title": "标签列表", "type": "array", "description": "标签id(所使用标签需要联系（<EMAIL>）加入到标签接口中）", "items": {"type": "object", "properties": {"field": {"title": "标签", "type": "string", "ui:width": "50%"}, "params": {"title": "值", "type": "string", "ui:width": "25%"}, "function": {"title": "逻辑", "type": "string", "ui:width": "25%", "enum": ["equal", "notEqual", "in", "notIn", "isSet", "notSet", "less", "greater", "greaterEqual", "lessEqual"], "enumNames": ["等于", "不等于", "字符串包含", "字符串不包含", "有值", "没值", "小于", "大于", "大于等于", "小于等于"]}}, "required": ["field"]}}}}}, "required": ["imgUrl", "url"]}}