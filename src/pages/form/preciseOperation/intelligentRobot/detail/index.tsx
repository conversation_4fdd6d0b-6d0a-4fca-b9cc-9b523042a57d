import React, { useEffect, useState, useRef } from 'react';
import { router } from 'umi';
import { Button, message, Breadcrumb } from 'antd';
import styles from './index.less';
import StrategyForm from './childComponent/strategyForm';
import TriggerModel from './childComponent/triggerModel';
import SelectCheckBox from './childComponent/SelectCheckBox';
import AssignUser from './childComponent/assignUser';
import UploadFile from './childComponent/uploadFile';
import { trigConditionsAll } from './childComponent/triggerModel/data';
import api from 'api';
const {
  addAIRobotStrategy,
  fetchOneAIRobotStrategy,
  updateAIRobotStrategy,
  uploadAIRobotStrategyFile,
} = api;

interface childComponent {
  current: any;
}
const Details: React.FC<React.ReactDOM> = () => {
  const strategyFormRef: childComponent = useRef();
  const selectCheckBoxRef: childComponent = useRef();
  const triggerModelRef: childComponent = useRef();
  const assignUserRef: childComponent = useRef();
  const uploadFileRef: childComponent = useRef();
  const [isEdit, setEdit] = useState(true);
  const [fileExist, setFileExist] = useState<any>({
    blackListFile: false,
    holdFundFile: false,
    holdDetailFundFile: false,
    myFundFile: false,
    selectFundFile: false,
    singleFundFile: false,
    whiteListFile: false,
  }); //文件是否已上传过，判断可否下载
  const queryString = location.hash.split('?')[1];
  const queryMap = queryString.split('&').reduce((pre: any, cur) => {
    const key = cur.split('=')[0];
    const value = cur.split('=')[1];
    pre[key] = value;
    return pre;
  }, {});
  const strategyId = queryMap.strategyId;
  const isNew = queryMap.isNew === 'true' ? true : false;
  // 编辑查看处理单个策略详情数据
  const setOneAIRobotStrategy = (data: any) => {
    // 回填策略详情信息
    strategyFormRef.current.setInfo(data);
    // 回填触发条件信息
    triggerModelRef.current.setTriggerInfo(data);
    // 回填适用平台、用户类型信息
    selectCheckBoxRef.current.setSelected(data);
    // 回填指定用户信息
    assignUserRef.current.setAssignUserInfo(data);
    let _fileExist = {
      blackListFile: data.blackList ? true : false,
      holdFundFile: data.holdFundList ? true : false,
      holdDetailFundFile: data.holdDetailFundList ? true : false,
      myFundFile: data.myFundList ? true : false,
      selectFundFile: data.selectFundList ? true : false,
      singleFundFile: data.singleFundList ? true : false,
      whiteListFile: data.whiteList ? true : false,
    };
    setFileExist(_fileExist);
  };
  useEffect(() => {
    if (isNew) {
      // 新增
      let _data = {
        platform: ['and', 'ios', 'andsdk', 'iossdk', 'iossdkvip', 'andsdkvip'],
        utype: ['u0', 'u1', 'u2', 'u3', 'u4', 'u5', 'u6', 'u7', 'u8', 'u9', 'F'],
        duration: '-1',
        strategyId: strategyId,
        triggerConditionLogic: 'and',
        lastEditor: localStorage.name,
      };
      strategyFormRef.current.setInfo(_data);
      selectCheckBoxRef.current.setSelected(_data);
      addAIRobotStrategy({ data: JSON.stringify(_data) }).then((res: any) => {
        if (res.code === '0000' && res.message === 'Success') {
          message.info('表单初始化成功');
        } else {
          message.error(res.message);
        }
      });
    } else {
      // 编辑 查看
      const isEdit = queryMap.isEdit === 'false' ? false : true;
      setEdit(isEdit);
      fetchOneAIRobotStrategy({ strategyId }).then((res: any) => {
        if (res.code === '0000' && res.message === 'Success') {
          console.log(res.data);
          setOneAIRobotStrategy(res.data);
        } else {
          message.error(res.message);
        }
      });
    }
  }, [isNew]);
  // 策略详情
  const handleStrategyForm = (fn: (queryObj: any) => void, queryObj: any) => {
    const promise = strategyFormRef.current.handleSubmit();
    promise
      .then(() => {
        console.log('success');
        console.log(strategyFormRef.current.formObj);
        const formObj = strategyFormRef.current.formObj;
        queryObj = { ...queryObj, ...formObj }; //收集策略详情参数
        fn(queryObj);
      })
      .catch((error: any) => {
        if (error === 'valid error') {
          message.error('请把策略详情填写完整');
        }
      });
  };
  // 触发条件中的标签检验
  const validLabel = (labelList: any[]) => {
    for (let i = 0; i < labelList.length; i++) {
      const labelItem = labelList[i];
      if (labelItem.tagId === '' || labelItem.value === '') {
        message.error('触发条件的标签值不能为空');
        return false;
      }
    }
    return true;
  };
  // 触发条件校验
  const validTrigger = (triggers: any[]) => {
    if (triggers.length === 0) {
      message.error('请至少填写一条触发条件');
      return false;
    }
    //统一页面的制定基金策略只能有一个以及触发页面输入值校验
    let _trigger: Array<Number> = [];
    for (let i = 0; i < triggers.length; i++) {
      const triggerItem = triggers[i];
      if (triggerItem.page === '') {
        message.error('触达页面不能为空');
        return false;
      } else if (triggerItem.condition === '') {
        message.error('触达条件不能为空');
        return false;
      } else if (
        trigConditionsAll[triggerItem.condition]?.threshold &&
        (triggerItem.threshold === '' || triggerItem.threshold?.indexOf(' ') > -1)
      ) {
        message.error('阈值不能为空且不能包含空格');
        return false;
      } else if (trigConditionsAll[triggerItem.condition]?.fund && triggerItem.appointFund === 0) {
        if (triggerItem.logic === '') {
          message.error('标签逻辑关系不能为空');
          return false;
        }
        if (triggerItem.labelList.length === 0) {
          message.error('请选择至少一条标签');
          return false;
        }
        if (triggerItem.condition == 14 && triggerItem.appointFund === 0) {
          if (_trigger.indexOf(triggerItem.page) > -1) {
            message.error(`指定基金配置重复`);
            return false;
          }
          _trigger.push(triggerItem.page);
        }
        if (triggerItem.labelList.length > 0) {
          const flag = validLabel(triggerItem.labelList);
          if (!flag) return false;
        }
      }
    }
    return true;
  };
  // 处理接口参数
  const dealWithQueryObj = (
    queryObj: any,
    triggerInfo: any,
    selectChecked: any,
    assignUserInfo: any,
    uploadFileInfo: any,
  ) => {
    queryObj = {
      ...queryObj,
      ...triggerInfo,
      ...selectChecked,
      ...assignUserInfo,
      ...uploadFileInfo,
      ...{ strategyId, lastEditor: localStorage.name },
    };
    return queryObj;
  };
  // 触发条件和指定用户
  const handleOtherSave = (queryObj: any) => {
    // 获取触发条件
    const triggerInfo = triggerModelRef.current.getTriggerInfo();
    if (!triggerInfo.triggerConditionLogic) {
      message.error('条件逻辑关系不能为空');
      return;
    }
    const validTriggerFlag = validTrigger(triggerInfo.triggers);
    // console.log(triggerInfo, 'triggerInfo', validTriggerFlag)
    const selectChecked = selectCheckBoxRef.current.getSelected();
    if (!validTriggerFlag) {
      return;
    }
    if (selectChecked.platform.length === 0) {
      message.error('请选择适用平台');
      return;
    }
    if (selectChecked.utype.length === 0) {
      message.error('请选择用户类型');
      return;
    }
    // console.log(selectChecked, 'selectChecked')
    // 获取指定用户
    const assignUserInfo = assignUserRef.current.getAssignUserInfo();
    // console.log(assignUserInfo, 'assignUserInfo')
    // 获取上传黑名单
    const uploadFileInfo = uploadFileRef.current.getUploadFileInfo();
    // console.log(uploadFileInfo, 'uploadFileInfo')
    queryObj = dealWithQueryObj(
      queryObj,
      triggerInfo,
      selectChecked,
      assignUserInfo,
      uploadFileInfo,
    );
    console.log(queryObj, 'queryObj');
    //保存文件
    let fileList: any[] = [];
    // 触发条件文件
    queryObj.triggers.map((item: any) => {
      if (item.appointFund === 1 && item?.fileInfo?.file) {
        fileList.push(item.fileInfo);
      }
    });
    // 用户名单文件
    if (queryObj?.assignFile?.file) {
      fileList.push(queryObj.assignFile);
    }
    // 黑名单文件
    if (uploadFileInfo.file) {
      fileList.push(uploadFileInfo);
    }
    if (fileList.length === 0) {
      uploadForm(queryObj);
      return;
    }
    let _data = new FormData();
    _data.append(`strategyId`, strategyId);
    fileList.map((val: any, index: number) => {
      _data.append(val.type, val.file);
    });
    uploadAIRobotStrategyFile(_data).then((res: any) => {
      if (res.code === '0000' && res.message === 'Success') {
        message.info('文件保存成功');
        uploadForm(queryObj);
      } else {
        message.error(res.message);
      }
    });
  };
  // 编辑策略接口
  const uploadForm = (queryObj: any) => {
    updateAIRobotStrategy({ data: JSON.stringify(queryObj), type: '0' }).then((res: any) => {
      if (res.code === '0000' && res.message === 'Success') {
        message.info('策略保存成功');
        history.push('/form/preciseOperation/intelligentRobot');
      } else {
        message.error(res.message);
      }
    });
  };
  // 保存
  const handleSave = () => {
    let queryObj: any = {};
    handleStrategyForm(handleOtherSave, queryObj);
  };

  return (
    <section>
      {/* 面包屑 */}
      <section>
        <Breadcrumb separator=">">
          <Breadcrumb.Item>当前位置：</Breadcrumb.Item>
          <Breadcrumb.Item>精准化运营</Breadcrumb.Item>
          <Breadcrumb.Item>
            <a href={'#/form/preciseOperation/intelligentRobot'}>AI智能机器人陪伴</a>
          </Breadcrumb.Item>
          <Breadcrumb.Item>策略详情</Breadcrumb.Item>
        </Breadcrumb>
      </section>
      {/* 策略详情 */}
      <p className={styles['m-title']}>策略详情</p>
      <StrategyForm wrappedComponentRef={strategyFormRef} isEdit={isEdit} />
      {/* 触发条件 */}
      <p className={styles['m-title']}>触发条件</p>
      <TriggerModel
        ref={triggerModelRef}
        isEdit={isEdit}
        fileExist={fileExist}
        strategyId={strategyId}
      />
      {/* 指定用户*/}
      <p className={styles['m-title']}>指定用户</p>
      <div className={styles['assign-user-div']}>
        <SelectCheckBox ref={selectCheckBoxRef} isEdit={isEdit} />
        <AssignUser
          ref={assignUserRef}
          isEdit={isEdit}
          isExist={fileExist['whiteListFile']}
          strategyId={strategyId}
          isNew={isNew}
        />
        <UploadFile
          ref={uploadFileRef}
          name={'黑名单：'}
          type={'blackListFile'}
          isEdit={isEdit}
          isExist={fileExist['blackListFile']}
          describe={'请上传excel文档，每行填入一个基金客户号（cust_id），第一行不要填数据'}
          strategyId={strategyId}
        />
      </div>
      {isEdit && (
        <Button type={'primary'} onClick={handleSave}>
          保存
        </Button>
      )}
    </section>
  );
};

export default Details;
