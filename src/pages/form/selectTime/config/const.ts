// 指数类型
export const INDEX_TYPE_MAP = {
  HU_SHEN_300: '沪深300',
  SHANG_HAI_INDEX: '上证指数',
  ENTREPRENEURSHIP_INDEX: '创业板指数',
  SCI_TECH_INNOVATION_INDEX: '科创板指数',
  CSI_1000_INDEX: '中证1000指数',
  CSI_500_INDEX: '中证500指数',
};

// 基金收益区间 最新、近一月、近三月、近六月、近一年
export const FUND_PERIOD = {
  year: '近一年',
  now: '最新',
  tmonth: '近三月',
  hyear: '近六月',
  month: '近一月',
};

// 初始化指数表格数据
export const INIT_FUND_TABLE = Object.keys(INDEX_TYPE_MAP).map(v => {
  return {
    fundCode: '',
    fundName: '',
    period: 'now',
    indexType: v,
    indexName: INDEX_TYPE_MAP[v],
  };
});
