import React, { useState, useEffect } from 'react';
import { Table, Button, Popconfirm } from 'antd';
import ModalContent from './modal';
let _index = 0;

const CustomTable = ({ tableData, handleData }) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [edit, setEdit] = useState<number>(0); //0 新增 1编辑
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [currentData, setCurrentData] = useState<any>({}); // 单条数据

  useEffect(() => {
    setDataSource(tableData || []);
  }, [tableData]);

  const handleDelete = (record, index: number) => {
    setDataSource(pre => {
      const _data = JSON.parse(JSON.stringify(pre));
      _data.splice(index, 1);
      handleData([..._data]);
      return [..._data];
    });
  };
  const handleEdit = (record, index: number) => {
    _index = index;
    setCurrentData(record);
    setEdit(1);
    setShowDialog(true);
  };
  /** 操作 */
  const renderOperate = (text: string, record: any, index: number) => {
    return (
      <div className="u-l-middle">
        <Button
          type="link"
          onClick={() => {
            handleEdit(record, index);
          }}
        >
          编辑
        </Button>
        <Popconfirm
          title="是否确认删除？"
          okText="确认"
          cancelText="取消"
          onConfirm={() => {
            handleDelete(record, index);
          }}
        >
          <Button type="link" className="g-ml10">
            <span style={{ color: '#ff0000' }}>删除</span>
          </Button>
        </Popconfirm>
      </div>
    );
  };
  const renderType = (text, record) => (record.type === 1 ? '社群' : '直播');
  const columns = [
    { title: '类型', dataIndex: 'type', key: 'type', render: renderType },
    { title: '文案', dataIndex: 'title', key: 'title' },
    { title: '跳转链接', dataIndex: 'url', key: 'url' },
    { title: '操作', key: '_operate', width: '100px', render: renderOperate },
  ];

  // const rowSelection = {
  //   onChange: (selectedRowKeys, selectedRows) => {
  //     console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
  //   },
  //   // type: 'radio',
  // };

  const addColumn = () => {
    setCurrentData([]);
    setEdit(0);
    setShowDialog(true);
  };

  const onEditClose = () => {
    setShowDialog(false);
  };

  const handleSingleData = (singleData: any) => {
    console.log(singleData);
    if (edit === 0) {
      setDataSource(pre => {
        const _data = pre;
        _data.push(singleData);
        handleData([..._data]);
        return [..._data];
      });
    } else if (edit === 1) {
      setDataSource(pre => {
        const _data = pre;
        _data[_index] = singleData;
        handleData([..._data]);
        return [..._data];
      });
    }
  };

  return (
    <>
      <Table
        rowKey="index"
        bordered
        style={{ width: '70%' }}
        // rowSelection={rowSelection}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
      />
      <Button type="dashed" onClick={addColumn} style={{ width: '70%' }}>
        <span style={{ color: '#1890ff' }}>新增</span>
      </Button>
      {showDialog && (
        <ModalContent
          currentData={currentData}
          showDialog={showDialog}
          onEditClose={onEditClose}
          handleSingleData={handleSingleData}
          edit={edit}
        ></ModalContent>
      )}
    </>
  );
};

export default CustomTable;
