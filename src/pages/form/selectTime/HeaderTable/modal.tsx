import React, { useEffect, useState } from 'react';
import { Form, Input, Radio, Row, Col, message, Button, Spin, Modal } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import api from 'api';

interface ModalContentProps extends FormComponentProps {
  showDialog: boolean;
  onEditClose: () => void;
  currentData: any;
  edit: number;
  handleSingleData: (val: any) => void;
  children?: React.ReactNode;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};
const marginStyle = { marginBottom: '10px' };

const ModalContent: React.FC<ModalContentProps> = ({
  showDialog,
  onEditClose,
  edit,
  currentData,
  form,
  handleSingleData,
}) => {
  const { getFieldDecorator } = form;
  const [loading, setLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState<any>({});

  useEffect(() => {
    if (edit === 0) {
      form.resetFields();
    }
  }, []);

  useEffect(() => {
    setFormData({ ...currentData });
  }, [currentData]);

  const onSubmit = () => {
    form.validateFields((err: any, values: any) => {
      if (values?.title?.length > 6) {
        message.error('文案不能超过6个字符，请检查');
        return;
      }
      if (!err) {
        handleSave(values);
      } else {
        message.error('请检查必填项');
      }
    });
  };

  /**
   * @description: 新增或编辑保存调用ajax方法
   */

  const handleSave = (values: any) => {
    handleSingleData(values);
    onEditClose();
  };

  return (
    <Modal
      visible={showDialog}
      maskClosable={false}
      title={edit === 0 ? '添加' : '编辑'}
      closable={false}
      width={600}
      onCancel={onEditClose}
      footer={
        <Spin spinning={loading}>
          <Button key="back" onClick={onEditClose}>
            取消
          </Button>
          <Button htmlType="submit" key="submit" type="primary" onClick={onSubmit}>
            保存
          </Button>
        </Spin>
      }
    >
      <Spin spinning={loading}>
        <Form {...formItemLayout} onSubmit={onSubmit} labelAlign="left">
          <Form.Item label="类型">
            {getFieldDecorator('type', {
              initialValue: formData.type,
              rules: [{ required: true, message: '请选择类型' }],
            })(
              <Radio.Group>
                <Radio style={marginStyle} value={1}>
                  社群
                </Radio>
                <Radio style={marginStyle} value={2}>
                  直播
                </Radio>
              </Radio.Group>,
            )}
          </Form.Item>
          <Form.Item label="文案" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('title', {
              initialValue: formData.title,
              rules: [{ required: true, message: '请输入文案' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="跳转链接" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('url', {
              initialValue: formData.url,
              rules: [{ required: false, message: '请输入跳转链接' }],
            })(<Input />)}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<ModalContentProps>()(ModalContent);
