import React, { useEffect, useState } from 'react';
import { Form, Input, Radio, Row, Col, message, Button, Spin, Modal, Select } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import { FUND_PERIOD, INDEX_TYPE_MAP } from '../config/const';

const { Option } = Select;

interface ModalContentProps extends FormComponentProps {
  showDialog: boolean;
  onEditClose: () => void;
  currentData: any;
  edit: number;
  handleSingleData: (val: any) => void;
  children?: React.ReactNode;
}

const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};
const marginStyle = { marginBottom: '10px' };

const ModalContent: React.FC<ModalContentProps> = ({
  showDialog,
  onEditClose,
  edit,
  currentData,
  form,
  handleSingleData,
}) => {
  const { getFieldDecorator } = form;
  const [loading, setLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState<any>({});

  useEffect(() => {
    if (edit === 0) {
      form.resetFields();
    }
  }, []);

  useEffect(() => {
    setFormData({ ...currentData });
  }, [currentData]);

  const onSubmit = () => {
    form.validateFields((err: any, values: any) => {
      if (!err) {
        handleSave(values);
      } else {
        message.error('请检查必填项');
      }
    });
  };

  /**
   * @description: 新增或编辑保存调用ajax方法
   */

  const handleSave = (values: any) => {
    handleSingleData({ ...values, ...{ indexType: currentData?.indexType } });
    onEditClose();
  };

  return (
    <Modal
      visible={showDialog}
      maskClosable={false}
      title={edit === 0 ? '添加' : '编辑'}
      closable={false}
      width={600}
      onCancel={onEditClose}
      footer={
        <Spin spinning={loading}>
          <Button key="back" onClick={onEditClose}>
            取消
          </Button>
          <Button htmlType="submit" key="submit" type="primary" onClick={onSubmit}>
            保存
          </Button>
        </Spin>
      }
    >
      <Spin spinning={loading}>
        <Form {...formItemLayout} onSubmit={onSubmit} labelAlign="left">
          <Form.Item label="指数名称" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('indexName', {
              initialValue: formData.indexName,
              rules: [{ required: false, message: '请输入指数名称' }],
            })(<Input disabled />)}
          </Form.Item>
          <Form.Item label="基金代码" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('fundCode', {
              initialValue: formData.fundCode,
              rules: [{ required: false, message: '请输入基金代码' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="基金简称" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('fundName', {
              initialValue: formData.fundName,
              rules: [{ required: false, message: '请输入基金简称' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="区间涨跌幅" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('period', {
              initialValue: formData.period,
              rules: [{ required: false, message: '请选择区间涨跌幅' }],
            })(
              <Select>
                {Object.keys(FUND_PERIOD).map(item => {
                  return (
                    <Option key={item} value={item}>
                      {FUND_PERIOD[item]}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<ModalContentProps>()(ModalContent);
