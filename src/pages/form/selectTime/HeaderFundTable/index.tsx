import React, { useState, useEffect } from 'react';
import { Table, Button, Popconfirm } from 'antd';
import ModalContent from './modal';
import { FUND_PERIOD, INDEX_TYPE_MAP } from '../config/const';
let _index = 0;

const CustomTable = ({ tableData, handleData }) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [edit, setEdit] = useState<number>(0); //0 新增 1编辑
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [currentData, setCurrentData] = useState<any>({}); // 单条数据

  useEffect(() => {
    setDataSource(tableData || []);
  }, [tableData]);

  const handleDelete = (record, index: number) => {
    setDataSource(pre => {
      const _data = JSON.parse(JSON.stringify(pre));
      _data.splice(index, 1);
      handleData([..._data]);
      return [..._data];
    });
  };
  const handleEdit = (record, index: number) => {
    _index = index;
    setCurrentData(record);
    setEdit(1);
    setShowDialog(true);
  };
  /** 操作 */
  const renderOperate = (text: string, record: any, index: number) => {
    return (
      <div className="u-l-middle">
        <Button
          type="link"
          onClick={() => {
            handleEdit(record, index);
          }}
        >
          编辑
        </Button>
      </div>
    );
  };
  const renderPeriod = (text: string, record: any) => {
    return <span>{FUND_PERIOD?.[record?.period]}</span>;
  };
  const columns = [
    { title: '指数名称', dataIndex: 'indexName', key: 'indexName' },
    { title: '基金代码', dataIndex: 'fundCode', key: 'fundCode' },
    { title: '基金简称', dataIndex: 'fundName', key: 'fundName' },
    { title: '区间涨跌幅', dataIndex: 'period', key: 'period', render: renderPeriod },
    { title: '操作', key: '_operate', width: '100px', render: renderOperate },
  ];

  // const rowSelection = {
  //   onChange: (selectedRowKeys, selectedRows) => {
  //     console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
  //   },
  //   // type: 'radio',
  // };

  const onEditClose = () => {
    setShowDialog(false);
  };

  const handleSingleData = (singleData: any) => {
    console.log(singleData);
    if (edit === 0) {
      setDataSource(pre => {
        const _data = pre;
        _data.push(singleData);
        handleData([..._data]);
        return [..._data];
      });
    } else if (edit === 1) {
      setDataSource(pre => {
        const _data = pre;
        _data[_index] = singleData;
        handleData([..._data]);
        return [..._data];
      });
    }
  };

  return (
    <>
      <Table
        rowKey="index"
        bordered
        style={{ width: '70%' }}
        // rowSelection={rowSelection}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
      />
      {/* <Button type="dashed" onClick={addColumn} style={{ width: '70%' }}>
        <span style={{ color: '#1890ff' }}>新增</span>
      </Button> */}
      {showDialog && (
        <ModalContent
          currentData={currentData}
          showDialog={showDialog}
          onEditClose={onEditClose}
          handleSingleData={handleSingleData}
          edit={edit}
        ></ModalContent>
      )}
    </>
  );
};

export default CustomTable;
