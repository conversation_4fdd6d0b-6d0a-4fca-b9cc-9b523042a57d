import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Popconfirm, message, Divider } from 'antd';
import HeaderTable from './HeaderTable';
import HeaderTagTable from './HeaderTagTable';
import HeaderFundTable from './HeaderFundTable';
import ShortLineTable from './ShortLineTable';
import api from 'api';
import { INIT_FUND_TABLE } from './config/const';

const Title = ({ title }: { title: string }) => {
  return <h2 style={{ marginTop: 24 }}>{title}</h2>;
};

const SelectTime = () => {
  const [headerTableData, setHeaderTableData] = useState<any[]>([]);
  const [headerTagTableData, setHeaderTagTableData] = useState<any[]>([]);
  const [headerFundTableData, setHeaderFundTableData] = useState<any[]>(INIT_FUND_TABLE);
  const [shortLineTableData, setShortLineTableData] = useState<any[]>([]);

  useEffect(() => {
    getConfig();
  }, []);

  /**
   * @description: 获取通用配置
   */

  const getConfig = () => {
    api.getSelectTimeConfig().then(res => {
      try {
        const _data = res?.data ? JSON.parse(res?.data) : [];
        console.log(_data);
        setHeaderTableData(_data?.headerTableData || []);
        setHeaderTagTableData(_data?.headerTagTableData || []);
        setHeaderFundTableData(
          _data?.headerFundTableData && _data?.headerFundTableData?.length > 0
            ? _data?.headerFundTableData
            : INIT_FUND_TABLE,
        );
        setShortLineTableData(_data?.shortLineTableData || []);
      } catch (error) {
        console.log(error);
      }
    });
  };
  const handleHeaderTable = data => {
    setHeaderTableData(data);
  };
  const handleHeaderTagTable = data => {
    setHeaderTagTableData(data);
  };
  const handleHeaderFundTable = data => {
    setHeaderFundTableData(data);
  };
  const handleShortLineTableData = data => {
    setShortLineTableData(data);
  };

  /**
   * @description: 发布配置保存
   */

  const publishHandle = () => {
    console.log(headerTableData, 'headerTableData');
    console.log(headerTagTableData, 'headerTagTableData');
    console.log(headerFundTableData, 'headerFundTableData');
    console.log(shortLineTableData, 'shortLineTableData');

    const formData = {
      headerTagTableData,
      headerTableData,
      headerFundTableData,
      shortLineTableData,
    };

    api
      .postSelectTimeConfig({
        value: JSON.stringify(formData),
      })
      .then(res => {
        if (res?.code !== '0000') {
          message.error('发布失败');
          console.log(res);
        } else {
          message.success('发布成功！');
        }
      });
  };
  return (
    <>
      <Button type="primary" onClick={publishHandle}>
        发布
      </Button>
      <section>
        <Title title="文案区" />
        <HeaderTable tableData={headerTableData} handleData={handleHeaderTable} />
      </section>
      <section>
        <Title title="头部解读" />
        <HeaderTagTable tableData={headerTagTableData} handleData={handleHeaderTagTable} />
      </section>
      <section>
        <Title title="头部基金" />
        <HeaderFundTable tableData={headerFundTableData} handleData={handleHeaderFundTable} />
      </section>
      <section>
        <Title title="短线研习群" />
        <ShortLineTable tableData={shortLineTableData} handleData={handleShortLineTableData} />
      </section>
    </>
  );
};

export default SelectTime;
