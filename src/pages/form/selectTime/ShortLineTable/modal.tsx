import React, { useEffect, useState } from 'react';
import { Form, Input, Radio, Row, Col, message, Button, Spin, Modal } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import UploadImg from '../components/UploadImg';

interface ModalContentProps extends FormComponentProps {
  showDialog: boolean;
  onEditClose: () => void;
  currentData: any;
  edit: number;
  handleSingleData: (val: any) => void;
  children?: React.ReactNode;
}

const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};
const marginStyle = { marginBottom: '10px' };

const ModalContent: React.FC<ModalContentProps> = ({
  showDialog,
  onEditClose,
  edit,
  currentData,
  form,
  handleSingleData,
}) => {
  const { getFieldDecorator } = form;
  const [loading, setLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState<any>({});
  const [picUrl, setPicUrl] = useState('');

  useEffect(() => {
    if (edit === 0) {
      form.resetFields();
    }
  }, []);

  useEffect(() => {
    setFormData({ ...currentData });
    if (currentData?.imgUrl) {
      setPicUrl(currentData.imgUrl);
    }
  }, [currentData]);

  const onSubmit = () => {
    form.validateFields((err: any, values: any) => {
      if (!picUrl) {
        message.error('请上传图片!');
        return;
      }
      if (!err) {
        handleSave(values);
      } else {
        message.error('请检查必填项');
      }
    });
  };

  /**
   * @description: 新增或编辑保存调用ajax方法
   */

  const handleSave = (values: any) => {
    handleSingleData({ ...values, ...{ imgUrl: picUrl } });
    onEditClose();
  };

  return (
    <Modal
      visible={showDialog}
      maskClosable={false}
      title={edit === 0 ? '添加' : '编辑'}
      closable={false}
      width={600}
      onCancel={onEditClose}
      footer={
        <Spin spinning={loading}>
          <Button key="back" onClick={onEditClose}>
            取消
          </Button>
          <Button htmlType="submit" key="submit" type="primary" onClick={onSubmit}>
            保存
          </Button>
        </Spin>
      }
    >
      <Spin spinning={loading}>
        <Form {...formItemLayout} onSubmit={onSubmit} labelAlign="left">
          <Form.Item label="跳转链接" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('url', {
              initialValue: formData.url,
              rules: [{ required: false, message: '请输入跳转链接' }],
            })(<Input />)}
          </Form.Item>
          <UploadImg
            value={picUrl}
            // size={['176*176']}
            title="图片"
            onChange={setPicUrl}
            disabled={false}
          />
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<ModalContentProps>()(ModalContent);
