{"type": "page", "title": "用户足迹配置", "body": [{"type": "crud", "syncLocation": false, "api": {"method": "get", "url": "/common_config/hash_data_get", "messages": {}, "dataType": "form", "data": {"key": "userFootprintConfig", "propName": "activityList"}, "adaptor": "const sortedItems = JSON.parse(payload.data);\r\nsortedItems.sort(compare);\r\n\r\nfunction compare(a, b) {\r\n  var timeA = new Date(a.lastModifyTime);\r\n  var timeB = new Date(b.lastModifyTime);\r\n\r\n  if (timeA > timeB) {\r\n    return -1;\r\n  } else if (timeA < timeB) {\r\n    return 1;\r\n  } else {\r\n    return 0;\r\n  }\r\n}\r\n  \r\nreturn {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n  data: {\r\n    items: sortedItems,\r\n    allItems: sortedItems,\r\n  }\r\n}"}, "columns": [{"name": "${(page - 1) * perPage + index + 1}", "label": "序号", "id": "u:b617adfc161a"}, {"name": "scene", "label": "适用场景", "type": "mapping", "id": "u:eeda5c2f52ba", "map": {"0": "开户", "1": "交易"}, "filterable": {"options": [{"label": "开户", "value": 0}, {"label": "交易", "value": 1}]}}, {"name": "activityName", "label": "活动名称", "type": "text", "id": "u:b0c663ac4616"}, {"type": "mapping", "label": "填写类型", "name": "type", "id": "u:7c0e3712f47c", "map": {"0": "埋点", "1": "URL"}}, {"type": "container", "label": "活动埋点/URL", "id": "u:16fd5653cfeb", "body": [{"type": "text", "label": "ums", "name": "ums", "id": "u:16fd5653cfec", "placeholder": "", "hiddenOn": "type === 1"}, {"type": "text", "label": "URL", "name": "url", "id": "u:16fd5653cfea", "placeholder": "", "hiddenOn": "type === 0"}]}, {"type": "mapping", "label": "状态", "name": "status", "id": "u:801ecb325df4", "map": {"0": "未上线", "1": "进行中", "2": "已暂停"}}, {"type": "text", "label": "最后编辑人", "name": "last<PERSON><PERSON><PERSON>", "id": "u:b202e4286cc2"}, {"type": "text", "label": "最后编辑时间", "name": "${DATETOSTR(lastModifyTime, 'YYYY-MM-DD HH:mm:ss')}", "id": "u:538e98110f25"}, {"type": "operation", "label": "操作", "buttons": [{"label": "上线", "type": "button", "level": "link", "id": "u:785330da9b4e", "disabledOn": "status === 1", "actionType": "ajax", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "userFootprintConfig", "propName": "activityList", "value": "${ENCODEJSON(allItems)}", "index": "${(page - 1) * perPage + index}"}, "dataType": "form", "requestAdaptor": "api.body.value = JSON.parse(api.body.value);\r\napi.body.value[api.body.index].status = 1;\r\napi.body.value = JSON.stringify(api.body.value);\r\napi.body.index = undefined", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}}, {"label": "暂停", "type": "button", "level": "link", "id": "u:785330da9b4a", "disabledOn": "status !== 1", "actionType": "ajax", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "userFootprintConfig", "propName": "activityList", "value": "${ENCODEJSON(allItems)}", "index": "${(page - 1) * perPage + index}"}, "dataType": "form", "requestAdaptor": "api.body.value = JSON.parse(api.body.value);\r\napi.body.value[api.body.index].status = 2;\r\napi.body.value = JSON.stringify(api.body.value);\r\napi.body.index = undefined", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}}, {"type": "button", "label": "删除", "level": "link", "id": "u:b1164abadb58", "actionType": "ajax", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "userFootprintConfig", "propName": "activityList", "value": "${ENCODEJSON(allItems)}", "index": "${(page - 1) * perPage + index}"}, "dataType": "form", "requestAdaptor": "api.body.value = JSON.parse(api.body.value);\r\napi.body.value.splice(api.body.index, 1);\r\napi.body.value = JSON.stringify(api.body.value);\r\napi.body.index = undefined", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "confirmText": "确认删除该条活动？", "className": "text-danger"}, {"label": "编辑", "type": "button", "actionType": "dialog", "level": "link", "dialog": {"title": "编辑活动页面", "body": [{"type": "form", "body": [{"type": "select", "name": "scene", "label": "适用场景", "id": "u:4c1dd62c6c48", "options": [{"label": "开户", "value": 0}, {"label": "交易", "value": 1}], "multiple": true, "required": true, "checkAll": false, "joinValues": false, "extractValue": true}, {"type": "input-text", "name": "activityName", "label": "活动名称", "id": "u:dfa743e9cc4f", "required": true}, {"type": "select", "name": "type", "label": "填写类型", "id": "u:cceadfb6c7bb", "options": [{"label": "埋点", "value": 0}, {"label": "URL", "value": 1}], "multiple": false, "required": true, "value": 0}, {"type": "input-text", "name": "ums", "label": "埋点", "id": "u:75e95188eea2", "required": true, "hiddenOn": "type !== 0"}, {"type": "input-text", "name": "url", "label": "URL", "id": "u:533d4426cf4e", "required": true, "hiddenOn": "type !== 1"}], "id": "u:b1c7e2ac700e", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "userFootprintConfig", "propName": "activityList", "value": "${ENCODEJSON(allItems)}", "index": "${(page - 1) * perPage + index}", "&": "$$"}, "dataType": "form", "requestAdaptor": "api.body.value = JSON.parse(api.body.value);\r\napi.body.value[api.body.index].scene = api.body.scene;\r\napi.body.value[api.body.index].activityName = api.body.activityName;\r\napi.body.value[api.body.index].type = api.body.type;\r\napi.body.value[api.body.index].url = api.body.url;\r\napi.body.value[api.body.index].ums = api.body.ums;\r\napi.body.value[api.body.index].lastModifyTime = new Date().toISOString();\r\napi.body.value = JSON.stringify(api.body.value);\r\napi.body.scene = undefined;\r\napi.body.activityName = undefined;\r\napi.body.type = undefined;\r\napi.body.url = undefined;\r\napi.body.ums = undefined;\r\napi.body.index = undefined;", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}}], "type": "dialog", "id": "u:f9c2e9d331cf", "showCloseButton": true, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true}, "id": "u:4101c6252242"}], "id": "u:6f695e760f80"}], "bulkActions": [], "itemActions": [], "features": ["create", "view", "update"], "headerToolbar": [{"label": "新增活动", "type": "button", "actionType": "dialog", "level": "primary", "dialog": {"title": "新增活动页面", "body": [{"type": "form", "body": [{"type": "select", "name": "scene", "label": "适用场景", "id": "u:4c1dd62c6c48", "options": [{"label": "开户", "value": 0}, {"label": "交易", "value": 1}], "multiple": true, "required": true, "checkAll": false, "joinValues": false, "extractValue": true}, {"type": "input-text", "name": "activityName", "label": "活动名称", "id": "u:dfa743e9cc4f", "required": true}, {"type": "select", "name": "type", "label": "填写类型", "id": "u:cceadfb6c7bb", "options": [{"label": "埋点", "value": 0}, {"label": "URL", "value": 1}], "multiple": false, "required": true, "value": 0}, {"type": "input-text", "name": "ums", "label": "埋点", "id": "u:75e95188eea2", "required": true, "hiddenOn": "type !== 0"}, {"type": "input-text", "name": "url", "label": "URL", "id": "u:533d4426cf4e", "required": true, "hiddenOn": "type !== 1"}], "id": "u:b1c7e2ac700e", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "userFootprintConfig", "propName": "activityList", "value": "${ENCODEJSON(CONCAT(allItems, [{\"scene\": ${scene}, \"activityName\": ${activityName}, \"type\": ${type}, \"ums\": ${ums}, \"url\": ${url}, \"status\": 0, \"lastEditor\": ${ls:name}, \"lastModifyTime\": ${NOW()}}]))}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}}], "type": "dialog", "id": "u:f9c2e9d331cd", "showCloseButton": true, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true}, "id": "u:5bf3054e0877"}, "bulkActions"], "id": "u:43b78bcaaa5e", "perPageAvailable": [10], "messages": {}, "initFetch": true, "loadDataOnce": true}, {"type": "form", "title": "场景参数配置", "body": [{"type": "combo", "label": "", "name": "sceneListConfig", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b5f45fd14dcb"}, "items": [{"type": "select", "label": "适用场景", "name": "scene", "options": [{"label": "开户", "value": 0}, {"label": "交易", "value": 1}], "id": "u:3c34d8177e22", "multiple": false, "required": true}, {"type": "select", "label": "有效时间", "name": "validTimeHours", "options": [{"label": "往前12小时", "value": 12}, {"label": "往前24小时", "value": 24}, {"label": "往前48小时", "value": 48}], "id": "u:114d42dba160", "multiple": false}, {"type": "select", "label": "截取活动数", "name": "validActivityCount", "options": [{"label": "1个", "value": 1}, {"label": "2个", "value": 2}, {"label": "3个", "value": 3}], "id": "u:a8a35d974601", "multiple": false}], "id": "u:fe674facc421", "strictMode": true, "syncFields": [], "value": "${config}"}], "id": "u:f6155865abd6", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "userFootprintConfig", "propName": "sceneListConfig", "value": "${ENCODEJSON(sceneListConfig)}"}, "dataType": "form"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "userFootprintConfig", "propName": "sceneListConfig"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}], "regions": ["body", "header"], "id": "u:581c6ba41c99"}