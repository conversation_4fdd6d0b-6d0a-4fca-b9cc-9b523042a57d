//原产品配置类型
export type OriginProduct={
    id:number;
    productName:string;
    productCode:string;
    holdProductName:string;
    holdProductCode:string;
}

//权益配置类型
export type Right={
    id:number;
    rightLimit:number;
    rightValue:number;
    rightID:string;
}

//承接产品配置类型
export type holdProduct={
    id:number;
    productName:string;
    productCode:string;
    productType:string;
    listDetail:string;
    incomeDetail:string;
    riskDetail:string;
    operatingMode:string;
    RedemptionRate:string;
}

export enum FormType{
    add="新增",
    update="编辑"
}
export enum RightStatusType{
    open="1",
    close="0"
}