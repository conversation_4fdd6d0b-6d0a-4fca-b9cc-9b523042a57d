import React,{useState,useEffect,FC,useImperative<PERSON>andle,forwardRef} from 'react'
import classnames from 'classnames'

import {Button, Input,Icon,Popconfirm} from 'antd'
import Form, { FormComponentProps } from 'antd/lib/form/Form';

export interface IProps{

}
const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 4 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 20 },
    },
};

const FCForm = forwardRef<FormComponentProps, IProps>(({ form, onSubmit,data,onAdd,onDelete}, ref) => {
    const { getFieldDecorator, getFieldValue } = form;
    useImperativeHandle(ref, () => ({
      form,
    }));
    // getFieldDecorator('keys', { initialValue: [1] });
    // const keys = getFieldValue('keys');
    const keys =data.map(item=>{
      return item.id
    });
    const formItems = keys.map((k, index) => {
        console.log("arr",index,k)
        return (
            <>
            <Form.Item
              {...formItemLayout}
              
              label={`${k}-权益门槛`}
              required={true}
              key={`${k}-1`}
            >
              {getFieldDecorator(`${k}.rightLimit`, {
              validateTrigger: ['onChange', 'onBlur'],
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: "请输入权益门槛",
                },
              ],
            })(<Input type={'number'} placeholder="请输入权益门槛" style={{ width: '60%', marginRight: 8 }} />)}
            </Form.Item>
            <Form.Item
              {...formItemLayout}
              label={`${k}-权益ID`}
              required={true}
              key={`${k}-2`}
            >
              {getFieldDecorator(`${k}.rightID`, {
              validateTrigger: ['onChange', 'onBlur'],
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: "请输入权益ID",
                },
              ],
            })(<Input  placeholder="请输入权益ID" style={{ width: '60%', marginRight: 8 }} />)}
            </Form.Item>
            <Form.Item
              {...formItemLayout}
              label={`${k}-权益文案`}
              required={true}
              key={`${k}-3`}
            >
              {getFieldDecorator(`${k}.rightValue`, {
              validateTrigger: ['onChange', 'onBlur'],
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: "请输入权益文案",
                },
              ],
            })(<Input placeholder="请输入权益文案" style={{ width: '60%', marginRight: 8 }} />)}
            <Popconfirm
            title={'确认是否删除'}
            cancelText={'取消'}
            okText={'确认'}
            onConfirm={()=>{
              onDelete(k,index)
            }}
            >
            <Icon type="delete" />
            </Popconfirm>
            </Form.Item>
            </>
        )
    });

    const  add = () => {
        // can use data-binding to get
        const keys = form.getFieldValue('keys');
        console.log(keys,"keys")
        const nextKeys = keys.concat([1]);
        // can use data-binding to set
        // important! notify form to detect changes
        form.setFieldsValue({
          keys: nextKeys,
        });
      };
    return <Form form={form}>
        {formItems}
        <Form.Item key={-1}>
            <Button　onClick={onAdd}>新增</Button>
            <Button　onClick={onSubmit}>发布</Button>
        </Form.Item>
    </Form>
  });
export default Form.create<IProps>()(FCForm);
