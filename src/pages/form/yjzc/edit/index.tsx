import React,{useState,useEffect,FC,createRef} from 'react'
import classnames from 'classnames'
import {<PERSON>,Button,Switch,Popconfirm, message}　from 'antd'
import RightsEdit from './rightsEdit'
import TableForm from './TableForm'
import { FormType, OriginProduct, Right, RightStatusType } from '../type'
import { originDatas, Rights } from '../test'
import Apis from '@/services/index'
import { originDataKey, rightStatusKey,rightDataKey, SUCCESS_CODE} from '../const'
import { Item } from '_rc-menu@7.5.5@rc-menu'
export interface IProps{

}
const APP:FC<IProps>=()=>{
    const formRef = createRef();
    const tableFormRef = createRef();
    const [tableData,setTableData]=useState<OriginProduct[]>([]);
    const [tableDataLoading,setTableDataLoading]=useState(false);
    const [rightsData,setRightsData]=useState<Right[]>([])
    const [rightsDataLoading,setRightsDataLoading]=useState(false);
    const [rightsStatus,setRightsStatus]=useState<RightStatusType>(RightStatusType.close);
    const fetchOriginDatas=async()=>{
        try{
            setTableDataLoading(true)
            const res=await Apis['fetchOrigin']({},originDataKey);
            if(res.code!==SUCCESS_CODE){
                throw new Error(res.message);
            }
            // console.log(res,"res");
            // const data=res.data?JSON.parse(res.data):[]
            setTableData(res.data)
        }catch(e){
            
            message.error(e?.message||'未知错误')
        }finally{
            setTableDataLoading(false)
        }
    }
    const fetchRightsDatas=async()=>{
        try{
            setRightsDataLoading(true)
            const res=await Apis['fetchYJZCConfig']({},rightDataKey);
            const res2=await Apis['fetchYJZCConfig']({},rightStatusKey);
            
            const result=res.data?JSON.parse(res.data):[]
            const data=result
            const status=res2.data||RightStatusType.close
            setRightsData(data)
            setRightsStatus(status);
        }catch(e){
            console.log(e,"error")
            message.error("获取权益失败")
        }finally{
            setRightsDataLoading(false)
        }
    }
    useEffect(()=>{
        fetchOriginDatas();
        fetchRightsDatas();
    },[])
    const columns=[
        {
            title:'大集合名称',
            dataIndex:"productName"
        },
        {
            title:'大集合代码',
            dataIndex:"productCode"
        },
        {
            title:'承接产品名称',
            dataIndex:"underTakeName"
        },
        {
            title:'承接产品代码',
            dataIndex:"underTakeCode"
        },
        {

            render:(text,record,index)=>{
                return <>
                    <Button onClick={()=>{
                        tableFormRef.current?.open(record,FormType.update)
                    }}>编辑</Button>
                    <Popconfirm title={'确认是否删除'}
                    cancelText={'取消'}
                    okText={'确认'}
                    onConfirm={()=>{
                        handleTableDelete(record,index)
                    }}>
                    <Button type={'danger'} style={{marginLeft:'12px'}}>删除</Button>
                    </Popconfirm>
                </>
            }
        }
    ]
    // useEffect(()=>{
        
    //     const values={};
    //     rightsData.forEach((item)=>{
    //         values[`${item.id}`]=item;
    //     })
        
    //     const form=formRef.current?.form
    //     form.setFieldsValue({
    //         ...values
    //     })
    // },[])
    useEffect(()=>{
        if(!rightsDataLoading){
        const values={};
        rightsData.forEach((item)=>{
            values[`${item.id}`]=item;
        })
        
        const form=formRef.current?.form
        form.setFieldsValue({
            ...values
        })
        }
    },[rightsDataLoading])
    const handleRightsAdd=()=>{
        const _rightsData=[...rightsData]
        const id=Number(rightsData[rightsData.length-1]?.id)+1||0
        _rightsData.push({id})
        setRightsData(_rightsData);
        
    }
    const handleRightsDelete=(id,index)=>{
       
        const _rightsData=[...rightsData]
        _rightsData.splice(index,1);
        setRightsData(_rightsData)
    }
    const rightsSubmit=async()=>{
        console.log(formRef);
        const form=formRef.current?.form
        try{
            await form.validateFields();
        }catch(e){
            return ;
        }
        const values=form.getFieldsValue();
        const keys=Object.keys(values);
        const data=keys.map((item)=>{
            return {id:item,...values[item]}
        })
        try{
            setRightsDataLoading(true)
            const res=await Apis['postYJZCConfig']({
                value:JSON.stringify(data)
            },rightDataKey)
            const res2=await Apis['postYJZCConfig']({
                value:rightsStatus
            },rightStatusKey);
        
            
            fetchRightsDatas();
            message.success("发布成功")
        }catch(e){
            console.log(e,"rights")
            message.error("发布失败")
        }finally{
            setRightsDataLoading(false)
        }
        
        console.log("form",values)
    }
    const handleTableDelete=async (record,index)=>{
       try{
            const res=await Apis['deleteOrigin']({productCode:record.productCode});
            if(res.code!==SUCCESS_CODE){
                throw new Error(res.message);
            }
            message.success('删除成功');
            fetchOriginDatas();
       }catch(e){
        message.error(e?.message||'未知错误')
       }finally{

       }
    }
    const tableSubmit=async (record,type)=>{
        const form=tableFormRef?.current?.form
        const current=tableFormRef.current
        try{
            await form.validateFields();
        }catch(e){
            return ;
        }
        const values=form.getFieldsValue()
        try{
            let res=null;
            if(type===FormType.add){
                 res=await Apis['addOrigin']({str:JSON.stringify(values)})
            }else{
                 res=await Apis['editOrigin']({str:JSON.stringify(values)})
                
            }
            console.log(res.code,"code")
            if(res.code!==SUCCESS_CODE){
               
                throw new Error(res.message);
                // throw new Error();
            }
            message.success(`${type}成功`)
            fetchOriginDatas();
            current?.close();
        }catch(e){
    
            message.error(e?.message||"未知错误");
        }finally{

        }
    }
    return <div>
        <Table loading={tableDataLoading} rowKey={'id'}　pagination={{pageSize:5}}　columns={columns} dataSource={tableData} 
        footer={()=>{
            return <div>
            <Button onClick={()=>{
                tableFormRef.current.open(null,FormType.add)
            }}>新增</Button>
        </div>
        }}
        ></Table>
        <TableForm   wrappedComponentRef={tableFormRef} onSubmit={tableSubmit} ></TableForm>
        
        <h3>权益配置<Switch checked={rightsStatus===RightStatusType.open} onChange={(checked)=>{
            if(checked){
                setRightsStatus(RightStatusType.open)
            }else{
                setRightsStatus(RightStatusType.close)
            }
        }}></Switch></h3>
        <div>

            <div style={{width:800}}>
            <RightsEdit data={rightsData} onSubmit={rightsSubmit}  wrappedComponentRef={formRef} onAdd={handleRightsAdd} onDelete={handleRightsDelete}></RightsEdit>
            </div>
            {/* <div>
            <Button onClick={()=>{
                rightsSubmit();
            }}>发布</Button>
            </div> */}
        </div>
    </div>
}
export default APP
