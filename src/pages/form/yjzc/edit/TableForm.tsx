import React,{useState,useEffect,FC,useImperativeHandle,forwardRef} from 'react'
import classnames from 'classnames'
import {Modal,Input} from 'antd'
import Form, { FormComponentProps } from 'antd/lib/form/Form';
import floorModel from '@/pages/frontend/compoment/floorModel';
import { FormType } from '../type';

export interface IProps{

}
const formItemLayout={
    labelCol: { span: 8 },
    wrapperCol: { span: 14 },
  }
const APP=forwardRef<FormComponentProps, IProps>(({ form, onSubmit }, ref) => {
    const { getFieldDecorator, getFieldValue } = form;
    const [show,setShow]=useState(false)
    const [record,setReocrd]=useState(null)
    const [type,setType]=useState<FormType>(FormType.add)
    useImperativeHandle(ref, () => ({
      form,
      open:(record,type:FormType)=>{
          console.log(record)
          setReocrd(record)
          form.resetFields();
          record&&form.setFieldsValue({...record})
          setShow(true)
          setType(type)
      },
      close:()=>{
          setShow(false);
      }
    }));
    return <Modal visible={show} title={type}　okText={'确认'}　cancelText={'取消'} onCancel={()=>setShow(false)} onOk={()=>{
        onSubmit&&onSubmit(record,type)
    }}>
        <Form form={form} layout={'horizontal'}
        >
            <Form.Item
            {...formItemLayout}
            label={'大集合产品名称'}
            >
                {
                    getFieldDecorator('productName',{
                        rules:[{ required: true, message: '请输入大集合产品名称' }]
                    })(<Input></Input>)
                }
            </Form.Item>
            <Form.Item
            {...formItemLayout}
            label={'大集合产品代码'}
            >
                {
                    getFieldDecorator('productCode',{
                        rules:[{ required: true, message: '请输入大集合产品代码' }]
                    })(<Input disabled={type===FormType.update}></Input>)
                }
            </Form.Item>
            <Form.Item
            {...formItemLayout}
            label={'承接产品名称'}
            >
                {
                    getFieldDecorator('underTakeName',{
                        rules:[{ required: true, message: '请输入承接产品名称' }]
                    })(<Input></Input>)
                }
            </Form.Item>
            <Form.Item
             {...formItemLayout}
            label={'承接产品代码'}
            >
                {
                    getFieldDecorator('underTakeCode',{
                        rules:[{ required: true, message: '请输入承接产品代码' }]
                    })(<Input></Input>)
                }
            </Form.Item>
        </Form>
    </Modal>

})
export default Form.create<IProps>()(APP);
