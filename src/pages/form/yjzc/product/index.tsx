import React,{useState,useEffect,FC,createRef} from 'react'
import classnames from 'classnames'
import {Table,Button,Popconfirm,message} from 'antd'
import TableForm from './TableForm'
import { FormType, holdProduct } from '../type'
import { holdProdcts } from '../test'
import { holdDataKey, productTypeMap, SUCCESS_CODE } from '../const'
import Apis from '@/services/index'
export interface IProps{

}
const APP:FC<IProps>=()=>{
    const tableFormRef = createRef();
    const [tableData,setTableData]=useState<holdProduct[]>([])
    const [tableDataLoading,setTableDataLoading]=useState(false);
    const fetchProductDatas=async()=>{
        try{
            setTableDataLoading(true)
            const res=await Apis['fetchProduct']({},holdDataKey);
            if(res.code!==SUCCESS_CODE){
                throw new Error(res.message)
             }
            // console.log(res,"res");
            // const data=res.data?JSON.parse(res.data):[]
            setTableData(res.data)
        }catch(e){
            message.error(e?.message||"未知错误")
            
        }finally{
            setTableDataLoading(false)
        }
    }
    useEffect(()=>{
        fetchProductDatas();
    },[])
    const columns=[
        {
            title:'承接产品名称',
            dataIndex:"underTakeProductName"
        },
        {
            title:'承接产品代码',
            dataIndex:"underTakeProductCode"
        },
        {
            title:'承接产品类型',
            dataIndex:"productType",
            render:(text)=>{
                return productTypeMap[text];
            }
        },
        {

            render:(text,record,index)=>{
                return <>
                    <Button onClick={()=>{
                        tableFormRef.current?.open(record,FormType.update)
                    }}>编辑</Button>
                    <Popconfirm title={'确认删除'}
                    cancelText={'取消'}
                    okText={'确认'}
                    onConfirm={()=>handleTableDelete(record,index)}
                    >
                    <Button type={'danger'} style={{marginLeft:'12px'}}>删除</Button>
                    </Popconfirm>
                </>
            }
        }
    ]
    const handleTableDelete=async (record,index)=>{
        try{
             const res=await Apis['deleteProduct']({code:record.underTakeProductCode});
             if(res.code!==SUCCESS_CODE){
                 throw new Error(res.message)
             }
             message.success('删除成功');
             fetchProductDatas();
        }catch(e){
         message.error(e?.message||"未知错误")
        }finally{
 
        }
     }
     const tableSubmit=async (record,type)=>{
         const form=tableFormRef?.current?.form
         const current=tableFormRef.current
         try{
             await form.validateFields();
         }catch(e){
             return ;
         }
         const values=form.getFieldsValue()
         try{
             let res=null;
             if(type===FormType.add){
                  res=await Apis['addProduct']({str:JSON.stringify(values)})
                    
             }else{
                  res=await Apis['editProduct']({str:JSON.stringify(values)})
             }
             if(res.code!==SUCCESS_CODE){
                throw new Error(res.message)
             }
             message.success(`${type}成功`)
             fetchProductDatas();
             current?.close();
         }catch(e){
            message.error(e?.message||"未知错误")
         }finally{
 
         }
     }
    return <div>
        <Table loading={tableDataLoading} rowKey={'id'} pagination={{pageSize:10}} columns={columns} dataSource={tableData} footer={()=>{
            return <div><Button onClick={()=>{
                tableFormRef.current.open(null,FormType.add)
            }}>新增</Button></div>
        }}></Table>
        <TableForm wrappedComponentRef={tableFormRef} onSubmit={tableSubmit}></TableForm>
        
    </div>
}
export default APP
