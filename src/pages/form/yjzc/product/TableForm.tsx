import React,{useState,useEffect,FC,useImperativeHandle,forwardRef} from 'react'
import classnames from 'classnames'
import {Modal,Input,Select} from 'antd'
import Form, { FormComponentProps } from 'antd/lib/form/Form';
import floorModel from '@/pages/frontend/compoment/floorModel';
import { FormType } from '../type';
import { productTypeMap } from '../const';
export interface IProps{

}
const formItemLayout={
    labelCol: { span: 8 },
    wrapperCol: { span: 14 },
  }
const APP=forwardRef<FormComponentProps, IProps>(({ form, onSubmit }, ref) => {
    const { getFieldDecorator, getFieldValue } = form;
    const [show,setShow]=useState(false)
    const [record,setReocrd]=useState(null)
    const [type,setType]=useState<FormType>(FormType.add)
    useImperativeHandle(ref, () => ({
      form,
      open:(record,type:FormType)=>{
          console.log(record,type,"open")
          setReocrd(record)
          form.resetFields();
          record&&form.setFieldsValue({...record})
          setShow(true)
          setType(type)
      },
      close:()=>{
          setShow(false);
      }
    }));

    
    return <Modal visible={show} title={type}　okText={'确认'}　cancelText={'取消'} onCancel={()=>setShow(false)} onOk={()=>{
        onSubmit(record,type)
    }}>
        <Form form={form} layout={'horizontal'}>
            <Form.Item
            {...formItemLayout}
            label={'产品名称'}
            >
                {
                    getFieldDecorator('underTakeProductName',{
                        rules:[{required:true,message:'请输入产品名称'}]
                    })(<Input></Input>)
                }
            </Form.Item>
            <Form.Item
             {...formItemLayout}
            label={'产品代码'}
            >
                {
                    getFieldDecorator('underTakeProductCode',{
                        rules:[{required:true,message:'请输入产品代码'}]
                    })(<Input disabled={type===FormType.update}></Input>)
                }
            </Form.Item>
            <Form.Item
             {...formItemLayout}
            label={'产品类型'}
            >
                {
                    getFieldDecorator('productType',{
                        rules:[{required:true,message:'请选择产品类型'}]
                    })(<Select >
                        {
                            Object.keys(productTypeMap).map(item=>{
                                return <Select.Option key={item} value={item}>{productTypeMap[item]}</Select.Option>
                            })
                        }
                    </Select>)
                }
            </Form.Item>
            <Form.Item
             {...formItemLayout}
            label={'列表简述'}
            >
                {
                    getFieldDecorator('resume',{
                        rules:[{required:true,message:'请输入列表简述'}]
                    })(<Input></Input>)
                }
            </Form.Item>
            <Form.Item
             {...formItemLayout}
            label={'收益描述'}
            >
                {
                    getFieldDecorator('incomeDescribe',{
                        rules:[{required:true,message:'请输入收益描述'}]
                    })(<Input></Input>)
                }
            </Form.Item>
            <Form.Item
             {...formItemLayout}
            label={'风险描述'}
            >
                {
                    getFieldDecorator('riskDescribe',{
                        rules:[{required:true,message:'请输入风险描述'}]
                    })(<Input></Input>)
                }
            </Form.Item>
            <Form.Item
             {...formItemLayout}
            label={'运作模式'}
            >
                {
                    getFieldDecorator('workMode',{
                        rules:[{required:true,message:'请输入运作模式'}]
                    })(<Input></Input>)
                }
            </Form.Item>
            <Form.Item
             {...formItemLayout}
            label={'申赎费率'}
            >
                {
                    getFieldDecorator('redeemRate',{
                        rules:[{required:true,message:'请输入申赎费率'}]
                    })(<Input></Input>)
                }
            </Form.Item>
        </Form>
    </Modal>

})
export default Form.create<IProps>()(APP);
