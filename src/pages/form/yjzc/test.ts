import { holdProduct, OriginProduct, Right } from "./type";

export const originDatas:OriginProduct[]=[
    {
        id:0,
        productName:"xxx",
        productCode:"01111",
        holdProductCode:"1110",
        holdProductName:"xxxxx"
    },
    {
        id:1,
        productName:"xxx",
        productCode:"01111",
        holdProductCode:"1110",
        holdProductName:"xxxxx"
    },
    {
        id:2,
        productName:"xxx",
        productCode:"01111",
        holdProductCode:"1110",
        holdProductName:"xxxxx"
    }
]

export const Rights:Right[]=[
    {
        id:0,
        rightLimit:100,
        rightValue:100,
        rightID:"011"
    },
    {
        id:2,
        rightLimit:100,
        rightValue:100,
        rightID:"011"
    },
    {
        id:3,
        rightLimit:100,
        rightValue:100,
        rightID:"011"
    }
]

export const holdProdcts:holdProduct[]=[
    {
        id:0,
        productName:"xxxx",
        productCode:"0111",
        productType:"0",
        listDetail:"00000",
        riskDetail:"0000",
        incomeDetail:"00",
        operatingMode:"---x",
        RedemptionRate:"xxxx"
    },
    {
        id:1,
        productName:"xxxx",
        productCode:"0111",
        productType:"1",
        listDetail:"00000",
        riskDetail:"0000",
        incomeDetail:"00",
        operatingMode:"---x",
        RedemptionRate:"xxxx"
    }
]