import React, { useEffect, useState } from 'react';
// 接口
import api from 'api';
// 组件
import { Table, Button, message, Popconfirm, Select } from 'antd';

// 路由
import { history } from 'umi';
// 时间
import moment from 'moment';
// TS
import { Confs, ActivityList } from '../types';
// store
import store from 'store';
const types = [
  {
    value: 'SIGN_IN',
    name: '签到活动（天天生金）',
  },
  {
    value: 'RECOMMEND',
    name: '行情推荐组件',
  },
  {
    value: 'FISSION',
    name: '分享裂变',
  },
  {
    value: 'GENERAL',
    name: '通用配置',
  },
];
const { Option } = Select;
const { fetchActivityManageList, postActivityStatus, deleteActivity, updateActivityManage } = api;
export default function() {
  const [selectType, setSelectType] = useState<String>('FISSION');
  const columns = [
    {
      title: '活动ID',
      dataIndex: 'activityId',
      render: (text: string) => (text ? text : '— —'),
    },

    {
      title: '活动状态',
      dataIndex: 'activityStatus',
      render: (text: number) => {
        let arr = ['下线', '上线  '];

        return arr[Number(text)] || '--';
      },
    },
    {
      title: '活动时间',
      dataIndex: 'activeTime',
      render: (text: string, record: Confs<string>) => {
        if (!record.startTime && !record.endTime) {
          return '— —';
        }
        return `${record.startTime} ~ ${record.endTime}`;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
    },
    {
      title: '创建人',
      dataIndex: 'creator',
    },
    {
      title: '修改人',
      dataIndex: 'editor',
    },
    {
      title: '操作',
      dataIndex: 'options',
      // 审核|编辑|作废|查看|导出发放名单|进行中|已结束|已作废
      render: (text: unknown, record: Confs<string>) => {
        function tempRender(
          inner: string,
          fn: (record: Confs<string>) => void,
          marginLeft: number = 0,
          type:
            | 'default'
            | 'link'
            | 'ghost'
            | 'primary'
            | 'dashed'
            | 'danger'
            | undefined = 'primary',
        ) {
          if (type === 'danger') {
            return (
              <Popconfirm
                title={`你确定${inner}活动么？`}
                okText="确定"
                cancelText="取消"
                onConfirm={() => fn(record)}
              >
                <Button type={type} style={{ marginLeft: `${marginLeft}px` }}>
                  {inner}
                </Button>
              </Popconfirm>
            );
          }
          return (
            <Button
              type={type}
              style={{ marginLeft: `${marginLeft}px` }}
              onClick={() => fn(record)}
            >
              {inner}
            </Button>
          );
        }
        switch (record.activityStatus) {
          case '0':
            return (
              <section>
                {tempRender('查看/编辑', handleEdit, 0, 'primary')}
                {/* {tempRender('上线', handleEdit,20, 'primary')} */}
                {tempRender('删除', handleDelete, 20, 'danger')}
              </section>
            );
          case '1':
            return (
              <section>
                {tempRender('查看/编辑', handleEdit, 0, 'primary')}
                {selectType === 'GENERAL'
                  ? tempRender('编辑活动奖励', goActivityReward, 0, 'primary')
                  : ''}
                {/* {tempRender('下线', handleStop,20, 'danger')} */}
              </section>
            );

          default:
            return '--';
        }
      },
    },
  ];
  // 表格数据
  const [dataSource, setDataSource] = useState<Confs<string>[]>();
  // 表格加载动画
  const [loadingFlag, setLoadingFlag] = useState<boolean>();
  // 表格总数据
  const [total, setTotal] = useState<number>();
  // 当前分页
  const [current, setCurrent] = useState<number>(1);
  useEffect(() => {
    try {
      store.set('name', JSON.parse(localStorage.getItem('name')));
    } catch (error) {
      store.set('name', localStorage.getItem('name'));
    }

    // getType().then((res:ActivityList)=>{
    //   if(res.code=='0'){
    //     setTypes(res.data||[])
    //   }
    //   else{
    //     message.error(res.message);
    //   }

    // }).catch((err: unknown) => {

    // });
  }, []);
  useEffect(() => {
    // searchAllActivityList(1);
    // store.set('activityType', selectType);
    // console.log(selectType);
  }, [selectType]);
  useEffect(() => {
    console.log('storeActivityType', store.get('activityType'));
    setSelectType(store.get('activityType') || 'FISSION');
    searchAllActivityList(1);
  }, [store.get('activityType')]);
  // 查询活动列表
  const searchAllActivityList = (pageNum: number) => {
    setLoadingFlag(true);
    fetchActivityManageList({
      activityType: store.get('activityType') || 'FISSION',
      offSet: (pageNum - 1) * 20 + 1,
      pageSize: 20,
    }).then((res: ActivityList) => {
      if (res.code == '0') {
        setDataSource(res.data.componentConfigDtoList);
        setTotal(res.data.size);
        setLoadingFlag(false);
        setCurrent(pageNum);
      } else {
        message.error(res.message);
      }
    });
  };
  // 改变活动状态
  const _postActivityStatus = (body: { activityIndex: string; status: 1 | 2 | 3 | 4 }) => {
    function success() {
      message.success(`状态改变成功`);
      searchAllActivityList(1);
    }
    postActivityStatus(body)
      .then((res: { code: string; message: string }) => {
        res.code === '0' ? success() : message.error(res.message);
      })
      .catch((err: unknown) => {
        message.error('网络请求错误，请稍后重试');
        console.log('改变活动状态错误：', err);
      });
  };
  // 跳转到活动编辑页面
  const goActivityReward = (record: Confs<string>) => {
    history.push({
      pathname: 'activityDetail',
      query: {
        id: record.activityId,
      },
    });
  };
  // 查看
  const handleCheck = (record: Confs<string>) => {
    history.push({
      pathname: 'detail',
      query: {
        type: 'check',
        id: record.activityId,
      },
    });
  };
  // 数据查看
  // const handleDataCheck = (record: Confs<string>) => {
  //   getDataExcel({}, `activityIndex=${record.activityId}`, '', { responseType: 'blob' })
  //     .then((res: any) => {
  //       if (!res.success) message.error(res.message);
  //     })
  //     .catch((err: unknown) => {
  //       message.error('网络请求错误，请稍后重试');
  //       console.log('数据查看DataExcel接口错误：', err);
  //     });
  //   getStatusDataExcel({}, `activityIndex=${record.activityId}`, '', { responseType: 'blob' })
  //     .then((res: any) => {
  //       if (!res.success) message.error(res.message);
  //     })
  //     .catch((err: unknown) => {
  //       message.error('网络请求错误，请稍后重试');
  //       console.log('数据查看StatusDataExcel接口错误：', err);
  //     });
  // };
  // 中止
  const handleStop = (record: Confs<string>) => {
    updateActivityManage({
      activityType: selectType,
      activityId: record.activityId,
      activityStatus: '0',
    }).then(res => {
      if (res.code === 0) {
        message.success('下线成功');
      } else {
        message.error(res.message);
      }
    });
  };
  const handleStart = (record: Confs<string>) => {
    updateActivityManage({
      activityType: selectType,
      activityId: record.activityId,
      activityStatus: '1',
    }).then(res => {
      if (res.code === 0) {
        message.success('上线成功');
      } else {
        message.error(res.message);
      }
    });
  };
  // 审核 未开始：1    进行中：2    已结束：3
  const handleVerify = (record: Confs<string>) => {
    const currentUser = store.get('name');
    const lastUser = record.lastEditor;
    if (currentUser === lastUser) {
      return message.error('最后编辑人不可以和审核人一致');
    }
    let status: 1 | 2 | 3 = 1;
    // 点击审核时间大于活动开始时间小于活动结束时间 进行中
    // 点击审核时间小于活动开始时间 进行中
    // 点击审核时间大于活动结束时间 已结束
    const nowTime = moment().format('YYYY-MM-DD HH:mm:ss');
    if (nowTime < record.startTime) {
      status = 1;
    }
    if (nowTime > record.endTime) {
      status = 3;
    }
    if (record.startTime < nowTime && nowTime < record.endTime) {
      status = 2;
    }
    _postActivityStatus({
      activityIndex: record.activityId,
      status,
    });
  };
  // 编辑
  const handleEdit = (record: Confs<string>) => {
    history.push({
      pathname: selectType === 'FISSION' ? 'detail' : 'generalDetail',
      query: {
        id: record.activityId,
      },
    });
  };
  // 删除
  const handleDelete = (record: Confs<string>) => {
    deleteActivity({
      activityType: selectType,
      activityId: record.activityId,
    }).then(res => {
      if (res.code === 0) {
        searchAllActivityList(current);
        message.success('删除成功');
      } else {
        message.error(res.message);
      }
    });
  };
  // 页码变化
  const handlePaginationChange = (page: number) => {
    searchAllActivityList(page);
  };
  return (
    <article>
      <section style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
        <Select
          value={selectType}
          onChange={(val: string) => {

            store.set('activityType', val);
            setSelectType(val);
          }}
        >
          {types.map(item => {
            return <Option value={item.value}>{item.name}</Option>;
          })}
        </Select>
        <Button
          type="primary"
          onClick={() => {
            // if(selectType !== "FISSION"){
            //   message.error("暂不支持");
            //   return;
            // }
            selectType === 'FISSION' ? history.push('detail') : history.push('generalDetail');
            // history.push('generalDetail')
          }}
          style={{ marginBottom: '20px' }}
        >
          新增活动
        </Button>
        <span>
          负责人：zhengzekai
          <br />
          手炒链接：https://fund.10jqka.com.cn/scym_scsy/public/m/truthTyj/dist/index$ff8000.html?activityId=
          <br />
          基金链接：https://fund.10jqka.com.cn/ifundapp_app/public/m/truthTyj/dist/index$ff8000.html?activityId=
        </span>
      </section>
      <Table
        columns={columns}
        dataSource={dataSource}
        pagination={{
          current,
          pageSize: 20,
          total: total,
          onChange: handlePaginationChange,
        }}
        scroll={{ x: 'max-content' }}
        loading={loadingFlag}
      ></Table>
    </article>
  );
}
