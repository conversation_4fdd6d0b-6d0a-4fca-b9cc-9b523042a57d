import React, { useState, useEffect, useCallback, useRef } from 'react';
// form-render
import FORM_JSON from './form.json';
import FormRender from 'form-render/lib/antd';
// 接口
import api from 'api';
// 组件
import { Button, message, Icon, Typography, Form, Row, Col, Input, Select, Divider } from 'antd';

// 样式
import styles from './index.less';
// 时间
import 'moment/locale/zh-cn';
import moment from 'moment';
// webStorage
import store from 'store';
// TypeScript
import { EnveLope, ActivityDetail, tradeDetail, params, CusRouter } from '../types';
// 路由
import { history } from 'umi';
import UserIdList from '@/pages/form/components/userIdList';
// import UploadImg from '../components/wrapperUploadImg';
import { genActivityId } from '../utils';
import { FormComponentProps } from 'antd/es/form/Form';
import UploadImg from '../components/uploadImg';
import { toast } from '@/utils/message';
import {
  TASK_TYPE,
  TASK_TYPE_TEXT,
  REWARD_TYPE,
  REWARD_TYPE_TEXT,
  TASK_CYCLE,
  TASK_CYCLE_TEXT,
  TASK_USER_TYPE,
} from '../const';
const { getTaskEnum, getAwardEnum, insertAward, updateAward, postHash, getActivityAwardConfig } = api;
type Props = {
  location: CusRouter;
  data?: { [k: string]: unknown };
  activityId: string;
};
const App: React.FunctionComponent<Props & FormComponentProps> = ({ data, location, form, activityId }) => {

  /** 任务关联的奖励数组 */
  const [activityRewardArr, setActivityRewardArr] = useState([])
  // 这就是个奖励枚举
  const [rewardArr, setRewardArr] = useState({})
  /**图片数组 */
  const [imageArr, setImageArr] = useState({})
  // 初始值
  const [initialValue, setInitialValue] = useState({
    rewardType: [],
    rewardArr: [],
  })
  // 判断是否为编辑状态
  const [isEdit, setIsEdit] = useState(true);
  /** 接口获取的奖励配置 */
  const [awardConfig, setAwardConfig] = useState([{}]);
  const handleChangeRewardType = (type, ind) => {
    // setRewardType(type)
    const defaultObjArr = rewardArr[type]

    let pushObj = {
      awardId: defaultObjArr[0].customId,
      type: type
    }
    defaultObjArr.map(e => {
      pushObj[e.customValue] = ''
    })
    const activityReward = [...activityRewardArr]
    activityReward[ind] = pushObj;
    // console.log('pushObj22',activityReward)
    setActivityRewardArr(activityReward)
  }

  /**对一些值进行初始化 */
  useEffect(() => {
    var tInitVal = { ...initialValue }
    if (rewardArr && awardConfig.length > 0 && initialValue.rewardArr.length === 0) {
      const award = awardConfig
      award.map((item, ind) => {
        Object.keys(activityRewardArr).map((e) => {
          if (activityRewardArr?.[e]?.awardId == item?.awardId && !tInitVal.rewardArr.includes(item)) {
            tInitVal.rewardType = [...tInitVal.rewardType, activityRewardArr?.[e].type]
            // console.log('tInitVal', item, ind)
            if (item?.awardImage) {
              setImageArr(prev => { return { ...prev, [`${ind}+awardImage`]: item?.awardImage } })
            }
            tInitVal.rewardArr = [...tInitVal.rewardArr, item]
          }
        })
      })
      console.log('tInitVal', tInitVal)
      setInitialValue(tInitVal);
    }
  }, [awardConfig, activityRewardArr])
  // /**获取枚举接口· */
  useEffect(() => {

    getAwardEnum().then(res => {
        if (res?.status_code === 0) {
          setRewardArr(res?.data?.awardEnumMap || [])
         
        }
        // console.log('getAwardEnum', res.data.awardEnumMap)
      }).catch(e=>{
        console.log('here is error', e)
        message.error(e?.message);
      })

  }, []);
  useEffect(()=>{
    // console.log('rewardArrrrr',rewardArr)
    if(Object.keys(rewardArr).length>0){
      console.log('rewardArrrrr',rewardArr)
      const activityReward = [...activityRewardArr]
      if (activityReward.length === 0) {
        if (awardConfig.length > 0) {
          console.log('data.awardConfig', awardConfig)
          awardConfig.map(e => {
            Object.keys(rewardArr).map(item => {
              // console.log('awardEnumMap', res.data.awardEnumMap[item])
              if (rewardArr[item][0].customId == e.awardId) {
                let pushObj = {
                  ...e,
                  type: item
                }
                activityReward.push(pushObj)
              }
            })
          })
          setActivityRewardArr(activityReward)
        } else {
          const defaultObjArr = rewardArr[Object.keys(rewardArr)[0]]
          let pushObj = {
            awardId: defaultObjArr[0].customId,
            type: Object.keys(rewardArr)[0]
          }
          defaultObjArr.map(e => {
            pushObj[e.customValue] = ''
          })
          activityReward.push(pushObj)
          setActivityRewardArr(activityReward)
        }
      }
    }
  },[rewardArr,awardConfig])
  useEffect(() => {
    const activityId = location['query'].id
    getActivityAwardConfig({ activityId }).then(res => {
      if (res?.status_code === 0) {


        const resAward = res.data?.activityAwardConfigDtoList[0]?.awardConfig === 'object' ? res.data?.activityAwardConfigDtoList[0]?.awardConfig : JSON.parse(res.data?.activityAwardConfigDtoList[0]?.awardConfig || '[]')
        if (resAward.length === 0) {
          setIsEdit(false);
        }
        const result = resAward.filter(e => { return Boolean(e.awardId) })
        setAwardConfig(result)

        // typeof res.data.awardConfig === 'object' ? setAwardConfig(res.data.awardConfig) : setAwardConfig(JSON.parse(res.data.awardConfig))
      }

      // console.log('getAwardEnum', res.data.awardEnumMap)
    })
  }, [])
  const handleAddReward = () => {
    const activityReward = [...activityRewardArr]
    const defaultObjArr = rewardArr[Object.keys(rewardArr)[0]]
    let pushObj = {
      awardId: defaultObjArr[0].customId,
      type: Object.keys(rewardArr)[0]
    }
    defaultObjArr.map(e => {
      pushObj[e.customValue] = ''
    })
    activityReward.push(pushObj)
    setActivityRewardArr(activityReward)
  }
  //获取奖励的初始值
  const getRewardKey = (ind, item) => {
    // console.log('getRewardKey', initialValue.rewardArr)

    return initialValue.rewardArr?.[ind] ? initialValue.rewardArr?.[ind][item.customValue] : ' '
  }
  const handleBack = () => {
    history.push('list')
  }
  const handleSubmit = () => {

    const submitItem = {}
    const awardConfig = [];
    const taskConfig = {};
    const formKey = Object.keys(form.getFieldsValue())
    console.log('formValue', imageArr)
    let percent = 0;
    let isError = false;
    formKey.map(e => {
      if (e.indexOf('%') === 0) {
        let eArr = e.split('+')
        // const ind = formKey.findIndex(e=>e.startsWith(eArr[1]))
        const awardKeyArr = formKey.filter(e => e.indexOf(eArr[2]) === 0)
        // console.log('ind', awardKeyArr)
        awardConfig.push({ awardId: parseFloat(eArr[1]) })
        awardKeyArr.map(item => {
          const tempKey = item.split('+')[1]
          // console.log('tempKey',tempKey)
          if (tempKey === 'probability') {
            if (isNaN(parseFloat(form.getFieldValue(item)))) {
              isError = true;
              toast.error('配置概率不能配置非数字')
            }
            percent += parseFloat(form.getFieldValue(item))
            if (percent > 1) {
              isError = true;
              toast.error('配置百分比大于100%！')
            }
          }

          if (item.indexOf('awardImage') >= 0) {
            console.log('imageArr', imageArr, item)
            awardConfig[awardConfig.length - 1][tempKey] = imageArr[item]
          }
          else awardConfig[awardConfig.length - 1][tempKey] = form.getFieldValue(item).replace(/\s*/g, "")
        })
        // console.log('awardConfig',awardConfig)
        submitItem['awardConfig'] = JSON.stringify(awardConfig)
      }
    })
    console.log('formKey', submitItem)
    if (!isError) {
      try {
        if (isEdit) {
          updateAward({ ...submitItem, activityId: location['query'].id }).then((res) => {
            console.log('res', res)
          })

          // onSubmit({ ...submitItem, id: data.id });
        } else {
          insertAward({ ...submitItem, activityId: location['query'].id }).then((res) => {
            console.log('res', res)
          })
          // onSubmit(submitItem, 'new');
        }
      } catch (e) {
        console.log('here is errror', e)
        message.error(e)
      }
    }
  }
  const handleGoActivity = () => {
    history.push({
      pathname: 'generalDetail',
      query: {
        id: location['query'].id,
      }
    })
  }
  const deleteItem = (ind) => {
    const act = [...activityRewardArr];
    act.splice(ind, 1);
    setActivityRewardArr(act);
    const intial = { ...initialValue };
    intial.rewardArr.splice(ind, 1);
    setInitialValue(intial)
    // console.log('initialValue.rewardArr?.[ind]',initialValue.rewardArr)
    const images = { ...imageArr };
    Object.keys(images).map((e) => {
      const imagePos = e.split('+')[0]
      if (ind < imagePos) {
        images[`${Number(imagePos) - 1}+awardImage`] = images[`${Number(imagePos)}+awardImage`]
      } else if (imagePos === ind) {
        delete images[`${Number(imagePos)}+awardImage`]
      }
    })
    console.log('afterDeleteImage', images);
    setImageArr(images)

  }
  return (
    <div className={styles['task-form']}>
      <Typography.Title level={2} align="center">
        活动配置编辑
      </Typography.Title>
      <Typography.Title level={4} align="center">
        注：这里配置的奖励无关于任务，和活动直接绑定
      </Typography.Title>
      <nav className={styles['task-form-nav']}>
        <Button type="secondary" onClick={handleBack}>
          返回列表
        </Button>
        <Button type="primary" onClick={handleSubmit}>
          保存
        </Button>
        <Button type="secondary" onClick={handleGoActivity}>
          去活动/任务配置
        </Button>
      </nav>
      <main className={styles['task-form-body']}>
        <Form>
          <section>
            {<section className={styles['task-form-block']}>
              <Row>
                <Col span={12}><span>添加数组奖励数组</span><Button type="primary" onClick={handleAddReward}>添加新的奖励</Button></Col>
              </Row>
              {activityRewardArr?.map((e, ind) => {
                return (
                  <div key={`activityReward${ind}+${activityRewardArr.length}`} id={`activityReward${ind}+${activityRewardArr.length}`}>
                    <Form.Item label="选择奖励">
                      {
                        form.getFieldDecorator(`%+${e.awardId}+${ind}`, {
                          initialValue: e.type,
                          rules: [{ required: true, message: '请选择奖励类型' }],
                        })(

                          <Select onChange={val => handleChangeRewardType(val, ind)}>
                            {
                              Object.keys(rewardArr)
                                ?.map((item, ind) =>
                                  <Select.Option key={`${String(item) + ind}`} value={item}>{item}</Select.Option>
                                )
                            }
                          </Select>
                        )
                      }
                    </Form.Item>

                    <Form.Item label="任务面向用户">
                      {
                        form.getFieldDecorator(`${ind}+awardUserType`, {
                          initialValue: String(initialValue.rewardArr?.[ind]?.awardUserType || 0),
                          rules: [{ required: true, message: '需要选择任务面向用户' }],

                        })(
                          <Select>
                            {
                              Object.entries(TASK_USER_TYPE)
                                .map(([key, text]) =>
                                  <Select.Option key={key} value={String(key)}>{text}</Select.Option>
                                )
                            }
                          </Select>
                        )
                      }
                    </Form.Item>
                    {rewardArr[e.type]?.map((item, index) => {
                      if (item?.customValue !== 'awardImage') {
                        return (
                          <Form.Item label={
                            item.customKey
                          }
                            key={`${index + item.customKey}x`}>
                            {form.getFieldDecorator(`${ind}+${item.customValue}`, {
                              initialValue: getRewardKey(ind, item) || ''
                            })

                              (
                                <Input
                                  type={item.customValueType === "INT_TYPE" ? 'number' : 'string'}
                                />)}
                          </Form.Item>
                        )
                      } else {
                        return (
                          <Form.Item label={
                            item.customKey
                          }
                            key={`${index + item.customKey}x`}>
                            {form.getFieldDecorator(`${ind}+${item.customValue}`, {
                              // initialValue: getRewardKey(ind, item) || ''
                            }
                            )

                              (
                                <UploadImg
                                  onChange={(name, url: string) => {
                                    setImageArr(prev => {
                                      return { ...prev, [`${ind}+${item.customValue}`]: url }
                                    })
                                    // console.log('urlll',`${ind}+${item.customValue}`,url)
                                    // form.setFieldsValue() 
                                  }
                                  }
                                  imgUrl={imageArr[`${ind}+awardImage`]}
                                  readonly={false}
                                />)}
                          </Form.Item>
                        )
                      }
                    }
                    )}
                    <Button
                      type="danger"
                      onClick={(e) => {
                        deleteItem(ind)
                      }}
                    >
                      删除配置
                    </Button>
                    <Divider />
                  </div>)
              })}

            </section>}
          </section>
        </Form>
      </main>
    </div>
  );
}
export default Form.create()(App)