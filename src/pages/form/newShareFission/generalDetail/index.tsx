import React, { useState, useEffect, useCallback, useRef } from 'react';
// form-render
import FORM_JSON from './form.json';
import FormRender from 'form-render/lib/antd';
// 接口
import api from 'api';
// 组件
import { Button, message, Icon, PageHeader, DatePicker, Col, Row, Divider } from 'antd';

// 样式
import styles from './index.less';
// 时间
// import type { Moment } from 'moment';
// webStorage
import store from 'store';
// TypeScript
import { EnveLope, ActivityDetail, tradeDetail, params, CusRouter } from '../types';
// 路由
import { history } from 'umi';
import UserIdList from '@/pages/form/components/userIdList';
import { genActivityId } from '../utils';
import moment, { Moment } from 'moment';
import Form from '../../bookingPush/form';
import TaskConfig from './TaskConfig';
const { newActivityManage, updateUserType, fetchActivityInfo, updateActivityManage } = api;
const { RangePicker } = DatePicker;
type RangeValue = [Moment | null, Moment | null] | null;
export default function({ location }: { location: CusRouter }) {
  const [formData, setFormData] = useState<ActivityDetail<string>>();
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);
  const [dates, setDates] = useState<RangeValue>(null);
  const [value, setValue] = useState<RangeValue>(null);
  const [generalActivityId, setGeneralActivityId] = useState('');
  const disabledDate = (current: Moment) => {
    if (!dates) {
      return false;
    }
    // const tooLate = dates[0] && current.diff(dates[0], 'days') > 7;
    // const tooEarly = dates[1] && dates[1].diff(current, 'days') > 7;
    // return !!tooEarly || !!tooLate;
    // console.log('')
    const data = {
      endTime: dates[1],
      startTime: dates[0],
    };

    // 精确到分
    if (dates[1]) {
      const tooEarly = dates[1] && dates[1].diff(current, 'days') < 0;
      message.error('结束时间不可以小于当前时间');
      console.log('tooEarly', tooEarly);
      return !!tooEarly;
    }

    // console.log('dates', dates[1]);
  };

  const onOpenChange = (open: boolean) => {
    if (open) {
      setDates([null, null]);
    } else {
      setDates(null);
    }
  };
  const ref = useRef({
    activityId: '',
  });
  const [isEdit, setEdit] = useState(true);
  //指定用户
  const [filterId, setFilterId] = useState('');
  const [isUserEdit, setIsUserEdit] = useState(false);
  const activityType = 'GENERAL';

  useEffect(() => {
    // 是否是编辑页
    if (location['query'].id) {
      ref.current.activityId = location['query'].id;
      let body = {
        activityType: activityType,
        activityId: location['query'].id,
      };
      try {
        fetchActivityInfo(body).then((res: { code: number; message: string; data: any }) => {
          if (res.code === 0) {
            const data = res.data;
            const { activityConfig: _activityConfig } = data;
            const activityConfig = JSON.parse(_activityConfig);
            const _formData = {
              ...data,
            };

            setFormData(_formData);
          } else {
            message.error(res.message);
          }
        });
      } catch (err) {
        console.log('获取活动详情接口错误:', err);
        message.error('网络请求错误，请稍后重试');
      }
    }
    let _activityId = ref.current.activityId;
    if (!location['query'].id) {
      genActivityId('GENERAL').then(res => {
        ref.current.activityId = res;
        // console.log('hreh is bug',res)
        setGeneralActivityId(res);
      });
    } else {
      setGeneralActivityId(location['query'].id);
    }
    // 修改dom新增按钮文描
    // let btn = document.getElementsByClassName('ant-btn ant-btn-sm');
    // btn[3].innerHTML = '新增资产类型';
  }, []);
  // 保存
  const onSave = async () => {
    const {
      openAward,
      openAwardCouponList = [],
      tradeAward,
      tradeAwardCouponList,
      awardUrl,
      firstStageIntro,
      secondStageIntro,
      thirdStageIntro,
      startTime,
      endTime,
      activityRule,
      activityStatus,
    } = formData;
    const _formData = formData;
    // 校验
    setShowValidate(true);
    if (formValid.length > 0) {
      return false;
    }
    // if ((openAward.amount && !openAward.fundCode) || (!openAward.amount && openAward.fundCode)) {
    //   message.error('发放数量和发放账号不能同时为空');
    // }

    // 体验金额限制
    // if (_formData?.activityDetails) {
    //   const maxAmount = 99999;
    //   const currentAmount = Number(_formData?.experienceAmount) * _formData?.activityDetails.length;
    //   if (currentAmount > maxAmount) {
    //     return message.error(`体验总金额超出最大值${maxAmount}`);
    //   }
    // }

    // 对于设置了"ui:hidden"属性的表单校验只有样式生效，逻辑不生效，所以需要补充逻辑

    // 最后编辑人、最后编辑时间
    let lastEditor = store.get('name');
    let updateTime = moment().format('YYYY-MM-DD HH:mm:ss');
    // 默认为新增页
    console.log('activity', generalActivityId, ref.current.activityId);
    const obj: any = {
      id: '',
      createTime: formData.createTime || updateTime,
      modifyTime: updateTime,
      activityType: activityType,
      activityId: generalActivityId || ref.current.activityId,
      filterId: '',
      startTime,
      endTime,
      creator: formData.creator || lastEditor,
      editor: lastEditor,
      activityStatus,
      signUpRule: '',
      joinRule: '',
      withdrawalRule: '',
    };
    // 如果是编辑页
    if (location['query'].id) {
      obj.indexStr = location['query'].id;
    } else {
    }
    console.log('obj', obj);
    _postActivityDetails(obj);
  };
  // 取消
  const onCancel = useCallback(() => {
    // console.log('qaq');
    history.push('list');
  }, []);
  // 处理表单变化
  const handleChange = (data: ActivityDetail<string>) => {
    if (data.endTime) {
      // 精确到分
      if (moment().format('YYYY-MM-DD HH:mm') > data.endTime) {
        return message.error('结束时间不可以小于当前时间');
      }
    }
    if (data.startTime && data.endTime) {
      if (data.startTime > data.endTime) {
        return message.error('结束时间不可以小于开始时间');
      }
    }
    let ifPass = true;
    console.log('activityType', activityType);
    ifPass ? setFormData({ ...formData, ...data }) : message.error('最少保留一个基金组合');
  };
  // 体验金活动详情
  const _postActivityDetails = (body: params) => {
    function _success() {
      message.success(ref.current.activityId + '保存成功');
      setTimeout(() => {
        history.push('list');
      }, 1000);
    }
    let apiSave;
    if (location['query'].id) {
      apiSave = updateActivityManage;
    } else {
      apiSave = newActivityManage;
    }

    apiSave(body)
      .then((res: { code: string; message: string }) => {
        res.code == '0' ? _success() : message.error(res.message);
      })
      .catch((err: unknown) => {
        console.log('更新/新增活动详情接口错误：', err);
        message.error('网络请求错误，请稍后重试');
      });
  };
  return (
    <section>
      <article className={styles['m-new-experience-gold']}>
        <section
          style={{ display: 'flex', justifyContent: 'space-between', flexDirection: 'column' }}
        >
          <PageHeader
            ghost={false}
            onBack={() => {
              // console.log('qaq1');
              store.set('activityType', 'GENERAL');
              history.push('list');
            }}
            title="通用配置配置"
            extra={[
              <Button onClick={onSave} type="primary">
                <Icon type="save" />
                保存
              </Button>,
            ]}
          />
          <FormRender
            propsSchema={FORM_JSON}
            formData={
              formData
                ? formData
                : {
                    abButton: false,
                  }
            }
            onChange={(data: any) => handleChange(data)}
            onValidate={setFormValid}
            showValidate={showValidate}
            displayType="row"
          />
        </section>
      </article>
      <Divider />
      <Row>
        <TaskConfig activityId={generalActivityId} />
      </Row>
    </section>
  );
}
