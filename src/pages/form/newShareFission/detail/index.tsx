import React, { useState, useEffect, useCallback, useRef } from 'react';
// form-render
import FORM_JSON from './form.json';
import FormRender from 'form-render/lib/antd';
// 接口
import api from 'api';
// 组件
import { Button, message, Icon } from 'antd';

// 样式
import styles from './index.less';
// 时间
import 'moment/locale/zh-cn';
import moment from 'moment';
// webStorage
import store from 'store';
// TypeScript
import { EnveLope, ActivityDetail, tradeDetail, params, CusRouter } from '../types';
// 路由
import { history } from 'umi';
import UserIdList from '@/pages/form/components/userIdList';
import UploadImg from '../components/uploadImg'; 
import { genActivityId } from '../utils';
const { newActivityManage, updateUserType,fetchActivityInfo ,updateActivityManage} = api;

export default function({ location }: { location: Cus<PERSON>outer }) {
  const [formData, setFormData] = useState<ActivityDetail<string>>();
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);
  const ref = useRef({
    activityId:""
  })
  const [isEdit, setEdit] = useState(true);
  //指定用户
  const [filterId, setFilterId] = useState('')
  const [isUserEdit, setIsUserEdit] = useState(false);
  const activityType = store.get("activityType") ;
  
  useEffect(() => {
    
    // 是否是编辑页
    if (location['query'].id) {
      ref.current.activityId = location['query'].id
      let body = {
        activityType: activityType,
        activityId: location['query'].id,
      };
      fetchActivityInfo(body)
        .then((res: { code: number; message: string; data: any }) => {
          if (res.code === 0) {
            const data = res.data;
            const {activityConfig:_activityConfig} = data;
            const activityConfig = JSON.parse(_activityConfig)
            const {   openAward,
              openAwardCouponList = [],
              tradeAward,
              tradeAwardCouponList=[]} = activityConfig
            const _formData = {
              ...data,
              ...activityConfig,
              openAward:{
                ...openAward,
                ...openAward.awardDetail
              },
              openAwardCouponList:openAwardCouponList.map(item =>{
                return {
                  ...item,
                  ...item.awardDetail
                }
              }),
              tradeAward:{
                ...tradeAward,
                ...tradeAward.awardDetail
              },
              tradeAwardCouponList:tradeAwardCouponList.map(item =>{
                return {
                  ...item,
                  ...item.awardDetail
                }
              }),
            }
      
            setFormData(_formData);
            
          } else {
            message.error(res.message)
          }
        })
        .catch((err: unknown) => {
          console.log('获取活动详情接口错误:', err);
          message.error('网络请求错误，请稍后重试');
        });
    }
    // 修改dom新增按钮文描
    // let btn = document.getElementsByClassName('ant-btn ant-btn-sm');
    // btn[3].innerHTML = '新增资产类型';
  }, []);
  // 保存
  const onSave = async () => {
    const {
      openAward,
      openAwardCouponList = [],
      tradeAward,
      tradeAwardCouponList,
      awardUrl,
      firstStageIntro,
      secondStageIntro,
      thirdStageIntro,
      startTime,
      endTime,
      activityRule,
      activityStatus} = formData;
    const _formData = formData;
    // 校验
    setShowValidate(true);
    if (formValid.length > 0) {
      return false;
    }
    if((openAward.amount && !openAward.fundCode)||(!openAward.amount && openAward.fundCode)){
      message.error("发放数量和发放账号不能同时为空")
    }
    
    // 体验金额限制
    // if (_formData?.activityDetails) {
    //   const maxAmount = 99999;
    //   const currentAmount = Number(_formData?.experienceAmount) * _formData?.activityDetails.length;
    //   if (currentAmount > maxAmount) {
    //     return message.error(`体验总金额超出最大值${maxAmount}`);
    //   }
    // }
    


    // 对于设置了"ui:hidden"属性的表单校验只有样式生效，逻辑不生效，所以需要补充逻辑
    
    // 最后编辑人、最后编辑时间
    let lastEditor = store.get('name');
    let updateTime = moment().format('YYYY-MM-DD HH:mm:ss');
    // 默认为新增页
    let _activityId = ref.current.activityId
    if (!location['query'].id && !_activityId) {
       _activityId = await genActivityId()
       ref.current.activityId  = _activityId

    }
  
     
    const activityConfig = {
      
      openAward:{
        awardId:1,
        operationType:openAward.operationType,
        awardDetail:{
          activityName: openAward.activityName,
          amount: openAward.amount,
          fundCode: openAward.fundCode,
          transferId: openAward.transferId,
          awardReason: openAward.awardReason,
          activityId:_activityId,
        }
      },
      openAwardCouponList:openAwardCouponList.map(item=>{
        const {awardId,operationType,...rest}  = item
        return {
          awardId:2,
          operationType:operationType,
          awardDetail:{
            ...rest,
            activityId:_activityId,
          }
        }
      }) 
        ,
      tradeAward:{
        awardId:1,
        operationType:tradeAward.operationType,
        awardDetail:{
          activityId:_activityId,
          activityName: tradeAward.activityName,
          amount: tradeAward.amount,
          fundCode: tradeAward.fundCode,
          transferId: tradeAward.transferId,
          awardReason: tradeAward.awardReason
        }
      },
      tradeAwardCouponList:tradeAwardCouponList.map(item=>{
        const {awardId,operationType,...rest}  = item
        return {
          awardId:2,
          operationType:operationType,
          awardDetail:{
            ...rest,
            activityId:_activityId,
          }
        }
      }) ,
      awardUrl,
      activityRule,
      firstStageIntro,
      secondStageIntro,
      thirdStageIntro
    }
    const obj: any = {
      id:"",
      createTime:formData.createTime || updateTime,
      modifyTime:updateTime,
      activityType:activityType,
      activityId:_activityId,
      filterId:"",
      startTime,
      endTime,
      creator:formData.creator || lastEditor,
      editor:lastEditor,
      activityStatus,
      signUpRule:"",
      joinRule:"",
      withdrawalRule:"",
      activityConfig:JSON.stringify(activityConfig)
    }
    // 如果是编辑页
    if (location['query'].id) {
      obj.indexStr = location['query'].id;
    }
    else{

    }
    // if (_formData?.abButton) {
    //   if (filterId) {
    //     obj.filterId = filterId;
    //   } else {
    //     let _userData = {
    //       blackUserId: "",
    //       id: "",
    //       kycLogic: "and",
    //       kycs: [],
    //       olasId: "",
    //       olasType: "1",
    //       platform: ["and", "ios", "andsdk", "iossdk", "iossdkvip", "andsdkvip"],
    //       targetType: "kyc",
    //       updateTime,
    //       userType: "1",
    //       utype: ["u0", "u1", "u2", "u3", "u4", "u5", "u6", "u7", "u8", "u9", "u10", "F"],
    //       whiteUserId: ""
    //     }
    //     try {
    //       const { code, message, data } = await updateUserType(_userData);
    //       if (code !== '0000') {
    //         return message.error(message || '系统错误');
    //       }
    //       setFilterId(data);
    //       obj.filterId = data;
    //     }catch(e) {
    //       message.error(e.message || '系统错误');
    //       return;
    //     }
    //   }
    // } else {
    //   obj.filterId = '';
    // }
    
    _postActivityDetails(obj);
  };
  // 取消
  const onCancel = useCallback(() => {
    store.set('activityType', 'FISSION');
    history.push('list');
  }, []);
  // 处理表单变化
  const handleChange = (data: ActivityDetail<string>) => {
    if (data.endTime) {
      // 精确到分
      if (moment().format('YYYY-MM-DD HH:mm') > data.endTime) {
        return message.error('结束时间不可以小于当前时间');
      }
    }
    if (data.startTime && data.endTime) {
      if (data.startTime > data.endTime) {
        return message.error('结束时间不可以小于开始时间');
      }
    }
    // if (data.activityDetails.length === 0) {
    //   return message.error('最少保留一个资产类型');
    // }
    // if (data.activityDetails.length > 3) {
    //   return message.error('最多有3个资产类型');
    // }
    let ifPass = true;
    // data.activityDetails.map((typeItem: tradeDetail) => {
    //   if (typeItem.fundList.length === 0) {
    //     ifPass = false;
    //   }
    // });
    console.log("activityType",activityType)
    ifPass ? setFormData({ ...formData, ...data }) : message.error('最少保留一个基金组合');
  };
  // 体验金活动详情
  const _postActivityDetails = (body: params) => {
    function _success() {
      message.success( ref.current.activityId+'保存成功');
      setTimeout(() => {
        history.push('list');
      }, 1000);
    }
   let apiSave;
    if (location['query'].id) {
      apiSave= updateActivityManage
    }
   else{
    apiSave =newActivityManage
   }
   
   apiSave(body).then((res: { code: string; message: string }) => {
        res.code == '0' ? _success() : message.error(res.message);
      })
      .catch((err: unknown) => {
        console.log('更新/新增活动详情接口错误：', err);
        message.error('网络请求错误，请稍后重试');
      });
  };
  return (
    <article className={styles['m-new-experience-gold']}>
      <section style={{display:"flex",justifyContent:"space-between"}}>
      <Button onClick={onCancel} className={styles['m-backward-btn']}>
        <Icon type="left" />
        取消
      </Button>
      <Button onClick={onSave} type="primary" >
            <Icon type="save" />
            保存
          </Button>
      </section>
     
      <FormRender
        propsSchema={FORM_JSON}
        formData={formData ? formData : {
          abButton: false
        }}
        onChange={(data: any) => handleChange(data)}
        onValidate={setFormValid}
        showValidate={showValidate}
        displayType="row"
        widgets={{
         uploadImg:UploadImg,
        }}
      />
      { formData && formData.abButton && (
        <UserIdList
          filterId={filterId}
          setFilterId={setFilterId}
          isAdd={false}
          isEdit={isEdit}
          setUserTypeData={() => { }}
          isCanEdit={isUserEdit}
          setIsCanEdit={setIsUserEdit}
      />
      ) }
      
      {location['query'].type !== 'check' && (
        <section>
          <Button onClick={onSave} type="primary">
            <Icon type="save" />
            保存
          </Button>
        </section>
      )}
    </article>
  );
}
