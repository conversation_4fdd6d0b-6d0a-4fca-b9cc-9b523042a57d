import React, { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON> from 'form-render/lib/antd';
import axios from 'axios';
import api from 'api';
import {Button, message} from 'antd';
import FROM_JSON from './form.json';

const {fetchValueBlock, postValueBlock} = api;

export default function () {
    const [init, setInit] = useState(false);
    const [formConfig, setFormConfig] = useState(null);
    const [formData, setData] = useState({});
    const [valid, setValid] = useState({});

    useEffect(() => {
        // axios.get('https://testfund.10jqka.com.cn/nodetest/ijijinfe/mock/entry/strategyBlock/valueBlock').then(data => {
            // data = data.data;
            let data = FROM_JSON;
            let _form = data.formData.data;
            let _promiseArr = [];
            for (let i = 0; i < _form.length; i++) {
                _promiseArr.push(
                    new Promise((resolve, reject) => {
                        fetchValueBlock(null, _form[i].thscode).then(res => {
				if (res.data) {
				resolve(JSON.parse(res.data));
				} else {
				reject();
				}
                            
                        }).catch((e = Error) => {
                            message.error(e.message);
                        })
                    }).catch((e = Error) => {
                        message.error(e.message);
                    })
                )
            }
            Promise.all(_promiseArr).then(res => {
                for (let i = 0; i < res.length; i++) {
                    if (res[i]) {
                        data.formData.data[i] = res[i];
                    }
                }
                setInit(true);
                setFormConfig(data);
                setData(data.formData);
            }).catch((e) => {
                // message.error('');
                setInit(true);
                setFormConfig(data);
                setData(data.formData);
            })
        // })
    }, []);

    const onSubmit = () => {
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
        } else {
            let _data = formData.data;
            let _flag = true;
            for (let i = 0; i < _data.length; i++) {
                postValueBlock({
                    value: JSON.stringify(_data[i])
                }, `${_data[i].thscode}`).then((res) => {
                    if (res.code !== '0000') {
                        message.error(`${_data[i].displayName}: ${res.message}`);
                        _flag = false
                    }
                    if (i === _data.length - 1 && _flag) {
                        message.success('保存成功');
                        // setTimeout(() => {
                        //     window.location.reload();
                        // }, 300);
                    }
                }).catch((e = Error) => {
                    message.error(e.message);
                })
            }
        }
    }

    if (!init) return '加载中';
    return (
        <div style={{ padding: 60 }}>
            <FormRender 
                {...formConfig}
                formData={formData}
                onChange={setData}
                onValidate={setValid}
                displayType="row"
                showDescIcon={true}
                column={2}
            />
            <Button type="primary" onClick={onSubmit}>发布</Button>
        </div>
    )
}