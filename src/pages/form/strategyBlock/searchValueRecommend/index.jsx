import React, { useState, useEffect } from 'react';
import FormRender from 'form-render/lib/antd';
import axios from 'axios';
import api from 'api';
import { Button, message, Select, Descriptions } from 'antd';

const { fetchRecommendData } = api;
const { Option } = Select;

/* global _ */
// 时间戳转 yyyy-mm-dd
const timeStringFormat = (time) => {
    let date = new Date(time),
        year = date.getFullYear(),
        month = date.getMonth() + 1,
        day = date.getDate();
    month = month < 10 ? "0" + month : month;
    day = day < 10 ? "0" + day : day;
    return year + '-' + month + '-' + day;
}

function safePerShow(number) {
    if (!number && number !== 0) {
      return '--';
    }
    return (number * 100).toFixed(2) + '%';
}

export default function () {
    const [queryBlockCode, setQueryBlockCode] = useState("");
    const [recommandDesList, setRecommandDesList] = useState([]);

    useEffect(() => {

    }, []);

    const handleChange = (value) => {
        setQueryBlockCode(value);
        setRecommandDesList([]);
    }

    const queryBlockRecommendData = () => {
        if (queryBlockCode) {
            // console.log(queryBlockCode);
            let BlockDataValue = {
                "period": null,
                "displayName": "",
                "thscode": queryBlockCode,
                "date": "",
                "endDate": ""
            }
            fetchRecommendData({
                BlockDataValue: JSON.stringify(BlockDataValue),
            }).then((res) => {
                if (res.code === '0000') {
                    if (!res.data) {
                        message.error('该板块暂无配置');
                        return;
                    }
                    let _data = JSON.parse(res.data);
                    if (_data.length === 0) {
                        message.error('该板块暂无配置');
                        return;
                    }
                    // console.log(_data);
                    let _descArr = [];
                    _descArr.length = _data.length;
                    for (let i = 0; i < _data.length; i++) {
                        _descArr[i] = {};
                        _descArr[i].data = [];
                        for (let key in _data[i]) {
                            switch(key) {
                                case 'period':
                                    _descArr[i].period = _data[i].period;
                                    break;
                                case 'date':
                                    _descArr[i].date = timeStringFormat(_data[i].date)
                                    break;
                                case 'averageGain':
                                    _descArr[i].data.push({
                                        key: '区间涨幅',
                                        value: safePerShow(_data[i][key])
                                    });
                                    break;
                                case 'displayName':
                                    _descArr[i].data.push({
                                        key: '板块名称',
                                        value: (_data[i][key])
                                    });
                                    break;
                                case 'endDate':
                                    _descArr[i].data.push({
                                        key: '结束时间',
                                        value: timeStringFormat(_data[i][key])
                                    });
                                    break;
                                case 'finishFlag':
                                    _descArr[i].data.push({
                                        key: '是否跟踪完成',
                                        value: _data[i][key] == 1 ? '是' : '否'
                                    });
                                    break;
                                case 'maxGain':
                                    _descArr[i].data.push({
                                        key: '区间最大涨幅',
                                        value: safePerShow(_data[i][key])
                                    });
                                    break;
                                case 'maxGainDate':
                                    _descArr[i].data.push({
                                        key: '最大涨幅时间',
                                        value: timeStringFormat(_data[i][key])
                                    });
                                    break;
                                case 'sumGain':
                                    _descArr[i].data.push({
                                        key: '总共收益',
                                        value: safePerShow(_data[i][key])
                                    });
                                    break;
                                case 'thscode':
                                    _descArr[i].data.push({
                                        key: '板块代码',
                                        value: (_data[i][key])
                                    });
                                    break;
                                case 'upFlag':
                                    _descArr[i].data.push({
                                        key: '达标情况',
                                        value: _data[i][key] == 1 ? '达标' : _data[i][key] == 0 ? '未达标' : '跟踪中'
                                    });
                                    break;
                                default:
                                    _descArr[i].data.push({
                                        key,
                                        value: _data[i][key]
                                    });
                                    break;
                            }
                        }
                    }
                    // console.log(_descArr);
                    setRecommandDesList(_descArr);
                } else {
                    message.error('网络错误，请稍后再试');
                }
            }).catch((e = Error) => {
                message.error(e.message);
            })
        } else {
            message.error('请选择板块');
        }
    }
    return (
        <div style={{ padding: 60 }}>
            <div>
                <span>查询板块：</span>
                <Select value={queryBlockCode} style={{ width: '300px' }} onChange={handleChange}>
                    <Option value="H30169.CSI">资本品|H30169.CSI</Option>
                    <Option value="H30170.CSI">商业指数|H30170.CSI</Option>
                    <Option value="H30171.CSI">运输指数|H30171.CSI</Option>
                    <Option value="H30164.CSI">汽车汽配|H30164.CSI</Option>
                    <Option value="H30173.CSI">消费品|H30173.CSI</Option>
                    <Option value="H30175.CSI">零售指数|H30175.CSI</Option>
                    <Option value="H30172.CSI">耐用服装|H30172.CSI</Option>
                    <Option value="H30174.CSI">媒体指数|H30174.CSI</Option>
                    <Option value="H30176.CSI">食药零售|H30176.CSI</Option>
                    <Option value="H30177.CSI">饮食烟草|H30177.CSI</Option>
                    <Option value="H30178.CSI">医疗保健|H30178.CSI</Option>
                    <Option value="H30179.CSI">医药生科|H30179.CSI</Option>
                    <Option value="399986.SZ">中证银行|399986.SZ</Option>
                    <Option value="H30186.CSI">保险指数|H30186.CSI</Option>
                    <Option value="H30211.CSI">资本市场|H30211.CSI</Option>
                    <Option value="H30165.CSI">房地产|H30165.CSI</Option>
                    <Option value="H30182.CSI">软件服务|H30182.CSI</Option>
                    <Option value="H30184.CSI">半导体|H30184.CSI</Option>
                    <Option value="931160.CSI">通信设备|931160.CSI</Option>
                    <Option value="H30183.CSI">硬件设备|H30183.CSI</Option>
                    <Option value="000827.SH">中证环保|000827.SH</Option>
                    <Option value="000986.SH">全指能源|000986.SH</Option>
                    <Option value="000987.SH">全指材料|000987.SH</Option>
                    <Option value="000995.CSI">全指公用|000995.CSI</Option>
                </Select>
                <Button style={{ marginLeft: 20 }} type="primary" onClick={queryBlockRecommendData}>查询</Button>
            </div>
            <div style={{ marginBottom: 60 }}>
                {
                    recommandDesList.map(item => (
                        <Descriptions title={`第${item.period}期 ${item.date}`} bordered size="small" style={{ marginBottom: 20 }}>
                            {
                                item.data.map((data, index) => (
                                    <Descriptions.Item key={index} label={data.key}>{data.value}</Descriptions.Item>
                                ))
                            }
                        </Descriptions>
                    ))
                }
            </div>
        </div>
    )
}