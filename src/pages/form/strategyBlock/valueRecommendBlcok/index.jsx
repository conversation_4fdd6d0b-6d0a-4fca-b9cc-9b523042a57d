import React, { useState, useEffect } from 'react';
import FormRender from 'form-render/lib/antd';
import axios from 'axios';
import api from 'api';
import {Button, message, Select } from 'antd';
import FROM_JSON from './form.json';

const {postValueRecommendBlcok, fetchRecommendData} = api;
const { Option } = Select;

/* global _ */
// 时间戳转 yyyy-mm-dd
const timeStringFormat = (time) => {
    let date = new Date(time),
        year = date.getFullYear(),
        month = date.getMonth() + 1,
        day = date.getDate();
    month = month < 10 ? "0" + month : month;
    day = day < 10 ? "0" + day : day;
    return year + '-' + month + '-' + day;
}

// 处理从接口获取到的数据
const handleFetchData = (data) => {
    let _formData = [];
    for (let i in data) {
        let _canModify = new Date(data[i][0].date) > new Date() ? true : false
        let _periodData = {
            period: data[i][0].period,
            date: [timeStringFormat(data[i][0].date), timeStringFormat(data[i][0].endDate)],
            block: [],
            canModify: _canModify
        }
        for (let j = 0; j < data[i].length; j++) {
            _periodData.block.push({
                displayName: data[i][j].displayName + "|" + data[i][j].thscode,
                maxGain: data[i][j].maxGain,
                averageGain: data[i][j].averageGain,
                sumGain: data[i][j].sumGain,
                canModify: _canModify
            })
        }
        _formData.push(_periodData);
    }
    return _formData;
}

const findOneInArr = (arr, obj) => {
    for (let i = 0; i < arr.length; i++) {
        if (arr[i].period === obj.period && arr[i].thscode === obj.thscode) {
            return arr[i];
        }
    }
    return null;
}

// 处理数据传给接口 delete update insert
const handlePostData = (data) => {
    let standard = [];
    for (let i = 0; i < _.valueRecommendBlock.length; i++) {
        let item = _.valueRecommendBlock[i];
        for (let j = 0; j < item.block.length; j++) {
            standard.push({
                period: item.period,
                displayName: item.block[j].displayName.split('|')[0],
                thscode: item.block[j].displayName.split('|')[1],
                date: item.date[0],
                endDate: item.date[1]
            })
        }
    }
    let _formData = [];
    for (let i = 0; i < data.data.length; i++) {
        let item = data.data[i];
        for (let j = 0; j < item.block.length; j++) {
            _formData.push({
                period: item.period,
                displayName: item.block[j].displayName.split('|')[0],
                thscode: item.block[j].displayName.split('|')[1],
                date: item.date[0],
                endDate: item.date[1]
            })
        }
    }
    // console.log(_formData);
    // console.log(standard);
    let postData = {
        _update: [],
        _insert: [],
        _delete: []
    }
    for (let i = 0; i < standard.length; i++) {
        if (findOneInArr(_formData, standard[i])) {
            // update
            postData._update.push(findOneInArr(_formData, standard[i]));
        } else {
            // delete
            postData._delete.push(standard[i]);
        }
    }
    for (let i = 0; i < _formData.length; i++) {
        if (!findOneInArr(standard, _formData[i])) {
            // insert
            postData._insert.push(_formData[i]);
        }
    }
    return postData;
}

export default function () {
    const [init, setInit] = useState(false);
    const [formConfig, setFormConfig] = useState(null);
    const [formData, setData] = useState({});
    const [valid, setValid] = useState({});

    useEffect(() => {
        // axios.get('https://testfund.10jqka.com.cn/nodetest/ijijinfe/mock/entry/strategyBlock/valueRecommendBlcok').then(data => {
            // data = data.data;
            let data = FROM_JSON;
            let _postData = {
                period: null,
                displayName: '',
                thscode: '',
                date: '',
                endDate: '',
            }
            postValueRecommendBlcok({
                BlockDataValue: JSON.stringify(_postData)
            }, 'get').then(res => {
                if (res.data) {
                    data.formData.data = handleFetchData(JSON.parse(res.data));
                    _.valueRecommendBlock = handleFetchData(JSON.parse(res.data));
                } else {
                    _.valueRecommendBlock = [];
                }
                setInit(true);
                setFormConfig(data);
                setTimeout(()=>{
                    setData(data.formData);
                })
            }).catch((e = Error) => {
                message.error(e.message);
            })
        // })
    }, []);

    const onSubmit = () => {
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
        } else {
            let postData = handlePostData(formData);
            console.log(postData);
            let {_update, _delete, _insert} = postData;
            let _promiseArr = [];
            for (let i = 0; i < _delete.length; i++) {
                _promiseArr.push(
                    new Promise((resolve, reject) => {
                        postValueRecommendBlcok({
                            BlockDataValue: JSON.stringify(_delete[i]),
                        }, `delete&${i}`).then((res) => {
                            if (res.code === '0000') {
                                resolve(true);
                            } else {
                                resolve(`第${_delete[i].period}期，${_delete[i].displayName}|${_delete[i].thscode}上传失败`);
                            }
                        }).catch((e = Error) => {
                            message.error(e.message);
                        })
                    }).catch((e = Error) => {
                        message.error(e.message);
                    })
                )
            }
            for (let i = 0; i < _update.length; i++) {
                _promiseArr.push(
                    new Promise((resolve, reject) => {
                        postValueRecommendBlcok({
                            BlockDataValue: JSON.stringify(_update[i]),
                        }, `update&${i}`).then((res) => {
                            if (res.code === '0000') {
                                resolve(true);
                            } else {
                                resolve(`第${_update[i].period}期，${_update[i].displayName}|${_update[i].thscode}上传失败`);
                            }
                        }).catch((e = Error) => {
                            message.error(e.message);
                        })
                    }).catch((e = Error) => {
                        message.error(e.message);
                    })
                )
            }
            for (let i = 0; i < _insert.length; i++) {
                _promiseArr.push(
                    new Promise((resolve, reject) => {
                        postValueRecommendBlcok({
                            BlockDataValue: JSON.stringify(_insert[i]),
                        }, `insert&${i}`).then((res) => {
                            if (res.code === '0000') {
                                resolve(true);
                            } else {
                                resolve(`第${_insert[i].period}期，${_insert[i].displayName}|${_insert[i].thscode}上传失败`);
                            }
                        }).catch((e = Error) => {
                            message.error(e.message);
                        })
                    }).catch((e = Error) => {
                        message.error(e.message);
                    })
                )
            }
            Promise.all(_promiseArr).then(res => {
                let _flag = true;
                for (let i = 0; i < res.length; i++) {
                    if (res[i] !== true) {
                        message.error(res[i]);
                        _flag = false;
                    }
                }
                if (_flag) {
                    message.success('发布成功');
                    setTimeout(() => {
                        // window.location.reload();
                    }, 300);
                }
            })
        }
    }

    if (!init) return '加载中';
    return (
        <div style={{ padding: 60 }}>
            <FormRender 
                {...formConfig}
                formData={formData}
                onChange={setData}
                onValidate={setValid}
                displayType="row"
                showDescIcon={true}
                column={2}
            />
            <Button type="primary" onClick={onSubmit}>发布</Button>
        </div>
    )
}