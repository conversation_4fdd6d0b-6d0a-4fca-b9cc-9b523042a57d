import React, { useState, useEffect, useCallback,useImperativeHandle, forwardRef } from 'react';
// form-render
import FORM_JSON from './form.json';
import FormRender from 'form-render/lib/antd';
// 接口
import api from 'api';
// 组件
import { Button, message, Icon } from 'antd';

import uploadImg from '@/components/uploadImg'

// 时间
import 'moment/locale/zh-cn';
import moment from 'moment';
// webStorage
import store from 'store';
// TypeScript
// 路由
import { history } from 'umi';
const { postHash,fetchHash,fetchBlockDetail } = api;

const key = "blockDetail_index"


export default forwardRef(function(props:any,ref) {
  const {  id,type,handleCancel} = props
  const [formData, setFormData] = useState();
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);
  useImperativeHandle(ref,()=>({
    onCancelClick:onCancel,
    onSubmitClick:onSave
  }))
  useEffect(() => {
    // 是否是编辑页
    if (id) {
      let body = {
        key,
        propName: id,
      };
      fetchHash(body)
        .then((res: { code: string; message: string; data: string }) => {
          if (res.code === '0000') {
            if(!res.data){
              return
            }
            const formData = JSON.parse(res.data)
            setFormData(formData);
          } else {
            message.error(res.message)
          }
        })
        .catch((err: unknown) => {
       
          message.error('网络请求错误，请稍后重试');
        });
    }
  }, []);
  // 保存
  const onSave = async () => {
    const _formData = formData;
    // 校验
    setShowValidate(true);
    if (formValid.length > 0) {
      return false;
    }

    // 最后编辑人、最后编辑时间
    const lastEditor = store.get('name');
    const updateTime = moment().format('YYYY-MM-DD HH:mm:ss');
    // 默认为新增页
    const params:any = {
      key,
    };
    const obj: any = {
      ..._formData,
      updateTime,
      lastEditor,
    }
    // 如果是编辑页
    if (id) {
      params.propName = id;
    }
    else{
      params.propName = _formData.code
    }
   
    params.value = JSON.stringify(obj);
    postHash(params).then((res: { code: string; message: string })=>{
      if(res.code ==="0000"){
        message.success("保存成功")
      }
      else{
        message.success(res.message)
      }
    });
  };
  // 取消
  const onCancel = useCallback(() => {
    handleCancel()
  }, []);
  // 处理表单变化
  const handleChange = (data: any) => {
    console.log("onChange",data)
    // if(formData && data.code != formData.code){
    //   data.name = ""
    //   if(data.code.length>=8){
    //     fetchBlockDetail({
    //       period:"year",
    //       pageNum:1,
    //       pageSize:5,
    //       sortType:"desc"
    //     },data.code).then(res =>{
    //       if(res.data){
    //         const name  = res.data.name;
    //         setFormData({...formData,name})
    //       }
    //     })
    //   }
    // }
    setFormData({
      ...formData,
      ...data
    })
  };
  return (
    <article >
     
      <FormRender
        propsSchema={FORM_JSON}
        formData={formData }
        onChange={(data: any) => handleChange(data)}
        onValidate={setFormValid}
        showValidate={showValidate}
        displayType="row"
        showDescIcon
        widgets={{ uploadImg: uploadImg }}
      />
     
    </article>
  );
})
