import React, { useState, useEffect } from 'react';
import api from 'api';
import { Input, Select, message, Button, Card, Radio, DatePicker } from 'antd';

import moment from 'moment';

interface IBaseProps {
	remark?: string;
	content: string;
	id: number;
	boardId: string;
	redPacketInfo: string;
}

interface IProps {
	onCancel: any;
	reload: any;
	cORm: string;
	boardType: number;
	activeData: IBaseProps|null;
}

export default function({onCancel, reload, cORm, boardType, activeData}: IProps) {

	const { addWholeDayMarketing, modifyWholeDayMarketing } = api;

	const [title, set_title] = useState(''); // 弹窗标题
	const [redPacketInfo, set_redPacketInfo] = useState(''); // 红包id
	const [remark, set_remark] = useState(''); // 备注
	const [overType, set_overType] = useState(0); // 领取后状态 0-领取成功 1-自定义产品 2-上一步已购买产品

	// 领取成功
	const [jumpUrl, set_jumpUrl] = useState(''); // 红包跳转链接

	// 自定义产品
	const [fundType, set_fundType] = useState(0); // 0-组合 1-新发 2-持营基金 3-小目标
	const [subtitle, set_subtitle] = useState(''); // 产品卡大标题
	// 组合
	const [groupName, set_groupName] = useState(''); // 组合名称
	const [groupCode, set_groupCode] = useState(''); // 组合代码
	const [groupTag, set_groupTag] = useState(''); // 组合标签
	// 新发基金
	const [newFundCode, set_newFundCode] = useState(''); // 新发基金代码
	const [fundManagerName, set_fundManagerName] = useState(''); // 基金经理名字
	const [fundManagerDesc, set_fundManagerDesc] = useState(''); // 基金经理文案
	const [newFundDesc, set_newFundDesc] = useState('');
	const [newFundTag, set_newFundTag] = useState('');
	const [endDate, set_endDate] = useState(''); // 募集结束时间
	// 持营基金
	const [fundCode, set_fundCode] = useState(''); // code
	const [fundName, set_fundName] = useState(''); // 基金名称
	const [fundDesc, set_fundDesc] = useState(''); // 亮点文案
	const [fundTag, set_fundTag] = useState(''); // 标签
	const [syRange, set_syRange] = useState<any>('year'); // 收益区间
	const [bottomText, set_bottomText] = useState('');
	// 小目标
	const [smallTargetCode, set_smallTargetCode] = useState(''); // 小目标代码
	const [smallTargetTag, set_smallTargetTag] = useState(''); // 小目标标签

	// 上一步已购买产品
	const [defaultFundCode, set_defaultFundCode] = useState(''); // code
	const [defaultFundName, set_defaultFundName] = useState(''); // 基金名称
	const [defaultFundDesc, set_defaultFundDesc] = useState(''); // 亮点文案
	const [defaultFundTag, set_defaultFundTag] = useState(''); // 标签
	const [defaultSyRange, set_defaultSyRange] = useState<any>('year'); // 收益区间
	const [defaultBottomText, set_defaultBottomText] = useState(''); // 辅助文案

	const save = () => {
		if (!title) {
			message.error('请填写弹窗标题');
			return;
		}
		if (!redPacketInfo) {
			message.error('请填写红包ID');
			return;
		} else if (redPacketInfo.includes(' ')) {
			message.error('红包ID中含有空格');
			return;
		} else if (redPacketInfo.includes('，')) {
			message.error('红包ID中含有中文逗号');
			return;
		}
		if (!jumpUrl) {
			message.error('请填写红包链接');
			return;
		} else if (!/^https:\/\/[^\n ，]*$/.test(jumpUrl)) {
			message.error('红包链接格式错误');
			return;
		}
		if (overType === 0) {
			// 领取成功
		} else if (overType === 1) {
			// 自定义产品
			if(!subtitle) {
				message.error('请填写产品卡大标题');
				return;
			}
			if (fundType === 0) {
				if (!groupCode) {
					message.error('请填写组合代码');
					return;
				} else if (groupCode.includes(' ')) {
					message.error('组合代码中含有空格');
					return;
				}
				if (!groupName) {
					message.error('请填写组合名称');
					return;
				}
			} else if (fundType === 1) {
				if (!newFundCode) {
					message.error('请填写基金代码');
					return;
				} else if (newFundCode.includes(' ')) {
					message.error('基金代码中含有空格');
					return;
				} else if (newFundCode.length !== 6) {
					message.error('基金代码需是6位');
					return;
				}
				if (!fundManagerName) {
					message.error('请填写基金经理名字');
					return;
				}
				if (!fundManagerDesc) {
					message.error('请填写基金经理文案');
					return;
				}
				if (!newFundDesc) {
					message.error('请填写亮点文案');
					return;
				}
				if (!newFundTag) {
					message.error('请填写标签');
					return;
				} else {
					let _fundTag: any = newFundTag;
					_fundTag = _fundTag.split(',');
					for (let i = 0; i <_fundTag.length; i++) {
						if (_fundTag[i].length > 5) {
							message.error('单个标签的长度不可超过5个');
							return;
						}
					}
				}
				if (!endDate) {
					message.error('请填写募集结束时间');
					return;
				}
			} else if (fundType === 2) {
				if (!fundCode) {
					message.error('请填写基金代码');
					return;
				} else if (fundCode.includes(' ')) {
					message.error('基金代码中含有空格');
					return;
				} else if (fundCode.length !== 6) {
					message.error('基金代码需是6位');
					return;
				}
				if (!fundName) {
					message.error('请填写基金名称');
					return;
				}
				if (!fundDesc) {
					message.error('请填写亮点文案');
					return;
				}
				if (!fundTag) {
					message.error('请填写标签');
					return;
				} else {
					let _fundTag: any = fundTag;
					_fundTag = _fundTag.split(',');
					for (let i = 0; i <_fundTag.length; i++) {
						if (_fundTag[i].length > 5) {
							message.error('单个标签的长度不可超过5个');
							return;
						}
					}
				}
				if (!bottomText) {
					message.error('请填写辅助文案');
					return;
				}
			} else if (fundType === 3) {
				if (!smallTargetCode) {
					message.error('请填写小目标代码');
					return;
				} else if (smallTargetCode.includes(' ')) {
					message.error('小目标代码中含有空格');
					return;
				}
			}
		} else if (overType === 2) {
			// 上一步已购买产品
			if (!subtitle) {
				message.error('请填写产品卡大标题');
				return;
			}
			if (!defaultFundCode) {
				message.error('请填写兜底产品代码');
				return;
			} else if (defaultFundCode.includes(' ')) {
				message.error('兜底产品代码中含有空格');
				return;
			} else if (defaultFundCode.length !== 6) {
				message.error('兜底产品代码需是6位');
				return;
			}
			if (!defaultFundName) {
				message.error('请填写兜底产品名称');
				return;
			}
			if (!defaultFundDesc) {
				message.error('请填写兜底产品亮点文案');
				return;
			}
			if (!defaultFundTag) {
				message.error('请填写兜底产品标签');
				return;
			} else {
				let _fundTag: any = defaultFundTag;
				_fundTag = _fundTag.split(',');
				for (let i = 0; i <_fundTag.length; i++) {
					if (_fundTag[i].length > 5) {
						message.error('单个标签的长度不可超过5个');
						return;
					}
				}
			}
			if (!defaultBottomText) {
				message.error('请填写兜底辅助文案');
				return;
			}
		}
		let _overData: any = {}
		if (overType === 0) {
			_overData = {
				jumpUrl
			}
		} else if (overType === 1) {
			let _prodData: any = {}
			if (fundType === 0) {
				_prodData = {
					jumpUrl,
					fundType,
					groupName,
					groupCode,
					groupTag
				}
			} else if (fundType === 1) {
				_prodData = {
					jumpUrl,
					fundType,
					newFundCode,
					fundManagerName,
					fundManagerDesc,
					newFundDesc,
					newFundTag,
					endDate
				}
			} else if (fundType === 2) {
				_prodData = {
					jumpUrl,
					fundType,
					fundCode,
					fundName,
					syRange,
					fundDesc,
					fundTag,
					bottomText
				}
			} else if (fundType === 3) {
				_prodData = {
					jumpUrl,
					fundType,
					smallTargetCode,
					smallTargetTag
				}
			}
			_overData = {..._prodData}
		} else if (overType === 2) {
			_overData = {
				jumpUrl,
				fundCode: defaultFundCode,
				fundName: defaultFundName,
				syRange: defaultSyRange,
				fundDesc: defaultFundDesc,
				fundTag: defaultFundTag,
				bottomText: defaultBottomText,
				fundType: 2
			}
		}
		let _content: any = {
			title,
			overType,
			subtitle: subtitle,
			overData: _overData
		}
		let _postData: any = {
			boardType,
			remark,
			redPacketInfo,
			content: JSON.stringify(_content)
		}
		if (cORm === 'c') {
			// 创建
			addWholeDayMarketing({
				marketContactOperationStr: JSON.stringify(_postData)
			}).then((data: any) => {
				if (data.code === "0000") {
					reload();
				} else {
					message.error(data.message || '保存失败，请稍后再试')
				}
			}).catch(() => {
				message.error('保存失败，请稍后再试');
			})
		} else if (cORm === 'm') {
			// 修改
			_postData.id = activeData?.id;
			_postData.boardId = activeData?.boardId;
			modifyWholeDayMarketing({
				marketContactOperationStr: JSON.stringify(_postData)
			}).then((data: any) => {
				if (data.code === "0000") {
					reload();
				} else {
					message.error(data.message || '保存失败，请稍后再试')
				}
			}).catch(() => {
				message.error('保存失败，请稍后再试');
			})
		}
	}

	useEffect(() => {
		if (cORm === 'm' && activeData && activeData.content) {
			let content:any = activeData.content;
			content = JSON.parse(content);
			const { overData, overType } = content;

			set_remark(activeData.remark || '');
			set_redPacketInfo(activeData.redPacketInfo || '');

			set_title(content.title || '');
			set_overType(overType || 0);

			set_subtitle(content.subtitle || '');

			set_jumpUrl(overData.jumpUrl || '');

			if (overType === 0) {
				// set_jumpUrl(overData.jumpUrl || '');
			} else if (overType === 1) {
				set_fundType(overData.fundType || 0);
				
				set_groupName(overData.groupName || '');
				set_groupCode(overData.groupCode || '');
				set_groupTag(overData.groupTag || '');

				set_newFundCode(overData.newFundCode || '');
				set_fundManagerName(overData.fundManagerName || '');
				set_fundManagerDesc(overData.fundManagerDesc || '');
				set_newFundDesc(overData.newFundDesc || '');
				set_newFundTag(overData.newFundTag || '');
				set_endDate(overData.endDate || '');

				set_fundCode(overData.fundCode || '');
				set_fundName(overData.fundName || '');
				set_fundDesc(overData.fundDesc || '');
				set_fundTag(overData.fundTag || '');
				set_syRange(overData.syRange || 'year');
				set_bottomText(overData.bottomText || '');

				set_smallTargetCode(overData.smallTargetCode || '');
				set_smallTargetTag(overData.smallTargetTag || '');
			} else if (overType === 2) {
				set_defaultFundCode(overData.fundCode);
				set_defaultFundName(overData.fundName || '');
				set_defaultFundDesc(overData.fundDesc || '');
				set_defaultFundTag(overData.fundTag || '');
				set_defaultSyRange(overData.syRange || 'year');
				set_defaultBottomText(overData.bottomText || '')
			}
		}
	}, [])

	const groupForm = () => {
		return (
			<>
				<div style={{marginTop: 10}}>
					<span>组合代码<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={groupCode}
						onChange={e => { set_groupCode(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>组合名称<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={groupName}
						onChange={e => { set_groupName(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>标签：</span>
					<Input
						style={{ width: 400 }}
						value={groupTag}
						onChange={e => { set_groupTag(e.target.value); }}
						placeholder="单标签，只需要配一个"
					/>
				</div>
			</>
		)
	}

	const newFundForm = () => {
		return (
			<>
				<div style={{marginTop: 10}}>
					<span>基金代码<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={newFundCode}
						onChange={e => { set_newFundCode(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>基金经理名字<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={fundManagerName}
						onChange={e => { set_fundManagerName(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>基金经理文案<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 400 }}
						value={fundManagerDesc}
						onChange={e => { set_fundManagerDesc(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>亮点文案<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 400 }}
						value={newFundDesc}
						onChange={e => { set_newFundDesc(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>标签<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 400 }}
						value={newFundTag}
						onChange={e => { set_newFundTag(e.target.value); }}
						placeholder="多个标签用英文逗号分隔，单个标签不超过5个字，仅展示前三个标签"
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>募集结束时间<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<DatePicker
						showTime
						format="YYYY-MM-DD HH:mm:ss"
						value={endDate ? moment(endDate, 'YYYY-MM-DD HH:mm:ss') : null}
						onChange={(date, dateString) => { set_endDate(dateString) }}
					/>
				</div>
			</>
		)
	}

	const normalFundForm = () => {
		return (
			<>
				<div style={{marginTop: 10}}>
					<span>基金代码<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={fundCode}
						onChange={e => { set_fundCode(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>基金名称<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={fundName}
						onChange={e => { set_fundName(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>收益区间<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Select 
						style={{width: 200}}
						onChange={(value: any) => {set_syRange(value)}}
						value={syRange}
					>
						<Select.Option value="month">近一月收益率</Select.Option>
						<Select.Option value="tmonth">近三月收益率</Select.Option>
						<Select.Option value="hyear">近半年收益率</Select.Option>
						<Select.Option value="year">近一年收益率</Select.Option>
						<Select.Option value="tyear">近三年收益率</Select.Option>
					</Select>
				</div>
				<div style={{marginTop: 10}}>
					<span>亮点文案<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 400 }}
						value={fundDesc}
						onChange={e => { set_fundDesc(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>标签<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 400 }}
						value={fundTag}
						onChange={e => { set_fundTag(e.target.value); }}
						placeholder="多个标签用英文逗号分隔，单个标签不超过5个字，仅展示前两个标签"
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>辅助文案<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 400 }}
						value={bottomText}
						onChange={e => { set_bottomText(e.target.value); }}
					/>
				</div>
			</>
		)
	}

	const smallTargetForm = () => {
		return (
			<>
				<div style={{marginTop: 10}}>
					<span>小目标代码<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={smallTargetCode}
						onChange={e => { set_smallTargetCode(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>标签：</span>
					<Input
						style={{ width: 400 }}
						value={smallTargetTag}
						onChange={e => { set_smallTargetTag(e.target.value); }}
						placeholder="单标签，只需要配一个"
					/>
				</div>
			</>
		)
	}

  return (
    <div>
			<div style={{marginTop: 10}}>
        <span>弹窗标题<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <Input
          style={{ width: 500 }}
          value={title}
          onChange={e => { set_title(e.target.value); }}
        />
      </div>
			<div style={{marginTop: 10}}>
        <span>红包ID<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <Input
          style={{ width: 500 }}
          value={redPacketInfo}
          onChange={e => { set_redPacketInfo(e.target.value); }}
					placeholder="多个红包id用英文逗号分隔"
        />
      </div>
			<div style={{marginTop: 10}}>
				<span>领取后状态<i style={{color: '#fe5d4e'}}> *</i>：</span>
				<Select style={{width: 200}} value={overType} onChange={set_overType}>
					<Select.Option value={0}>领取成功</Select.Option>
					<Select.Option value={1}>自定义产品</Select.Option>
					<Select.Option value={2}>上一步已购买产品</Select.Option>
				</Select>
			</div>
			<Card style={{marginTop: 30, marginBottom: 30}}>
				<div style={{marginTop: 10}}>
					<span>红包链接<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 500 }}
						value={jumpUrl}
						onChange={e => { set_jumpUrl(e.target.value); }}
					/>
				</div>
				{
					overType === 1 ?
					<>
						<div style={{marginTop: 10}}>
							<span>产品卡大标题<i style={{color: '#fe5d4e'}}> *</i>：</span>
							<Input
								style={{ width: 500 }}
								value={subtitle}
								onChange={e => { set_subtitle(e.target.value); }}
							/>
						</div> 
						<div style={{marginTop: 10, marginBottom: 30}}>
							<span>产品类型<i style={{color: '#fe5d4e'}}> *</i>：</span>
							<Radio.Group onChange={(e) => {set_fundType(e.target.value)}} value={fundType}>
								<Radio value={0}>组合</Radio>
								<Radio value={1}>新发</Radio>
								<Radio value={2}>持营基金</Radio>
								<Radio value={3}>小目标</Radio>
							</Radio.Group>
							<div style={{marginTop: 30}}>
								{
									fundType === 0 ?
									groupForm() :
									fundType === 1 ?
									newFundForm() :
									fundType === 2 ?
									normalFundForm() :
									fundType === 3 ?
									smallTargetForm() : null
								}
							</div>
						</div>
					</> : null
				}
				{
					overType === 2 ?
					<>
						<p>兜底产品配置</p>
						<div style={{marginTop: 10}}>
							<span>产品卡大标题<i style={{color: '#fe5d4e'}}> *</i>：</span>
							<Input
								style={{ width: 500 }}
								value={subtitle}
								onChange={e => { set_subtitle(e.target.value); }}
							/>
						</div> 
						<div style={{marginTop: 10}}>
							<span>基金代码<i style={{color: '#fe5d4e'}}> *</i>：</span>
							<Input
								style={{ width: 200 }}
								value={defaultFundCode}
								onChange={e => { set_defaultFundCode(e.target.value); }}
							/>
						</div>
						<div style={{marginTop: 10}}>
							<span>基金名称<i style={{color: '#fe5d4e'}}> *</i>：</span>
							<Input
								style={{ width: 200 }}
								value={defaultFundName}
								onChange={e => { set_defaultFundName(e.target.value); }}
							/>
						</div>
						<div style={{marginTop: 10}}>
							<span>收益区间<i style={{color: '#fe5d4e'}}> *</i>：</span>
							<Select 
								style={{width: 200}}
								onChange={(value: any) => {set_defaultSyRange(value)}}
								value={defaultSyRange}
							>
								<Select.Option value="month">近一月收益率</Select.Option>
								<Select.Option value="tmonth">近三月收益率</Select.Option>
								<Select.Option value="hyear">近半年收益率</Select.Option>
								<Select.Option value="year">近一年收益率</Select.Option>
								<Select.Option value="tyear">近三年收益率</Select.Option>
							</Select>
						</div>
						<div style={{marginTop: 10}}>
							<span>亮点文案<i style={{color: '#fe5d4e'}}> *</i>：</span>
							<Input
								style={{ width: 400 }}
								value={defaultFundDesc}
								onChange={e => { set_defaultFundDesc(e.target.value); }}
							/>
						</div>
						<div style={{marginTop: 10}}>
							<span>标签<i style={{color: '#fe5d4e'}}> *</i>：</span>
							<Input
								style={{ width: 400 }}
								value={defaultFundTag}
								onChange={e => { set_defaultFundTag(e.target.value); }}
								placeholder="多个标签用英文逗号分隔，单个标签不超过5个字，仅展示前两个标签"
							/>
						</div>
						<div style={{marginTop: 10}}>
							<span>辅助文案<i style={{color: '#fe5d4e'}}> *</i>：</span>
							<Input
								style={{ width: 400 }}
								value={defaultBottomText}
								onChange={e => { set_defaultBottomText(e.target.value); }}
							/>
						</div>
					</> : null
				}
			</Card>
			
			<div style={{marginTop: 20}}>
        <span>备注：</span>
        <Input
          style={{ width: 500 }}
          value={remark}
          onChange={e => { set_remark(e.target.value); }}
        />
      </div>
			<div style={{marginTop: 20}}>
				<Button onClick={onCancel} type="primary">取消</Button>
				<Button onClick={save} type="primary" style={{marginLeft: 10}}>保存</Button>
			</div>
    </div>
  );
}
