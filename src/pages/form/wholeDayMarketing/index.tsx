import React, { useState, useEffect } from 'react';
import api from 'api';
import { Spin, Divider, Button, Table, Icon, Modal, Drawer, message, Popconfirm, Pagination } from 'antd';
import Clipboard from 'clipboard';

import TYPE1 from './images/type-1.jpg';
import TYPE2 from './images/type-2.jpg';
import TYPE3 from './images/type-3.jpg';
import TYPE4 from './images/type-4.jpg';

import RedpaperForm from './redpaperForm';
import BannerForm from './bannerForm';
import FundForm from './fundForm';

const { confirm } = Modal;

export default function() {

	const { fetchWholeDayMarketing, deleteWholeDayMarketing } = api;

	const columns = [{
		title: '模板id',
		dataIndex: 'boardId',
		key: 'boardId'
	}, {
		title: '模板种类',
		dataIndex: 'boardType',
		key: 'boardType',
		render: (item: any) => (getTypeName(item) + '模板')
	}, {
		title: '备注',
		dataIndex: 'remark',
		key: 'remark',
		render: (item: any) => (<span style={{fontWeight: 'bold', color: '#ff801a'}}>{item}</span>)
	}, {
		title: '地址',
		dataIndex: 'url',
		key: 'url',
		render: (item: any, record: any, index: number) => {
			let _url = '';
			if (location.host === 'febs.5ifund.com:8080' || location.hostname === 'localhost') {
				// 测试环境
				_url = 'https://testfund.10jqka.com.cn/ifundapp_app/public/m/wholeDayMarketing/dist/index.html?id=' + record.boardId
			} else {
				// 正式环境
				_url = 'https://fund.10jqka.com.cn/ifundapp_app/public/m/wholeDayMarketing/dist/index.html?id=' + record.boardId
			}
			return (
				<>
					<Icon 
						type="copy" 
						onClick={() => {message.success('复制成功')}} 
						className={'copy'} 
						data-clipboard-text={_url} 
					/>
				</>
			)
		}
	}, {
		title: '创建人',
		dataIndex: 'creator',
		key: 'creator'
	}, {
		title: '创建时间',
		dataIndex: 'createTime',
		key: 'createTime'
	}, {
		title: '最后编辑人',
		dataIndex: 'editor',
		key: 'editor'
	}, {
		title: '最后编辑时间',
		dataIndex: 'editTime',
		key: 'editTime'
	}, {
		title: '操作',
		dataIndex: 'operator',
		key: 'operator',
		render: (item: any, record: any, index: number) => {
			return (
				<>
					<Button type="primary" onClick={() => {modifyBtn(record, index)}}>编辑</Button>
					{/* <Button type="primary" style={{marginLeft: 10}}>预览</Button> */}
					<Popconfirm
						title="确定删除吗"
						onConfirm={() => {deleteData(record)}}
						okText="确定"
						cancelText="取消"
					>
						<Button type="danger" style={{marginLeft: 10}}>删除</Button>
					</Popconfirm>
				</>
			)
		}
	}];

	new Clipboard('.copy');

	const [typeModalVisible, set_typeModalVisible] = useState(false); // 类型选择弹窗的开关
	const [handleDrawerVisible, set_handleDrawerVisible] = useState(false); // 处理抽屉的开关

	const [activeType, set_activeType] = useState(0); // 当前处理的类型
	const [cORm, set_cORm] = useState('c'); // c-创建 m-修改
	const [activeData, set_activeData] = useState(null);

	const [masterplateList, set_masterplateList] = useState([]); // 模板列表
	const [currentPage, set_currentPage] = useState(1); // 当前页数
	const [totalData, set_totalData] = useState(0); // 数据总数

	const [init, set_init] = useState(false);

	/**
	 * 打开处理抽屉
	 * @param type 
	 */
	const openDrawer = (type: number, ways: string) => {
		set_cORm(ways);
		set_typeModalVisible(false);
		set_activeType(type);
		set_handleDrawerVisible(true);
	}

	/**
	 * 关闭处理抽屉
	 */
	const closeDrawer = () => {
		set_activeData(null);
		set_activeType(0);
		set_handleDrawerVisible(false);
	}

	/**
	 * 编辑
	 * @param record 
	 * @param index 
	 */
	const modifyBtn = (record: any, index: number) => {
		set_activeData(record);
		openDrawer(record.boardType, 'm');
	}

	/**
	 * 映射类型名称
	 * @param type 
	 * @returns 
	 */
	const getTypeName = (type: number) => {
		switch (type) {
			case 1:
				return '红包'
			case 2:
				return '产品';
			case 3:
				return '购买结果页banner';
			case 4:
				return '通用banner';
			default:
				return '--';
		}
	}

	/**
	 * 删除模板
	 * @param item
	 */
	const deleteData = (item: any) => {
		let id = item.id;
		if (id || id === 0) {
			deleteWholeDayMarketing({
				data: {
					id: id
				}
			}).then((data: any) => {
				if (data.code === '0000') {
					fetchData();
				} else {
					message.error(data.message || '删除错误，请稍后再试');
				}
			}).catch(() => {
				message.error('删除错误，请稍后再试');
			})
		} else {
			message.error('程序出错');
		}
	}

	/**
	 * 获取数据
	 */
	const fetchData = (page = currentPage) => {
		set_init(false);
		fetchWholeDayMarketing({
			pageSize: 10,
			pageNum: page
		}).then((data: any) => {
			set_init(true);
			if (data.code === "0000") {
				data = data.data;
				console.log(data);
				set_totalData(data.total);
				set_masterplateList(data.records);
			}
		}).catch(() => {
			confirm({
				title: '错误',
				content: '请求错误，请重试',
				centered: true,
				okText: '确定',
				cancelButtonProps: {
					style: {display: 'none'}
				},
				onOk() {
					location.reload();
				}
			})
		})
	}

	/**
	 * 修改页数
	 * @param page 改变后的页码
	 * @param pageSize 每页条数
	 */
	const changePage = (page: number, pageSize: number) => {
		set_currentPage(page);
		fetchData(page);
	}

	const initFn = () => {
		fetchData();
	}

	useEffect(() => {
		initFn();
	}, [])

  return (
    <Spin spinning={!init}>
			<Button type="primary" size="large" onClick={() => {set_typeModalVisible(true)}}>新增</Button>
			<Divider />
			<Table columns={columns} dataSource={masterplateList} rowKey="boardId" pagination={false} />
			<div style={{marginTop: 10}} className={'u-r-middle'}>
				<Pagination current={currentPage} total={totalData} onChange={changePage} />
			</div>
			<Modal
				title="选择类型"
				centered={true}
				footer={null}
				mask={false}
				width={648}
				visible={typeModalVisible}
				onCancel={() => {set_typeModalVisible(false)}}
			>
				<div style={{textAlign: 'center', width: 300, display: 'inline-block', marginBottom: 10}}>
					<img style={{width: 200}} src={TYPE1} alt="" /><br />
					<Button onClick={() => {openDrawer(1, 'c')}} type="primary" style={{marginTop: 10}}>红包配置</Button>
				</div>
				<div style={{textAlign: 'center', width: 300, display: 'inline-block', marginBottom: 10}}>
					<img style={{width: 200}} src={TYPE2} alt="" /><br />
					<Button onClick={() => {openDrawer(2, 'c')}} type="primary" style={{marginTop: 10}}>产品配置</Button>
				</div>
				<div style={{textAlign: 'center', width: 300, display: 'inline-block', marginBottom: 10}}>
					<img style={{width: 200}} src={TYPE3} alt="" /><br />
					<Button onClick={() => {openDrawer(3, 'c')}} type="primary" style={{marginTop: 10}}>购买结果页banner配置</Button>
				</div>
				<div style={{textAlign: 'center', width: 300, display: 'inline-block', marginBottom: 10}}>
					<img style={{width: 200}} src={TYPE4} alt="" /><br />
					<Button onClick={() => {openDrawer(4, 'c')}} type="primary" style={{marginTop: 10}}>通用banner配置</Button>
				</div>
			</Modal>
			<Drawer
				title={getTypeName(activeType) + '配置'}
				placement="right"
				onClose={closeDrawer}
				visible={handleDrawerVisible}
				width={800}
				destroyOnClose={true}
			>
				{
					activeType === 1 ?
					<RedpaperForm 
						cORm={cORm}
						activeData={activeData}
						boardType={activeType}
						onCancel={closeDrawer}
						reload={() => {closeDrawer(); initFn();}}
					/> : null
				}
				{
					activeType === 2 ?
					<FundForm 
						cORm={cORm}
						activeData={activeData}
						boardType={activeType}
						onCancel={closeDrawer}
						reload={() => {closeDrawer(); initFn();}}
					/> : null
				}
				{
					(activeType === 3 || activeType === 4) ?
					<BannerForm 
						cORm={cORm}
						activeData={activeData}
						boardType={activeType}
						onCancel={closeDrawer}
						reload={() => {closeDrawer(); initFn();}}
					/> : null
				}
			</Drawer>
			{
				handleDrawerVisible ?
				<div
					style={{position: "absolute", top: "50%", left: "30%", transform: 'translate3d(-50%, -50%, 0)',zIndex: 1001, width: 400}}
				>
					<img style={{width: 400}} src={activeType === 1 ? TYPE1 : activeType === 2 ? TYPE2 : activeType === 3 ? TYPE3 : TYPE4} alt="" /><br />
				</div> : null
			}
		</Spin>
  );
}
