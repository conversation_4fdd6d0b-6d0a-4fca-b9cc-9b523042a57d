import React, { useState, useEffect } from 'react';
import api from 'api';
import { Input, DatePicker, message, Button, Radio, Select, Card } from 'antd';

import moment from 'moment';

interface IBaseProps {
	remark?: string;
	content: string;
	id: number;
	boardId: string;
}

interface IProps {
	onCancel: any;
	reload: any;
	cORm: string;
	boardType: number;
	activeData: IBaseProps|null;
}

export default function({onCancel, reload, cORm, boardType, activeData}: IProps) {

	const { addWholeDayMarketing, modifyWholeDayMarketing } = api;

	const [title, set_title] = useState(''); // 弹窗标题
	const [subtitle, set_subtitle] = useState(''); // 产品主标题
	const [remark, set_remark] = useState(''); // 备注
	const [fundType, set_fundType] = useState(0); // 0-组合 1-新发 2-持营基金 3-小目标

	// 产品数据
	// 组合
	const [groupName, set_groupName] = useState(''); // 组合名称
	const [groupCode, set_groupCode] = useState(''); // 组合代码
	const [groupTag, set_groupTag] = useState(''); // 组合标签

	// 新发基金
	const [newFundCode, set_newFundCode] = useState(''); // 新发基金代码
	const [fundManagerName, set_fundManagerName] = useState(''); // 基金经理名字
	const [fundManagerDesc, set_fundManagerDesc] = useState(''); // 基金经理文案
	const [newFundDesc, set_newFundDesc] = useState('');
	const [newFundTag, set_newFundTag] = useState('');
	const [endDate, set_endDate] = useState(''); // 募集结束时间

	// 持营基金
	const [fundCode, set_fundCode] = useState(''); // code
	const [fundName, set_fundName] = useState(''); // 基金名称
	const [fundDesc, set_fundDesc] = useState(''); // 亮点文案
	const [fundTag, set_fundTag] = useState(''); // 标签
	const [syRange, set_syRange] = useState<any>('year'); // 收益区间
	const [bottomText, set_bottomText] = useState(''); // 辅助文案

	// 小目标
	const [smallTargetCode, set_smallTargetCode] = useState(''); // 小目标代码
	const [smallTargetTag, set_smallTargetTag] = useState(''); // 小目标标签

	const save = () => {
		if (!title) {
			message.error('请填写弹窗标题');
			return;
		}
		if (!subtitle) {
			message.error('请填写产品主标题');
			return;
		}
		if (fundType === 0) {
			if (!groupCode) {
				message.error('请填写组合代码');
				return;
			} else if (groupCode.includes(' ')) {
				message.error('组合代码中含有空格');
				return;
			}
			if (!groupName) {
				message.error('请填写组合名称');
				return;
			}
		} else if (fundType === 1) {
			if (!newFundCode) {
				message.error('请填写基金代码');
				return;
			} else if (newFundCode.includes(' ')) {
				message.error('基金代码中含有空格');
				return;
			} else if (newFundCode.length !== 6) {
				message.error('基金代码需是6位');
				return;
			}
			if (!fundManagerName) {
				message.error('请填写基金经理名字');
				return;
			}
			if (!fundManagerDesc) {
				message.error('请填写基金经理文案');
				return;
			}
			if (!newFundDesc) {
				message.error('请填写亮点文案');
				return;
			}
			if (!newFundTag) {
				message.error('请填写标签');
				return;
			} else {
				let _newFundTag: any = newFundTag;
				_newFundTag = _newFundTag.split(',');
				for (let i = 0; i <_newFundTag.length; i++) {
					if (_newFundTag[i].length > 5) {
						message.error('单个标签的长度不可超过5个');
						return;
					}
				}
			}
			if (!endDate) {
				message.error('请填写募集结束时间');
				return;
			}
		} else if (fundType === 2) {
			if (!fundCode) {
				message.error('请填写基金代码');
				return;
			} else if (fundCode.includes(' ')) {
				message.error('基金代码中含有空格');
				return;
			} else if (fundCode.length !== 6) {
				message.error('基金代码需是6位');
				return;
			}
			if (!fundName) {
				message.error('请填写基金名称');
				return;
			}
			if (!fundDesc) {
				message.error('请填写亮点文案');
				return;
			}
			if (!fundTag) {
				message.error('请填写标签');
				return;
			} else {
				let _fundTag: any = fundTag;
				_fundTag = _fundTag.split(',');
				for (let i = 0; i <_fundTag.length; i++) {
					if (_fundTag[i].length > 5) {
						message.error('单个标签的长度不可超过5个');
						return;
					}
				}
			}
			if (!bottomText) {
				message.error('请填写辅助文案');
				return;
			}
		} else if (fundType === 3) {
			if (!smallTargetCode) {
				message.error('请填写小目标代码');
				return;
			} else if (smallTargetCode.includes(' ')) {
				message.error('小目标代码中含有空格');
				return;
			}
		}
		let _prodData: any = {}
		if (fundType === 0) {
			_prodData = {
				fundType,
				groupName,
				groupCode,
				groupTag
			}
		} else if (fundType === 1) {
			_prodData = {
				fundType,
				newFundCode,
				fundManagerName,
				fundManagerDesc,
				newFundDesc,
				newFundTag,
				endDate
			}
		} else if (fundType === 2) {
			_prodData = {
				fundType,
				fundCode,
				fundName,
				syRange,
				fundDesc,
				fundTag,
				bottomText
			}
		} else if (fundType === 3) {
			_prodData = {
				fundType,
				smallTargetCode,
				smallTargetTag
			}
		}
		let _content: any = {
			title,
			subtitle,
			prodData: _prodData
		}
		let _postData: any = {
			boardType,
			remark,
			content: JSON.stringify(_content)
		}
		if (cORm === 'c') {
			// 创建
			addWholeDayMarketing({
				marketContactOperationStr: JSON.stringify(_postData)
			}).then((data: any) => {
				if (data.code === "0000") {
					reload();
				} else {
					message.error(data.message || '保存失败，请稍后再试')
				}
			}).catch(() => {
				message.error('保存失败，请稍后再试');
			})
		} else if (cORm === 'm') {
			// 修改
			_postData.id = activeData?.id;
			_postData.boardId = activeData?.boardId;
			modifyWholeDayMarketing({
				marketContactOperationStr: JSON.stringify(_postData)
			}).then((data: any) => {
				if (data.code === "0000") {
					reload();
				} else {
					message.error(data.message || '保存失败，请稍后再试')
				}
			}).catch(() => {
				message.error('保存失败，请稍后再试');
			})
		}
	}

	useEffect(() => {
		if (cORm === 'm' && activeData && activeData.content) {
			let content:any = activeData.content;
			content = JSON.parse(content);
			const { prodData } = content;
			set_title(content.title || '');
			set_subtitle(content.subtitle || '');
			set_remark(activeData.remark || '');
			set_fundType(prodData.fundType || 0);
			
			set_groupName(prodData.groupName || '');
			set_groupCode(prodData.groupCode || '');
			set_groupTag(prodData.groupTag || '');

			set_newFundCode(prodData.newFundCode || '');
			set_fundManagerName(prodData.fundManagerName || '');
			set_fundManagerDesc(prodData.fundManagerDesc || '');
			set_newFundDesc(prodData.newFundDesc || '');
			set_newFundTag(prodData.newFundTag || '');
			set_endDate(prodData.endDate || '');

			set_fundCode(prodData.fundCode || '');
			set_fundName(prodData.fundName || '');
			set_fundDesc(prodData.fundDesc || '');
			set_fundTag(prodData.fundTag || '');
			set_syRange(prodData.syRange || 'year');
			set_bottomText(prodData.bottomText || '');

			set_smallTargetCode(prodData.smallTargetCode || '');
			set_smallTargetTag(prodData.smallTargetTag || '');
		}
	}, []);

	const groupForm = () => {
		return (
			<>
				<div style={{marginTop: 10}}>
					<span>组合代码<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={groupCode}
						onChange={e => { set_groupCode(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>组合名称<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={groupName}
						onChange={e => { set_groupName(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>标签：</span>
					<Input
						style={{ width: 400 }}
						value={groupTag}
						onChange={e => { set_groupTag(e.target.value); }}
						placeholder="单标签，只需要配一个"
					/>
				</div>
			</>
		)
	}

	const newFundForm = () => {
		return (
			<>
				<div style={{marginTop: 10}}>
					<span>基金代码<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={newFundCode}
						onChange={e => { set_newFundCode(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>基金经理名字<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={fundManagerName}
						onChange={e => { set_fundManagerName(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>基金经理文案<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 400 }}
						value={fundManagerDesc}
						onChange={e => { set_fundManagerDesc(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>亮点文案<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 400 }}
						value={newFundDesc}
						onChange={e => { set_newFundDesc(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>标签<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 500 }}
						value={newFundTag}
						onChange={e => { set_newFundTag(e.target.value); }}
						placeholder="多个标签用英文逗号分隔，单个标签不超过5个字，仅展示前三个标签"
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>募集结束时间<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<DatePicker
						showTime
						format="YYYY-MM-DD HH:mm:ss"
						value={endDate ? moment(endDate, 'YYYY-MM-DD HH:mm:ss') : null}
						onChange={(date, dateString) => { set_endDate(dateString) }}
					/>
				</div>
			</>
		)
	}

	const normalFundForm = () => {
		return (
			<>
				<div style={{marginTop: 10}}>
					<span>基金代码<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={fundCode}
						onChange={e => { set_fundCode(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>基金名称<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={fundName}
						onChange={e => { set_fundName(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>收益区间<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Select 
						style={{width: 200}}
						onChange={(value: any) => {set_syRange(value)}}
						value={syRange}
					>
						<Select.Option value="month">近一月收益率</Select.Option>
						<Select.Option value="tmonth">近三月收益率</Select.Option>
						<Select.Option value="hyear">近半年收益率</Select.Option>
						<Select.Option value="year">近一年收益率</Select.Option>
						<Select.Option value="tyear">近三年收益率</Select.Option>
					</Select>
				</div>
				<div style={{marginTop: 10}}>
					<span>亮点文案<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 400 }}
						value={fundDesc}
						onChange={e => { set_fundDesc(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>标签<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 500 }}
						value={fundTag}
						onChange={e => { set_fundTag(e.target.value); }}
						placeholder="多个标签用英文逗号分隔，单个标签不超过5个字，仅展示前两个标签"
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>辅助文案<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 400 }}
						value={bottomText}
						onChange={e => { set_bottomText(e.target.value); }}
					/>
				</div>
			</>
		)
	}

	const smallTargetForm = () => {
		return (
			<>
				<div style={{marginTop: 10}}>
					<span>小目标代码<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 200 }}
						value={smallTargetCode}
						onChange={e => { set_smallTargetCode(e.target.value); }}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>标签：</span>
					<Input
						style={{ width: 400 }}
						value={smallTargetTag}
						onChange={e => { set_smallTargetTag(e.target.value); }}
						placeholder="单标签，只需要配一个"
					/>
				</div>
			</>
		)
	}
 
  return (
    <div>
			<div style={{marginTop: 10}}>
        <span>弹窗标题<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <Input
          style={{ width: 500 }}
          value={title}
          onChange={e => { set_title(e.target.value); }}
        />
      </div>
			<div style={{marginTop: 10}}>
        <span>产品大标题<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <Input
          style={{ width: 500 }}
          value={subtitle}
          onChange={e => { set_subtitle(e.target.value); }}
        />
      </div>
			<div style={{marginTop: 10, marginBottom: 30}}>
				<span>产品类型<i style={{color: '#fe5d4e'}}> *</i>：</span>
				<Radio.Group onChange={(e) => {set_fundType(e.target.value)}} value={fundType}>
					<Radio value={0}>组合</Radio>
					<Radio value={1}>新发</Radio>
					<Radio value={2}>持营基金</Radio>
					<Radio value={3}>小目标</Radio>
				</Radio.Group>
			</div>
			<Card>
				{
					fundType === 0 ?
					groupForm() :
					fundType === 1 ?
					newFundForm() :
					fundType === 2 ?
					normalFundForm() :
					fundType === 3 ?
					smallTargetForm() : null
				}
			</Card>
			<div style={{marginTop: 30}}>
        <span>备注：</span>
        <Input
          style={{ width: 500 }}
          value={remark}
          onChange={e => { set_remark(e.target.value); }}
        />
      </div>
			<div style={{marginTop: 20}}>
				<Button onClick={onCancel} type="primary">取消</Button>
				<Button onClick={save} type="primary" style={{marginLeft: 10}}>保存</Button>
			</div>
    </div>
  );
}
