import React, { useState, useEffect } from 'react';
import api from 'api';
import { Input, DatePicker, message, Button, Radio } from 'antd';

import ImgUpload from '../../frontend/compoment/uploadImg';
import moment from 'moment';

interface IBaseProps {
	remark?: string;
	content: string;
	id: number;
	boardId: string;
}

interface IProps {
	onCancel: any;
	reload: any;
	cORm: string;
	boardType: number;
	activeData: IBaseProps|null;
}

export default function({onCancel, reload, cORm, boardType, activeData}: IProps) {

	const { addWholeDayMarketing, modifyWholeDayMarketing } = api;

	const [bannerUrl, set_bannerUrl] = useState(''); // 广告图
	const [jumpUrl, set_jumpUrl] = useState(''); // 链接
	const [startDate, set_startDate] = useState(''); // 开始时间
	const [endDate, set_endDate] = useState(''); // 结束时间
	const [remark, set_remark] = useState(''); // 备注
	const [title, set_title] = useState(''); // 弹窗标题
	const [scene, set_scene] = useState('bannerBottom1'); // 投放场景

	/**
	 * 保存
	 * @returns 
	 */
	const save = () => {
		if (!bannerUrl) {
			message.error('请上传广告图');
			return;
		}
		if (!jumpUrl) {
			message.error('请填写跳转链接');
			return;
		} else if (!/^https:\/\/[^\n ，]*$/.test(jumpUrl)) {
			message.error('跳转链接格式错误');
			return;
		}
		if (boardType === 3 && !title) {
			message.error('请填写弹窗标题');
			return;
		}
		if (!startDate) {
			message.error('请填写开始时间');
			return;
		}
		if (!endDate) {
			message.error('请填写结束时间');
			return;
		}
		if (new Date(startDate) >= new Date(endDate)) {
			message.error('时间顺序配置错误');
			return;
		}
		
		let _content: any = {}
		_content = {
			bannerUrl,
			jumpUrl,
			startDate,
			endDate,
		};
		if (boardType === 3) {
			_content.title = title;
		} else if (boardType === 4) {
			_content.scene = scene;
		}
		let _postData: any = {
			boardType,
			remark,
			content: JSON.stringify(_content)
		}
		if (cORm === 'c') {
			// 创建
			addWholeDayMarketing({
				marketContactOperationStr: JSON.stringify(_postData)
			}).then((data: any) => {
				if (data.code === "0000") {
					reload();
				} else {
					message.error(data.message || '保存失败，请稍后再试')
				}
			}).catch(() => {
				message.error('保存失败，请稍后再试');
			})
		} else if (cORm === 'm') {
			// 修改
			_postData.id = activeData?.id;
			_postData.boardId = activeData?.boardId;
			modifyWholeDayMarketing({
				marketContactOperationStr: JSON.stringify(_postData)
			}).then((data: any) => {
				if (data.code === "0000") {
					reload();
				} else {
					message.error(data.message || '保存失败，请稍后再试')
				}
			}).catch(() => {
				message.error('保存失败，请稍后再试');
			})
		}
	}

	useEffect(() => {
		if (cORm === 'm' && activeData && activeData.content) {
			set_remark(activeData.remark || '');
			let content: any = activeData.content;
			content = JSON.parse(content);
			set_bannerUrl(content.bannerUrl || '');
			set_jumpUrl(content.jumpUrl || '');
			set_startDate(content.startDate || '');
			set_endDate(content.endDate || '');
			set_title(content.title || '');
			set_scene(content.scene || 'bannerBottom1');
		}
	}, [])

  return (
    <div>
			<div>
				<span>广告图<i style={{color: '#fe5d4e'}}> *</i>：</span>
				<ImgUpload
					handleChange={set_bannerUrl}
					imageUrl={bannerUrl}
					limit={0.2}
					isEdit={true}
					title=""
				/>
			</div>
			<div style={{marginTop: 10}}>
        <span>链接<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <Input
          style={{ width: 500 }}
          value={jumpUrl}
          onChange={e => { set_jumpUrl(e.target.value); }}
        />
      </div>
			{
				boardType === 3 ?
				<div style={{marginTop: 10}}>
					<span>弹窗标题<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input
						style={{ width: 500 }}
						value={title}
						onChange={e => { set_title(e.target.value); }}
					/>
				</div> : null
			}
			<div style={{marginTop: 20}}>
        <span>开始时间<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <DatePicker
					showTime
					format="YYYY-MM-DD HH:mm:ss"
					value={startDate ? moment(startDate, 'YYYY-MM-DD HH:mm:ss') : null}
          onChange={(date, dateString) => { set_startDate(dateString) }}
        />
      </div>
			<div style={{marginTop: 20}}>
        <span>结束时间<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <DatePicker
					showTime
					format="YYYY-MM-DD HH:mm:ss"
					value={endDate ? moment(endDate, 'YYYY-MM-DD HH:mm:ss') : null}
          onChange={(date, dateString) => { set_endDate(dateString) }}
        />
      </div>
			{
				boardType === 4 ?
				<div style={{marginTop: 20}}>
					<p>投放场景<i style={{color: '#fe5d4e'}}> *</i>：</p>
					<Radio.Group onChange={(e) => {set_scene(e.target.value)}} value={scene}>
						<Radio value={'bannerBottom1'}>有底部tab</Radio>
						<p>首页 市场 自选 我的 持仓详情 同花顺钱包 高端理财</p>
						<Radio value={'bannerBottom2'}>底部tab+广告条</Radio>
						<p>个基详情页 高端理财详情</p>
						<Radio value={'bannerBottom3'}>无底部tab</Radio>
						<p>排行榜 持仓列表 新发基金 交易结果页</p>
					</Radio.Group>
				</div> : null
			}
			<div style={{marginTop: 20}}>
        <span>备注：</span>
        <Input
          style={{ width: 500 }}
          value={remark}
          onChange={e => { set_remark(e.target.value); }}
        />
      </div>
			<div style={{marginTop: 20}}>
				<Button onClick={onCancel} type="primary">取消</Button>
				<Button onClick={save} type="primary" style={{marginLeft: 10}}>保存</Button>
			</div>
    </div>
  );
}
