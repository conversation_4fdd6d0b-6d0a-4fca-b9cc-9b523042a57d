{"type": "page", "title": "趋势强弱", "body": [{"type": "form", "id": "u:413d0bb5c527", "title": "配置项", "body": [{"label": "", "type": "combo", "name": "net_worth_config", "multiple": true, "items": [{"label": "介绍文案", "type": "input-text", "name": "introduceText", "id": "u:276ef6554048", "clearable": true, "maxLength": 30, "description": "", "placeholder": "不超过30字"}, {"type": "input-text", "label": "使用说明文案", "name": "InstructionsText", "id": "u:7c78e051d36a", "maxLength": 6, "placeholder": "不超过6字", "clearable": true}, {"type": "input-text", "label": "使用说明链接", "name": "InstructionsUrl", "id": "u:50f488f57c73", "clearable": true}, {"type": "input-number", "label": "新高新低胜率阈值", "name": "highWinRateThreshold", "id": "u:4622823e9159", "placeholder": "0——1", "clearable": true, "step": 1, "min": 0, "max": 1, "validateOnChange": true, "precision": 2}, {"label": "新高新低累计收益率阈值", "type": "input-number", "name": "highRateThreshold", "id": "u:0773c390ab3b", "placeholder": "0——1", "clearable": true, "step": 1, "min": 0, "max": 1, "validateOnChange": true, "precision": 2}, {"type": "input-number", "label": "走强走弱胜率阈值", "name": "strongWinRateThreshold", "id": "u:4622823e9159", "placeholder": "0——1", "clearable": true, "step": 1, "min": 0, "max": 1, "validateOnChange": true, "precision": 2}, {"label": "走强走弱累计收益率阈值", "type": "input-number", "name": "strongRateThreshold", "id": "u:0773c390ab3b", "placeholder": "0——1", "clearable": true, "step": 1, "min": 0, "max": 1, "validateOnChange": true, "precision": 2}, {"type": "input-image", "label": "运营位图片", "name": "bannerImg", "id": "u:20d3f5af1de8", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "validateOnChange": true, "proxy": true, "accept": ".jpeg, .jpg, .png, .gif", "uploadType": "fileReceptor", "multiple": false, "hideUploadButton": false, "autoUpload": false}, {"type": "input-text", "label": "排行榜榜单key", "name": "<PERSON><PERSON><PERSON>", "id": "u:3322c4c32763"}, {"type": "input-text", "label": "运营位链接", "name": "bannerUrl", "id": "u:e6b330b76d7f", "clearable": true}, {"type": "input-text", "label": "立即领取跳转链接", "name": "jumpUrl", "clearable": true, "trimContents": true, "required": true}], "id": "u:0d376e5bdcac", "strictMode": true, "syncFields": [], "tabsMode": false, "mode": "normal", "multiLine": true, "addable": true, "value": "${config}", "maxLength": 1, "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:89ce436c5e27"}, "minLength": 1}], "mode": "horizontal", "api": {"url": "/common_config/kv_data_save", "method": "post", "messages": {}, "dataType": "form", "data": {"key": "trend_hunter_pool_threshold_v1", "value": "${net_worth_config}"}, "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}", "requestAdaptor": "console.log(api)\r\nconst data = api.body.value[0];\r\napi.body.value = JSON.stringify(data);\r\nconsole.log(api.body)\r\nreturn api"}, "initApi": {"url": "/common_config/kv_data_get", "method": "get", "requestAdaptor": "", "adaptor": "const config = JSON.parse(payload.data);\r\nconsole.log(JSON.parse(payload.data))\r\nreturn {\r\n  data: {\r\n    config: [config],\r\n  },\r\n};", "messages": {}, "data": {"key": "trend_hunter_pool_threshold_v1"}}, "debug": false}], "id": "u:b4583d2b5daa", "regions": ["body", "header"], "pullRefresh": {"disabled": true}}