import React from 'react'
import classNames from 'classnames'
import styles from './index.less'

const Loading = ({ 
  spinning = false, 
  className,
  fullScreen,
  theme 
}: any) => {
  let _className = classNames(
    'u-c-middle f-tc', 
    styles.loader, 
    styles[theme],
    className, 
    {
      [styles.hidden]: !spinning, 
      'g-pf': fullScreen
    });

  return (
    <div
      className={_className}
    >
      <div className={styles.warpper}>
        <div className={styles.inner} />
        <div className="u-w100 f-tc g-fs12 f-lspacing_4">加载中...</div>
      </div>
    </div>
  )
}

export default Loading
