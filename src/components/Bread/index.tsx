import React, { PureComponent, Fragment } from 'react'
import { Breadcrumb, Icon } from 'antd'
import Link from 'umi/navlink'
import withRouter from 'umi/withRouter'
import {
    queryAncestors,
    pathMatchRegexp,
} from 'functions/utils'
import styles from './index.less'


class Bread extends PureComponent<any, any> {

  generateBreadcrumbs = (paths: any) => {
    return paths.map((item: any, key: number) => {
      const content = item && (
        <Fragment>
          {item.icon ? (
            <Icon type={item.icon} style={{ marginRight: 4 }} />
          ) : null}
          {item.name}
        </Fragment>
      )

      return (
        item && (
          <Breadcrumb.Item key={key}>
            {paths.length - 1 !== key ? (
              <Link to={item.route || '#'}>{content}</Link>
            ) : (
              content
            )}
          </Breadcrumb.Item>
        )
      )
    })
  }
  render() {
    const { 
      routeList, 
      location, 
    } = this.props;
	
	let seachList: any[] = [];
	(JSON.parse(JSON.stringify(routeList))).map((routeItem: any, index: any) => {
		index = '' + index;
		routeItem.id = index;
		if (!routeItem.children) seachList.push(routeItem);
		else {
			seachList.push(routeItem);
			routeItem.children.map((item: any, itemidx: number) => {
				item.breadcrumbParentId = index;
				item.id = index + itemidx;
				seachList.push(item);
			})
		}
	})
	
    // Find a route that matches the pathname.
    const currentRoute = seachList.find(
      (_: any) => {
        // console.log(_.path, location.pathname)
        return _.path && pathMatchRegexp(
          _.path, 
          location.pathname)
      })
    
    // Find the breadcrumb navigation of the current route match and all its ancestors.
    const paths = currentRoute
      ? queryAncestors(seachList, currentRoute, 'breadcrumbParentId').reverse()
      : [
          seachList[0],
          {
            id: 404,
            name: `Not Found`,
          },
        ]

    return (
      <Breadcrumb className={styles.bread}>
        {this.generateBreadcrumbs(paths)}
      </Breadcrumb>
    )
  }
}


export default withRouter(Bread)
