import React, {useState} from 'react'
import { Layout, Icon, Popover, Menu  } from 'antd'
import classnames from 'classnames'
import styles from './index.less'
import store from 'store'
import { getAvatar } from 'utils/user'
import ChangePwd from './ChangePwd'

interface HRMProps {
    handleChangePwd: any;
}
const HeaderRightMenu: React.FC<HRMProps> = props => {
    let {
        handleChangePwd,
    } = props;

    let logout = () => {
        store.remove('userId');
        // store.remove('name');
        store.remove('user_token');
        history.go(0);
    };

    let changeAvatar = () => {
        store.set('avatar', '' + ~~(Math.random() * 50));
        history.go(0);
    };

    return (
        <div>
            <p className="f-cursor_p u-h30" s-link="1" onClick={handleChangePwd}>
                修改密码
            </p>
            <p className="f-cursor_p u-h30" s-link="1" onClick={changeAvatar}>
                切换头像
            </p>
            <p className="f-cursor_p u-h30" s-link="1" onClick={logout}>
                退出登录
            </p>
        </div>
    )
};

const Header: React.FC = (props: any) => {
    let {
        collapsed,
        //isMobile,
        name,
        handleCollapsed,
        userinfo,
    } = props;

    const [changePwdVisible, triggerChangePwdVisible]: [boolean, any] = useState(false);
    const handleChangePwd = () => triggerChangePwdVisible(!changePwdVisible);

    return (
        <Layout.Header
            className={classnames('m-page_head u-j-middle', styles.header)}
        >
            <div className="f-cursor_p" title={`点击${collapsed ? '展开' : '收起'}侧边栏`} onClick={handleCollapsed}>
                <Icon
                    type={classnames({
                    'menu-unfold': collapsed,
                    'menu-fold': !collapsed,
                    })}
                />
            </div>

            <Popover
                content={<HeaderRightMenu handleChangePwd={handleChangePwd}/>}
                className="f-cursor_p"
            >
                {/* {getAvatar(userinfo)} */}
                <span className="g-ml10" s-link="1">欢迎，{name || '--'}</span>
            </Popover>
            <ChangePwd
                name={name}
                visible={changePwdVisible}
                handleChangePwd={handleChangePwd}
            />
        </Layout.Header>
    )

}

export default Header
