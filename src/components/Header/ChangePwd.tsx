import React from 'react'
import { Modal, Form, Input } from 'antd'
import {FormComponentProps} from 'antd/lib/form/Form';
import api from 'api'
import { toast } from 'utils/message'
const { changePassword, checkPassword }  = api;
const FormItem = Form.Item;

/**
 * 修改密码
 */
interface ChangePwdProps extends FormComponentProps {
    name?: string;
    visible: boolean;
    form: any;
    handleChangePwd: any;
    children?: React.ReactNode;
}
const ChangePwd: React.FC<ChangePwdProps> = props => {
    const {
        name,
        visible,
        form,
        handleChangePwd,
    } = props;
    const { getFieldDecorator } = form;

    let _handleOk = () => {
        form.validateFields((err: any, values: any) => {
            if (!err) {
              console.log(values);
              changePassword({
                password: values.password,
                username: name
              }).then((data: any) => {
                  if (data.success) {
                    toast.success('修改成功');
                    setTimeout(() => {
                        history.go(0);
                    }, 2000);
                  }
              })
            }
        })
    };

    let _checkOldPassowrd = (rule: any, value: any, callback: Function) => {
        checkPassword({
            password: value,
            username: name,
        }).then((data: any) => {
            if (data.data) {
                callback()
            } else {
                callback(new Error('旧密码不正确'))
            }
        })
    };
    let _checkPwd1 = (rule: any, value: any, callback: Function) => {
        if (value === undefined) {
          callback(new Error('请输入密码'))
        }
        const hasLetter = /[a-zA-Z]/.test(value);
        const hasNumber = /\d/.test(value);
        const hasChar = /[\W_]/.test(value);
        const isLongEnough= value.length > 6;
        if (!hasLetter || !hasNumber || !hasChar || !isLongEnough) {
            callback(new Error('密码设置过弱，请重新设置。密码需包含字母、数字、字符，且长度大于6位。'))
        }
        callback()
    };
    let _checkPwd2 = (rule: any, value: any, callback: Function) => {
        let password = form.getFieldValue('password')
        if (value === undefined) {
          callback(new Error('请输入密码'))
        }
        if (value && password && value.trim() !== password.trim()) {
          callback(new Error('两次密码不一致'))
        }
        callback()
    };

    return (
        <Modal
          title="修改密码"
          okText="确认修改"
          cancelText="取消"
          visible={visible}
          onOk={_handleOk}
          onCancel={handleChangePwd}
        >
          <Form 
            labelCol={{
                span: 4, 
            }} 
            wrapperCol={{
                span: 18, 
            }}>
            <FormItem label="旧密码">
                {getFieldDecorator('oldPassword', {
                    rules: [
                        { required: true, message: '请输入旧密码!' },
                        { validator: _checkOldPassowrd }
                    ],
                    validateTrigger: 'onBlur',
                })(
                    <Input placeholder="请输入旧密码" />
                )}
            </FormItem>
            <FormItem label="新密码">
                {getFieldDecorator('password', {
                    rules: [
                        { required: true, message: '请输入新密码!' },
                        { validator: _checkPwd1 }
                    ],
                    validateTrigger: ['onChange', 'onBlur']
                })(
                    <Input type="password" placeholder="请输入新密码" />
                )}
            </FormItem>
            <FormItem label="再次确认">
                {getFieldDecorator('password2', {
                    rules: [
                        { required: true, message: '请再次确认新密码!' },
                        { validator: _checkPwd2 }
                    ],
                    validateTrigger: ['onChange', 'onBlur']
                })(
                    <Input type="password" placeholder="请再次确认新密码" />
                )}
            </FormItem>
          </Form>
        </Modal>
    )
}

export default Form.create<ChangePwdProps>()(ChangePwd);
