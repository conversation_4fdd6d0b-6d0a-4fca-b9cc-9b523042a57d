
/**
 * @module request
 * @export {Function} get
 * @export {Function} post
 */

import Axios,{AxiosResponse} from 'axios'
import store from 'store'

interface HeaderState {
    ['Content-Type']: string;
    user?: string;
    Authentication?: string;
}



let requestHeader: HeaderState = {
    'Content-Type': 'application/x-www-form-urlencoded',
};
let requestHeader2: HeaderState = {
    'Content-Type': 'multipart/form-data',
};
let requestHeader3: HeaderState = {
    'Content-Type': 'application/json',
}

let old_token = store.get('user_token');
let userName = store.get('name');

if (old_token) {
    requestHeader['Authentication'] = old_token;
    requestHeader2['Authentication'] = old_token;
    requestHeader3['Authentication'] = old_token;
}

if (userName) {
    userName = encodeURIComponent(userName);
    requestHeader['user'] = userName;
    requestHeader2['user'] = userName;
    requestHeader3['user'] = userName;
}

let _setAxios = () => (Axios.create({
    transformRequest: [function(data: any) {
        let ret = ''
        for (let it in data) {
            ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
        }
        return ret
    }],
    headers: requestHeader,
    timeout: 15000
}));
let _setAxios2 = () => (Axios.create({
    
    headers: requestHeader2,
    timeout: 60000
}));

let _setAxios3 = () => (Axios.create({
    
    headers: requestHeader3,
    timeout: 15000
}));

let _setAxios4 = () => (Axios.create({
    transformRequest: [function(data: any) {
        let ret = ''
        for (let it in data) {
            ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
        }
        return ret
    }],
    headers: requestHeader,
    timeout: 60000
}));

let _setBigFileAxios = () => (Axios.create({
    
    headers: requestHeader2,
    timeout: 60000
}));

export let axios = _setAxios();
export let axios2 = _setAxios2();
export let axios3 = _setAxios3();
export let longAxios = _setAxios4();
export let bigFileAxios = _setBigFileAxios();

interface resultInterface {
    list?: any[];
    data?: object;
}

/**
 * @function handleSuccess
 * @description request sucess callback
 */
function handleSuccess (res: AxiosResponse, url: any) {
    const { statusText, status, data } = res

    let result: resultInterface = {};
    if (typeof data === 'object') {
        result = data;
        if (Array.isArray(data)) {
            result.list = data;
        }
    } else {
        result.data = data;
    }

    delete ajaxMap[url];
    return Promise.resolve({
        success: true,
        message: statusText,
        statusCode: status,
        ...result,
    })
}

/**
 * @function handleError
 * @description request fail callback
 */
function handleError (e: any, url: any) {
    const { 
        response, 
        message 
    } = e;

    let msg;
    let statusCode;

    delete ajaxMap[url];
    if (response && response instanceof Object) {
        const { 
            data, 
            statusText, 
        } = response;

        statusCode = response.status;
        msg = data.message || statusText;
    } else {
        statusCode = 600;
        msg = message || 'Network Error';
    }

    return Promise.reject({
        success: false,
        statusCode,
        message: msg,
    })
}
/**
 * @function handleSuccessWithFile
 * @description handleSuccess and download file
 */
 function handleSuccessWithFile (res: any, url: any) {
    if(res.data.size === 0) {
        delete ajaxMap[url];

        return Promise.resolve({
            success: false,
            message: '�ļ�������',
            statusCode: 200,
        })
    }
    const { statusText, status, data } = res
    let result: resultInterface = {};
    if(data instanceof Blob && data.size > 0){
        let reader = new FileReader();
        if (~data.type.indexOf('/json')) {
            delete ajaxMap[url];
            return get(url)
        }
        reader.readAsDataURL(data);
        reader.onload = function(e) {
            try {
                let a: any = document.createElement('a');
                let fileName: string = res.headers["content-disposition"].split("=");
                fileName = decodeURI(fileName[fileName.length - 1]);
                fileName = fileName.replace(/"/g, "");
                a.download = fileName;
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            } catch(e) {
                console.log(e.message)
            }
        }
    } else {
        result.data = data;
    }

    delete ajaxMap[url];
    return Promise.resolve({
        success: true,
        message: statusText,
        statusCode: status,
        ...result,
    })
}

let ajaxMap: any = {};
export function get (url: string, ...rest: any) {
    if (ajaxMap[url]) return false;

    ajaxMap[url] = 1;
    let newStore = store.get('user_token')
    if (newStore && newStore !== old_token) {
        requestHeader['Authentication'] = newStore;
        requestHeader2['Authentication'] = newStore;
        requestHeader3['Authentication'] = newStore;
        old_token = newStore;
        axios = _setAxios();
        axios2 = _setAxios2();
        axios3 = _setAxios3();
        longAxios = _setAxios4();
        bigFileAxios = _setBigFileAxios();
    }
    
    return axios.get(url, typeof rest[0] === 'function' ? {} : { params: rest[0], ...rest[1] })
        .then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}

export function post (url: string, ...rest: any) {
    if (ajaxMap[url]) return false;

    ajaxMap[url] = 1;
    return axios.post(url, rest[0], rest[1])
        .then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}

export function longPost (url: string, ...rest: any) {
    if (ajaxMap[url]) return false;

    ajaxMap[url] = 1;
    return longAxios.post(url, rest[0], rest[1])
        .then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}

export function put (url: string, ...rest: any) {
    if (ajaxMap[url]) return false;

    ajaxMap[url] = 1;
    return axios.put(url, rest[0])
        .then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}

export function $delete (url: string, ...rest: any) {
    if (ajaxMap[url]) return false;

    ajaxMap[url] = 1;
    return axios.delete(url, rest[0])
        .then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}
export function postFormData (url: string, ...rest: any) {
    if (ajaxMap[url]) return false;
    ajaxMap[url] = 1;
    return axios2.post(url, rest[0], rest[1])
        .then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}
export function postFormData2 (url: string, ...rest: any) {
    return axios2.post(url, rest[0], rest[1])
        .then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}
export function postBigFile (url: string, ...rest: any) {
    return bigFileAxios.post(url, rest[0], rest[1])
        .then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}
export function postJson (url: string, ...rest: any) {
    // if (ajaxMap[url]) return false;
    ajaxMap[url] = 1;
    return axios3.post(url, rest[0], rest[1])
        .then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}
export function getFile (url: string, ...rest: any) {
    console.log(url, rest)
    if (ajaxMap[url]) return false;
    ajaxMap[url] = 1;
    return axios.get(url, rest[1])
        .then(res => handleSuccessWithFile(res, url)).catch(e => handleError(e, url))
}