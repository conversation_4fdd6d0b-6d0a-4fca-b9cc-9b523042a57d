# 运营平台模板

TypeScript, ReactJs + UmiJs + DvaJs + Antd

```
├─dist     生成目录
├─config   配置文件
├─mock     mock
├─public   开放资源
└─src	   源码
    ├─assets	资源文件
    ├─components 组件,V
    ├─functions  方法
	├─layouts	 布局
	├─models	 M
	├─pages		 C
	├─services	 ajax C
	├─themes	 CSS
	├─types		 for typescript
    └─utils		 工具
```

## 使用

### 操作命令

#### 安装依赖(建议用cnpm或yarn)
``` sh
npm i
```

或执行`install.sh`

#### 本地开发

``` sh
npm run start
```

或

``` sh
npm run dev
```

#### 打包

``` sh
npm run build
```

#### 上传测试服务器

``` sh
npm run upload
```

## 反馈
<EMAIL>

## 生产环境发布
1、拷贝出开发分支的代码

2、提 mr 到 master

3、用 master 分支打包产物

``` sh
npm run build:prod
```

4、产物拷贝到内网走生产发布流程